.messageWrapper {
  padding: 0 0 16px 0;
  box-sizing: border-box;
}

.messageContainer {
  display: flex;
  
  &.userContainer {
    justify-content: flex-end;
  }
  
  &.systemContainer {
    justify-content: flex-start;
  }
}

.bubble {
  padding: 12px;
  border-radius: 24px;
  position: relative;
  font-family: 'Quasimoda', sans-serif;
  
  &.userBubble {
    background-color: #E5E7EB;

    
    .caption {
      color: #000000;
    }
  }
  
  &.systemBubble {
    background-color: #FFFFFF;
    border-bottom-left-radius: 4px;

    
    .caption {
      color: #000000;
    }
  }
}

.image {
  width: 100%;
  border-radius: 8px;
}

.caption {
  margin: 8px 0 0;
  font-size: 14px;
  line-height: 150%;
  white-space: pre-wrap;
  word-break: break-word;
}

.logoContainer {
  width: 32px;
  height: 32px;
  margin-right: 12px;
}
