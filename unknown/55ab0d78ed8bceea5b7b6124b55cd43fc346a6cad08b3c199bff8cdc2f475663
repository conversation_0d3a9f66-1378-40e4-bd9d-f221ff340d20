import React, { useState } from "react";
import type { <PERSON><PERSON>, StoryObj } from "@storybook/react";
import { Radio } from "./Radio";
import { RadioState } from "./Radio.types";

const meta = {
  title: "Components/Radio",
  component: Radio,
  parameters: {
    layout: "centered",
  },
  tags: ["autodocs"],
  argTypes: {
    state: {
      control: "select",
      options: Object.values(RadioState),
      defaultValue: RadioState.NORMAL,
    },
  },
} satisfies Meta<typeof Radio>;

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
  args: {
    labelText: "Radio option",
    helperText: "Helper text goes here",
    state: RadioState.NORMAL,
  },
};

export const AllStates: Story = {
  render: () => (
    <div className="flex flex-col gap-4">
      <Radio
        labelText="Normal state"
        helperText="This is a normal radio button"
        state={RadioState.NORMAL}
      />
      <Radio
        labelText="Checked state"
        helperText="This is a checked radio button"
        state={RadioState.CHECKED}
        checked
      />
      <Radio
        labelText="Error state"
        helperText="This is an error state"
        errorText="Error message goes here"
        state={RadioState.ERROR}
      />
      <Radio
        labelText="Disabled state"
        helperText="This is a disabled radio button"
        state={RadioState.DISABLED}
      />
    </div>
  ),
};

export const WithHelperText: Story = {
  args: {
    ...Default.args,
    helperText: "Additional information about this option",
  },
};

export const WithError: Story = {
  args: {
    ...Default.args,
    state: RadioState.ERROR,
    errorText: "Please select an option",
  },
};

export const Controlled: Story = {
  render: () => {
    const [checked, setChecked] = useState(false);

    return (
      <Radio
        labelText="Controlled radio"
        helperText="Click to toggle"
        checked={checked}
        onChange={setChecked}
      />
    );
  },
};

export const RadioGroup: Story = {
  render: () => {
    const [selected, setSelected] = useState("1");

    return (
      <div className="flex flex-col gap-4">
        <Radio
          labelText="Option 1"
          helperText="First option description"
          name="group"
          value="1"
          checked={selected === "1"}
          onChange={() => setSelected("1")}
        />
        <Radio
          labelText="Option 2"
          helperText="Second option description"
          name="group"
          value="2"
          checked={selected === "2"}
          onChange={() => setSelected("2")}
        />
        <Radio
          labelText="Option 3"
          helperText="Third option description"
          name="group"
          value="3"
          checked={selected === "3"}
          onChange={() => setSelected("3")}
        />
      </div>
    );
  },
};
