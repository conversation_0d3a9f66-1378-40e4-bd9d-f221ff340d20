services:
  postgres:
    image: postgres:16-alpine
    restart: unless-stopped
    environment:
      POSTGRES_USER: dev-user
      POSTGRES_PASSWORD: password
      POSTGRES_DB: dev_db
    ports:
      - "5432:5432"
    volumes:
      - db-data:/var/lib/postgresql/data:cached
#  qdrant-test:
#    image: qdrant/qdrant
#    ports:
#      - "6333:6333"
#      - "6334:6334"
#    environment:
#      QDRANT__SERVICE__HTTP_PORT: 6333
#      QDRANT__SERVICE__GRPC_PORT: 6334
#      #QDRANT__SERVICE__API_KEY: test-key
#    restart: unless-stopped
#    volumes:
#      - qdrant_test_data:/qdrant/storage
  backend:
    build:
      context: .
      dockerfile: Dockerfile
      target: runtime
    tty: true
    environment:
      PYTHONPATH: .
      DATABASE_URL: "dev-user:password@postgres:5432/dev_db"
      SENTRY_ENVIRONMENT: "local"
      SENTRY_DSN: "https://<EMAIL>/4508080474751056"
      CRYSTAL_ROOF_API_KEY: "DEMO"
      AI_ENGINE_API_KEY: "test_apiengine_key"
      QDRANT_DB_URL: "https://28f80180-ee5f-4df4-b161-f4b3feeaf54b.us-east4-0.gcp.cloud.qdrant.io"
      QDRANT_API_KEY: "1kR99FiB6mFvJult_IZS30N8xVIByLv5Sz4ySOBfROIJbsQ8ktnJgQ"
      OPEN_AI_API_KEY: test_key
      ANTHROPIC_API_KEY: test_key
      TOGETHER_AI_API_KEY: test_key
      LANGSMITH_API_KEY: test_key
      GEMINI_API_KEY: test_key
      BACKEND_API_BASE_URL: test_key
    depends_on:
      - "postgres"
    ports:
      - "8000:8000"
  postgres-tests:
    image: postgres:16-alpine
    restart: unless-stopped
    environment:
      POSTGRES_USER: test-user
      POSTGRES_PASSWORD: password
      POSTGRES_DB: test_db
    ports:
      - "5433:5432"
    volumes:
      - test-db-data:/var/lib/postgresql/data:cached
  backend-tests:
    build:
      context: .
      dockerfile: Dockerfile
      target: test
    tty: true
    environment:
      PYTHONPATH: .
      DATABASE_URL: "test-user:password@postgres-tests:5432/test_db"
      AI_ENGINE_API_KEY: "test_apiengine_key"
      QDRANT_DB_URL: "http://qdrant-test:6333"
      QDRANT_API_KEY: "test-key"
      OPEN_AI_API_KEY: test_key
      ANTHROPIC_API_KEY: test_key
      TOGETHER_AI_API_KEY: test_key
      LANGSMITH_API_KEY: test_key
      GEMINI_API_KEY: test_key
      BACKEND_API_BASE_URL: test_key
    depends_on:
      - "postgres-tests"
      #- "qdrant-test"

volumes:
  db-data:
  test-db-data:
#  qdrant_test_data:
