from typing import Annotated

from fastapi import Depends, APIRouter

from src.db_models.user import User
from src.dependencies import get_user_service
from src.schemas import UserDetails, UserUpdate, B2BDemoRequest
from src.services.auth import get_current_user
from src.services.users import UserService

router = APIRouter(
    prefix="/user",
    tags=["user"],
)


@router.get("/", response_model=UserDetails)
async def get_current_user_details_endpoint(current_user: Annotated[User, Depends(get_current_user)]):
    return current_user


@router.patch("/", response_model=UserDetails)
async def update_user_details_endpoint(
    current_user: Annotated[User, Depends(get_current_user)],
    user_update: UserUpdate,
    user_service: UserService = Depends(get_user_service),
):
    updated_user = await user_service.update_user(current_user, user_update)
    return updated_user


@router.post("/request-b2b-demo/")
async def request_b2b_demo_endpoint(
    current_user: Annotated[User, Depends(get_current_user)],
    b2b_demo_request: B2BDemoRequest,
    user_service: UserService = Depends(get_user_service),
):
    await user_service.request_b2b_demo(current_user, b2b_demo_request)
