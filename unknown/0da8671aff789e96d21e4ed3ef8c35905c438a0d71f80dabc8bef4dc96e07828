/// <reference types="vitest" />
import { defineConfig, UserConfig } from 'vitest/config'
import react from '@vitejs/plugin-react'
import { resolve } from 'path'

export default defineConfig({
  plugins: [react()] as UserConfig['plugins'],
  test: {
    environment: 'jsdom',
    setupFiles: ['./vitest.setup.ts'],
    globals: true,
    alias: {
      '@': resolve(__dirname, './src')
    },
    deps: {
      inline: ['@clerk/nextjs']
    },
    pool: 'threads',
    poolOptions: {
      threads: {
        minThreads: 1,
        maxThreads: 2,
      }
    },
    fileParallelism: false
  },
  resolve: {
    alias: {
      'next/headers': resolve(__dirname, './__mocks__/next/headers.ts'),
      'next/navigation': resolve(__dirname, './__mocks__/next/navigation.ts')
    }
  }
}) 