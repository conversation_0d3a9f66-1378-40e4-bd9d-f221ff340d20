import aiohttp
import pytest
from aioresponses import aioresponses

from .crystalroof import CrystalRoofAPI, Postcode, Coordinates, UPRN


@pytest.mark.asyncio(loop_scope="session")
class TestCrystalRoofAPI:
    api_key = "DEMO"
    locations = [Postcode("W60WW"), Coordinates("51.496105", "-0.242921"), UPRN("34134534")]

    @pytest.fixture(scope="class")
    def api(self):
        return CrystalRoofAPI(api_key=self.api_key)

    @pytest.mark.asyncio
    async def test_get_identifier_methods(self):
        postcode = Postcode("W60WW")
        assert postcode.get_identifier() == "W60WW"

        coordinates = Coordinates("51.496105", "-0.242921")
        assert coordinates.get_identifier() == "51.496105,-0.242921"

        uprn = UPRN("34134534")
        assert uprn.get_identifier() == "uprn34134534"

    @pytest.mark.asyncio
    async def test_get_accommodation_type_success(self, api):
        mock_data = {
            "data": {
                "residents": {
                    "Total": 85,
                    "Detached": 1,
                    "Semi-detached": 0,
                    "Terraced": 4,
                    "In a purpose-built block of flats or tenement": 77,
                    "Part of a converted or shared house, including bedsits": 3,
                    "Part of another converted building, for example, former school, church or warehouse": 0,
                    "In a commercial building, for example, in an office building, hotel or over a shop": 0,
                    "A caravan or other mobile or temporary structure": 0,
                },
                "rank": {
                    "Detached": 1,
                    "Semi-detached": 1,
                    "Terraced": 1,
                    "In a purpose-built block of flats or tenement": 10,
                    "Part of a converted or shared house, including bedsits": 2,
                    "Part of another converted building, for example, former school, church or warehouse": 1,
                    "In a commercial building, for example, in an office building, hotel or over a shop": 1,
                    "A caravan or other mobile or temporary structure": 1,
                },
            },
            "dataDate": "2021-03-21",
            "refreshedDate": "2021-03-21",
        }

        for location in self.locations:
            url = (
                f"{api._base_url}/housing/accommodation-type/postcode/v1/"
                f"{location.get_identifier()}?api_key={self.api_key}"
            )

            with aioresponses() as m:
                m.get(url, payload=mock_data, status=200)
                result = await api.get_accommodation_type(location)
                assert result == mock_data

    @pytest.mark.asyncio
    async def test_get_mean_household_income_success(self, api):

        mock_data = {
            "data": {"totalMeanAnnualIncome": 64200, "totalMeanAnnualIncomeRank": 8},
            "dataDate": "2022",
            "refreshedDate": "2023-10-01",
        }
        for location in self.locations:
            url = (
                f"{api._base_url}/income/mean-household-income/postcode/v1/"
                f"{location.get_identifier()}?api_key={self.api_key}"
            )

            with aioresponses() as m:
                m.get(url, payload=mock_data, status=200)
                result = await api.get_mean_household_income(location)
                assert result.model_dump() == mock_data

    @pytest.mark.asyncio
    async def test_get_accommodation_type_invalid_location(self, api):
        location = Postcode("INVALID_POSTCODE")
        url = (
            f"{api._base_url}/housing/accommodation-type/postcode/v1/{location.get_identifier()}?api_key={self.api_key}"
        )

        with aioresponses() as m:
            m.get(url, status=404)
            with pytest.raises(aiohttp.ClientResponseError) as exc_info:
                await api.get_accommodation_type(location)
            assert exc_info.value.status == 404

    @pytest.mark.asyncio
    async def test_get_accommodation_type_server_error(self, api):
        location = Postcode("W60WW")
        url = (
            f"{api._base_url}/housing/accommodation-type/postcode/v1/{location.get_identifier()}?api_key={self.api_key}"
        )

        with aioresponses() as m:
            m.get(url, status=500)
            with pytest.raises(aiohttp.ClientResponseError) as exc_info:
                await api.get_accommodation_type(location)
            assert exc_info.value.status == 500

    @pytest.mark.asyncio
    async def test_get_accommodation_type_invalid_api_key(self, api):
        api._api_key = "INVALID_API_KEY"
        location = Postcode("W60WW")
        url = (
            f"{api._base_url}/housing/accommodation-type/postcode/v1/{location.get_identifier()}?api_key={api._api_key}"
        )

        with aioresponses() as m:
            m.get(url, status=401)
            with pytest.raises(aiohttp.ClientResponseError) as exc_info:
                await api.get_accommodation_type(location)
            assert exc_info.value.status == 401
