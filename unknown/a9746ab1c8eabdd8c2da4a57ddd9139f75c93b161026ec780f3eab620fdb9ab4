from unittest.mock import Mock

import pytest
from sendgrid import SendGridAP<PERSON>lient

from .sendgrid import SendgridService


@pytest.mark.asyncio(loop_scope="session")
class TestSendgridService:
    async def test_sends_email_successfully(self, mocker):
        mock_send = mocker.patch.object(SendGridAPIClient, "send", new_callable=Mock)
        mock_send.return_value.status_code = 202
        service = SendgridService(api_key="test", default_from_email="<EMAIL>", stage="test")
        await service.send_welcome_email("<EMAIL>", "Firstname")
        assert mock_send.called

    async def test_fails_to_send_email(self, mocker):
        mock_send = mocker.patch.object(SendGridAPIClient, "send", new_callable=Mock)
        mock_send.side_effect = ValueError("Invalid email address")
        service = SendgridService(api_key="test", default_from_email="<EMAIL>", stage="test")
        with pytest.raises(ValueError, match="Invalid email address"):
            await service.send_welcome_email("<EMAIL>", "Firstname")
        assert mock_send.called
