import pytest
from unittest.mock import patch, AsyncMock, MagicMock
from pathlib import Path
import os
from PIL import Image

from src.services.DocumentSummarizationService import DocumentSummarizationService


@pytest.fixture
def service():
    os.environ["ANTHROPIC_API_KEY"] = "test_key"
    os.environ["GEMINI_API_KEY"] = "test_key"
    return DocumentSummarizationService()


@pytest.fixture
def mock_anthropic_messages():
    messages = MagicMock()
    messages.create = MagicMock(return_value=MagicMock(content=[MagicMock(text="Test description")]))
    return messages


@pytest.mark.asyncio
async def test_process_pdf(service, mock_anthropic_messages):
    with patch("pdf2image.convert_from_path") as mock_convert, patch.object(
        service.anthropic_client, "messages", mock_anthropic_messages
    ):
        mock_convert.return_value = [Image.new("RGB", (60, 30), color="red")]
        result = service.process_pdf(Path(__file__))
        assert result == "Test description"


@pytest.mark.asyncio
async def test_process_image(service, mock_anthropic_messages):
    with patch("PIL.Image.open") as mock_image_open, patch.object(
        service.anthropic_client, "messages", mock_anthropic_messages
    ):
        mock_image_open.return_value.__enter__.return_value = Image.new("RGB", (60, 30), color="red")
        result = service.process_image(Path(__file__))
        assert result == "Test description"


# @pytest.mark.asyncio
# async def test_process_file_routing(service):
#     with patch.object(service, 'process_pdf', AsyncMock(return_value="PDF description")), \
#             patch.object(service, 'process_image', AsyncMock(return_value="Image description")), \
#             patch('pathlib.Path.exists', return_value=True):
#         pdf_result = await service.process_file(Path('test.pdf'))
#         print('PDF Result: ' + str(pdf_result))
#         #assert await service.process_file(Path('test.pdf')) == "PDF description"
#         #assert await service.process_file(Path('test.jpg')) == "Image description"
#         assert True


@pytest.mark.asyncio
async def test_process_file_invalid_path(service):
    with pytest.raises(FileNotFoundError):
        await service.process_file(Path("nonexistent.pdf"))
