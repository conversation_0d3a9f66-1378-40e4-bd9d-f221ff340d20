from datetime import datetime
from typing import Optional, List

from sqlalchemy import Integer, ForeignKey
from sqlalchemy.orm import mapped_column, Mapped, relationship

from .base import BaseModel


class Appliance(BaseModel):
    __tablename__ = "appliances"

    id: Mapped[int] = mapped_column(Integer, primary_key=True, index=True)
    type: Mapped[str]
    brand: Mapped[Optional[str]]
    model: Mapped[Optional[str]]
    serialNumber: Mapped[Optional[str]]
    warranty: Mapped[Optional[str]]
    dateOfPurchase: Mapped[Optional[datetime]]
    otherDetails: Mapped[Optional[str]]  # description, service history, etc.
    # relationships:
    propertyId: Mapped[int] = mapped_column(ForeignKey("properties.id"))
    property: Mapped["Property"] = relationship(back_populates="appliances")
    invoiceReceiptDocumentId: Mapped[Optional[int]] = mapped_column(ForeignKey("documents.id"))
    invoiceReceiptDocument: Mapped[Optional["Document"]] = relationship(back_populates="appliances")
    documents: Mapped[List["Document"]] = relationship(
        "Document",
        primaryjoin="and_(SourceLink.destTable == 'appliances', SourceLink.destId == foreign(Appliance.id), SourceLink.srcType == 'document', remote(SourceLink.srcId) == Document.id)",
        viewonly=True,
        lazy="immediate",
        uselist=True,
    )
