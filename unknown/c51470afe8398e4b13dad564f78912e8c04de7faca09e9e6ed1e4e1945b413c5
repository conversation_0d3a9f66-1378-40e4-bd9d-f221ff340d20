@use 'tokens';
@use 'sass:map';
@use 'sass:meta';

:root {
  // Generate CSS variables for primitive colors
  @each $color-name, $color-variants in tokens.$colors {
    @each $variant, $value in $color-variants {
      --color-#{$color-name}-#{$variant}: #{$value};
    }
  }

  // Generate CSS variables for spacing
  @each $space-name, $space-value in tokens.$spacing {
    --spacing-#{$space-name}: #{$space-value};
  }

  // Generate semantic color variables for each theme
  @each $theme, $categories in tokens.$semantic-colors {
    @each $category, $variants in $categories {
      @each $variant, $value in $variants {
        --#{$theme}-#{$category}-#{$variant}: #{$value};
      }
    }
  }

  --font-primary: #{meta.inspect(map.get(tokens.$font-families, 'primary'))};
  --font-heading: #{meta.inspect(map.get(tokens.$font-families, 'heading'))};
}

$font-family-primary: var(--font-primary);
$font-family-heading: var(--font-heading);

:export {
  fontFamilyPrimary: $font-family-primary;
  fontFamilyHeading: $font-family-heading;
} 