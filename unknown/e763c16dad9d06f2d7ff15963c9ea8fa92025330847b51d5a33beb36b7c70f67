from typing import List, Optional

from sqlalchemy import Integer, ForeignKey
from sqlalchemy.orm import mapped_column, Mapped, relationship

from .base import BaseModel
from .relationships import jobs_professionals


class Professional(BaseModel):
    __tablename__ = "professionals"

    id: Mapped[int] = mapped_column(Integer, primary_key=True, index=True)
    firstName: Mapped[str]
    lastName: Mapped[Optional[str]]
    serviceProviderId: Mapped[int] = mapped_column(ForeignKey("service_providers.id"))
    serviceProvider: Mapped["ServiceProvider"] = relationship(back_populates="professionals")
    jobs: Mapped[List["Job"]] = relationship(
        "Job", secondary=jobs_professionals, back_populates="professionals", lazy="selectin"
    )
