import { ReactNode } from "react";

export interface DropdownItem {
  icon?: React.ReactNode;
  label: string;
  onClick?: () => void | Promise<void>;
  href?: string;
  variant?: 'default' | 'danger';
  divider?: boolean;
  disabled?: boolean;
  loading?: boolean;
}

export interface DropdownsProps {
  items: DropdownItem[];
  trigger?: ReactNode;
  className?: string;
  menuClassName?: string;
  itemClassName?: string;
  placement?: 'left' | 'right';
  showIcon?: boolean;
  menuIcon?: string;
}
