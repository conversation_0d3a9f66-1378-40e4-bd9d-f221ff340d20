from decimal import Decimal
from typing import List, Optional, Any

from sqlalchemy import Integer, BigInteger, Numeric
from sqlalchemy.orm import relationship, Mapped, mapped_column

from .base import BaseModel
from .relationships import users_addresses


class Address(BaseModel):
    __tablename__ = "addresses"

    id: Mapped[int] = mapped_column(Integer, primary_key=True, index=True)
    streetLine1: Mapped[str]
    streetLine2: Mapped[Optional[str]]
    streetLine3: Mapped[Optional[str]]
    townOrCity: Mapped[str]
    postcode: Mapped[str]
    locality: Mapped[Optional[str]]
    country: Mapped[str]
    houseAccess: Mapped[Optional[str]]
    parkingInstructions: Mapped[Optional[str]]
    uprn: Mapped[Optional[int]] = mapped_column(BigInteger)
    udprn: Mapped[Optional[int]] = mapped_column(BigInteger)
    umprn: Mapped[Optional[int]] = mapped_column(BigInteger)
    latitude: Mapped[Optional[Decimal]] = mapped_column(Numeric(precision=8, scale=6))
    longitude: Mapped[Optional[Decimal]] = mapped_column(Numeric(precision=8, scale=6))
    osPlacesOutput: Mapped[Optional[dict[str, Any]]]
    idealPostcodesOutput: Mapped[Optional[dict[str, Any]]]
    users: Mapped[List["User"]] = relationship("User", secondary=users_addresses, back_populates="addresses")
    property: Mapped["Property"] = relationship(back_populates="address", lazy="selectin")
    bookings: Mapped[List["Booking"]] = relationship(back_populates="address")
