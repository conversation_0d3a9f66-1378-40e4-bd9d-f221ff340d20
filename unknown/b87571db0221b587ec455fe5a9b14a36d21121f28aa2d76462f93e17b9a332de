import asyncio
import logging
import pathlib
import uuid
from datetime import datetime, timed<PERSON><PERSON>
from math import ceil
from os import environ
from typing import AsyncGenerator, Optional

import aioboto3
import boto3
from fastapi import UploadFile
from sentry_sdk import capture_exception as sentry_capture_exception
from sqlalchemy import select, func, update
from sqlalchemy.ext.asyncio import AsyncSession

from src.db_models.document import Document, DocumentStatusType
from src.db_models.message import Message
from src.db_models.user import User
from src.integrations.aiengine import AIEngineAPI
from src.schemas import DocumentAiUpdate
from src.services.properties import PropertyService

logger = logging.getLogger("uvicorn")


class UnsupportedDocumentType(Exception):
    pass


class DocumentSizeLimitExceeded(Exception):
    pass


class UserDocumentSizeLimitExceeded(Exception):
    pass


class DocumentDoesNotExist(Exception):
    pass


class DocumentService:
    # also check AttachmentDetails in aiengine
    _ALLOWED_DOCUMENT_EXTENSIONS = {"png", "jpg", "jpeg", "webp", "gif", "pdf", "heic", "heif"}
    _MAXIMUM_DOCUMENT_SIZE_IN_KILOBYTES = 20_000
    _MAXIMUM_DOCUMENTS_SIZE_PER_USER_IN_KILOBYTES = 500_000

    def __init__(
        self,
        db: AsyncSession,
        property_service: PropertyService,
        aioboto3_session: aioboto3.Session,
        ai_engine: AIEngineAPI,
        s3_bucket_name: str = None,
    ):
        self._db = db
        self._property_service = property_service
        self._aioboto3_session = aioboto3_session
        self._ai_engine = ai_engine
        if s3_bucket_name:
            self._s3_bucket_name = s3_bucket_name
        else:
            self._s3_bucket_name = environ["DOCUMENTS_S3_BUCKET_NAME"]

    async def timeout_documents(self):
        threshold = datetime.now() - timedelta(minutes=15)
        logger.info(f"Timeout documents updated before {threshold.isoformat()}")
        result = await self._db.execute(
            update(Document)
            .where(
                Document.status.in_([DocumentStatusType.saved, DocumentStatusType.processing]),
                Document.updated_at < threshold,
            )
            .values(status=DocumentStatusType.error, errorMessage="Processing timeout")
        )
        await self._db.commit()
        logger.info(f"Documents updated before {threshold.isoformat()}: {result.rowcount}")

    async def save_document(self, document: UploadFile, user: User, upload_context: Optional[str] = None) -> Document:
        document_file_extension = self._get_extension_from_filename(document.filename)

        if document_file_extension not in self._ALLOWED_DOCUMENT_EXTENSIONS:
            raise UnsupportedDocumentType(f"Document type {document.content_type} is not supported.")

        if not document.size:
            raise DocumentSizeLimitExceeded("Document size is missing.")

        document_size_in_kilobytes = ceil(document.size / 1000)

        if document_size_in_kilobytes > self._MAXIMUM_DOCUMENT_SIZE_IN_KILOBYTES:
            raise DocumentSizeLimitExceeded(
                f"Document size exceeds the limit of {self._MAXIMUM_DOCUMENT_SIZE_IN_KILOBYTES} KB."
            )

        if not await self._is_document_size_allowed_for_user(user, document_size_in_kilobytes):
            raise UserDocumentSizeLimitExceeded(
                f"""User's total document size exceeds the limit of
                 {self._MAXIMUM_DOCUMENTS_SIZE_PER_USER_IN_KILOBYTES} KB."""
            )

        s3_file_key = f"user={user.id}/{uuid.uuid4()}.{document_file_extension}"

        async with self._aioboto3_session.client("s3") as s3:
            try:
                await s3.put_object(
                    Bucket=self._s3_bucket_name,
                    Key=s3_file_key,
                    Body=document.file,
                    ContentType=document.content_type,
                    Metadata={"originalFilename": document.filename.encode("ascii", "backslashreplace").decode()},
                    # US-ASCII characters only, backslashreplace to handle non-ASCII characters
                )
            except Exception as e:
                logger.error(f"Failed to put document in S3: {e}")
                raise

        users_property = await self._property_service.get_first_property_for_user(user)

        await self.mark_documents_status_displayed(user, upload_context)

        db_document = Document(
            userId=user.id,
            source="user",
            sizeInKiloBytes=document_size_in_kilobytes,
            s3Key=s3_file_key,
            s3Bucket=self._s3_bucket_name,
            fileExtension=document_file_extension,
            originalFileName=document.filename,
            browserMimeType=document.content_type,
            property=users_property,
            status=DocumentStatusType.saved,
            uploadContext=upload_context,
        )
        self._db.add(db_document)
        await self._db.flush()

        await self._on_document_saved(db_document)

        return db_document

    async def _on_document_saved(self, document: Document) -> None:
        asyncio.create_task(self._ai_engine.parse_file(document))

    @staticmethod
    def list_documents_for_user_query(
        user: User,
        upload_context: str = None,
        status: Optional[DocumentStatusType] = None,
        exclude_displayed_errors: bool = True,
    ):
        stmt = select(Document).where(Document.userId == user.id)
        if upload_context and upload_context != "filesPage":
            stmt = stmt.where(Document.uploadContext == upload_context)
        if status:
            stmt = stmt.where(Document.status == status)
        if exclude_displayed_errors:
            stmt = stmt.where(
                (Document.status.not_in([DocumentStatusType.error, DocumentStatusType.irrelevant]))
                | (
                    Document.status.in_([DocumentStatusType.error, DocumentStatusType.irrelevant])
                    & (Document.hasStatusBeenDisplayed.is_(False))
                )
            )
        return stmt

    @staticmethod
    def list_documents_for_chat_query(chat_id: int):
        return select(Document).join(Message.documents).where(Message.chatId == chat_id)

    @staticmethod
    def get_presigned_url_expiration_in_seconds():
        return 604800

    async def get_presigned_urls_of_chat_documents(self, chat_id: int) -> list[str]:
        documents = (await self._db.scalars(self.list_documents_for_chat_query(chat_id))).all()
        presigned_urls = []

        for document in documents:
            try:
                presigned_url = self.get_presigned_url_from_s3(
                    s3_bucket=document.s3Bucket,
                    s3_key=document.s3Key,
                    expires_in_seconds=self.get_presigned_url_expiration_in_seconds(),
                )
                presigned_urls.append(presigned_url)
            except Exception as e:
                logger.error(f"Failed to generate presigned URL for document {document.id}: {e}")
                sentry_capture_exception(e)
                continue

        return presigned_urls

    async def mark_documents_status_displayed(self, user: User, upload_context: Optional[str] = None) -> None:
        stmt = update(Document).where(
            Document.userId == user.id,
            Document.status.in_([DocumentStatusType.error, DocumentStatusType.irrelevant]),
            Document.hasStatusBeenDisplayed.is_(False),
        )
        if upload_context and upload_context != "filesPage":
            stmt = stmt.where(Document.uploadContext == upload_context)
        stmt = stmt.values(hasStatusBeenDisplayed=True)

        await self._db.execute(stmt)

        await self._db.flush()

    async def update_document_by_ai(self, document_id: int, document_update: DocumentAiUpdate) -> Document:
        update_dict = document_update.model_dump(exclude_unset=True)

        stmt = update(Document).where(Document.id == document_id).values(**update_dict).returning(Document)
        document = (await self._db.execute(stmt)).scalar_one_or_none()

        if document is None:
            raise DocumentDoesNotExist(f"Document with id {document_id} does not exist or is not saved.")

        return document

    async def delete_document(self, document_id: int, user: User) -> None:
        document = (
            await self._db.execute(select(Document).where(Document.id == document_id, Document.userId == user.id))
        ).scalar()

        if document is None:
            raise DocumentDoesNotExist(f"Document with id {document_id} does not exist.")

        async with self._aioboto3_session.client("s3") as s3:
            try:
                await s3.delete_object(Bucket=document.s3Bucket, Key=document.s3Key)
            except Exception as e:
                logger.error(f"Failed to delete document from S3: {e}")
                raise
        await self._db.delete(document)
        await self._db.flush()

    async def get_document_for_user(self, document_id: int, user: User) -> Document:
        document = (
            await self._db.execute(select(Document).where(Document.id == document_id, Document.userId == user.id))
        ).scalar()

        if document is None:
            raise DocumentDoesNotExist(f"Document with id {document_id} does not exist.")

        return document

    async def get_document_content_from_s3(self, s3_bucket: str, s3_key: str) -> AsyncGenerator:
        async with self._aioboto3_session.client("s3") as s3:
            try:
                response = await s3.get_object(Bucket=s3_bucket, Key=s3_key)
                async with response["Body"] as stream:
                    async for data, _ in stream.content.iter_chunks():
                        yield data
            except Exception as e:
                logger.error(f"Failed to get document from S3: {e}")
                raise

    @staticmethod
    def get_presigned_url_from_s3(s3_bucket: str, s3_key: str, expires_in_seconds: int) -> str:
        s3_client = boto3.client("s3")
        try:
            url = s3_client.generate_presigned_url(
                ClientMethod="get_object", Params={"Bucket": s3_bucket, "Key": s3_key}, ExpiresIn=expires_in_seconds
            )
            return url
        except Exception as e:
            logger.error(f"Failed to generate presigned URL from S3: {e}")
            raise

    @staticmethod
    def _get_extension_from_filename(filename: str) -> str:
        return pathlib.Path(filename).suffix[1:].lower()

    async def _get_user_total_document_size_in_kilobytes(self, user: User) -> int:
        result = await self._db.execute(select(func.sum(Document.sizeInKiloBytes)).where(Document.userId == user.id))
        total_size = result.scalar() or 0
        return total_size

    async def _is_document_size_allowed_for_user(self, user: User, size: int) -> bool:
        return (
            await self._get_user_total_document_size_in_kilobytes(user) + size
            <= self._MAXIMUM_DOCUMENTS_SIZE_PER_USER_IN_KILOBYTES
        )

    async def close_db_session(self):
        await self._db.close()
