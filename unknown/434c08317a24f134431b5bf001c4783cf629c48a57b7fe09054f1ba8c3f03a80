import logging
from typing import Dict, List, Any, Optional
from src.db_models.property import Property


class PropertyDetailsManager:
    """
    Only supports update and do_nothing operations (no insert for properties).
    """

    def __init__(self):
        self.logger = logging.getLogger("uvicorn")

    def _normalize_string(self, value: Any) -> Optional[str]:
        if value is None:
            return None
        if isinstance(value, str):
            stripped = value.strip()
            return stripped if stripped else None
        return str(value).strip() if str(value).strip() else None

    def _extract_non_null_fields(self, data: Dict[str, Any]) -> Dict[str, Any]:
        result = {}
        for key, value in data.items():
            if value is not None:
                # Handle different data types appropriately
                if isinstance(value, str):
                    normalized_value = self._normalize_string(value)
                    if normalized_value is not None:
                        result[key] = normalized_value
                else:
                    result[key] = value
        return result

    def _get_property_dict(self, property_obj: Property) -> Dict[str, Any]:
        address_dict = None
        if property_obj.address:
            address_dict = {
                "streetLine1": property_obj.address.streetLine1,
                "streetLine2": property_obj.address.streetLine2,
                "townOrCity": property_obj.address.townOrCity,
                "postcode": property_obj.address.postcode,
                "houseAccess": property_obj.address.houseAccess,
                "parkingInstructions": property_obj.address.parkingInstructions
            }

        return {
            "id": property_obj.id,
            "address": address_dict,
            "type": property_obj.type.value if property_obj.type else None,
            "tenureType": property_obj.tenureType.value if property_obj.tenureType else None,
            "sizeInSqft": property_obj.sizeInSqft,
            "onFloorLevel": property_obj.onFloorLevel,
            "hasBalconyTerrace": property_obj.hasBalconyTerrace,
            "balconyTerraceDetails": property_obj.balconyTerraceDetails,
            "hasGarden": property_obj.hasGarden,
            "gardenDetails": property_obj.gardenDetails,
            "hasSwimmingPool": property_obj.hasSwimmingPool,
            "swimmingPoolDetails": property_obj.swimmingPoolDetails,
            "numberOfBedrooms": property_obj.numberOfBedrooms,
            "numberOfBathrooms": property_obj.numberOfBathrooms,
            "numberOfFloors": property_obj.numberOfFloors,
            "lastSoldPriceInGbp": property_obj.lastSoldPriceInGbp,
            "condition": property_obj.condition.value if property_obj.condition else None,
            "yearsOfOwnership": property_obj.yearsOfOwnership,
            "architecturalType": property_obj.architecturalType,
            "valuationInGbp": property_obj.valuationInGbp,
            "conservationStatus": property_obj.conservationStatus.value if property_obj.conservationStatus else None,
            "typeOfLock": property_obj.typeOfLock,
            "typeOfConstruction": property_obj.typeOfConstruction,
            "proportionOfFlatRoof": property_obj.proportionOfFlatRoof,
            "epcRating": property_obj.epcRating.value if property_obj.epcRating else None
        }

    def _map_extracted_data(self, extracted_data: Dict[str, Any]) -> Dict[str, Any]:
        return {
            "address": extracted_data.get("address"),
            "type": extracted_data.get("type"),
            "tenureType": extracted_data.get("tenureType"),
            "sizeInSqft": extracted_data.get("sizeInSqft"),
            "onFloorLevel": extracted_data.get("onFloorLevel"),
            "hasBalconyTerrace": extracted_data.get("hasBalconyTerrace"),
            "balconyTerraceDetails": extracted_data.get("balconyTerraceDetails"),
            "hasGarden": extracted_data.get("hasGarden"),
            "gardenDetails": extracted_data.get("gardenDetails"),
            "hasSwimmingPool": extracted_data.get("hasSwimmingPool"),
            "swimmingPoolDetails": extracted_data.get("swimmingPoolDetails"),
            "numberOfBedrooms": extracted_data.get("numberOfBedrooms"),
            "numberOfBathrooms": extracted_data.get("numberOfBathrooms"),
            "numberOfFloors": extracted_data.get("numberOfFloors"),
            "lastSoldPriceInGbp": extracted_data.get("lastSoldPriceInGbp"),
            "condition": extracted_data.get("condition"),
            "yearsOfOwnership": extracted_data.get("yearsOfOwnership"),
            "architecturalType": extracted_data.get("architecturalType"),
            "valuationInGbp": extracted_data.get("valuationInGbp"),
            "conservationStatus": extracted_data.get("conservationStatus"),
            "typeOfLock": extracted_data.get("typeOfLock"),
            "typeOfConstruction": extracted_data.get("typeOfConstruction"),
            "proportionOfFlatRoof": extracted_data.get("proportionOfFlatRoof"),
            "epcRating": extracted_data.get("epcRating")
        }

    def _concatenate_string_values(self, existing_value: str, new_value: str) -> str:
        """Concatenate string values with semicolon separator if both exist"""
        existing_norm = self._normalize_string(existing_value)
        new_norm = self._normalize_string(new_value)

        if not existing_norm:
            return new_norm or ""
        if not new_norm:
            return existing_norm

        # Check if new value is already in existing value
        if new_norm in existing_norm:
            return existing_norm

        return f"{existing_norm}; {new_norm}"

    def _get_fields_to_update(self,
                              extracted_data: Dict[str, Any],
                              db_property: Dict[str, Any]) -> List[str]:
        fields_to_update = []

        # String fields that should be concatenated if they conflict
        concatenate_fields = {
            "balconyTerraceDetails", "gardenDetails", "swimmingPoolDetails",
            "architecturalType", "typeOfLock", "typeOfConstruction"
        }

        # Check each field
        for field, extracted_value in extracted_data.items():
            if field in ["id"]:
                continue

            if extracted_value is None:
                continue

            # Special handling for address object
            if field == "address":
                db_address = db_property.get("address")
                if db_address is None:
                    # No existing address, update with new address
                    fields_to_update.append(field)
                    self.logger.info(f"Field '{field}' marked for update (empty in DB)")
                    continue

                # Check if any address components are new or different
                address_needs_update = False
                for addr_field, addr_value in extracted_value.items():
                    if addr_value is not None:
                        db_addr_value = db_address.get(addr_field)
                        if db_addr_value is None:
                            address_needs_update = True
                            break
                        if self._normalize_string(addr_value) != self._normalize_string(db_addr_value):
                            address_needs_update = True
                            break

                if address_needs_update:
                    fields_to_update.append(field)
                    self.logger.info(f"Field '{field}' marked for update (address components changed)")
                continue

            db_value = db_property.get(field)

            self.logger.info(f"Checking field '{field}': extracted='{extracted_value}', db='{db_value}'")

            # If DB doesn't have this field, update it
            if db_value is None:
                fields_to_update.append(field)
                self.logger.info(f"Field '{field}' marked for update (empty in DB)")
                continue

            # For string fields that can be concatenated, check if values differ
            if field in concatenate_fields and isinstance(extracted_value, str) and isinstance(db_value, str):
                extracted_norm = self._normalize_string(extracted_value)
                db_norm = self._normalize_string(db_value)

                if extracted_norm and db_norm and extracted_norm != db_norm:
                    # Only update if the new value is not already contained in the existing value
                    if extracted_norm not in db_norm:
                        fields_to_update.append(field)
                        self.logger.info(f"Field '{field}' marked for concatenation update")
                continue

            # For other fields, only update if DB value is None (don't overwrite

        return fields_to_update

    def _is_data_already_present(self,
                                 extracted_data: Dict[str, Any],
                                 db_property: Dict[str, Any]) -> bool:
        extracted_non_null = self._extract_non_null_fields(extracted_data)

        for field, extracted_value in extracted_non_null.items():
            if field in ["id"]:
                continue

            db_value = db_property.get(field)

            # Special handling for address object
            if field == "address":
                if extracted_value is None:
                    continue
                if db_value is None:
                    return False  # New address information available

                # Compare address components
                for addr_field, addr_value in extracted_value.items():
                    if addr_value is not None:
                        db_addr_value = db_value.get(addr_field) if db_value else None
                        if db_addr_value is None:
                            return False  # New address component available
                        if self._normalize_string(addr_value) != self._normalize_string(db_addr_value):
                            return False  # Different address component
                continue

            # If extracted value is different from DB value and DB has a value,
            # we consider this as new information (not already present)
            if db_value is None:
                return False  # New information available

            # For string comparisons
            if isinstance(extracted_value, str) and isinstance(db_value, str):
                extracted_norm = self._normalize_string(extracted_value)
                db_norm = self._normalize_string(db_value)

                if extracted_norm and extracted_norm not in db_norm:
                    return False  # New information available
            elif extracted_value != db_value:
                return False  # New information available

        return True  # All extracted data is already present

    async def update_property_details(self,
                                      extracted_property_data: Dict[str, Any],
                                      user_property: Property,
                                      user_id: int) -> Dict[str, Any]:
        """
        Determine if extracted property data should be used to update existing property.
        Only returns 'update' or 'do_nothing' commands (no insert for properties).

        Args:
            extracted_property_data: Dictionary containing the extracted property data
            user_property: The user's property object from the database
            user_id: The ID of the user who owns the property

        Returns:
            Dict with command ('do_nothing' or 'update') and fields to update if applicable
        """
        try:
            # Map extracted data to database field names
            mapped_extracted_data = self._map_extracted_data(extracted_property_data)

            # Convert property object to dictionary
            property_dict = self._get_property_dict(user_property)

            self.logger.info(f"Mapped extracted data: {mapped_extracted_data}")
            self.logger.info(f"Current property data: {property_dict}")

            # Check if extracted data is already fully present in the database
            if self._is_data_already_present(mapped_extracted_data, property_dict):
                # All extracted data is already in database - DO NOTHING
                self.logger.info("All extracted data already present in database - doing nothing")
                return {"command": "do_nothing"}

            # Check if there are new fields to update
            fields_to_update = self._get_fields_to_update(mapped_extracted_data, property_dict)

            self.logger.info(f"Fields to update: {fields_to_update}")

            if not fields_to_update:
                # No new information to add - DO NOTHING
                self.logger.info("No new information to add - doing nothing")
                return {"command": "do_nothing"}

            # Prepare update data with concatenation for string fields
            update_data = {}
            concatenate_fields = {
                "balconyTerraceDetails", "gardenDetails", "swimmingPoolDetails",
                "architecturalType", "typeOfLock", "typeOfConstruction"
            }

            for field in fields_to_update:
                extracted_value = mapped_extracted_data.get(field)

                if field in concatenate_fields:
                    existing_value = property_dict.get(field)
                    if existing_value is not None and isinstance(extracted_value, str):
                        update_data[field] = self._concatenate_string_values(existing_value, extracted_value)
                    else:
                        update_data[field] = extracted_value
                else:
                    update_data[field] = extracted_value

            # Add userId and propertyId to the update data
            update_data["userId"] = user_id
            update_data["propertyId"] = user_property.id

            # There are fields to update: UPDATE
            self.logger.info(f"Updating property {user_property.id} with fields: {fields_to_update}")
            return {
                "command": "update",
                "id": user_property.id,
                "fields": fields_to_update,
                "update_data": update_data
            }

        except Exception as e:
            self.logger.error(f"Error in update_property_details: {str(e)}")
            # Default to do_nothing on error to avoid data corruption
            return {"command": "do_nothing"}
