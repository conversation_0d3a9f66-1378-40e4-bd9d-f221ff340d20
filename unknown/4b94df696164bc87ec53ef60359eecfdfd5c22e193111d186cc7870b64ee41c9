import type { <PERSON>a, StoryObj } from "@storybook/react";
import { HugeiconsIcon } from "./HugeiconsIcon";
import {
  Notification03Icon,
  SunIcon,
} from "@hugeicons-pro/core-stroke-rounded";
import { AbacusBulkRounded } from "@hugeicons-pro/core-bulk-rounded";
import { IconSvgObject } from "@hugeicons/react";

const meta = {
  title: "Components/HugeiconsIcon",
  component: HugeiconsIcon,
  parameters: {
    layout: "centered",
  },
  tags: ["autodocs"],
} satisfies Meta<typeof HugeiconsIcon>;

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
  args: {
    icon: AbacusBulkRounded as unknown as IconSvgObject,
    size: 24,
    color: "currentColor",
    strokeWidth: 1.5,
  },
};

export const CustomColor: Story = {
  args: {
    icon: Notification03Icon as unknown as IconSvgObject,
    size: 24,
    color: "#4F46E5",
    strokeWidth: 1.5,
  },
};

export const LargeSize: Story = {
  args: {
    icon: SunIcon as unknown as IconSvgObject,
    size: 48,
    color: "#000",
    strokeWidth: 1.5,
  },
};
