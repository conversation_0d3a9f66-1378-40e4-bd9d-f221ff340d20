// Vitest Snapshot v1, https://vitest.dev/guide/snapshot.html

exports[`ApplianceModal Snapshots > matches snapshot when closed 1`] = `<div />`;

exports[`ApplianceModal Snapshots > matches snapshot when loading 1`] = `
<div>
  <div
    data-testid="mock-modal"
  >
    <h2
      class="_title_3da5cd"
    >
      Add new appliance
    </h2>
    <div
      data-testid="mock-appliance-form"
    />
    <div
      data-testid="action-buttons"
    >
      <button
        data-testid="mock-button"
        disabled=""
      >
        Save
      </button>
    </div>
  </div>
</div>
`;

exports[`ApplianceModal Snapshots > matches snapshot when open 1`] = `
<div>
  <div
    data-testid="mock-modal"
  >
    <h2
      class="_title_3da5cd"
    >
      Add new appliance
    </h2>
    <div
      data-testid="mock-appliance-form"
    />
    <div
      data-testid="action-buttons"
    >
      <button
        data-testid="mock-button"
        disabled=""
      >
        Save
      </button>
    </div>
  </div>
</div>
`;

exports[`ApplianceModal Snapshots > matches snapshot with validation state 1`] = `
<div>
  <div
    data-testid="mock-modal"
  >
    <h2
      class="_title_3da5cd"
    >
      Add new appliance
    </h2>
    <div
      data-testid="mock-appliance-form"
    />
    <div
      data-testid="action-buttons"
    >
      <button
        data-testid="mock-button"
        disabled=""
      >
        Save
      </button>
    </div>
  </div>
</div>
`;
