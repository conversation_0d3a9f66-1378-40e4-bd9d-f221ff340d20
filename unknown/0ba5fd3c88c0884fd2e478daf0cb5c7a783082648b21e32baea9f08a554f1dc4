import React from 'react';
import styles from '../MessageAttachments.module.scss';
import { HugeiconsIcon } from '@/components/HugeiconsIcon';
import * as StrokeRounded from '@hugeicons-pro/core-stroke-rounded';
import { IconSvgObject } from '@hugeicons/react';
import { RemoveFileButton } from '@/components/RemoveFileButton';
import { Spinner } from '@/components/Spinner/Spinner';
import { Attachment } from '@/types/messages';

interface DocumentPreviewProps {
  fileUpload: Attachment;
  index: number;
  onRemove?: () => void;
}

export const MessageDocumentPreview: React.FC<DocumentPreviewProps> = ({
  fileUpload,
  index,
  onRemove,
}) => {
  const fileExtension = fileUpload.name.split('.').pop() || '';

  const filename = fileUpload.originalFileName || fileUpload.name;

  return (
    <div key={`${fileUpload.documentId}-${index}`} className={styles.documentPreviewContainer}>
      <div className={styles.documentPreviewIcon}>
        {fileUpload.status === 'uploading' ? (
          <Spinner color="#fff" size={20} />
        ) : (
          <HugeiconsIcon
            icon={StrokeRounded.Files01Icon as unknown as IconSvgObject}
            size={20}
            color="#fff"
          />
        )}
      </div>
      <div className={styles.documentContent}>
        <div className={styles.documentName} title={filename}>
          {filename}
        </div>
        <div className={styles.documentExtension}>{fileExtension}</div>
      </div>

      {/* {fileUpload.status === 'error' && onRetry && (
        <RetryButton onRetry={onRetry} className={styles.retryButton} />
      )} */}

      {onRemove && <RemoveFileButton onRemove={onRemove} className={styles.removeButton} />}
    </div>
  );
};
