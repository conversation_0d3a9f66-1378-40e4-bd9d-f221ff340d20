.container {
  display: flex;
  flex-direction: column;
  align-items: center;
  border: 1px solid var(--colors-gray-400);
  background-color: var(--colors-gray-50);
  padding: 2rem 1rem;
  border-radius: 20px;
  gap: 1.25rem;
  max-width: 696px;
  margin-left: auto;
  margin-right: auto;
}

.icon {
  color: var(--colors-gray-600);
}

.heading {
  text-align: center;
  font-weight: 700;
}

.description {
  text-align: center;
}
