import asyncio
import logging
from abc import ABC

import aiohttp


class BaseAPI(ABC):
    _session = None
    _session_lock = asyncio.Lock()

    async def _ensure_session(self):
        if self._session is None:
            async with self._session_lock:
                if self._session is None:
                    self.__class__._session = aiohttp.ClientSession()

    async def close(self):
        logging.info(f"Closing {self.__class__.__name__} ClientSession")
        if self._session:
            await self._session.close()
            self._session = None
