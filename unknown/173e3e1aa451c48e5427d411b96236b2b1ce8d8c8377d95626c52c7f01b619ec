import '@testing-library/jest-dom';
import React from 'react';

// Mock CSS modules
jest.mock('../Checkbox.module.scss', () => ({
  'checkbox-field': 'checkbox-field',
  'checkbox': 'checkbox',
  'checkbox-wrapper': 'checkbox-wrapper',
  'content': 'content',
  'label-helper': 'label-helper',
  'label': 'label',
  'caption': 'caption',
  'error-container': 'error-container',
  'error-icon': 'error-icon',
  'error-text': 'error-text',
  'checked': 'checked',
  'normal': 'normal',
  'error': 'error',
  'disabled': 'disabled',
}));

// Mock HugeIcons
jest.mock('@hugeicons/react', () => new Proxy({}, {
  get: () => {
    return React.createElement.bind(null, 'div', {
      'data-testid': 'mock-icon',
      className: 'mock-icon'
    });
  }
}));

// Global test setup
beforeEach(() => {
  jest.clearAllMocks();
}); 