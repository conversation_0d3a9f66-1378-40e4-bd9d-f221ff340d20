import React from "react";
import { render } from "@testing-library/react";
import { Button } from "../Button";
import {
  ButtonColor,
  ButtonSize,
  ButtonType,
  ButtonState,
} from "../Button.types";

describe("Button Snapshots", () => {
  const defaultProps = {
    children: "Test Button",
    type: ButtonType.PRIMARY,
    size: ButtonSize.BASE,
    color: ButtonColor.BLUE_SECONDARY,
  };

  it("matches primary button snapshot", () => {
    const { container } = render(<Button {...defaultProps} />);
    expect(container).toMatchSnapshot();
  });

  it("matches secondary button snapshot", () => {
    const { container } = render(
      <Button {...defaultProps} type={ButtonType.SECONDARY} />
    );
    expect(container).toMatchSnapshot();
  });

  it("matches disabled button snapshot", () => {
    const { container } = render(
      <Button {...defaultProps} state={ButtonState.DISABLED} />
    );
    expect(container).toMatchSnapshot();
  });

  it("matches button with icons snapshot", () => {
    const { container } = render(
      <Button
        {...defaultProps}
        leftIconName="TestLeftIcon"
        rightIconName="TestRightIcon"
        showLeftIcon={true}
        showRightIcon={true}
      />
    );
    expect(container).toMatchSnapshot();
  });

  it("matches icon-only button snapshot", () => {
    const { container } = render(
      <Button
        {...defaultProps}
        iconOnly={true}
        leftIconName="TestIcon"
        showLeftIcon={true}
      />
    );
    expect(container).toMatchSnapshot();
  });

  it("matches link button snapshot", () => {
    const { container } = render(<Button {...defaultProps} href="/test" />);
    expect(container).toMatchSnapshot();
  });
});
