"""add fields to document

Revision ID: 8cd5a95b1016
Revises: 7d55940206ea
Create Date: 2025-05-30 15:40:14.367156

"""

from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision: str = "8cd5a95b1016"
down_revision: Union[str, None] = "7d55940206ea"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    documentstatustype_enum = postgresql.ENUM(
        "saved",
        "processing",
        "processingCompleted",
        "irrelevant",
        "error",
        name="documentstatustype",
        create_type=False,
    )
    documentstatustype_enum.create(op.get_bind(), checkfirst=True)
    op.add_column(
        "documents",
        sa.Column(
            "status",
            documentstatustype_enum,
            nullable=False,
            server_default="processingCompleted",
        ),
    )

    documentcategorytype_enum = postgresql.ENUM(
        "propertyDetails",
        "insurance",
        "billsAndSubscriptions",
        "legal",
        "appliance",
        "buildingInformation",
        "other",
        name="documentcategorytype",
        create_type=False,
    )
    documentcategorytype_enum.create(op.get_bind(), checkfirst=True)
    op.add_column(
        "documents",
        sa.Column(
            "category",
            documentcategorytype_enum,
            nullable=True,
        ),
    )
    op.add_column("documents", sa.Column("label", sa.String(), nullable=True))
    op.add_column("documents", sa.Column("errorMessage", sa.String(), nullable=True))
    op.alter_column(
        "documents",
        "status",
        server_default="saved",
    )

    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column("documents", "errorMessage")
    op.drop_column("documents", "label")
    op.drop_column("documents", "category")
    op.drop_column("documents", "status")
    # ### end Alembic commands ###
