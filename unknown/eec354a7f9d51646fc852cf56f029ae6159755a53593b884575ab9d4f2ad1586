from datetime import datetime
from typing import Optional

from sqlalchemy import DateTime, <PERSON><PERSON><PERSON>, Integer, Enum
from sqlalchemy.orm import relationship, mapped_column, Mapped
from sqlalchemy.sql import func

from .base import BaseModel


class Quote(BaseModel):
    __tablename__ = "quotes"

    id: Mapped[int] = mapped_column(Integer, primary_key=True, index=True)
    costLowest: Mapped[int]
    costHighest: Mapped[int]
    availability: Mapped[Optional[str]]  # just string?
    status: Mapped[str] = mapped_column(
        Enum("open", "accepted", "rejected", "cancelled", name="quote_status"), default="open"
    )
    timestamp: Mapped[datetime] = mapped_column(DateTime, server_default=func.now(), nullable=False)
    jobId: Mapped[int] = mapped_column(ForeignKey("jobs.id"))
    job: Mapped["Job"] = relationship(back_populates="quotes")
    serviceProviderId: Mapped[int] = mapped_column(ForeignKey("service_providers.id"))
    serviceProvider: Mapped["ServiceProvider"] = relationship(back_populates="quotes")
