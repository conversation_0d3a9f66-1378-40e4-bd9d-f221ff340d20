.attachmentsContainer {
  display: flex;
  flex-direction: column;
  gap: 12px;
  margin-bottom: 12px;
  overflow-x: auto;
  max-width: 420px;
  width: 100%;
  scroll-snap-type: x mandatory;
  scroll-behavior: smooth;
  -webkit-overflow-scrolling: touch;
  margin-left: auto;

  &::-webkit-scrollbar {
    height: 3px;
  }

  &::-webkit-scrollbar-track {
    background: transparent;
  }

  &::-webkit-scrollbar-thumb {
    background-color: #caccd0;
    border-radius: 1.5px;
    min-width: 40px;
    max-width: 40px;
    cursor: pointer;
  }

  &:empty {
    display: none;
  }

  &.spacing,
  &.spacing .previewsContainer {
    margin-bottom: 0;
    padding-bottom: 0;
  }
}

.loading {
  font-size: 14px;
  color: #666;
  font-style: italic;
  margin-bottom: 8px;
}

.error {
  color: #d32f2f;
  font-size: 14px;
  margin-bottom: 8px;
}

.previewsContainer {
  display: flex;
  flex-flow: column wrap;
  gap: 8px;
  overflow-x: auto;
  padding: 2px 2px 7px 2px;
  scroll-snap-type: x mandatory;
  scroll-behavior: smooth;
  -webkit-overflow-scrolling: touch; /* Smooth scrolling for iOS */
  width: 100%;
  align-items: flex-end;

  &::-webkit-scrollbar {
    height: 3px;
    margin: 0 8px;
  }

  &::-webkit-scrollbar-track {
    background: transparent;
    margin: 0 8px;
  }

  &::-webkit-scrollbar-thumb {
    background-color: #caccd0;
    border-radius: 1.5px;
    min-width: 40px;
    max-width: 40px;
    margin: 0 8px;
    cursor: pointer;
  }
}

.documentPreview {
  position: relative;
  width: 80px;
  height: 81px;
  flex-shrink: 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  border-radius: 8px;
  overflow: hidden;
  scroll-snap-align: start;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: #fff;
    border-radius: 8px;
    z-index: 0;
  }

  &::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(75, 85, 99, 0.3);
    border-radius: 8px;
    z-index: 1;
  }
}

.loaderContainer {
  width: 32px;
  height: 32px;
  position: relative;
  z-index: 2;
  display: flex;
  align-items: center;
  justify-content: center;
}

.errorIndicator {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  background-color: #d32f2f;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  z-index: 2;
  pointer-events: none;
}

.previewContent {
  position: relative;
  width: 100%;
  height: 100%;
  z-index: 2;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: flex-end;
  overflow: hidden;
}

.previewImage {
  width: 100%;
  height: 100%;
  object-fit: cover;
  position: absolute;
  top: 0;
  left: 0;
}

.filename {
  position: absolute;
  bottom: 0;
  width: 100%;
  font-size: 10px;
  color: #fff;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  text-align: center;
  z-index: 3;
  padding: 4px;
  background: rgba(0, 0, 0, 0.5);
}

.fallbackContainer {
  display: flex;
  flex-direction: column;
  gap: 12px;
  overflow-x: auto;
  scroll-snap-type: x mandatory;
  scroll-behavior: smooth;
  -webkit-overflow-scrolling: touch;

  /* For horizontal layout when multiple images */
  &:has(> .imageContainer + .imageContainer) {
    flex-direction: row;
    gap: 8px;
    padding-bottom: 7px;

    &::-webkit-scrollbar {
      height: 3px;
      margin: 0 8px;
    }

    &::-webkit-scrollbar-track {
      background: transparent;
      margin: 0 8px;
    }

    &::-webkit-scrollbar-thumb {
      background-color: #caccd0;
      border-radius: 1.5px;
      min-width: 40px;
      max-width: 40px;
      margin: 0 8px;
      cursor: pointer;
    }
  }
}

.imageContainer {
  position: relative;
  max-width: 100%;
  border-radius: 8px;
  overflow: hidden;
  scroll-snap-align: start;
  flex-shrink: 0;

  /* When in horizontal layout */
  .fallbackContainer:has(> .imageContainer + .imageContainer) & {
    max-width: 200px;
    width: 200px;
    height: 200px;
  }
}

.attachmentImage {
  max-width: 100%;
  max-height: 300px;
  object-fit: contain;
  border-radius: 8px;
}

.imageName {
  font-size: 12px;
  color: #666;
  margin-top: 4px;
  word-break: break-word;
}

.documentContainer {
  padding: 8px 12px;
  background-color: #f5f5f5;
  border-radius: 6px;
  margin-bottom: 8px;
  scroll-snap-align: start;
  flex-shrink: 0;
  display: flex;
  align-items: center;
  gap: 8px;

  /* When in horizontal fallback layout */
  .fallbackContainer:has(> .documentContainer + .documentContainer) & {
    max-width: 200px;
    width: 200px;
  }
}

.documentLink {
  color: #2c68e5;
  text-decoration: none;
  font-size: 14px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;

  &:hover {
    text-decoration: underline;
  }
}

.fileName {
  font-size: 10px;
  color: #444;
  text-align: center;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  max-width: 100%;
}

.fileNameOnly {
  font-size: 10px;
  color: #444;
  text-align: center;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  max-width: 100%;
  padding: 0 4px;
}

.imagesContainer {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
  margin-bottom: 12px;
}

.filesContainer {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.sectionTitle {
  font-size: 14px;
  font-weight: 500;
  color: #444;
  margin: 0 0 8px 0;
}

.itemsContainer {
  display: flex;
  gap: 8px;
  overflow-x: auto;

  &.nonImageUploads {
    flex-flow: column wrap;
    max-width: 80%;
  }
}

.documentPreviewContainer {
  position: relative;
  font-family: Quasimoda, sans-serif;
  display: flex;
  gap: 10px;
  align-items: center;
  max-width: 100%;
  width: 315px;
  padding: 12px;
  border-radius: 8px;
  background-color: #e5e7eb;
  height: 68px;
  flex-shrink: 0;

  .retryButton {
    background: none;
    border: none;
    padding: 0;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 24px;
    height: 24px;
    border-radius: 50%;

    &:hover {
      background-color: rgba(0, 0, 0, 0.05);
    }
  }

  .removeButton {
    position: absolute;
    top: 4px;
    right: 4px;
  }
}

.documentPreviewIcon {
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 44px;
  height: 44px;
  border-radius: 8px;
  background-color: #d28e28;
}

.documentContent {
  font-size: 14px;
  display: flex;
  gap: 2px;
  flex-direction: column;
  overflow: hidden;
}

.documentName {
  font-weight: 400;
  line-height: 150%;
  color: #333;
  margin-bottom: 2px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 100%;
}

.documentExtension {
  font-size: 11px;
  color: #666;
  text-transform: uppercase;
}

.imagesContainer {
  display: flex;
  flex-direction: column;
  margin-bottom: 16px;

  > .itemsContainer {
    flex-wrap: wrap;
    gap: 12px;
  }
}

.filesContainer {
  display: flex;
  flex-direction: column;
  gap: 4px;
}
