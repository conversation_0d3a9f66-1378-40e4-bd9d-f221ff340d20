"""update enums

Revision ID: 049a7f067184
Revises: 7fd7921213ec
Create Date: 2025-05-15 16:51:35.051587

"""

from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = "049a7f067184"
down_revision: Union[str, None] = "7fd7921213ec"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    op.execute("ALTER TYPE userpropertyrelationtype RENAME VALUE 'owner' TO 'ownerAndOccupier'")
    op.execute("ALTER TYPE userpropertyrelationtype RENAME VALUE 'guarantor' TO 'landlord'")
    op.execute("ALTER TYPE userpropertyrelationtype ADD VALUE 'managingProfessional'")


def downgrade() -> None:
    pass
