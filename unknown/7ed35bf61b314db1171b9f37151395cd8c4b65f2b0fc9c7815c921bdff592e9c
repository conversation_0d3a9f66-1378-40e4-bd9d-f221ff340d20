from unittest.mock import AsyncMock

import pytest
import pytest_asyncio
from sqlalchemy.ext.asyncio import AsyncSession

from src.db_models.relationships import UserPropertyRelationType
from src.db_models.user import User
from src.integrations.clerk import <PERSON><PERSON><PERSON>
from src.schemas import UserUpdate
from src.services.users import UserService


@pytest_asyncio.fixture
async def user_service(
    async_db_session: AsyncSession,
) -> UserService:
    return UserService(async_db_session, AsyncMock(), AsyncMock(), AsyncMock())


@pytest.mark.asyncio
async def test_update_user(
    async_db_session: AsyncSession,
    user_service: UserService,
):
    user = User(
        id=1,
        email="<EMAIL>",
        clerkId="clerk_user_id",
        firstName="Test",
        lastName="User",
    )
    async_db_session.add(user)
    await async_db_session.flush()

    assert user.mainUsage is None

    hubspot_sync_service = AsyncMock()
    user_service = UserService(async_db_session, AsyncMock(), AsyncMock(), hubspot_sync_service)

    user_update = UserUpdate(mainUsage=UserPropertyRelationType.tenant)
    updated_user = await user_service.update_user(user, user_update)
    assert updated_user.mainUsage == UserPropertyRelationType.tenant

    updated_user_from_db = await async_db_session.get(User, user.id)
    assert updated_user_from_db.mainUsage == UserPropertyRelationType.tenant

    hubspot_sync_service.sync_user.assert_called_once_with(user.id)


@pytest.mark.asyncio
async def test_delete_user(
    async_db_session: AsyncSession,
    user_service: UserService,
):
    user = User(
        id=1,
        email="<EMAIL>",
        clerkId="clerk_user_id",
        firstName="Test",
        lastName="User",
    )
    async_db_session.add(user)
    await async_db_session.flush()

    user_from_db = await async_db_session.get(User, user.id)
    assert user_from_db is not None

    await user_service.delete_user(user)

    deleted_user = await user_service.get_user_by_clerk_id(user.clerkId)
    assert deleted_user is None


@pytest.mark.asyncio
async def test_create_user_from_clerk_user(
    async_db_session: AsyncSession,
):
    clerk_user = ClerkUser(
        clerk_id="clerk_user_id_2",
        primary_email="<EMAIL>",
        is_primary_email_verified=True,
        first_name="New",
        last_name="User",
        primary_phone_number=None,
        is_primary_phone_number_verified=None,
    )

    hubspot_sync_service = AsyncMock()
    user_service = UserService(async_db_session, AsyncMock(), AsyncMock(), hubspot_sync_service)

    created_user = await user_service.upsert_user_from_clerk_user(clerk_user)
    assert created_user is not None
    assert created_user.clerkId == clerk_user.clerk_id
    assert created_user.email == clerk_user.primary_email
    assert created_user.firstName == clerk_user.first_name
    assert created_user.lastName == clerk_user.last_name

    user_from_db = await async_db_session.get(User, created_user.id)
    assert user_from_db is not None
    assert user_from_db.clerkId == clerk_user.clerk_id
    assert user_from_db.email == clerk_user.primary_email
    assert user_from_db.firstName == clerk_user.first_name
    assert user_from_db.lastName == clerk_user.last_name

    hubspot_sync_service.sync_user.assert_called_once_with(created_user.id)


@pytest.mark.asyncio
async def test_update_user_from_clerk_user(
    async_db_session: AsyncSession,
):
    original_user = User(
        id=1,
        clerkId="clerk_user_id_3",
        email="<EMAIL>",
        isEmailVerified=False,
        firstName="Original",
        lastName="User",
        phoneNumber="0000000000",
        isPhoneNumberVerified=False,
    )
    async_db_session.add(original_user)
    await async_db_session.flush()

    updated_clerk_user = ClerkUser(
        clerk_id="clerk_user_id_3",
        primary_email="<EMAIL>",
        is_primary_email_verified=True,
        first_name="Updated",
        last_name="User",
        primary_phone_number="1234567890",
        is_primary_phone_number_verified=True,
    )

    hubspot_sync_service = AsyncMock()
    user_service = UserService(async_db_session, AsyncMock(), AsyncMock(), hubspot_sync_service)

    updated_user = await user_service.upsert_user_from_clerk_user(updated_clerk_user)

    assert updated_user is not None
    assert updated_user.email == updated_clerk_user.primary_email
    assert updated_user.isEmailVerified == updated_clerk_user.is_primary_email_verified
    assert updated_user.firstName == updated_clerk_user.first_name
    assert updated_user.lastName == updated_clerk_user.last_name
    assert updated_user.phoneNumber == updated_clerk_user.primary_phone_number
    assert updated_user.isPhoneNumberVerified == updated_clerk_user.is_primary_phone_number_verified

    hubspot_sync_service.sync_user.assert_called_once_with(updated_user.id)
