import os
from os import environ
from typing import Any, Callable, AsyncGenerator, Generator
from unittest.mock import patch

import pytest
import pytest_asyncio
from faker import Faker
from fastapi import FastAPI
from fastapi.testclient import TestClient
from sqlalchemy import text
from sqlalchemy.ext.asyncio import AsyncSession, create_async_engine, AsyncConnection

# from src.routers import healthcheck, message
# from src.routers import healthcheck

environ["CRYSTAL_ROOF_API_KEY"] = "DEMO"
environ["OS_DATA_PLACES_API_KEY"] = "TEST"
environ["IDEALPOSTCODES_API_KEY"] = "TEST"
environ["CLERK_PEM_PUBLIC_KEY"] = "TEST"
environ["CLERK_API_KEY"] = "TEST"
environ["CLERK_WEBHOOK_SECRET"] = "whsec_TEST"
environ["PERMITTED_ORIGINS"] = "http://localhost:8000"
environ["BACKEND_API_BASE_URL"] = "test"

DATABASE_URL = environ.get("DATABASE_URL")
if not DATABASE_URL:
    DATABASE_URL = "test-user:password@127.0.0.1:5433/test_db"
    environ["DATABASE_URL"] = DATABASE_URL
SQLALCHEMY_DATABASE_URL = f"postgresql+psycopg://{DATABASE_URL}"

from src.db_models import BaseModel


fake = Faker()

@pytest.fixture(scope="session", autouse=True)
def set_required_env_vars_for_imports():
    variables_to_set = {
        "ANTHROPIC_API_KEY": "dummy_anthropic_key_for_import",
        "GEMINI_API_KEY": "dummy_gemini_key_for_import",
        "PSE_API_KEY": "dummy_pse_key_for_import",
        "PSE_CX": "dummy_pse_cx_for_import",
        "IS_AWS": "true",
        "LANGSMITH_API_KEY": "dummy_langsmith_key_for_import",
        "TOGETHER_AI_API_KEY": "dummy_together_key_for_import",
        "AI_ENGINE_API_KEY": "dummy_ai_engine_key_for_import",
        "OPEN_AI_API_KEY": "dummy_ai_engine_key_for_import",
        "QDRANT_DB_URL": "dummy_ai_engine_key_for_import",
        "QDRANT_API_KEY": "dummy_ai_engine_key_for_import",
        "QDRANT_COLLECTION_NAME": "dummy_collection"
    }

    original_values = {}
    for key, value in variables_to_set.items():
        original_values[key] = os.environ.get(key)
        os.environ[key] = value
        # print(f"  Set {key}") # Optional: reduce verbosity

    print("Environment variables set for import.")

    # --- Mock Qdrant initialization during import ---
    print("Patching QdrantDAO._init_collection for import...")
    # Target the specific method that makes the network call in __init__
    # Replace it with a function that does nothing for the duration of the imports.
    qdrant_patcher = patch("src.dao.QdrantDAO.QdrantDAO._init_collection", lambda self: None)

    try:
        qdrant_patcher.start() # Activate the patch
        print("  QdrantDAO._init_collection patched.")

        yield # Let the test session imports and execution run

    finally:
        qdrant_patcher.stop() # Deactivate the patch
        print("QdrantDAO._init_collection patch stopped.")

        # Teardown: Restore original environment variables
        print("Restoring original environment variables in conftest session fixture...")
        for key, original_value in original_values.items():
            if original_value is None:
                if key in os.environ:
                    del os.environ[key]
                    # print(f"  Unset {key}")
            else:
                os.environ[key] = original_value
                # print(f"  Restored {key}")
        print("Environment variables restored.")


@pytest.fixture(scope="session")
def client() -> Generator[TestClient, Any, None]:
    app = FastAPI()
    # app.include_router(healthcheck.router)
    # app.include_router(message.router)
    with TestClient(app) as _client:
        yield _client
    app.dependency_overrides = {}


def override_dependency(dependency: Callable[..., Any], mocked_response: Any) -> None:
    app.dependency_overrides[dependency] = lambda: mocked_response


@pytest_asyncio.fixture
async def engine():
    engine = create_async_engine(f"postgresql+asyncpg://{DATABASE_URL}")
    yield engine
    await engine.dispose()


@pytest_asyncio.fixture
async def connection(engine) -> AsyncGenerator[AsyncConnection, None]:
    async with engine.connect() as connection:
        yield connection


@pytest_asyncio.fixture
async def create(engine):
    async with engine.begin() as conn:
        await conn.run_sync(BaseModel.metadata.create_all)
    yield
    async with engine.begin() as conn:
        await conn.run_sync(BaseModel.metadata.drop_all)

        def drop_tables(c):
            c.execute(text("DROP TABLE IF EXISTS alembic_version"))

        await conn.run_sync(drop_tables)


@pytest_asyncio.fixture
async def async_db_session(connection: AsyncConnection, create) -> AsyncGenerator[AsyncSession, None]:
    async_session = AsyncSession(bind=connection, join_transaction_mode="create_savepoint")

    yield async_session
    await async_session.close()
