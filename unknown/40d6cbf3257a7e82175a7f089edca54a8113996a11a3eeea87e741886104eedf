from enum import Enum
from typing import Optional, List

from sqlalchemy import Integer, ForeignKey
from sqlalchemy.orm import relationship, mapped_column, Mapped

from .base import BaseModel


class NumberOfFlatsType(Enum):
    two = "2"
    threeToTen = "3-10"
    elevenToFourtyNine = "11-49"
    fiftyPlus = "50+"


class Building(BaseModel):
    __tablename__ = "buildings"

    id: Mapped[int] = mapped_column(Integer, primary_key=True, index=True)
    name: Mapped[Optional[str]]
    numberOfFlats: Mapped[Optional[NumberOfFlatsType]]
    freeholder: Mapped[Optional[str]]
    hasConcierge: Mapped[Optional[bool]]
    hasSwimmingPool: Mapped[Optional[bool]]
    hasMailRoom: Mapped[Optional[bool]]
    hasBicycleStore: Mapped[Optional[bool]]
    hasGym: Mapped[Optional[bool]]
    otherDetails: Mapped[Optional[str]]
    # relationships:
    managingAgentId: Mapped[Optional[int]] = mapped_column(ForeignKey("users.id"))
    managingAgent: Mapped[Optional["User"]] = relationship(back_populates="buildingsManaged")
    properties: Mapped[List["Property"]] = relationship(back_populates="building")
