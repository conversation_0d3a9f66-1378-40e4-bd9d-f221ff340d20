import React from 'react';
import { render } from '@testing-library/react';
import { Dropdowns } from '../Dropdowns';
import { Button } from '../../Button';

describe('Dropdowns Snapshots', () => {
  const defaultItems = [
    {
      icon: <div data-testid="mock-icon">Icon1</div>,
      label: 'First Action',
      onClick: () => console.log('First action clicked'),
    },
    {
      icon: <div data-testid="mock-icon">Icon2</div>,
      label: 'Second Action',
      onClick: () => console.log('Second action clicked'),
    },
  ];

  const defaultProps = {
    items: defaultItems,
    trigger: <Button>Menu</Button>,
    placement: 'left' as const,
  };

  it('matches default dropdown snapshot', () => {
    const { container } = render(<Dropdowns {...defaultProps} />);
    expect(container).toMatchSnapshot();
  });

  it('matches dropdown with custom placement snapshot', () => {
    const { container } = render(
      <Dropdowns {...defaultProps} placement="right" />
    );
    expect(container).toMatchSnapshot();
  });

  it('matches dropdown with custom classes snapshot', () => {
    const { container } = render(
      <Dropdowns
        {...defaultProps}
        className="custom-class"
        menuClassName="custom-menu"
        itemClassName="custom-item"
      />
    );
    expect(container).toMatchSnapshot();
  });

  it('matches dropdown with danger item snapshot', () => {
    const itemsWithDanger = [
      ...defaultItems,
      {
        icon: <div data-testid="mock-icon">Icon3</div>,
        label: 'Danger Action',
        variant: 'danger' as const,
        onClick: () => console.log('Danger clicked'),
      },
    ];
    const { container } = render(
      <Dropdowns {...defaultProps} items={itemsWithDanger} />
    );
    expect(container).toMatchSnapshot();
  });

  it('matches dropdown with divider snapshot', () => {
    const itemsWithDivider = [
      ...defaultItems,
      {
        icon: <div data-testid="mock-icon">Icon3</div>,
        label: 'Divided Action',
        divider: true,
        onClick: () => console.log('Divided clicked'),
      },
    ];
    const { container } = render(
      <Dropdowns {...defaultProps} items={itemsWithDivider} />
    );
    expect(container).toMatchSnapshot();
  });
});
