"""merge heads a738975<PERSON><PERSON> and f4326be0dce6

Revision ID: 58778b40c081
Revises: a738975afbee, f4326be0dce6
Create Date: 2025-06-05 09:48:50.051620

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '58778b40c081'
down_revision: Union[str, None] = ('a738975afbee', 'f4326be0dce6')
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    pass


def downgrade() -> None:
    pass
