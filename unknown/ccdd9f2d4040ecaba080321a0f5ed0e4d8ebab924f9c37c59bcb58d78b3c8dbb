import React, { useState, useEffect } from 'react';
import { Modal } from '../Modal';
import { ApplianceModalProps, ApplianceFormData } from './ApplianceModal.types';
import { ApplianceForm } from './ApplianceForm';
import { Button } from '../Button';
import { ButtonColor, ButtonSize, ButtonState } from '../Button/Button.types';

const initialForm: ApplianceFormData = {
  applianceType: '',
  brand: '',
  model: '',
  serialNumber: '',
  warranty: '',
};

export const ApplianceModal: React.FC<ApplianceModalProps> = ({
  open,
  onClose,
  onSave,
  loading,
}) => {
  const [form, setForm] = useState<ApplianceFormData>(initialForm);
  const [touched, setTouched] = useState(false);

  useEffect(() => {
    if (open) {
      setForm(initialForm);
      setTouched(false);
    }
  }, [open]);

  const handleSave = () => {
    if (form.applianceType.trim()) {
      onSave(form);
    } else {
      setTouched(true);
    }
  };

  const isFormValid = () => {
    return form.applianceType.trim() !== '';
  };

  return (
    <Modal
      open={open}
      onClose={onClose}
      title="Add new appliance"
      actionButtons={
        <Button
          onClick={handleSave}
          disabled={!isFormValid()}
          state={isFormValid() ? ButtonState.DEFAULT : ButtonState.DISABLED}
          color={ButtonColor.GREEN_PRIMARY}
          size={ButtonSize.L}
        >
          Save
        </Button>
      }
    >
      <ApplianceForm value={form} onChange={setForm} disabled={loading} showValidation={touched} />
    </Modal>
  );
};
