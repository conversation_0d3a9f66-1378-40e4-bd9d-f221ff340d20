import React from "react";
import "./spacing.css";

export interface SpacingItem {
  name: string;
  size: string;
  pixels: string;
}

interface SpacingTableProps {
  spacings: SpacingItem[];
}

export const SpacingTable: React.FC<SpacingTableProps> = ({ spacings }) => (
  <div className="spacing-table">
    <div className="table-header">
      <div className="header-cell">Name</div>
      <div className="header-cell">Size</div>
      <div className="header-cell">Pixels</div>
      <div className="header-cell">Preview</div>
    </div>
    <div className="table-body">
      {spacings.map((spacing) => (
        <div key={spacing.name} className="table-row">
          <div className="table-cell">{spacing.name}</div>
          <div className="table-cell">{spacing.size}</div>
          <div className="table-cell">{spacing.pixels}</div>
          <div className="table-cell">
            <div
              className="spacing-preview"
              style={{ width: spacing.pixels }}
            />
          </div>
        </div>
      ))}
    </div>
  </div>
);
