import React from 'react';

interface CloseIconProps {
  className?: string;
  size?: number;
  strokeWidth?: number;
}

export const CloseIcon: React.FC<CloseIconProps> = ({ 
  className,
  size = 10,
  strokeWidth = 1.2
}) => {
  return (
    <svg 
      width={size} 
      height={size} 
      viewBox="0 0 10 10" 
      fill="none" 
      xmlns="http://www.w3.org/2000/svg"
      className={className}
    >
      <path 
        d="M1 1L9 9M9 1L1 9" 
        stroke="currentColor" 
        strokeWidth={strokeWidth} 
        strokeLinecap="round"
      />
    </svg>
  );
}; 