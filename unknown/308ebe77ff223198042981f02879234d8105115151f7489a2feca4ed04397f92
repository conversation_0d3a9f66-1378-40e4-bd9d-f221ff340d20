"""make address optional

Revision ID: 9ef4170c9f70
Revises: 58778b40c081
Create Date: 2025-06-10 14:57:34.520849

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '9ef4170c9f70'
down_revision: Union[str, None] = '58778b40c081'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('properties', 'addressId',
               existing_type=sa.INTEGER(),
               nullable=True)
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('properties', 'addressId',
               existing_type=sa.INTEGER(),
               nullable=False)
    # ### end Alembic commands ###
