# ai-engine

Running test instance:
screen -R ai-engine-backend
pipenv install
pipenv shell
uvicorn src.main:app --host 0.0.0.0 --port 8003 --reload

screen -R ai-engine-frontend
HOST=0.0.0.0 npm start


# ai-base-rag
Base RAG system for answering general questions about Presonas household documents

**Retrieval based on summarizations**
1. LLM 1: Summary each document in the catalog
2. OpenAI embeddings: Compute OpanAI embeddings for each summarization Build RAG text index based o it with the use of qdrant library.
3. For given question retrieve 5 closest summaries from qdrant index
4. Selection Agent: Read descriptions of retrieved documents and choose these that are relevant for answering the user question.
5. Answering Agent: Provide the agent with user question and appropriate original documents. We send jpg not summarizations so the model is able to extract information from the source file not the shorten text description.

