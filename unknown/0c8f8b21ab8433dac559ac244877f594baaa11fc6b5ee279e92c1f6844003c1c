import asyncio
import os
import json
import logging
from pathlib import Path

from src.agents.dataextractors.DocumentLabelingAgent import DocumentLabellingAgent

logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger("document_labelling_test")


async def test_document_labelling():
    try:
        agent = DocumentLabellingAgent()
        logger.info("Successfully initialized DocumentLabellingAgent")
    except KeyError as e:
        logger.error(f"Failed to initialize DocumentLabellingAgent: {e}")
        logger.error("Make sure the GEMINI_API_KEY environment variable is set")
        return False

    # Define expected results for each test file
    expected_results = {
        "house_survey.pdf": {
            "category": "Property",
            "label": "Home Survey"
        },
        "gas_certificate.pdf": {
            "category": "Bills",
            "label": "Gas Certificates"
        },
        "house_insurance.pdf": {
            "category": "Insurance",
            "label": "Home Insurance Policy"
        }
    }

    project_dir = Path(os.getcwd())
    test_dir = project_dir / "test-pdf"

    if not test_dir.exists():
        logger.error(f"Test directory not found: {test_dir}")
        logger.info("Creating test directory...")
        test_dir.mkdir(parents=True, exist_ok=True)
        logger.error("Please place test files in the newly created directory and run test again")
        return False

    test_files = ["house_survey.pdf", "gas_certificate.pdf", "house_insurance.pdf"]
    missing_files = []

    for file_name in test_files:
        file_path = test_dir / file_name
        if not file_path.exists():
            missing_files.append(file_name)

    if missing_files:
        logger.error(f"The following test files are missing: {', '.join(missing_files)}")
        logger.error(f"Please place these files in {test_dir} and run the test again")
        return False

    test_results = {}
    test_passed = True

    for file_name in test_files:
        file_path = test_dir / file_name
        logger.info(f"Testing file: {file_path}")

        try:
            result_json = await agent.process_file(file_path)
            result = json.loads(result_json)

            test_results[file_name] = result

            expected = expected_results[file_name]
            if result.get("category") == expected["category"] and result.get("label") == expected["label"]:
                logger.info(f"OK {file_name} correctly classified as {result.get('category')} / {result.get('label')}")
            else:
                logger.error(f"FAILED {file_name} misclassified!")
                logger.error(f"  Expected: {expected['category']} / {expected['label']}")
                logger.error(f"  Got: {result.get('category')} / {result.get('label', 'N/A')}")
                test_passed = False

        except Exception as e:
            logger.error(f"Error processing {file_name}: {str(e)}")
            test_passed = False

    logger.info("\n--- Test Summary ---")
    if test_passed:
        logger.info("PASS All tests passed!")
    else:
        logger.info("FAILED Some tests failed!")

    for file_name, result in test_results.items():
        logger.info(f"{file_name}: {result}")

    return test_passed


async def main():
    logger.info("Starting DocumentLabellingAgent test...")

    if "GEMINI_API_KEY" not in os.environ:
        logger.error("GEMINI_API_KEY environment variable is not set!")
        logger.error("Please set this variable and try again:")
        logger.error("  export GEMINI_API_KEY='your-api-key'")
        return

    success = await test_document_labelling()

    if success:
        logger.info("DocumentLabellingAgent is functioning correctly.")
        exit(0)
    else:
        logger.error("DocumentLabellingAgent test failed!")
        exit(1)


if __name__ == "__main__":
    # TO run TESTS
    # python -m src.agents.dataextractors.test_document_labelling
    asyncio.run(main())