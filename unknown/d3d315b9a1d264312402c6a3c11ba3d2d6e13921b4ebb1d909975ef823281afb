import { useState, useEffect, RefObject, useCallback } from 'react';

interface UseDropdownPositionProps {
  triggerRef: RefObject<HTMLDivElement>;
  menuRef: RefObject<HTMLDivElement>;
  isOpen: boolean;
  placement: 'left' | 'right';
}

export const useDropdownPosition = ({
  triggerRef,
  menuRef,
  isOpen,
  placement
}: UseDropdownPositionProps) => {
  const [menuStyle, setMenuStyle] = useState<React.CSSProperties>({
    visibility: 'hidden'
  });

  const updatePosition = useCallback(() => {
    if (!menuRef.current || !triggerRef.current) return;

    const triggerRect = triggerRef.current.getBoundingClientRect();
    const menuRect = menuRef.current.getBoundingClientRect();
    const windowWidth = window.innerWidth;

    let left = placement === 'left' ? triggerRect.left : triggerRect.right - menuRect.width;

    if (left + menuRect.width > windowWidth) {
      left = windowWidth - menuRect.width - 8;
    }
    if (left < 8) {
      left = 8;
    }

    setMenuStyle({
      top: `${triggerRect.bottom + 8}px`,
      left: `${left}px`,
      visibility: 'visible'
    });
  }, [menuRef, triggerRef, placement]);

  useEffect(() => {
    if (isOpen) {
      updatePosition();
      
      const frame = requestAnimationFrame(updatePosition);
      
      window.addEventListener('resize', updatePosition);
      window.addEventListener('scroll', updatePosition, true);

      return () => {
        cancelAnimationFrame(frame);
        window.removeEventListener('resize', updatePosition);
        window.removeEventListener('scroll', updatePosition, true);
      };
    }
  }, [isOpen, updatePosition]);

  return { menuStyle };
}; 