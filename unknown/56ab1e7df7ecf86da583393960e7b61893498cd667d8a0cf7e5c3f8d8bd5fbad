import type { <PERSON><PERSON>, <PERSON>Obj } from "@storybook/react";
import { useState } from "react";
import { Toggler } from "./Toggler";
import { TogglerSize } from "./Toggler.types";
import { CheckboxState } from "../Checkbox/Checkbox.types";

const meta = {
  title: "Components/Toggler",
  component: Toggler,
  parameters: {
    layout: "centered",
  },
  tags: ["autodocs"],
  argTypes: {
    state: {
      control: "select",
      options: Object.values(CheckboxState),
      defaultValue: CheckboxState.NORMAL,
    },
    size: {
      control: "select",
      options: Object.values(TogglerSize),
      defaultValue: TogglerSize.DEFAULT,
    },
  },
} satisfies Meta<typeof Toggler>;

export default meta;
type Story = StoryObj<typeof meta>;

const InteractiveToggler = (args: Story["args"]) => {
  const [isChecked, setIsChecked] = useState(args?.checked || false);
  return (
    <Toggler
      {...args}
      checked={isChecked}
      onChange={(checked) => setIsChecked(checked)}
    />
  );
};

export const Default: Story = {
  args: {
    labelText: "Toggler label",
    helperText: "Helper text",
    size: TogglerSize.DEFAULT,
  },
  render: (args) => <InteractiveToggler {...args} />,
};

export const Small: Story = {
  args: {
    ...Default.args,
    size: TogglerSize.SM,
  },
  render: (args) => <InteractiveToggler {...args} />,
};

export const Large: Story = {
  args: {
    ...Default.args,
    size: TogglerSize.LG,
  },
  render: (args) => <InteractiveToggler {...args} />,
};

export const InitiallyChecked: Story = {
  args: {
    ...Default.args,
    checked: true,
  },
  render: (args) => <InteractiveToggler {...args} />,
};

export const WithoutHelperText: Story = {
  args: {
    labelText: "Toggler label",
    size: TogglerSize.DEFAULT,
  },
  render: (args) => <InteractiveToggler {...args} />,
};

export const Error: Story = {
  args: {
    ...Default.args,
    state: CheckboxState.ERROR,
    errorText: "Error message",
  },
  render: (args) => <InteractiveToggler {...args} />,
};

export const Disabled: Story = {
  args: {
    ...Default.args,
    state: CheckboxState.DISABLED,
  },
};

export const Group: Story = {
  render: () => {
    const [checkedItems, setCheckedItems] = useState<Record<string, boolean>>(
      {}
    );

    return (
      <div style={{ display: "flex", flexDirection: "column", gap: "8px" }}>
        <Toggler
          labelText="Option 1"
          helperText="Helper text 1"
          name="group"
          value="1"
          checked={checkedItems["1"]}
          onChange={(checked) =>
            setCheckedItems((prev) => ({ ...prev, "1": checked }))
          }
        />
        <Toggler
          labelText="Option 2"
          helperText="Helper text 2"
          name="group"
          value="2"
          size={TogglerSize.SM}
          checked={checkedItems["2"]}
          onChange={(checked) =>
            setCheckedItems((prev) => ({ ...prev, "2": checked }))
          }
        />
        <Toggler
          labelText="Option 3"
          helperText="Helper text 3"
          name="group"
          value="3"
          size={TogglerSize.LG}
          checked={checkedItems["3"]}
          onChange={(checked) =>
            setCheckedItems((prev) => ({ ...prev, "3": checked }))
          }
        />
      </div>
    );
  },
};
