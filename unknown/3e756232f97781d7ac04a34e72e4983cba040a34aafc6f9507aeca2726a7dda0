import json

import aiohttp
import pytest
from aioresponses import aioresponses

from .landregistry import LandRegistryAPI


@pytest.mark.asyncio(loop_scope="session")
class TestLandRegistryAPI:
    @pytest.fixture
    def api(self):
        return LandRegistryAPI()

    async def test_get_average_price_paid_for_street_non_200_response(self, api):
        street = "BAKER STREET"
        town = "LONDON"
        postcode = "NW16XE"
        url = api._base_url
        with aioresponses() as m:
            m.post(url, status=500)  # Simulate a server error
            with pytest.raises(aiohttp.ClientResponseError) as exc_info:
                await api.get_average_price_paid_for_street(street, town, postcode)
            assert exc_info.value.status == 500

    async def test_get_average_price_paid_for_postcode(self, api):
        postcode = "W60WW"
        mock_data = {
            "status": 200,
            "result": """{
                "head": {"vars": ["postcode", "avg"]},
                "results": {
                    "bindings": [
                        {"postcode": {"value": "W60WW"}, "avg": {"value": "1098765"}}
                    ]
                }
            }""",
        }
        url = api._base_url
        with aioresponses() as m:
            m.post(url, payload=mock_data, status=200)
            result = await api.get_average_price_paid_for_postcode(postcode)
            m.assert_called_once()
            assert result == 1098765

    async def test_get_average_price_paid_for_street(self, api):
        street = "BAKER STREET"
        town = "LONDON"
        postcode = "NW16XE"
        mock_data = {
            "status": 200,
            "result": """{
                "head": {"vars": ["postcode", "avg"]},
                "results": {
                    "bindings": [
                        {"postcode": {"value": "NW16XE"}, "avg": {"value": "750000"}}
                    ]
                }
            }""",
        }
        url = api._base_url
        with aioresponses() as m:
            m.post(url, payload=mock_data, status=200)
            result = await api.get_average_price_paid_for_street(street, town, postcode)
            m.assert_called_once()
            assert result == 750000

    async def test_capitalizing_inputs(self, api):
        postcode = "nw16xe"
        mock_data = {
            "status": 200,
            "result": """{
                "head": {"vars": ["postcode", "avg"]},
                "results": {
                    "bindings": [
                        {"postcode": {"value": "NW16XE"}, "avg": {"value": "750000"}}
                    ]
                }
            }""",
        }
        url = api._base_url
        with aioresponses() as m:
            m.post(url, payload=mock_data, status=200)
            result = await api.get_average_price_paid_for_postcode(postcode)
            m.assert_called_once()
            assert postcode.upper() in next(iter(m.requests.values()))[0].kwargs["data"]._fields[2][2]
            assert result == 750000

    async def test_get_average_price_paid_for_flat_for_town(self, api):
        town = "LONDON"
        mock_data = {
            "status": 200,
            "result": """{
                "head": {"vars": ["town", "avg"]},
                "results": {
                    "bindings": [
                        {"town": {"value": "LONDON"}, "avg": {"value": "500000"}}
                    ]
                }
            }""",
        }
        url = api._base_url
        with aioresponses() as m:
            m.post(url, payload=mock_data, status=200)
            result = await api.get_average_price_paid_for_flat_for_town(town)
            m.assert_called_once()
            assert result == 500000

    async def test_get_average_price_paid_change_over_years_for_town(self, api):
        town = "LONDON"
        mock_data = {
            "status": 200,
            "result": """{
                "head": {"vars": ["year", "avgPrice"]},
                "results": {
                    "bindings": [
                        {"year": {"value": "2018"}, "avgPrice": {"value": "400000"}},
                        {"year": {"value": "2022"}, "avgPrice": {"value": "600000"}}
                    ]
                }
            }""",
        }
        url = api._base_url
        with aioresponses() as m:
            m.post(url, payload=mock_data, status=200)
            result = await api.get_average_price_paid_change_over_years_for_town(town)
            m.assert_called_once()
            assert result == 50  # (600000 - 400000) * 100 / 400000

        mock_data = {
            "status": 200,
            "result": """{
                "head": {"vars": ["year", "avgPrice"]},
                "results": {
                    "bindings": [
                        {"year": {"value": "2018"}, "avgPrice": {"value": "600000"}},
                        {"year": {"value": "2022"}, "avgPrice": {"value": "400000"}}
                    ]
                }
            }""",
        }
        url = api._base_url
        with aioresponses() as m:
            m.post(url, payload=mock_data, status=200)
            result = await api.get_average_price_paid_change_over_years_for_town(town)
            m.assert_called_once()
            assert result == -33  # (400000 - 600000) * 100 / 600000

    async def test_get_most_expensive_postcodes_for_town(self, api):
        town = "LONDON"
        mock_data = {
            "status": 200,
            "result": """{
                "head": {"vars": ["postcode", "avgPrice", "transactions"]},
                "results": {
                    "bindings": [
                        {"postcode": {"value": "W1A1AA"}, "avgPrice": {"value": "2000000"},
                        "transactions": {"value": "5"}},
                        {"postcode": {"value": "SW1A1AA"}, "avgPrice": {"value": "1500000"},
                        "transactions": {"value": "3"}}
                    ]
                }
            }""",
        }
        url = api._base_url
        with aioresponses() as m:
            m.post(url, payload=mock_data, status=200)
            result = await api.get_most_expensive_postcodes_for_town(town)
            m.assert_called_once()
            assert result == [("W1A1AA", 2000000, 5), ("SW1A1AA", 1500000, 3)]

    async def test_get_property_type(self, api):
        mock_response = {
            "status": 200,
            "result": json.dumps(
                {
                    "head": {"vars": ["propertytype"]},
                    "results": {
                        "bindings": [
                            {"propertytype": {"type": "literal", "xml:lang": "en", "value": "flat-maisonette"}}
                        ]
                    },
                }
            ),
        }

        url = api._base_url
        with aioresponses() as m:
            m.post(url, payload=mock_response, status=200)
            result = await api.get_property_type(
                postcode="W60WW", street="BAKER STREET", building_number="221B", building_name="", sub_building_name=""
            )
            m.assert_called_once()
            assert result == "flat-maisonette"
