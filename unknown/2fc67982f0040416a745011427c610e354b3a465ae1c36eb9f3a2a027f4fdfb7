"""add browserMimeType to documents

Revision ID: 60870e5c96ca
Revises: 47b07d4093c4
Create Date: 2025-01-31 10:35:15.864935

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '60870e5c96ca'
down_revision: Union[str, None] = '47b07d4093c4'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('documents', sa.Column('browserMimeType', sa.String(), nullable=True))
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('documents', 'browserMimeType')
    # ### end Alembic commands ###
