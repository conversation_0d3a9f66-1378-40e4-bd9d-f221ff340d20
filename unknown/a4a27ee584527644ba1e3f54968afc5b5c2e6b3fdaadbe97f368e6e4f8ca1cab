"""add property subgroupType

Revision ID: 9e4cd810a4e4
Revises: 9ef4170c9f70
Create Date: 2025-06-16 10:25:45.598431

"""

from typing import Sequence, Union

import sqlalchemy as sa
from alembic import op
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision: str = "9e4cd810a4e4"
down_revision: Union[str, None] = "9ef4170c9f70"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    propertysubgrouptype_enum = postgresql.ENUM(
        "detached",
        "semiDetached",
        "terraced",
        name="propertysubgrouptype",
        create_type=False,
    )
    propertysubgrouptype_enum.create(op.get_bind(), checkfirst=True)
    op.add_column(
        "properties",
        sa.Column(
            "subgroupType",
            propertysubgrouptype_enum,
            nullable=True,
        ),
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column("properties", "subgroupType")
    # ### end Alembic commands ###
