import logging
import traceback
import uuid

from langchain_anthropic import ChatAnthropic

# from langchain_together import Together
from langsmith import Client
import langsmith as ls
import os

from langsmith.run_helpers import get_run_tree_context

from src.agents.ImageAgentGemini import ImageAgentGemini
from src.agents.JobSummaryExtractor import JobSummaryExtractor
from src.dao.QdrantDAO import QdrantDAO
from src.schemas import ParseFileRequest, ImageUrl, ErrorDetail
from src.services.FileParser import FileParser
from typing import List, Optional, Union
import json
import re
import asyncio

from src.services.jobs import JobService
from src.agents.utils.ChatFormatter import ChatFormatter

session_id = "agents-playground-session" + str(uuid.uuid4())
thread_id = uuid.uuid4()
conversation_id = uuid.uuid4()


class DiagnosticAgentStreaming:
    def __init__(self, job_service: JobService, agent_prompt):
        # Initialize Lang<PERSON>mith
        self._together_ai_api_key = os.environ["TOGETHER_AI_API_KEY"]
        self.anthropic_api_key = os.environ["ANTHROPIC_API_KEY"]
        self._langsmith_key = os.environ["LANGSMITH_API_KEY"]
        self.IS_LOCAL_TESTING = os.environ.get("IS_LOCAL_TESTING", "false")
        os.environ["LANGCHAIN_TRACING_V2"] = "true"
        os.environ["LANGSMITH_ENDPOINT"] = "https://eu.api.smith.langchain.com"

        # Check if the variable is not present in the environment
        if "LANGCHAIN_PROJECT" not in os.environ:
            # Set the environment variable
            os.environ["LANGCHAIN_PROJECT"] = "property_manager"

        self.logger = logging.getLogger("uvicorn")
        self._langsmith_client = Client()
        # Option 1: Using session_id

        self.job_summary_extractor = JobSummaryExtractor()
        self.job_summary = None

        self.file_parser_instance = FileParser()
        self.qdrant_dao = QdrantDAO()
        self.image_agent = ImageAgentGemini()

        # self.web_search_pattern = re.compile(r"\[web search:(.*?)\]")
        # self.options_image_pattern = re.compile(r"\[OptionsImage:(.*?)\]")
        #self.job_summary_pattern = re.compile(r"\[\[([\s\S]*?)jobSummary\s*=\s*\{([\s\S]*?)\}([\s\S]*?)\]\]")

        self.web_search_pattern = re.compile(r"\[\s*web\s*search\s*:\s*(.*?)\s*\]", re.DOTALL)
        self.options_image_pattern = re.compile(r"\[\s*OptionsImage\s*:\s*(.*?)\s*\]", re.DOTALL)
        self.job_summary_pattern = self.job_summary_pattern = re.compile(r"\[\[([\s\S]*?)jobSummary\s*=\s*\{([\s\S]*?)\}([\s\S]*?)\]\]")


        self._job_service: JobService = job_service

        # Initialize LLMs
        self.diagnostic_llm = ChatAnthropic(
            model="claude-3-7-sonnet-20250219",
            # temperature=0.3,
            max_tokens=2000,
        )

        # Initialize LLMs
        # self.diagnostic_llm = Together(
        #     model="deepseek-ai/DeepSeek-V3",
        #     temperature=0.3,
        #     max_tokens=2000,
        #     together_api_key=self._together_ai_api_key,
        # )

        # Initialize LLMs
        # self.diagnostic_llm = ChatOpenAI(
        #     model="o1-mini",
        #     # temperature=0.3,
        #     max_tokens=2000,
        #     openai_api_key="********************************************************************************************************************************************************************"
        # )

        self.system_prompt = agent_prompt

    @staticmethod
    def get_content(obj):
        if hasattr(obj, "content"):
            return obj.content
        else:
            return obj

    @staticmethod
    def map_to_image_urls(query, image_data: List[str]) -> List[ImageUrl]:

        # Create an ImageUrl object for each URL in the list
        return [ImageUrl(imageUrl=url, description=query) for url in image_data]

    @staticmethod
    def format_image_descriptions(image_data: list[str]) -> str:
        """
        Converts a list of image descriptions into a formatted string.

        Args:
            image_data: List of image description strings

        Returns:
            str: Formatted string with numbered image descriptions

        Example:
            >>> descriptions = ["A cat sleeping", "A dog running"]
            >>> format_image_descriptions(descriptions)
            'IMAGE 1 DESCRIPTION:\nA cat sleeping\n\nIMAGE 2 DESCRIPTION:\nA dog running'
        """
        if not image_data:
            return ""

        formatted_descriptions = []
        for index, description in enumerate(image_data, 1):
            formatted_descriptions.append(f"IMAGE {index} DESCRIPTION:\n{description}")

        return "\n\n".join(formatted_descriptions)

    async def _process_llm_output(
        self,
        full_response_content: str,
        accumulated_image_urls: List[ImageUrl],
        accumulated_clickable_urls: List[ImageUrl],
        job_id: Optional[int] # Pass job_id if needed
    ):
        """Process the complete LLM response content after streaming and image commands."""
        # NOTE: full_response_content already has the special tokens removed by the streaming logic

        processed_response_text, job_summary = self.job_summary_extractor.extract_job_summary(full_response_content)

        if job_summary:
            self.logger.info(f"Job summary extracted: {str(job_summary)}")
            if job_id:
                job_summary = job_summary.model_copy(update={"jobId": job_id})
        # ImageAgentGemini process_response is Nno longercalled here
        # because the commands were handled during the stream.
        # We already have the image URLs retrieved.

        # The processed_response_text is the final text content without job summary JSON.
        return processed_response_text, job_summary, accumulated_image_urls, accumulated_clickable_urls

    def _detect_incomplete_token_at_end(self, buffer: str) -> int:
        """
        Detect if theres incomplete token at the end of the buffer.
        Returns the index where the incomplete token starts, or -1 if no incomplete token.

        This method fixes the case when LLM returned special token as the last character of the chunk.

        Exemplar chunks:
        data: {"type": "content", "data": " help you figure out why you have no hot water"}
        data: {"type": "content", "data": ". Let me ask a few quick"}
        data: {"type": "content", "data": " questions to understand the problem."}
        data: {"type": "content", "data": "\n\nFirst, what type of heating"}
        data: {"type": "content", "data": " system do you have in your home?\n["}
        data: {"type": "content", "data": "OptionsImage: Com"}
        data: {"type": "content", "data": "bi boiler; System boiler with"}
        data: {"type": "content", "data": " cylinder; Regular/conventional boiler]"}
        data: {"type": "final_data", "data": {"jobSummary": null, "imageUrls": null, "imageClickableUrls": null}}

        """
        # Check for incomplete tokens - search for [
        # [[ Handles cases: "[[jobSummary="
        incomplete_token_start = buffer.rfind("[[")
        closing_mark="]]"
        if incomplete_token_start==-1:
            # Check for incomplete tokens - search for [
            # Handles both cases: "[web search:", "[OptionsImage:"
            incomplete_token_start = buffer.rfind("[")
            closing_mark = "]"

        # Check if they are actually incomplete (no closing bracket after the last start)
        is_web_incomplete = incomplete_token_start != -1 and buffer.find(closing_mark, incomplete_token_start) == -1

        # Find the earliest potential incomplete token
        candidates = []
        if is_web_incomplete:
            candidates.append(incomplete_token_start)

        return min(candidates) if candidates else -1

    # Problem: We cannot wait until full response to remove special tokens for search images ([web search: or [OptionsImage:) and end (]).
    # Incoming chunks should be analyzed on the fly. While special token is identified we wait until the end of the special token,
    # remove the token for chunk and yield this chunk. Image search could be called asynchronously thank while the main loop
    # will still process incoming chunks of the response from the llm.
    #
    # We need to modify DiagnosticAgent.process_query_stream to:
    #   1. Buffer incoming text chunks.
    #   2. Detect the start ([web search: or [OptionsImage:) and end (]) of the special tokens.
    #   3. Pause yielding text when an incomplete token is detected at the end of the buffer.
    #   4. Once a complete token is detected:
    #       - Extract the command content (query or options).
    #       - Asynchronously call the relevant image processing logic (ImageAgentGemini.handle_search_request or a similar new function for OptionsImage).
    #       - Store the resulting ImageUrl objects in lists that will be added to the final_data payload.
    #       - Do not yield the special token text itself.
    #       - Continue yielding subsequent text chunks.
    #  5. Ensure the final additionalData payload includes all collected image URLs.
    @ls.traceable(
        run_type="llm",
        name="Process Query Stream Call Decorator",
        tags=["process_user_msg_stream"],
        metadata={"session_id": session_id, "thread_id": thread_id, "conversation_id": conversation_id},
    )
    async def process_query_stream(self, user_input, user_id, chat_id, image_data: list[str]):
        """Process user query with optional image data, streaming the response and handling image commands."""

        self.logger.info(f"process_query_stream(): user_id={user_id}, chat_id={chat_id} user_input={user_input}")

        job_id = None
        if not self.IS_LOCAL_TESTING:
            job = await self._job_service.get_last_job_for_user_and_chat(user_id, chat_id)
            if job:
                job_id = job.id

        run_context = get_run_tree_context()
        if run_context:
            run_context.metadata["session_id"] = f"chat_id: {chat_id}"
            run_context.metadata["user_id"] = f"user_id: {user_id}"

        image_context = ""
        if image_data:
            image_context = f"\nAttached images analysis: {self.format_image_descriptions(image_data)}\n"

        conversation_history = await self.qdrant_dao.get_chat_history(user_id, chat_id)
        formatted_conversation_history = ChatFormatter.format_conversation_for_context(conversation_history)

        full_prompt = (
            f"{self.system_prompt}\n\n"
            f"{formatted_conversation_history}"
            f"{image_context}"
            f"Current user message: {user_input}\n\n"
            "Respond directly to the user's current message, considering any relevant context from the conversation history. "
            "Do not simulate or script a conversation - provide a single, direct response as Alfie. "
            "Use [web search: query] or [OptionsImage: option1; option2] where appropriate."  # Remind LLM if needed
        )

        # !!! Streaming Logic with Token Interception
        buffer = ""
        full_response_for_history = ""  # Store raw LLM output for history
        accumulated_text_for_processing = ""  # Store text after removing tokens
        accumulated_image_urls: List[ImageUrl] = []
        accumulated_clickable_urls: List[ImageUrl] = []
        pending_tasks = []  # Store async tasks for image searches

        try:
            async for chunk in self.diagnostic_llm.astream(full_prompt):
                self.logger.info(f"llm_chunk={chunk}")
                content_chunk = self.get_content(chunk)
                if not isinstance(content_chunk, str):
                    continue  # Skip nonstring chunks

                full_response_for_history += content_chunk  # Append raw chunk for history
                buffer += content_chunk
                self.logger.info(f"buffer={buffer}")

                # Process buffer repeatedly for completed tokens
                while True:
                    web_match = self.web_search_pattern.search(buffer)
                    options_match = self.options_image_pattern.search(buffer)
                    job_summary_match =  self.job_summary_pattern.search(buffer)

                    # Find the earliest match
                    first_match = None
                    match_type = None
                    # Here we cover situation when we have two matches in the block and weseek which is first!
                    if web_match and options_match:
                        if web_match.start() < options_match.start():
                            first_match = web_match
                            match_type = "web"
                        else:
                            first_match = options_match
                            match_type = "options"
                    elif web_match:
                        first_match = web_match
                        match_type = "web"
                    elif options_match:
                        first_match = options_match
                        match_type = "options"
                    elif job_summary_match:
                        first_match = job_summary_match
                        match_type = "job_summary"

                    if first_match:
                        start_index, end_index = first_match.span()
                        # 1. Yield text before the matched token
                        text_before = buffer[:start_index]
                        if text_before:
                            yield f"data: {json.dumps({'type': 'content', 'data': text_before})}\n\n"
                            accumulated_text_for_processing += text_before

                        # 2. Extract content and schedule image processing
                        command_content = first_match.group(1).strip()
                        self.logger.info(f"Detected image command: Type='{match_type}', Content='{command_content}'")

                        if match_type == "web":
                            # Schedule the task
                            task = asyncio.create_task(
                                self.image_agent.handle_search_request(command_content, max_images=1)
                            )
                            pending_tasks.append(task)
                        elif match_type == "options":
                            # Schedule task for OptionsImage (needs specific handling)
                            task = asyncio.create_task(
                                self._handle_options_image_command(command_content)
                            )
                            pending_tasks.append(task)
                        elif match_type == "job_summary":
                            _, self.job_summary = self.job_summary_extractor.extract_job_summary(buffer)

                        # 3. Update buffer to remove the processed part (token + preceding text)
                        buffer = buffer[end_index:]

                        # 4. Continue checking the rest of the buffer in the while loop
                        continue
                    else:
                        # No more complete tokens found in the buffer for now
                        break

                # After checking for complete tokens check for incomplete parts at the end
                # Yield text up to the start of any potential incomplete token
                # Should handle cases:
                #        "Okay, I understand. It sounds like you have a combi boiler. [we",
                #        "b search: combi",
                #
                split_index = self._detect_incomplete_token_at_end(buffer)

                if split_index != -1:
                    # Yield text before the earliest incomplete token starts
                    text_to_yield = buffer[:split_index]
                    if text_to_yield:
                        yield f"data: {json.dumps({'type': 'content', 'data': text_to_yield})}\n\n"
                        accumulated_text_for_processing += text_to_yield
                    # Keep the earliest incomplete token onwards in the buffer
                    buffer = buffer[split_index:]
                else:
                    # No incomplete token suspected at the end, yield the whole buffer
                    if buffer:
                        yield f"data: {json.dumps({'type': 'content', 'data': buffer})}\n\n"
                        accumulated_text_for_processing += buffer
                    buffer = ""

            # --- End of LLM Stream ---

            # Yield any remaining text in the buffer (should be empty ideally, but handles edge cases)
            if buffer:
                # Treat remaining buffer content. If it contains an unclosed token it might be an error or truncated output.
                self.logger.warning(f"Yielding remaining buffer content after stream end: {buffer}")
                yield f"data: {json.dumps({'type': 'content', 'data': buffer})}\n\n"
                accumulated_text_for_processing += buffer

            # --- Process Pending Tasks ---
            fallback_strings_to_append = []
            if pending_tasks:
                self.logger.info(f"Waiting for {len(pending_tasks)} pending image tasks...")
                # Use asyncio.wait to handle timeouts per task. TODO - how to process exceptions from ImageAgent like timeouts?
                results = await asyncio.gather(*pending_tasks, return_exceptions=True)
                self.logger.info("Image tasks finished.")

                for result in results:
                    if isinstance(result, Exception):
                        self.logger.error(f"Image processing task failed: {result}")
                        # Optionally add error info to fallback_strings or handle differently
                    elif isinstance(result, tuple) and len(
                            result) == 2:  # Expected from handle_search_request (web search)
                        _, urls = result
                        if isinstance(urls, list):
                            accumulated_image_urls.extend(urls)
                    elif isinstance(result, list):  # Expected from successful _handle_options_image_command
                        accumulated_clickable_urls.extend(result)
                    # Here I convert clickable images to Options in case ImageAgent is unable to find appropriate image
                    elif isinstance(result,
                                    str):  # Expected from failed _handle_options_image_command (fallback string)
                        fallback_strings_to_append.append(result)
                    else:
                        self.logger.warning(f"Unexpected result type from image task: {type(result)}")

            # --- Final Processing & Payloads ---
            self.logger.debug(
                f"Streaming finished. Final accumulated text length (before fallbacks): {len(accumulated_text_for_processing)}")

            # Update conversation history using raw LLM output
            conversation_history.append((user_input, full_response_for_history))
            await self.qdrant_dao.update_chat_search_on_history_change(user_id, chat_id, conversation_history)

            # extract JobSummary
            _, job_summary, _, _ = await self._process_llm_output(
                accumulated_text_for_processing,  # Text without tokens or fallbacks
                [],
                [],
                job_id
            )

            # Yield Fallback Strings if present
            if fallback_strings_to_append:
                self.logger.info(f"Appending {len(fallback_strings_to_append)} fallback [Options:...] strings.")
                for fallback_str in fallback_strings_to_append:
                    yield f"data: {json.dumps({'type': 'content', 'data': '\n' + fallback_str})}\n\n"

            # extracted form final body or extracted from stream. In general should be extracted
            # from stream but lets not remove previous extraction
            job_summary_to_dump = job_summary or self.job_summary

            # Prepare and Yield the Final Data Payload
            final_data = {
                "jobSummary": job_summary_to_dump.model_dump(exclude_none=True) if job_summary_to_dump else None,
                "imageUrls": [img.model_dump(exclude_none=True) for img in
                              accumulated_image_urls] if accumulated_image_urls else None,
                "imageClickableUrls": [img.model_dump(exclude_none=True) for img in
                                       accumulated_clickable_urls] if accumulated_clickable_urls else None,
            }
            yield f"data: {json.dumps({'type': 'final_data', 'data': final_data})}\n\n"

            # Yield End Event
            yield f"event: end\ndata: Stream finished\n\n"

        except Exception as e:
            self.logger.error(f"Error during streaming query: {str(e)}\nStacktrace: {traceback.format_exc()}")
            error_str = str(e).lower()
            error_payload = None
            if (("529" in error_str and ("overload" in error_str or "too many requests" in error_str)) or
                    ("500" in error_str and "api_error" in error_str) or
                    ("429" in error_str) or ("resourceexhausted" in error_str) or
                    ("ratelimiterror" in error_str)):
                error_payload = ErrorDetail(
                    code="LLM_OVERLOADED",
                    message="The LLM service is currently overloaded or rate limited",
                    details={"original_error": str(e)}
                ).model_dump()
            else:
                error_payload = ErrorDetail(
                    code="INTERNAL_ERROR",
                    message="An unexpected error occurred during streaming",
                    details={"original_error": str(e)}
                ).model_dump()

            if error_payload:
                yield f"event: error\ndata: {json.dumps(error_payload)}\n\n"
            yield f"event: end\ndata: Stream finished with error\n\n"

    async def _handle_options_image_command(self, options_string: str) -> Union[List[ImageUrl], str]:
        """
        Handles the [OptionsImage:...] command asynchronously.
        Returns List[ImageUrl] on success (images found for all options).
        Returns str "[Options: original_query]" if any option fails to find an image.
        """
        options = [opt.strip() for opt in options_string.split(";") if opt.strip()]
        self.logger.info(f"Handling OptionsImage command async with options: {options}")

        if not options:
            return []  # Success case technically, no options provided -> empty list

        tasks = []
        for option in options:
            tasks.append(asyncio.create_task(self.image_agent.handle_search_request(option, max_images=1)))

        results = await asyncio.gather(*tasks, return_exceptions=True)

        was_one_empty = False
        temp_urls = []
        for i, result in enumerate(results):
            option = options[i]
            if isinstance(result, Exception):
                self.logger.error(f"Failed search for option '{option}': {result}")
                was_one_empty = True
                break
            elif isinstance(result, tuple) and len(result) == 2:
                _, urls = result
                if isinstance(urls, list) and len(urls) > 0:
                    urls[0].description = option
                    temp_urls.append(urls[0])
                else:
                    self.logger.warning(f"No image found for option: '{option}'")
                    was_one_empty = True
                    break
            else:
                self.logger.warning(f"Unexpected result type for option '{option}': {type(result)}")
                was_one_empty = True
                break

        if not was_one_empty:
            self.logger.info(f"Successfully gathered {len(temp_urls)} clickable images for options.")
            return temp_urls  # List[ImageUrl]
        else:
            self.logger.warning("Failed to get images for all options, returning fallback string.")
            fallback_string = f"[Options: {options_string}]"
            return fallback_string  # str

    async def process_next_message_stream(self, user_input, user_id, chat_id,
                                          attachments: list[ParseFileRequest] = None):
        image_data: list[str] = []
        appliance_prompt = (
            "Analyze this image and describe any relevant details about the appliance or issue shown. "
            "Try to identify appliance type and make and model if possible"
        )
        try:
            if attachments:
                async with asyncio.timeout(120):
                    attachment_tasks = []
                    for file in attachments:
                        attachment_tasks.append(self._process_single_attachment(file, appliance_prompt))

                    results = await asyncio.gather(*attachment_tasks, return_exceptions=True)

                    for result in results:
                        if isinstance(result, Exception):
                            self.logger.error(f"Error processing attachment: {result}")
                            error_payload = ErrorDetail(
                                code="ATTACHMENT_ERROR",
                                message="Failed to process an attached file.",
                                #details={"original_error": str(result)} # hidden not to show to the user
                                details={"original_error": "truncated"}
                            ).model_dump()
                            yield f"event: error\ndata: {json.dumps(error_payload)}\n\n"
                        elif isinstance(result, str):
                            image_data.append(result)

            async for chunk in self.process_query_stream(user_input, user_id, chat_id, image_data):
                yield chunk

        except asyncio.TimeoutError:
            self.logger.error(f"Timeout processing attachments for user_id={user_id}, chat_id={chat_id}")
            error_payload = ErrorDetail(
                code="ATTACHMENT_TIMEOUT",
                message="Processing attached files took too long.",
                details={}
            ).model_dump()
            yield f"event: error\ndata: {json.dumps(error_payload)}\n\n"
            yield f"event: end\ndata: Stream finished with attachment error\n\n"
        except Exception as e:
            self.logger.error(
                f"Error processing attachments or initiating stream for user_id={user_id}, chat_id={chat_id}: {str(e)}\nStacktrace: {traceback.format_exc()}")
            error_payload = ErrorDetail(
                code="STREAM_INIT_ERROR",
                message="Failed to process attachments or start response stream.",
                #details={"original_error": str(e)}
                details = {"original_error": str(e)}
            ).model_dump()
            yield f"event: error\ndata: {json.dumps(error_payload)}\n\n"
            yield f"event: end\ndata: Stream finished with init error\n\n"

    async def _process_single_attachment(self, file: ParseFileRequest, prompt: str) -> str:
        """Helper to process a single attachment asynchronously."""
        if file.base64:
            return await self.file_parser_instance.process_file_for_appliance_analysis_base64(
                file.base64, prompt
            )
        else:
            return await self.file_parser_instance.process_file_for_appliance_analysis(
                file.userId,
                file.documentId,
                file.documentS3Key,
                file.documentS3Bucket,
                file.type,
                prompt,
            )