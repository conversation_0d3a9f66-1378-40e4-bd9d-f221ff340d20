.markdownWrapper {
    max-width: 100%;

    ul,
    ol {
        list-style: none;
        margin: 0;
        padding: 0;
    }

    .customList {
        display: flex;
        flex-direction: column;
        padding: 0;

        .listItem {
            display: flex;
            align-items: flex-start;


            &:last-child {
                margin-bottom: 0;
            }

            .listMarker {
                text-align: left;
                padding-right: 0.3rem;
                font-weight: 500;
                color: var(--text-primary, #1F2937);
                line-height: 1.5;
                display: flex;
                justify-content: flex-start;
            }

            .listContent {
                flex: 1;
                min-width: 0;
                display: flex;
                flex-direction: column;

                p {
                    &:last-child {
                        margin-bottom: 0;
                    }
                }

                .customList {
                    .listMarker {
                        font-size: 0.95em;
                    }
                }
            }
        }
    }

    .orderedList {
        .orderedList * .listMarker {
            font-size: 0.95em;
        }

        .orderedList .orderedList * .listMarker {
            font-size: 0.9em;
        }

        * .listMarker::before {
            content: attr(data-index) ".";
        }
    }

    .unorderedList {
        .unorderedList .unorderedList * .listMarker {
            font-size: 0.9em;
        }

        * .listMarker::before {
            content: "-";
        }
    }

    .table {
        width: 100%;
        border-collapse: collapse;
        background: var(--background-white, #FFFFFF);
        position: relative;
        table-layout: fixed;
        border-radius: 12px;
        overflow: hidden;

        &::before {
            content: '';
            position: absolute;
            inset: 0;
            border-radius: 12px;
            border: 1px solid #D1D5DB;
            pointer-events: none;
        }
    }

    .th {
        padding: 8px;
        text-align: left;
        font-weight: 600;
        font-size: 16px;
        background: #E5E7EB;
        border: 1px solid #D1D5DB;
        border-width: 0 1px 1px 0;
        width: 35%;
        vertical-align: top;

        &:last-child {
            border-right-width: 0;
            width: 65%;
        }
    }

    .td {
        padding: 8px;
        font-size: 16px;
        line-height: 150%;
        border: 1px solid #D1D5DB;
        border-width: 0 1px 1px 0;
        background: white;
        vertical-align: top;

        &:last-child {
            border-right-width: 0;
        }

        img {
            max-width: 33px;
            height: 31px;
            border-radius: 4px;
            object-fit: cover;
        }
    }

    a {
        color: #3B82F6;
        text-decoration: none;

        &:hover {
            text-decoration: underline;
        }
    }

    code {
        font-family: monospace;
        background-color: #F3F4F6;
        padding: 0.2rem 0.4rem;
        border-radius: 4px;
        font-size: 0.9em;
    }

    pre {
        background-color: #F3F4F6;
        padding: 1rem;
        border-radius: 8px;
        overflow-x: auto;
        margin: 0 0 1rem 0;

        code {
            background-color: transparent;
            padding: 0;
            border-radius: 0;
        }
    }
}