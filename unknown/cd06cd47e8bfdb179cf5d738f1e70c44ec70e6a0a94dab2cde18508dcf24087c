import React from 'react';
import { render } from '@testing-library/react';
import { vi } from 'vitest';
import { PropertyForm } from '../PropertyForm';
import { OwnershipType, PropertyType, PropertySubType, YesNoOption } from '../PropertyForm.types';

// Mock HugeIcons
vi.mock('@hugeicons-pro/core-stroke-rounded', () => ({
  ArrowDown01Icon: ({ className, size }: { className?: string; size?: number }) => (
    <div data-testid="arrow-icon" className={className}>
      Arrow {size}
    </div>
  ),
}));

vi.mock('../../HugeiconsIcon', () => ({
  HugeiconsIcon: ({ size, className }: { size?: number; className?: string }) => (
    <div data-testid="hugicons-icon" className={className}>
      Mock Icon {size}
    </div>
  ),
}));

describe('PropertyForm Snapshots', () => {
  const mockOnSubmit = vi.fn();

  it('renders default PropertyForm correctly', () => {
    const { container } = render(<PropertyForm onSubmit={mockOnSubmit} />);
    expect(container.firstChild).toMatchSnapshot();
  });

  it('renders PropertyForm with initial data correctly', () => {
    const initialData = {
      ownershipType: OwnershipType.FREEHOLD,
      propertyType: PropertyType.HOUSE,
      propertySubType: PropertySubType.DETACHED,
      numberOfBedrooms: '3',
      numberOfBathrooms: '2',
      numberOfFloors: '2',
      grossInternalArea: '120',
      balconyTerrace: YesNoOption.YES,
      garden: YesNoOption.NO,
      swimmingPool: YesNoOption.YES,
      documents: [],
    };

    const { container } = render(
      <PropertyForm onSubmit={mockOnSubmit} initialData={initialData} />
    );
    expect(container.firstChild).toMatchSnapshot();
  });

  it('renders PropertyForm with partial data correctly', () => {
    const partialData = {
      ownershipType: OwnershipType.LEASEHOLD,
      propertyType: PropertyType.FLAT,
      numberOfBedrooms: '2',
      balconyTerrace: YesNoOption.YES,
    };

    const { container } = render(
      <PropertyForm onSubmit={mockOnSubmit} initialData={partialData} />
    );
    expect(container.firstChild).toMatchSnapshot();
  });

  it('renders PropertyForm with custom className correctly', () => {
    const { container } = render(
      <PropertyForm onSubmit={mockOnSubmit} className="custom-form-class" />
    );
    expect(container.firstChild).toMatchSnapshot();
  });

  it('renders PropertyForm for office property correctly', () => {
    const officeData = {
      ownershipType: OwnershipType.FREEHOLD,
      propertyType: PropertyType.OFFICE,
      propertySubType: PropertySubType.DETACHED,
      numberOfBedrooms: '0',
      numberOfBathrooms: '2',
      numberOfFloors: '1',
      grossInternalArea: '200',
      balconyTerrace: YesNoOption.NO,
      garden: YesNoOption.NO,
      swimmingPool: YesNoOption.NO,
    };

    const { container } = render(<PropertyForm onSubmit={mockOnSubmit} initialData={officeData} />);
    expect(container.firstChild).toMatchSnapshot();
  });
});
