import pytest
from sqlalchemy.ext.asyncio import AsyncSession

from src.db_models.chat import Chat
from src.db_models.document import Document, DocumentStatusType
from src.db_models.property import PropertyConditionType, PropertyEpcRatingType
from src.db_models.property import PropertyConservationStatusType
from src.db_models.property import PropertySubgroupType
from src.db_models.property import PropertyType, PropertyTenureType
from src.db_models.relationships import UserPropertyRelationType
from src.db_models.source_link import SourceType
from src.db_models.user import User
from src.schemas import (
    PropertyCreate,
    ManualAddressCreate,
    OSDataAddressCreate,
    PropertyUpdate,
    PropertyDetailsData,
    PropertyAiUpdate,
    Address,
    PropertyInfo,
    AddressUpdate,
)
from src.services.properties import PropertyService, PropertyDoesNotExist
from src.tests.matchers import model_dump


# Fixtures for dummy entities
@pytest.fixture
async def test_user(async_db_session):
    user = User(
        id=1,
        email="<EMAIL>",
        clerkId="clerk_user_id",
        firstName="Test",
        lastName="User",
    )
    async_db_session.add(user)
    await async_db_session.flush()
    return user


@pytest.fixture
async def another_test_user(async_db_session):
    user = User(
        id=2,
        email="<EMAIL>",
        clerkId="clerk_user_id2",
        firstName="Test2",
        lastName="User2",
    )
    async_db_session.add(user)
    await async_db_session.flush()
    return user


@pytest.fixture
async def test_document(async_db_session, test_user):
    document = Document(
        id=123,
        userId=test_user.id,
        source="system",
        s3Key="test-document-key-123",
        s3Bucket="test-bucket",
        fileExtension="pdf",
        sizeInKiloBytes=1024,
        originalFileName="test_document.pdf",
        status=DocumentStatusType.processingCompleted,
    )
    async_db_session.add(document)
    await async_db_session.flush()
    return document


@pytest.fixture
async def test_chat(async_db_session, test_user):
    chat = Chat(id=456, userId=test_user.id, title="Test chat for updates")
    async_db_session.add(chat)
    await async_db_session.flush()
    return chat


@pytest.fixture
async def property_with_user(async_db_session: AsyncSession, property_service: PropertyService, test_user):
    property_create = PropertyCreate(idealPostcodesAddressId="ID12345")
    created_property = await property_service.create_property_for_user(test_user, property_create)
    async_db_session.add(test_user)
    await async_db_session.flush()
    return created_property, test_user


@pytest.fixture
async def empty_property_with_user(async_db_session: AsyncSession, property_service: PropertyService, test_user):
    empty_property = await property_service.create_property_for_user(test_user)
    return empty_property, test_user


@pytest.mark.asyncio
async def test_create_property_with_ideal_postcodes(async_db_session: AsyncSession, property_service, test_user):
    property_create = PropertyCreate(
        idealPostcodesAddressId="ID12345", type=PropertyType.flat, tenureType=PropertyTenureType.leasehold
    )
    await property_service.create_property_for_user(test_user, property_create)
    await async_db_session.flush()

    fetched_property = await property_service.get_first_property_for_user(test_user)
    assert fetched_property is not None
    assert fetched_property.address.streetLine1 == "22 Eardley Crescent"
    assert fetched_property.address.townOrCity == "London"
    assert fetched_property.address.postcode == "SW5 9JZ"
    assert fetched_property.address.uprn == 217025320
    assert float(fetched_property.address.latitude) == pytest.approx(51.488550, rel=1e-4)
    assert float(fetched_property.address.longitude) == pytest.approx(-0.1953660, rel=1e-4)
    assert fetched_property.type == PropertyType.flat
    assert fetched_property.tenureType == PropertyTenureType.leasehold
    assert fetched_property.address.idealPostcodesOutput is not None


async def test_create_property_with_ideal_postcodes_no_prop_type(async_db_session, property_service, test_user):
    property_create = PropertyCreate(idealPostcodesAddressId="ID12345")
    property_service._land_registry_api.get_property_type.return_value = None
    await property_service.create_property_for_user(test_user, property_create)
    await async_db_session.flush()

    fetched_property = await property_service.get_first_property_for_user(test_user)
    assert fetched_property is not None
    assert fetched_property.address.streetLine1 == "22 Eardley Crescent"
    assert fetched_property.address.townOrCity == "London"
    assert fetched_property.address.postcode == "SW5 9JZ"
    assert fetched_property.address.uprn == 217025320
    assert float(fetched_property.address.latitude) == pytest.approx(51.488550, rel=1e-4)
    assert float(fetched_property.address.longitude) == pytest.approx(-0.1953660, rel=1e-4)
    assert fetched_property.type is None


async def test_create_property_with_ideal_postcodes_other_prop_type(
    async_db_session,
    property_service,
    test_user,
):
    property_create = PropertyCreate(idealPostcodesAddressId="ID12345")
    property_service._land_registry_api.get_property_type.return_value = "RD-unknown"
    await property_service.create_property_for_user(test_user, property_create)
    await async_db_session.flush()

    fetched_property = await property_service.get_first_property_for_user(test_user)
    assert fetched_property is not None
    assert fetched_property.address.streetLine1 == "22 Eardley Crescent"
    assert fetched_property.address.townOrCity == "London"
    assert fetched_property.address.postcode == "SW5 9JZ"
    assert fetched_property.address.uprn == 217025320
    assert float(fetched_property.address.latitude) == pytest.approx(51.488550, rel=1e-4)
    assert float(fetched_property.address.longitude) == pytest.approx(-0.1953660, rel=1e-4)
    assert fetched_property.type is None


@pytest.mark.asyncio
async def test_create_property_with_manual_address(property_service, test_user):
    manual_address = ManualAddressCreate(
        streetLine1="123 Test St",
        streetLine2="",
        townOrCity="Testville",
        postcode="TE57 1NG",
        country="UK",
    )
    property_create = PropertyCreate(manualAddress=manual_address)
    created_property = await property_service.create_property_for_user(test_user, property_create)
    assert created_property is not None
    assert created_property.address.streetLine1 == "123 Test St"
    assert created_property.address.townOrCity == "Testville"
    assert created_property.address.postcode == "TE57 1NG"


@pytest.mark.asyncio
async def test_create_property_with_os_data_address(async_db_session, property_service, test_user):
    os_data_address = OSDataAddressCreate(
        BUILDING_NUMBER="1",
        THOROUGHFARE_NAME="Test Road",
        POST_TOWN="Testville",
        POSTCODE="TE57 1NG",
        UPRN="123456789",
        X_COORDINATE=391654.39,
        Y_COORDINATE=174902.5,
        CLASSIFICATION_CODE="RD02",
        COUNTRY_CODE="E",
    )
    property_create = PropertyCreate(osDataAddress=os_data_address, type=PropertyType.house)
    await property_service.create_property_for_user(test_user, property_create)
    await async_db_session.flush()

    fetched_property = await property_service.get_first_property_for_user(test_user)
    assert fetched_property is not None
    assert fetched_property.address.streetLine1 == "1, Test Road"
    assert fetched_property.address.townOrCity == "Testville"
    assert fetched_property.address.postcode == "TE57 1NG"
    assert fetched_property.address.uprn == 123456789
    assert float(fetched_property.address.latitude) == pytest.approx(51.473023, rel=1e-4)
    assert float(fetched_property.address.longitude) == pytest.approx(-2.121548, rel=1e-4)
    assert fetched_property.type == PropertyType.house
    assert fetched_property.tenureType is None


@pytest.mark.asyncio
async def test_create_property_for_user(async_db_session, property_service, test_user):
    property_create = PropertyCreate(
        idealPostcodesAddressId="ID12345", userPropertyRelationshipType=UserPropertyRelationType.tenant
    )
    created_property = await property_service.create_property_for_user(test_user, property_create)

    expected_property = {
        "address": {
            "country": "UK",
            "houseAccess": None,
            "id": 1,
            "parkingInstructions": None,
            "postcode": "SW5 9JZ",
            "streetLine1": "22 Eardley Crescent",
            "streetLine2": "",
            "townOrCity": "London",
        },
        "architecturalType": None,
        "balconyTerraceDetails": None,
        "condition": None,
        "conservationStatus": None,
        "epcRating": None,
        "gardenDetails": None,
        "hasBalconyTerrace": None,
        "hasGarden": None,
        "hasSwimmingPool": None,
        "id": 1,
        "lastSoldPriceInGbp": None,
        "numberOfBathrooms": None,
        "numberOfBedrooms": None,
        "numberOfFloors": None,
        "onFloorLevel": None,
        "proportionOfFlatRoof": None,
        "sizeInSqft": None,
        "subgroupType": None,
        "swimmingPoolDetails": None,
        "tenureType": None,
        "type": None,
        "typeOfConstruction": None,
        "typeOfLock": None,
        "userPropertyRelationshipType": UserPropertyRelationType.tenant,
        "valuationInGbp": None,
        "yearsOfOwnership": None,
    }

    assert model_dump(created_property) == expected_property

    fetched_properties = list(await property_service.get_properties_for_user(test_user))
    assert model_dump(fetched_properties) == [expected_property]


@pytest.mark.asyncio
async def test_create_property_for_user_without_relationship(async_db_session, property_service, test_user):
    property_create = PropertyCreate(idealPostcodesAddressId="ID12345")
    created_property = await property_service.create_property_for_user(test_user, property_create)

    assert created_property is not None
    assert created_property.address.streetLine1 == "22 Eardley Crescent"
    assert created_property.address.townOrCity == "London"

    fetched_properties = list(await property_service.get_properties_for_user(test_user))
    assert model_dump(fetched_properties) == [
        {
            "address": {
                "country": "UK",
                "houseAccess": None,
                "id": 1,
                "parkingInstructions": None,
                "postcode": "SW5 9JZ",
                "streetLine1": "22 Eardley Crescent",
                "streetLine2": "",
                "townOrCity": "London",
            },
            "architecturalType": None,
            "balconyTerraceDetails": None,
            "condition": None,
            "conservationStatus": None,
            "epcRating": None,
            "gardenDetails": None,
            "hasBalconyTerrace": None,
            "hasGarden": None,
            "hasSwimmingPool": None,
            "id": 1,
            "lastSoldPriceInGbp": None,
            "numberOfBathrooms": None,
            "numberOfBedrooms": None,
            "numberOfFloors": None,
            "onFloorLevel": None,
            "proportionOfFlatRoof": None,
            "sizeInSqft": None,
            "subgroupType": None,
            "swimmingPoolDetails": None,
            "tenureType": None,
            "type": None,
            "typeOfConstruction": None,
            "typeOfLock": None,
            "userPropertyRelationshipType": None,
            "valuationInGbp": None,
            "yearsOfOwnership": None,
        }
    ]


@pytest.mark.asyncio
async def test_get_property_by_id_for_user(async_db_session, property_service, test_user, another_test_user):
    property_create = PropertyCreate(idealPostcodesAddressId="ID12345")
    created_property = await property_service.create_property_for_user(test_user, property_create)
    await async_db_session.flush()

    fetched_property = await property_service.get_property_by_id_for_user(created_property.id, test_user)
    assert fetched_property is not None
    assert fetched_property.id == created_property.id
    assert fetched_property.address.streetLine1 == "22 Eardley Crescent"
    assert fetched_property.address.townOrCity == "London"
    assert fetched_property.address.postcode == "SW5 9JZ"
    assert fetched_property.address.uprn == 217025320

    fetched_property = await property_service.get_property_by_id_for_user(created_property.id, another_test_user)
    assert fetched_property is None


@pytest.mark.asyncio
async def test_delete_property_by_id_for_user(async_db_session, property_service, test_user, another_test_user):
    property_create = PropertyCreate(idealPostcodesAddressId="ID12345")
    created_property = await property_service.create_property_for_user(test_user, property_create)
    await async_db_session.flush()

    fetched_property = await property_service.get_first_property_for_user(test_user)
    assert fetched_property is not None

    await property_service.delete_property_by_id_for_user(created_property.id, another_test_user)
    await async_db_session.flush()
    fetched_property = await property_service.get_first_property_for_user(test_user)
    assert fetched_property is not None

    await property_service.delete_property_by_id_for_user(created_property.id, test_user)
    await async_db_session.flush()

    # Ensure the property is deleted
    fetched_property = await property_service.get_first_property_for_user(test_user)
    assert fetched_property is None


@pytest.mark.asyncio
async def test_property_update_all_fields_by_user(
    async_db_session, property_service, property_with_user, another_test_user
):
    created_property, user = property_with_user

    updated_data = PropertyUpdate(
        address=AddressUpdate(
            streetLine1="22 Eardley Crescent",  # Keep existing required fields
            townOrCity="London",  # Keep existing required fields
            postcode="SW5 9JZ",  # Keep existing required fields
            houseAccess="Main entrance with security code",
            parkingInstructions="Use visitor parking space #5",
            country="United Kingdom",
        ),
        tenureType=PropertyTenureType.freehold,
        type=PropertyType.house,
        subgroupType=PropertySubgroupType.detached,
        numberOfBedrooms=4,
        numberOfBathrooms=3,
        numberOfFloors=2,
        sizeInSqft=2500,
        onFloorLevel=0,
        hasBalconyTerrace=True,
        hasGarden=True,
        hasSwimmingPool=False,
        userPropertyRelationshipType=UserPropertyRelationType.tenant,
    )

    await property_service.update_property_by_id_for_user(created_property.id, updated_data, user)
    await async_db_session.flush()

    fetched_property = await property_service.get_first_property_for_user(user)
    assert fetched_property is not None
    assert fetched_property.address.houseAccess == "Main entrance with security code"
    assert fetched_property.address.parkingInstructions == "Use visitor parking space #5"
    assert fetched_property.tenureType == PropertyTenureType.freehold
    assert fetched_property.type == PropertyType.house
    assert fetched_property.subgroupType == PropertySubgroupType.detached
    assert fetched_property.numberOfBedrooms == 4
    assert fetched_property.numberOfBathrooms == 3
    assert fetched_property.numberOfFloors == 2
    assert fetched_property.sizeInSqft == 2500
    assert fetched_property.onFloorLevel == 0
    assert fetched_property.hasBalconyTerrace is True
    assert fetched_property.hasGarden is True
    assert fetched_property.hasSwimmingPool is False
    assert fetched_property.usersProperties[0].relationType == UserPropertyRelationType.tenant

    with pytest.raises(PropertyDoesNotExist):
        await property_service.update_property_by_id_for_user(created_property.id, updated_data, another_test_user)


@pytest.mark.asyncio
async def test_property_update_partial_fields_by_user(async_db_session, property_service, property_with_user):
    created_property, user = property_with_user

    # Update only a few fields
    partial_update = PropertyUpdate(
        numberOfBedrooms=3,
        hasGarden=True,
    )

    await property_service.update_property_by_id_for_user(created_property.id, partial_update, user)
    await async_db_session.flush()

    fetched_property = await property_service.get_first_property_for_user(user)
    assert fetched_property.numberOfBedrooms == 3
    assert fetched_property.hasGarden is True
    # Original address should remain unchanged
    assert fetched_property.address.streetLine1 == "22 Eardley Crescent"
    assert fetched_property.address.townOrCity == "London"


@pytest.mark.asyncio
async def test_property_update_address_only_by_user(async_db_session, property_service, property_with_user):
    created_property, user = property_with_user

    # Update only address access fields
    address_update = PropertyUpdate(
        address=AddressUpdate(
            streetLine1="22 Eardley Crescent",  # Keep existing
            townOrCity="London",  # Keep existing
            postcode="SW5 9JZ",  # Keep existing
            houseAccess="Updated access instructions",
            parkingInstructions="New parking info",
            country="United Kingdom",
        )
    )

    await property_service.update_property_by_id_for_user(created_property.id, address_update, user)
    await async_db_session.flush()

    fetched_property = await property_service.get_first_property_for_user(user)
    assert fetched_property.address.houseAccess == "Updated access instructions"
    assert fetched_property.address.parkingInstructions == "New parking info"
    # Core address should remain
    assert fetched_property.address.streetLine1 == "22 Eardley Crescent"


@pytest.mark.asyncio
async def test_property_update_with_null_values_by_user(async_db_session, property_service, property_with_user):
    created_property, user = property_with_user

    # Set some initial values
    created_property.numberOfBedrooms = 5
    created_property.hasGarden = True
    await async_db_session.flush()

    # Update with explicit None values (should reset the values)
    null_update = PropertyUpdate(
        numberOfBedrooms=None,
        hasGarden=None,
    )

    await property_service.update_property_by_id_for_user(created_property.id, null_update, user)
    await async_db_session.flush()

    fetched_property = await property_service.get_first_property_for_user(user)
    assert fetched_property.numberOfBedrooms == None
    assert fetched_property.hasGarden == None


@pytest.mark.asyncio
async def test_property_update_empty_property_by_user(async_db_session, property_service, empty_property_with_user):
    empty_property, user = empty_property_with_user

    # Add address and property details
    full_update = PropertyUpdate(
        address=Address(
            streetLine1="New Street 123",
            townOrCity="New City",
            postcode="NW1 1AA",
            houseAccess="Main entrance",
            country="United Kingdom",
        ),
        type=PropertyType.house,
        numberOfBedrooms=4,
    )

    await property_service.update_property_by_id_for_user(empty_property.id, full_update, user)
    await async_db_session.flush()

    fetched_property = await property_service.get_first_property_for_user(user)
    # Verify everything was added
    assert fetched_property.address is not None
    assert fetched_property.address.streetLine1 == "New Street 123"
    assert fetched_property.address.townOrCity == "New City"
    assert fetched_property.address.houseAccess == "Main entrance"
    assert fetched_property.type == PropertyType.house
    assert fetched_property.numberOfBedrooms == 4


@pytest.mark.asyncio
async def test_update_property_by_id_by_ai(
    async_db_session, property_service, property_with_user, test_document, source_link_service
):
    created_property, user = property_with_user
    test_document.propertyId = created_property.id
    await async_db_session.flush()

    property_details = PropertyDetailsData(
        type=PropertyType.house.value,
        tenureType=PropertyTenureType.freehold.value,
        numberOfBedrooms=3,
        numberOfBathrooms=2,
        condition=PropertyConditionType.excellent.value,
        epcRating=PropertyEpcRatingType.a.value,
    )

    ai_update = PropertyAiUpdate(
        propertyDetails=property_details,
        srcType=SourceType.document,
        srcId=test_document.id,
    )

    await property_service.update_property_by_id_by_ai(created_property.id, ai_update)
    await async_db_session.flush()

    fetched_property = await property_service.get_first_property_for_user(user)
    assert fetched_property is not None
    assert fetched_property.type == PropertyType.house
    assert fetched_property.tenureType == PropertyTenureType.freehold
    assert fetched_property.numberOfBedrooms == 3
    assert fetched_property.numberOfBathrooms == 2
    assert fetched_property.condition == PropertyConditionType.excellent
    assert fetched_property.epcRating == PropertyEpcRatingType.a

    # Original address should remain unchanged
    assert fetched_property.address.streetLine1 == "22 Eardley Crescent"
    assert fetched_property.address.townOrCity == "London"
    assert fetched_property.address.postcode == "SW5 9JZ"

    source_links = await source_link_service.get_source_links(fetched_property)
    assert model_dump(source_links) == [
        {"destinationField": "condition", "source": {"id": 123, "srcType": "document"}},
        {"destinationField": "epcRating", "source": {"id": 123, "srcType": "document"}},
        {"destinationField": "idealPostcodesAddressId", "source": {"id": 1, "srcType": "userInput"}},
        {"destinationField": "numberOfBathrooms", "source": {"id": 123, "srcType": "document"}},
        {"destinationField": "numberOfBedrooms", "source": {"id": 123, "srcType": "document"}},
        {"destinationField": "tenureType", "source": {"id": 123, "srcType": "document"}},
        {"destinationField": "type", "source": {"id": 123, "srcType": "document"}},
    ]


@pytest.mark.asyncio
async def test_that_ai_cannot_override_properties_set_by_user(
    async_db_session, property_service, test_user, test_document, source_link_service
):
    initial_tenure_type = PropertyTenureType.freehold
    property_create = PropertyCreate(idealPostcodesAddressId="ID12345", tenureType=initial_tenure_type)
    created_property = await property_service.create_property_for_user(test_user, property_create)
    test_document.propertyId = created_property.id
    await async_db_session.flush()

    ai_update = PropertyAiUpdate(
        propertyDetails=PropertyDetailsData(
            type=PropertyType.house.value,
            tenureType=PropertyTenureType.shareOfFreehold.value,
        ),
        srcType=SourceType.document,
        srcId=test_document.id,
    )

    await property_service.update_property_by_id_by_ai(created_property.id, ai_update)
    await async_db_session.flush()

    fetched_property = await property_service.get_first_property_for_user(test_user)
    assert fetched_property is not None
    assert fetched_property.type == PropertyType.house
    assert fetched_property.tenureType == initial_tenure_type

    source_links = await source_link_service.get_source_links(fetched_property)
    assert model_dump(source_links) == [
        {"destinationField": "idealPostcodesAddressId", "source": {"id": 1, "srcType": "userInput"}},
        {"destinationField": "tenureType", "source": {"id": 1, "srcType": "userInput"}},
        {"destinationField": "type", "source": {"id": 123, "srcType": "document"}},
    ]


@pytest.mark.asyncio
async def test_ai_update_with_enum_mapping_valid_values(
    async_db_session, property_service, property_with_user, test_document
):
    created_property, user = property_with_user
    test_document.propertyId = created_property.id
    await async_db_session.flush()

    property_details = PropertyDetailsData(
        type=PropertyType.house.value,
        tenureType=PropertyTenureType.freehold.value,
        condition=PropertyConditionType.excellent.value,
        epcRating=PropertyEpcRatingType.a.value,
        conservationStatus=PropertyConservationStatusType.gradeI.value,
    )

    ai_update = PropertyAiUpdate(
        propertyDetails=property_details,
        srcType=SourceType.document,
        srcId=test_document.id,
    )

    await property_service.update_property_by_id_by_ai(created_property.id, ai_update)
    await async_db_session.flush()

    fetched_property = await property_service.get_first_property_for_user(user)
    assert fetched_property.type == PropertyType.house
    assert fetched_property.tenureType == PropertyTenureType.freehold
    assert fetched_property.condition == PropertyConditionType.excellent
    assert fetched_property.epcRating == PropertyEpcRatingType.a
    assert fetched_property.conservationStatus == PropertyConservationStatusType.gradeI


@pytest.mark.asyncio
async def test_ai_update_no_updated_fields_no_source_links(async_db_session, property_service, property_with_user):
    created_property, user = property_with_user

    test_document = Document(
        id=999,
        userId=user.id,
        propertyId=created_property.id,
        source="system",
        s3Key="empty-update-key",
        s3Bucket="test-bucket",
        fileExtension="pdf",
        sizeInKiloBytes=512,
        originalFileName="empty.pdf",
        status=DocumentStatusType.processingCompleted,
    )
    async_db_session.add(test_document)
    await async_db_session.flush()

    # Create empty update (no actual changes)
    empty_property_details = PropertyDetailsData()

    ai_update = PropertyAiUpdate(
        propertyDetails=empty_property_details,
        srcType=SourceType.document,
        srcId=test_document.id,
    )

    # This should not create source links since no fields were updated
    result = await property_service.update_property_by_id_by_ai(created_property.id, ai_update)
    await async_db_session.flush()

    # Property should be returned but unchanged
    assert result.id == created_property.id
    # Original address should remain unchanged
    assert result.address.streetLine1 == "22 Eardley Crescent"


@pytest.mark.asyncio
async def test_user_update_vs_ai_update(async_db_session, property_service, property_with_user):
    created_property, user = property_with_user

    user_update = PropertyUpdate(
        type=PropertyType.house,
        tenureType=PropertyTenureType.freehold,
    )

    await property_service.update_property_by_id_for_user(created_property.id, user_update, user)
    await async_db_session.flush()

    fetched_after_user = await property_service.get_first_property_for_user(user)
    assert fetched_after_user.type == PropertyType.house
    assert fetched_after_user.tenureType == PropertyTenureType.freehold

    test_chat = Chat(id=111, userId=user.id, propertyId=created_property.id, title="Enum test chat")
    async_db_session.add(test_chat)
    await async_db_session.flush()

    ai_update = PropertyAiUpdate(
        propertyDetails=PropertyDetailsData(
            type=PropertyType.flat.value,
            tenureType=PropertyTenureType.leasehold.value,
        ),
        srcType=SourceType.chat,
        srcId=test_chat.id,
    )

    await property_service.update_property_by_id_by_ai(created_property.id, ai_update)
    await async_db_session.flush()

    fetched_after_ai = await property_service.get_first_property_for_user(user)
    assert fetched_after_ai.type == PropertyType.house
    assert fetched_after_ai.tenureType == PropertyTenureType.freehold


@pytest.mark.asyncio
async def test_property_query_returns_mappable_results(test_user, property_service, async_db_session):
    property_create = PropertyCreate(
        manualAddress=ManualAddressCreate(
            streetLine1="22 Eardley Crescent", townOrCity="London", postcode="SW5 9JZ", country="UK"
        ),
        userPropertyRelationshipType=UserPropertyRelationType.tenant,
        type=PropertyType.house,
        tenureType=PropertyTenureType.freehold,
    )
    await property_service.create_property_for_user(test_user, property_create)

    sequence = await property_service.get_properties_for_user(test_user)
    result = list(sequence)
    assert len(result) == 1
    raw_property = result[0]
    property_info = PropertyInfo.model_validate(raw_property)
    assert property_info == PropertyInfo(
        id=1,
        address=Address(id=1, streetLine1="22 Eardley Crescent", townOrCity="London", postcode="SW5 9JZ", country="UK"),
        userPropertyRelationshipType=UserPropertyRelationType.tenant,
        type=PropertyType.house,
        tenureType=PropertyTenureType.freehold,
    )
