import React from 'react';
import type { Meta, StoryObj } from '@storybook/react';
import { Composer } from './Composer';
import { ComposerSize } from './Composer.types';

const meta = {
  title: 'Components/Composer',
  component: Composer,
  parameters: {
    layout: 'centered',
  },
  tags: ['autodocs'],
  argTypes: {
    size: {
      control: 'select',
      options: Object.values(ComposerSize),
      description: 'Size variant of the composer',
    },
    placeholder: {
      control: 'text',
      description: 'Placeholder text',
    },
  },
} satisfies Meta<typeof Composer>;

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
  args: {
    placeholder: 'Message Alfie',
    size: ComposerSize.DEFAULT,
  },
};

export const Playground: Story = {
  args: {
    size: ComposerSize.PLAYGROUND,
  },
};

export const AllVariants: Story = {
  render: () => (
    <div style={{ display: 'flex', flexDirection: 'column', gap: '24px' }}>
      {/* Empty state */}
      <div>
        <h3>Empty State</h3>
        <Composer size={ComposerSize.DEFAULT} />
      </div>

      {/* With voice recording */}
      <div>
        <h3>Voice Recording</h3>
        <Composer size={ComposerSize.DEFAULT} isRecording={true} />
      </div>

      {/* Mobile version */}
      <div>
        <h3>Mobile Version (Playground)</h3>
        <Composer size={ComposerSize.PLAYGROUND} />
      </div>

      {/* With multiple files in different states */}
      <div>
        <h3>Multiple Files Upload States</h3>
        <Composer size={ComposerSize.DEFAULT} />
      </div>

      {/* With loading state */}
      <div>
        <h3>Loading State</h3>
        <Composer
          size={ComposerSize.DEFAULT}
          defaultValue="Some text being processed..."
          loading={true}
        />
      </div>
    </div>
  ),
};
