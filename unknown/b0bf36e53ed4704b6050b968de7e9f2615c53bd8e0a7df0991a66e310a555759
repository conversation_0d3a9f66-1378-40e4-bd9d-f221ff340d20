name: Deploy to staging
on:
  release:
    types: [published]

permissions:
  id-token: write # This is required for requesting the JWT
  contents: read # This is required for actions/checkout

jobs:
  deploy-staging:
    runs-on: ubuntu-latest
    steps:
      - name: Configure A<PERSON> credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          role-to-assume: ${{ vars.AWS_OICD_ROLE_ARN_STAGING }}
          aws-region: ${{ vars.AWS_REGION }}
          mask-aws-account-id: false

      - name: Redeploy the ECS service
        run: |
          aws ecs update-service --cluster ${{ secrets.SERVICES_ECS_CLUSTER_NAME_STAGING }} --service ${{ secrets.BACKEND_ECS_SERVICE_NAME_STAGING }} --force-new-deployment