import React, { useState, useEffect, useCallback } from 'react';
import { debounce } from 'lodash';
import classNames from 'classnames';
import { Radio } from '../Radio/Radio';
import { RadioState } from '../Radio/Radio.types';
import { MultiFieldInput } from '../MultiFieldInput/MultiFieldInput';
import {
  MultiFieldInputType,
  MultiFieldInputMode,
  Option,
} from '../MultiFieldInput/MultiFieldInput.types';
import { AddressRoleFormProps, UserRoleOption, AddressType } from './AddressRoleForm.types';
import styles from './AddressRoleForm.module.scss';

const addressRoleOptions: UserRoleOption[] = [
  {
    id: 'owner',
    title: 'Owner and occupier',
    description: 'You own the property and also occupy or live in it',
  },
  {
    id: 'landlord',
    title: 'Landlord',
    description: 'You own the property and tenant it out for rent',
  },
  {
    id: 'tenant',
    title: 'Tenant',
    description: 'You rent this property from a landlord',
  },
];

export const AddressRoleForm: React.FC<AddressRoleFormProps> = ({
  selectedAddress,
  selectedRole,
  onAddressChange,
  onRoleChange,
  forceDropdownPosition,
  className,
}) => {
  const [currentMode, setCurrentMode] = useState(() => {
    if (selectedAddress) {
      return typeof selectedAddress === 'string'
        ? MultiFieldInputMode.SELECT
        : MultiFieldInputMode.MANUAL;
    }
    return MultiFieldInputMode.SELECT;
  });

  const [displayAddress, setDisplayAddress] = useState<string | null>(null);

  useEffect(() => {
    if (selectedAddress) {
      if (typeof selectedAddress === 'string') {
        setCurrentMode(MultiFieldInputMode.SELECT);
      } else {
        setCurrentMode(MultiFieldInputMode.MANUAL);
      }
    }
  }, [selectedAddress]);

  const debouncedAddressChange = useCallback(
    debounce((address: AddressType) => {
      if (onAddressChange) {
        onAddressChange(address);
      }
    }, 500),
    [onAddressChange]
  );

  const handleSwitchToManual = () => {
    setCurrentMode(MultiFieldInputMode.MANUAL);
  };

  const handleSelectOption = (option: Option) => {
    setDisplayAddress(option.label);
    if (onAddressChange) {
      onAddressChange(option.value, option.label);
    }
  };

  const handleAddressChange = (value: AddressType) => {
    if (currentMode === MultiFieldInputMode.MANUAL) {
      debouncedAddressChange(value);
    }
  };

  const handleRoleChange = (role: string) => {
    if (onRoleChange) {
      onRoleChange(role);
    }
  };

  const getSubtitle = () => {
    switch (selectedRole) {
      case 'owner':
        return 'Find your address';
      case 'landlord':
        return 'Find your address';
      case 'tenant':
        return 'Find your address';
      default:
        return 'Find your address';
    }
  };

  return (
    <div className={classNames(styles.container, className)}>
      <p className={styles.subtitle}>{getSubtitle()}</p>
      <div className={styles.inputContainer}>
        <MultiFieldInput
          type={MultiFieldInputType.ADDRESS}
          title=""
          value={
            displayAddress || selectedAddress || { line1: '', line2: '', city: '', postcode: '' }
          }
          onChange={handleAddressChange}
          placeholder="Find address"
          manualEntryText="Enter address manually"
          hideBorder
          hideSaveButton
          forceDropdownPosition={forceDropdownPosition}
          initialMode={currentMode}
          onSwitchToManual={handleSwitchToManual}
          onSelectOption={handleSelectOption}
          hideTitle
          compactMode
        />
      </div>

      <div className={styles.roleSelectionContainer}>
        <p className={styles.roleSelectionTitle}>How you will use Hey Alfie</p>
        <p className={styles.roleSelectionSubtitle}>
          Select the option that describes your situation for the provided address
        </p>

        <div className={styles.roleOptionsContainer}>
          {addressRoleOptions.map((option) => (
            <div
              key={option.id}
              className={classNames(styles.radioOption, {
                [styles.selected]: selectedRole === option.id,
              })}
            >
              <Radio
                labelText={option.title}
                helperText={option.description}
                checked={selectedRole === option.id}
                onChange={() => handleRoleChange(option.id)}
                state={RadioState.NORMAL}
                name="userRole"
                value={option.id}
              />
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};
