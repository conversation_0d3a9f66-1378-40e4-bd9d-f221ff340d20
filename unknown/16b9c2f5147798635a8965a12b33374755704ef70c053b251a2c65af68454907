.radio-field {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-3);
  cursor: pointer;

  &.disabled {
    cursor: not-allowed;
  }
}

.content {
  display: flex;
  align-items: flex-start;
  gap: var(--spacing-2);
  cursor: inherit;
}

.radio {
  appearance: none;
  width: var(--spacing-4);
  height: var(--spacing-4);
  border: 1.5px solid var(--colors-gray-300);
  border-radius: var(--border-radius-rounded-full);
  background-color: var(--colors-gray-50);
  cursor: inherit;
  position: relative;
  margin: 0;
  border-width: 0.5px;

  &:checked {
    border-color: var(--colors-green-500);
    background-color: var(--colors-white);
    background-color: var(--colors-green-500);

    &::after {
      content: '';
      position: absolute;
      width: 8px;
      height: 8px;
      background-color: var(--colors-white);
      border-radius: var(--border-radius-rounded-full);
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
    }
  }

  &.error {
    border-color: var(--colors-red-500);
  }

  &.disabled {
    background-color: var(--colors-gray-100);
    border-color: var(--colors-gray-300);
    cursor: not-allowed;
  }
}

.label-helper {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-1);
  cursor: inherit;
}

.label {
  color: var(--colors-gray-900);
  font-family: "Quasimoda", Helvetica;
  font-size: 16px;
  font-weight: 400;
  line-height: 16px;
  text-align: left;
}

.caption {
  color: var(--colors-gray-500);
  font-family: "Quasimoda", Helvetica;
  font-size: 12px;
  font-weight: 400;
  line-height: 16px;
  text-align: left;

  &.disabled {
    color: var(--colors-gray-400);
  }
}

.error-container {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
}

.error-icon {
  color: var(--colors-red-500);
}

.error-text {
  color: var(--colors-red-500);
  font-family: "Quasimoda", Helvetica;
  font-size: 16px;
  font-weight: 400;
  line-height: 16px;
  text-align: left;
} 