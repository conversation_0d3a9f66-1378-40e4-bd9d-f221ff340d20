{"version": "3.2.4", "results": [[":frontend/src/components/PersonalizationCard/__tests__/PersonalizationCard.test.tsx", {"duration": 0, "failed": true}], [":frontend/src/components/PropertyForm/__tests__/PropertyForm.test.tsx", {"duration": 0, "failed": true}], [":frontend/src/components/Onboarding/__tests__/Onboarding.test.tsx", {"duration": 0, "failed": true}], [":frontend/src/components/Alert/__tests__/Alert.test.tsx", {"duration": 0, "failed": true}], [":frontend/src/hooks/__tests__/useEditableFields.test.ts", {"duration": 0, "failed": true}], [":frontend/src/components/Button/__tests__/Button.test.tsx", {"duration": 0, "failed": true}], [":frontend/src/components/ConfirmToast/__tests__/ConfirmToast.test.tsx", {"duration": 0, "failed": true}], [":frontend/src/components/FormRadioCard/__tests__/FormRadioCard.test.tsx", {"duration": 0, "failed": true}], [":frontend/src/components/Message/components/TextMessage/__tests__/TextMessage.test.tsx", {"duration": 0, "failed": true}], [":frontend/src/components/Toast/__tests__/Toast.test.tsx", {"duration": 0, "failed": true}], [":frontend/src/components/ApplianceModal/__tests__/ApplianceModal.test.tsx", {"duration": 0, "failed": true}], [":frontend/src/components/FormDropdown/__tests__/FormDropdown.test.tsx", {"duration": 0, "failed": true}], [":frontend/src/components/Dropdowns/__tests__/Dropdowns.test.tsx", {"duration": 0, "failed": true}], [":frontend/src/components/ProcessingIndicator/__tests__/ProcessingIndicator.test.tsx", {"duration": 0, "failed": true}], [":frontend/src/components/Alert/__tests__/Alert.snapshot.test.tsx", {"duration": 0, "failed": true}], [":frontend/src/components/Message/components/ImageMessage/__tests__/ImageMessage.test.tsx", {"duration": 0, "failed": true}], [":frontend/src/components/Toggler/__tests__/Toggler.test.tsx", {"duration": 0, "failed": true}], [":frontend/src/components/Radio/__tests__/Radio.test.tsx", {"duration": 0, "failed": true}], [":frontend/src/components/Message/__tests__/Message.snapshot.test.tsx", {"duration": 0, "failed": true}], [":frontend/src/components/MarkdownRenderer/__tests__/MarkdownRenderer.test.tsx", {"duration": 0, "failed": true}], [":frontend/src/components/Checkbox/__tests__/Checkbox.test.tsx", {"duration": 0, "failed": true}], [":frontend/src/components/FormRadioCard/__tests__/FormRadioCard.snapshot.test.tsx", {"duration": 0, "failed": true}], [":frontend/src/components/Composer/__tests__/Composer.test.tsx", {"duration": 0, "failed": true}], [":frontend/src/components/Message/__tests__/Message.test.tsx", {"duration": 0, "failed": true}], [":frontend/src/components/Onboarding/__tests__/Onboarding.snapshot.test.tsx", {"duration": 0, "failed": true}], [":frontend/src/components/PersonalizationCard/__tests__/PersonalizationCard.snapshot.test.tsx", {"duration": 0, "failed": true}], [":frontend/src/components/PropertyForm/__tests__/PropertyForm.snapshot.test.tsx", {"duration": 0, "failed": true}], [":frontend/src/containers/FilesPage/utils/__tests__/getUploadErrorMessage.test.ts", {"duration": 0, "failed": true}], [":frontend/src/components/FormDropdown/__tests__/FormDropdown.snapshot.test.tsx", {"duration": 0, "failed": true}], [":frontend/src/components/QuickActions/__tests__/QuickActions.test.tsx", {"duration": 0, "failed": true}], [":frontend/src/components/Message/components/TextMessage/__tests__/TextMessage.snapshot.test.tsx", {"duration": 0, "failed": true}], [":frontend/src/components/Sidebar/__tests__/Sidebar.test.tsx", {"duration": 0, "failed": true}], [":frontend/src/containers/ChatPage/__tests__/ChatPage.test.tsx", {"duration": 0, "failed": true}], [":frontend/src/components/FirstPromptPage/__tests__ 2/FirstPromptPage.test.tsx", {"duration": 0, "failed": true}], [":frontend/src/components/Sidebar/__tests__/Sidebar.snapshot.test.tsx", {"duration": 0, "failed": true}], [":frontend/src/components/FirstPromptPage/__tests__/FirstPromptPage.test.tsx", {"duration": 0, "failed": true}], [":frontend/src/app/__tests__/page.test.tsx", {"duration": 0, "failed": true}], [":frontend/src/components/Dropdowns/__tests__/Dropdowns.snapshot.test.tsx", {"duration": 0, "failed": true}], [":frontend/src/containers/ChatPage/__tests__/ChatPage.snapshot.test.tsx", {"duration": 0, "failed": true}], [":frontend/src/components/Composer/__tests__/Composer.snapshot.test.tsx", {"duration": 0, "failed": true}], [":frontend/src/components/ApplianceModal/__tests__/ApplianceModal.snapshot.test.tsx", {"duration": 0, "failed": true}], [":frontend/src/components/NotificationTag/__tests__/NotificationTag.test.tsx", {"duration": 0, "failed": true}], [":frontend/src/__tests__/middleware.test.ts", {"duration": 0, "failed": true}], [":frontend/src/components/Button/__tests__/Button.snapshot.test.tsx", {"duration": 0, "failed": true}], [":frontend/src/components/Toggler/__tests__/Toggler.snapshot.test.tsx", {"duration": 0, "failed": true}], [":frontend/src/components/FirstPromptPage/__tests__/FirstPromptPage.snapshot.test.tsx", {"duration": 0, "failed": true}], [":frontend/src/components/MarkdownRenderer/__tests__/MarkdownRenderer.snapshot.test.tsx", {"duration": 0, "failed": true}], [":frontend/src/components/Toast/__tests__/Toast.snapshot.test.tsx", {"duration": 0, "failed": true}], [":frontend/src/components/Checkbox/__tests__/Checkbox.snapshot.test.tsx", {"duration": 0, "failed": true}], [":frontend/src/components/Message/components/ImageMessage/__tests__/ImageMessage.snapshot.test.tsx", {"duration": 0, "failed": true}], [":frontend/src/components/NotificationTag/__tests__/NotificationTag.snapshot.test.tsx", {"duration": 0, "failed": true}], [":frontend/src/components/Radio/__tests__/Radio.snapshot.test.tsx", {"duration": 0, "failed": true}], [":frontend/src/components/Header/__tests__/Header.test.tsx", {"duration": 0, "failed": true}]]}