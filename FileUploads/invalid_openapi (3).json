{"openapi": "3.1.0", "info": {"title": "FastAPI", "version": "0.1.0"}, "paths": {"/messages/": {"post": {"tags": ["messages"], "summary": "Send User Message", "operationId": "send_user_message_messages__post", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/SendMessageRequest"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SendMessageResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}, "security": [{"HTTPBearer": []}]}}, "/messages/stream": {"post": {"tags": ["messages"], "summary": "Send User Message Stream", "operationId": "send_user_message_stream_messages_stream_post", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/SendMessageRequest"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}, "security": [{"HTTPBearer": []}]}}, "/chats/": {"get": {"tags": ["chats"], "summary": "List Of Chats For User", "operationId": "list_of_chats_for_user_chats__get", "security": [{"HTTPBearer": []}], "parameters": [{"name": "page", "in": "query", "required": false, "schema": {"type": "integer", "minimum": 1, "description": "Page number", "default": 1, "title": "Page"}, "description": "Page number"}, {"name": "size", "in": "query", "required": false, "schema": {"type": "integer", "maximum": 100, "minimum": 1, "description": "Page size", "default": 50, "title": "Size"}, "description": "Page size"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Page_ChatInfo_"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/chats/{chat_id}/messages/": {"get": {"tags": ["chats"], "summary": "List Messages For Chat", "operationId": "list_messages_for_chat_chats__chat_id__messages__get", "security": [{"HTTPBearer": []}], "parameters": [{"name": "chat_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Chat Id"}}, {"name": "page", "in": "query", "required": false, "schema": {"type": "integer", "minimum": 1, "description": "Page number", "default": 1, "title": "Page"}, "description": "Page number"}, {"name": "size", "in": "query", "required": false, "schema": {"type": "integer", "maximum": 100, "minimum": 1, "description": "Page size", "default": 50, "title": "Size"}, "description": "Page size"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Page_Message_"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/documents/": {"post": {"tags": ["documents"], "summary": "Upload Document", "operationId": "upload_document_documents__post", "security": [{"HTTPBearer": []}], "requestBody": {"required": true, "content": {"multipart/form-data": {"schema": {"$ref": "#/components/schemas/Body_upload_document_documents__post"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DocumentInfo"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "get": {"tags": ["documents"], "summary": "List Documents For User", "operationId": "list_documents_for_user_documents__get", "security": [{"HTTPBearer": []}], "parameters": [{"name": "page", "in": "query", "required": false, "schema": {"type": "integer", "minimum": 1, "description": "Page number", "default": 1, "title": "Page"}, "description": "Page number"}, {"name": "size", "in": "query", "required": false, "schema": {"type": "integer", "maximum": 100, "minimum": 1, "description": "Page size", "default": 50, "title": "Size"}, "description": "Page size"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Page_DocumentInfo_"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/documents/{document_id}/": {"get": {"tags": ["documents"], "summary": "Download Document", "operationId": "download_document_documents__document_id___get", "security": [{"HTTPBearer": []}], "parameters": [{"name": "document_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Document Id"}}], "responses": {"200": {"description": "Successful Response"}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "delete": {"tags": ["documents"], "summary": "Delete Document", "operationId": "delete_document_documents__document_id___delete", "security": [{"HTTPBearer": []}], "parameters": [{"name": "document_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Document Id"}}], "responses": {"204": {"description": "Successful Response"}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/properties/": {"post": {"tags": ["properties"], "summary": "Create Property Endpoint", "operationId": "create_property_endpoint_properties__post", "security": [{"HTTPBearer": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PropertyCreate"}}}}, "responses": {"201": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Property"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "get": {"tags": ["properties"], "summary": "Get Properties Endpoint", "operationId": "get_properties_endpoint_properties__get", "security": [{"HTTPBearer": []}], "parameters": [{"name": "page", "in": "query", "required": false, "schema": {"type": "integer", "minimum": 1, "description": "Page number", "default": 1, "title": "Page"}, "description": "Page number"}, {"name": "size", "in": "query", "required": false, "schema": {"type": "integer", "maximum": 100, "minimum": 1, "description": "Page size", "default": 50, "title": "Size"}, "description": "Page size"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Page_Property_"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/properties/{property_id}": {"get": {"tags": ["properties"], "summary": "Get Property By Id Endpoint", "operationId": "get_property_by_id_endpoint_properties__property_id__get", "security": [{"HTTPBearer": []}], "parameters": [{"name": "property_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Property Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Property"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "patch": {"tags": ["properties"], "summary": "Update Property By Id Endpoint", "operationId": "update_property_by_id_endpoint_properties__property_id__patch", "security": [{"HTTPBearer": []}], "parameters": [{"name": "property_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Property Id"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PropertyUpdate"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Property"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "delete": {"tags": ["properties"], "summary": "Delete Property By Id Endpoint", "operationId": "delete_property_by_id_endpoint_properties__property_id__delete", "security": [{"HTTPBearer": []}], "parameters": [{"name": "property_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Property Id"}}], "responses": {"204": {"description": "Successful Response"}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/user/": {"get": {"tags": ["user"], "summary": "Get Current User Details Endpoint", "operationId": "get_current_user_details_endpoint_user__get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserDetails"}}}}}, "security": [{"HTTPBearer": []}]}, "patch": {"tags": ["user"], "summary": "Update User Details Endpoint", "operationId": "update_user_details_endpoint_user__patch", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserUpdate"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserDetails"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}, "security": [{"HTTPBearer": []}]}}, "/user/request-b2b-demo/": {"post": {"tags": ["user"], "summary": "Request B2B Demo Endpoint", "operationId": "request_b2b_demo_endpoint_user_request_b2b_demo__post", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/B2BDemoRequest"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}, "security": [{"HTTPBearer": []}]}}, "/jobs/": {"get": {"tags": ["jobs"], "summary": "List Of Jobs For User", "operationId": "list_of_jobs_for_user_jobs__get", "security": [{"HTTPBearer": []}], "parameters": [{"name": "page", "in": "query", "required": false, "schema": {"type": "integer", "minimum": 1, "description": "Page number", "default": 1, "title": "Page"}, "description": "Page number"}, {"name": "size", "in": "query", "required": false, "schema": {"type": "integer", "maximum": 100, "minimum": 1, "description": "Page size", "default": 50, "title": "Size"}, "description": "Page size"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Page_JobInfo_"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/jobs/{job_id}": {"get": {"tags": ["jobs"], "summary": "Get Job By Id", "operationId": "get_job_by_id_jobs__job_id__get", "security": [{"HTTPBearer": []}], "parameters": [{"name": "job_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Job Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/JobInfo"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/jobs/{job_id}/user-accept/": {"post": {"tags": ["jobs"], "summary": "User Accept Job", "operationId": "user_accept_job_jobs__job_id__user_accept__post", "security": [{"HTTPBearer": []}], "parameters": [{"name": "job_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Job Id"}}], "responses": {"202": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/appliances/": {"get": {"tags": ["appliances"], "summary": "Get Appliances Endpoint", "operationId": "get_appliances_endpoint_appliances__get", "security": [{"HTTPBearer": []}], "parameters": [{"name": "page", "in": "query", "required": false, "schema": {"type": "integer", "minimum": 1, "description": "Page number", "default": 1, "title": "Page"}, "description": "Page number"}, {"name": "size", "in": "query", "required": false, "schema": {"type": "integer", "maximum": 100, "minimum": 1, "description": "Page size", "default": 50, "title": "Size"}, "description": "Page size"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Page_ApplianceInfo_"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "post": {"tags": ["appliances"], "summary": "Create Appliance Endpoint", "operationId": "create_appliance_endpoint_appliances__post", "security": [{"HTTPBearer": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApplianceCreate"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApplianceInfo"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/appliances/{appliance_id}": {"patch": {"tags": ["appliances"], "summary": "Update Appliance Endpoint", "operationId": "update_appliance_endpoint_appliances__appliance_id__patch", "security": [{"HTTPBearer": []}], "parameters": [{"name": "appliance_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Appliance Id"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApplianceUpdate"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApplianceInfo"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/external-datasources/mean-household-income-postcode": {"get": {"tags": ["external-datasources"], "summary": "Get Mean Household Income Endpoint", "operationId": "get_mean_household_income_endpoint_external_datasources_mean_household_income_postcode_get", "security": [{"HTTPBearer": []}], "parameters": [{"name": "postcode", "in": "query", "required": true, "schema": {"type": "string", "title": "Postcode"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/MeanHouseHoldIncomeResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/external-datasources/average-price-postcode": {"get": {"tags": ["external-datasources"], "summary": "Get Average Price For Postcode Endpoint", "operationId": "get_average_price_for_postcode_endpoint_external_datasources_average_price_postcode_get", "security": [{"HTTPBearer": []}], "parameters": [{"name": "postcode", "in": "query", "required": true, "schema": {"type": "string", "title": "Postcode"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/AverageHousePriceForPostcodeResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/external-datasources/average-price-street": {"get": {"tags": ["external-datasources"], "summary": "Get Average Price For Street Endpoint", "operationId": "get_average_price_for_street_endpoint_external_datasources_average_price_street_get", "security": [{"HTTPBearer": []}], "parameters": [{"name": "postcode", "in": "query", "required": true, "schema": {"type": "string", "title": "Postcode"}}, {"name": "town", "in": "query", "required": true, "schema": {"type": "string", "title": "Town"}}, {"name": "street", "in": "query", "required": true, "schema": {"type": "string", "title": "Street"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/AverageHousePriceForStreetResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/external-datasources/average-flat-price-town": {"get": {"tags": ["external-datasources"], "summary": "Get Average Price For Flat In Town Endpoint", "operationId": "get_average_price_for_flat_in_town_endpoint_external_datasources_average_flat_price_town_get", "security": [{"HTTPBearer": []}], "parameters": [{"name": "town", "in": "query", "required": true, "schema": {"type": "string", "title": "Town"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/AverageHousePriceForFlatForTownResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/external-datasources/average-price-changed-over-years-town": {"get": {"tags": ["external-datasources"], "summary": "Get Average Price Change For Town Endpoint", "operationId": "get_average_price_change_for_town_endpoint_external_datasources_average_price_changed_over_years_town_get", "security": [{"HTTPBearer": []}], "parameters": [{"name": "town", "in": "query", "required": true, "schema": {"type": "string", "title": "Town"}}, {"name": "years", "in": "query", "required": false, "schema": {"type": "integer", "default": 5, "title": "Years"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/AverageHousePriceChangeOverYearsForTownResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/external-datasources/most-expensive-postcodes-for-town": {"get": {"tags": ["external-datasources"], "summary": "Get Most Expensive Postcodes For Town Endpoint", "operationId": "get_most_expensive_postcodes_for_town_endpoint_external_datasources_most_expensive_postcodes_for_town_get", "security": [{"HTTPBearer": []}], "parameters": [{"name": "town", "in": "query", "required": true, "schema": {"type": "string", "title": "Town"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/MostExpensivePostcodesForTownResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/external-datasources/find-place": {"get": {"tags": ["external-datasources"], "summary": "Find Place Endpoint", "operationId": "find_place_endpoint_external_datasources_find_place_get", "security": [{"HTTPBearer": []}], "parameters": [{"name": "name", "in": "query", "required": true, "schema": {"type": "string", "title": "Name"}}, {"name": "max_results", "in": "query", "required": false, "schema": {"type": "integer", "default": 5, "title": "Max Results"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "object", "title": "Response Find Place Endpoint External Datasources Find Place Get"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/external-datasources/find-address": {"get": {"tags": ["external-datasources"], "summary": "Find Address Endpoint", "operationId": "find_address_endpoint_external_datasources_find_address_get", "security": [{"HTTPBearer": []}], "parameters": [{"name": "query", "in": "query", "required": true, "schema": {"type": "string", "title": "Query"}}, {"name": "limit", "in": "query", "required": false, "schema": {"type": "integer", "default": 10, "title": "Limit"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/FindAddressesResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/webhooks/clerk": {"post": {"tags": ["webhooks"], "summary": "Receive Clerk Event", "operationId": "receive_clerk_event_webhooks_clerk_post", "responses": {"204": {"description": "Successful Response"}}}}, "/healthcheck/": {"get": {"tags": ["healthcheck"], "summary": "Healthcheck", "operationId": "healthcheck_healthcheck__get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}}}}}, "components": {"schemas": {"Address": {"properties": {"id": {"type": "integer", "title": "Id"}, "streetLine1": {"type": "string", "title": "Streetline1"}, "streetLine2": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Streetline2"}, "townOrCity": {"type": "string", "title": "Townorcity"}, "postcode": {"type": "string", "title": "Postcode"}, "houseAccess": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Houseaccess"}, "parkingInstructions": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Parkinginstructions"}}, "type": "object", "required": ["id", "streetLine1", "streetLine2", "townOrCity", "postcode"], "title": "Address"}, "AddressUpdate": {"properties": {"houseAccess": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Houseaccess"}, "parkingInstructions": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Parkinginstructions"}}, "type": "object", "title": "AddressUpdate"}, "ApplianceCreate": {"properties": {"type": {"type": "string", "title": "Type"}, "brand": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Brand"}, "model": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Model"}, "serialNumber": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Serialnumber"}, "warranty": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Warranty"}, "dateOfPurchase": {"anyOf": [{"type": "string", "format": "date"}, {"type": "null"}], "title": "Dateofpurchase"}, "otherDetails": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Otherdetails"}, "invoiceReceiptDocumentId": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Invoicereceiptdocumentid"}, "propertyId": {"type": "integer", "title": "Propertyid"}}, "type": "object", "required": ["type", "propertyId"], "title": "ApplianceCreate"}, "ApplianceInfo": {"properties": {"id": {"type": "integer", "title": "Id"}, "type": {"type": "string", "title": "Type"}, "brand": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Brand"}, "model": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Model"}, "serialNumber": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Serialnumber"}, "warranty": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Warranty"}, "dateOfPurchase": {"anyOf": [{"type": "string", "format": "date"}, {"type": "null"}], "title": "Dateofpurchase"}, "otherDetails": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Otherdetails"}, "propertyId": {"type": "integer", "title": "Propertyid"}, "invoiceReceiptDocumentId": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Invoicereceiptdocumentid"}, "documents": {"anyOf": [{"items": {"$ref": "#/components/schemas/DocumentInfo"}, "type": "array"}, {"type": "null"}], "title": "Documents"}}, "type": "object", "required": ["id", "type", "brand", "model", "serialNumber", "warranty", "dateOfPurchase", "otherDetails", "propertyId", "invoiceReceiptDocumentId", "documents"], "title": "ApplianceInfo"}, "ApplianceUpdate": {"properties": {"type": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Type"}, "brand": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Brand"}, "model": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Model"}, "serialNumber": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Serialnumber"}, "warranty": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Warranty"}, "dateOfPurchase": {"anyOf": [{"type": "string", "format": "date"}, {"type": "null"}], "title": "Dateofpurchase"}, "otherDetails": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Otherdetails"}, "invoiceReceiptDocumentId": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Invoicereceiptdocumentid"}}, "type": "object", "title": "ApplianceUpdate"}, "Attachment": {"properties": {"documentId": {"type": "integer", "title": "Documentid"}}, "type": "object", "required": ["documentId"], "title": "Attachment"}, "AttachmentInfo": {"properties": {"documentId": {"type": "integer", "title": "Documentid"}, "originalFileName": {"type": "string", "title": "<PERSON><PERSON><PERSON><PERSON>"}, "sizeInKiloBytes": {"type": "integer", "title": "Sizeinkilobytes"}, "createdAt": {"type": "string", "format": "date-time", "title": "Createdat"}, "fileExtension": {"type": "string", "title": "Fileextension"}}, "type": "object", "required": ["documentId", "originalFileName", "sizeInKiloBytes", "createdAt", "fileExtension"], "title": "AttachmentInfo"}, "AverageHousePriceChangeOverYearsForTownResponse": {"properties": {"town": {"type": "string", "title": "Town"}, "changeInPercent": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Changeinpercent"}, "years": {"type": "integer", "title": "Years"}}, "type": "object", "required": ["town", "changeInPercent", "years"], "title": "AverageHousePriceChangeOverYearsForTownResponse"}, "AverageHousePriceForFlatForTownResponse": {"properties": {"town": {"type": "string", "title": "Town"}, "avg": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Avg"}}, "type": "object", "required": ["town", "avg"], "title": "AverageHousePriceForFlatForTownResponse"}, "AverageHousePriceForPostcode": {"properties": {"postcode": {"type": "string", "title": "Postcode"}, "avg": {"type": "integer", "title": "Avg"}, "numberOfTransactions": {"type": "integer", "title": "Numberoftransactions"}}, "type": "object", "required": ["postcode", "avg", "numberOfTransactions"], "title": "AverageHousePriceForPostcode"}, "AverageHousePriceForPostcodeResponse": {"properties": {"postcode": {"type": "string", "title": "Postcode"}, "avg": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Avg"}}, "type": "object", "required": ["postcode", "avg"], "title": "AverageHousePriceForPostcodeResponse"}, "AverageHousePriceForStreetResponse": {"properties": {"street": {"type": "string", "title": "Street"}, "town": {"type": "string", "title": "Town"}, "postcode": {"type": "string", "title": "Postcode"}, "avg": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Avg"}}, "type": "object", "required": ["street", "town", "postcode", "avg"], "title": "AverageHousePriceForStreetResponse"}, "B2BDemoRequest": {"properties": {"companyName": {"type": "string", "title": "Companyname"}, "businessEmail": {"type": "string", "format": "email", "title": "Businessemail"}, "numberOfPropertiesManaged": {"type": "string", "title": "Numberofpropertiesmanaged"}}, "type": "object", "required": ["companyName", "businessEmail", "numberOfPropertiesManaged"], "title": "B2BDemoRequest"}, "Body_upload_document_documents__post": {"properties": {"file": {"type": "string", "format": "binary", "title": "File"}}, "type": "object", "required": ["file"], "title": "Body_upload_document_documents__post"}, "ChatInfo": {"properties": {"id": {"type": "integer", "title": "Id"}, "title": {"type": "string", "title": "Title"}, "status": {"type": "string", "enum": ["active", "closed"], "title": "Status"}}, "type": "object", "required": ["id", "title", "status"], "title": "ChatInfo"}, "DocumentInfo": {"properties": {"id": {"type": "integer", "title": "Id"}, "fileName": {"type": "string", "title": "Filename"}, "sizeInKiloBytes": {"type": "integer", "title": "Sizeinkilobytes"}, "browserMimeType": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Browsermimetype"}, "createdAt": {"type": "string", "format": "date-time", "title": "Createdat"}}, "type": "object", "required": ["id", "fileName", "sizeInKiloBytes", "browserMimeType", "createdAt"], "title": "DocumentInfo"}, "FindAddressesHit": {"properties": {"id": {"type": "string", "title": "Id"}, "suggestion": {"type": "string", "title": "Suggestion"}, "udprn": {"type": "integer", "title": "Udprn"}}, "type": "object", "required": ["id", "suggestion", "udprn"], "title": "FindAddressesHit"}, "FindAddressesResponse": {"properties": {"hits": {"items": {"$ref": "#/components/schemas/FindAddressesHit"}, "type": "array", "title": "Hits"}}, "type": "object", "required": ["hits"], "title": "FindAddressesResponse"}, "HTTPValidationError": {"properties": {"detail": {"items": {"$ref": "#/components/schemas/ValidationError"}, "type": "array", "title": "Detail"}}, "type": "object", "title": "HTTPValidationError"}, "JobInfo": {"properties": {"id": {"type": "integer", "title": "Id"}, "headline": {"type": "string", "title": "Headline"}, "subTitle": {"type": "string", "title": "Subtitle"}, "details": {"type": "string", "title": "Details"}, "urgency": {"type": "string", "title": "Urgency"}, "availability": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Availability"}, "status": {"type": "string", "title": "Status"}, "timestamp": {"type": "string", "format": "date-time", "title": "Timestamp"}}, "type": "object", "required": ["id", "headline", "subTitle", "details", "urgency", "availability", "status", "timestamp"], "title": "JobInfo"}, "ManualAddressCreate": {"properties": {"streetLine1": {"type": "string", "title": "Streetline1"}, "streetLine2": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Streetline2"}, "townOrCity": {"type": "string", "title": "Townorcity"}, "postcode": {"type": "string", "title": "Postcode"}}, "type": "object", "required": ["streetLine1", "streetLine2", "townOrCity", "postcode"], "title": "ManualAddressCreate"}, "MeanHouseHoldIncome": {"properties": {"totalMeanAnnualIncome": {"type": "integer", "title": "Totalmeanannualincome"}, "totalMeanAnnualIncomeRank": {"type": "integer", "title": "Totalmeanannualincomerank"}}, "type": "object", "required": ["totalMeanAnnualIncome", "totalMeanAnnualIncomeRank"], "title": "MeanHouseHoldIncome"}, "MeanHouseHoldIncomeResponse": {"properties": {"data": {"$ref": "#/components/schemas/MeanHouseHoldIncome"}, "dataDate": {"type": "string", "title": "Datadate"}, "refreshedDate": {"type": "string", "title": "Refresheddate"}}, "type": "object", "required": ["data", "dataDate", "refreshedDate"], "title": "MeanHouseHoldIncomeResponse"}, "Message": {"properties": {"id": {"type": "integer", "title": "Id"}, "content": {"type": "string", "title": "Content"}, "type": {"type": "string", "title": "Type"}, "senderType": {"type": "string", "title": "Sendertype"}, "timestamp": {"type": "string", "format": "date-time", "title": "Timestamp"}, "additionalData": {"anyOf": [{"type": "object"}, {"type": "null"}], "title": "Additionaldata"}, "attachments": {"anyOf": [{"items": {"$ref": "#/components/schemas/AttachmentInfo"}, "type": "array"}, {"type": "null"}], "title": "Attachments"}}, "type": "object", "required": ["id", "content", "type", "senderType", "timestamp", "additionalData", "attachments"], "title": "Message"}, "MostExpensivePostcodesForTownResponse": {"properties": {"town": {"type": "string", "title": "Town"}, "postcodes": {"items": {"$ref": "#/components/schemas/AverageHousePriceForPostcode"}, "type": "array", "title": "Postcodes"}}, "type": "object", "required": ["town", "postcodes"], "title": "MostExpensivePostcodesForTownResponse"}, "OSDataAddressCreate": {"properties": {"UPRN": {"type": "string", "title": "Uprn"}, "BUILDING_NUMBER": {"type": "string", "title": "Building Number"}, "THOROUGHFARE_NAME": {"type": "string", "title": "Thoroughfare Name"}, "POST_TOWN": {"type": "string", "title": "Post Town"}, "POSTCODE": {"type": "string", "title": "Postcode"}, "X_COORDINATE": {"type": "number", "title": "X Coordinate"}, "Y_COORDINATE": {"type": "number", "title": "Y Coordinate"}, "CLASSIFICATION_CODE": {"type": "string", "title": "Classification Code"}, "COUNTRY_CODE": {"type": "string", "title": "Country Code"}}, "type": "object", "required": ["UPRN", "BUILDING_NUMBER", "THOROUGHFARE_NAME", "POST_TOWN", "POSTCODE", "X_COORDINATE", "Y_COORDINATE", "CLASSIFICATION_CODE", "COUNTRY_CODE"], "title": "OSDataAddressCreate"}, "Page_ApplianceInfo_": {"properties": {"items": {"items": {"$ref": "#/components/schemas/ApplianceInfo"}, "type": "array", "title": "Items"}, "total": {"anyOf": [{"type": "integer", "minimum": 0.0}, {"type": "null"}], "title": "Total"}, "page": {"anyOf": [{"type": "integer", "minimum": 1.0}, {"type": "null"}], "title": "Page"}, "size": {"anyOf": [{"type": "integer", "minimum": 1.0}, {"type": "null"}], "title": "Size"}, "pages": {"anyOf": [{"type": "integer", "minimum": 0.0}, {"type": "null"}], "title": "Pages"}}, "type": "object", "required": ["items", "total", "page", "size"], "title": "Page[ApplianceInfo]"}, "Page_ChatInfo_": {"properties": {"items": {"items": {"$ref": "#/components/schemas/ChatInfo"}, "type": "array", "title": "Items"}, "total": {"anyOf": [{"type": "integer", "minimum": 0.0}, {"type": "null"}], "title": "Total"}, "page": {"anyOf": [{"type": "integer", "minimum": 1.0}, {"type": "null"}], "title": "Page"}, "size": {"anyOf": [{"type": "integer", "minimum": 1.0}, {"type": "null"}], "title": "Size"}, "pages": {"anyOf": [{"type": "integer", "minimum": 0.0}, {"type": "null"}], "title": "Pages"}}, "type": "object", "required": ["items", "total", "page", "size"], "title": "Page[ChatInfo]"}, "Page_DocumentInfo_": {"properties": {"items": {"items": {"$ref": "#/components/schemas/DocumentInfo"}, "type": "array", "title": "Items"}, "total": {"anyOf": [{"type": "integer", "minimum": 0.0}, {"type": "null"}], "title": "Total"}, "page": {"anyOf": [{"type": "integer", "minimum": 1.0}, {"type": "null"}], "title": "Page"}, "size": {"anyOf": [{"type": "integer", "minimum": 1.0}, {"type": "null"}], "title": "Size"}, "pages": {"anyOf": [{"type": "integer", "minimum": 0.0}, {"type": "null"}], "title": "Pages"}}, "type": "object", "required": ["items", "total", "page", "size"], "title": "Page[DocumentInfo]"}, "Page_JobInfo_": {"properties": {"items": {"items": {"$ref": "#/components/schemas/JobInfo"}, "type": "array", "title": "Items"}, "total": {"anyOf": [{"type": "integer", "minimum": 0.0}, {"type": "null"}], "title": "Total"}, "page": {"anyOf": [{"type": "integer", "minimum": 1.0}, {"type": "null"}], "title": "Page"}, "size": {"anyOf": [{"type": "integer", "minimum": 1.0}, {"type": "null"}], "title": "Size"}, "pages": {"anyOf": [{"type": "integer", "minimum": 0.0}, {"type": "null"}], "title": "Pages"}}, "type": "object", "required": ["items", "total", "page", "size"], "title": "Page[JobInfo]"}, "Page_Message_": {"properties": {"items": {"items": {"$ref": "#/components/schemas/Message"}, "type": "array", "title": "Items"}, "total": {"anyOf": [{"type": "integer", "minimum": 0.0}, {"type": "null"}], "title": "Total"}, "page": {"anyOf": [{"type": "integer", "minimum": 1.0}, {"type": "null"}], "title": "Page"}, "size": {"anyOf": [{"type": "integer", "minimum": 1.0}, {"type": "null"}], "title": "Size"}, "pages": {"anyOf": [{"type": "integer", "minimum": 0.0}, {"type": "null"}], "title": "Pages"}}, "type": "object", "required": ["items", "total", "page", "size"], "title": "Page[Message]"}, "Page_Property_": {"properties": {"items": {"items": {"$ref": "#/components/schemas/Property"}, "type": "array", "title": "Items"}, "total": {"anyOf": [{"type": "integer", "minimum": 0.0}, {"type": "null"}], "title": "Total"}, "page": {"anyOf": [{"type": "integer", "minimum": 1.0}, {"type": "null"}], "title": "Page"}, "size": {"anyOf": [{"type": "integer", "minimum": 1.0}, {"type": "null"}], "title": "Size"}, "pages": {"anyOf": [{"type": "integer", "minimum": 0.0}, {"type": "null"}], "title": "Pages"}}, "type": "object", "required": ["items", "total", "page", "size"], "title": "Page[Property]"}, "Property": {"properties": {"id": {"type": "integer", "title": "Id"}, "type": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Type"}, "address": {"$ref": "#/components/schemas/Address"}}, "type": "object", "required": ["id", "type", "address"], "title": "Property"}, "PropertyCreate": {"properties": {"osDataAddress": {"$ref": "#/components/schemas/OSDataAddressCreate"}, "manualAddress": {"$ref": "#/components/schemas/ManualAddressCreate"}, "idealPostcodesAddressId": {"type": "string", "title": "Idealpostcodesaddressid"}, "type": {"anyOf": [{"$ref": "#/components/schemas/PropertyType"}, {"type": "null"}]}, "tenureType": {"anyOf": [{"$ref": "#/components/schemas/PropertyTenureType"}, {"type": "null"}]}}, "type": "object", "title": "PropertyCreate"}, "PropertyTenureType": {"type": "string", "enum": ["freehold", "leasehold", "shareOfFreehold"], "title": "PropertyTenureType"}, "PropertyType": {"type": "string", "enum": ["house", "flat", "office", "retail"], "title": "PropertyType"}, "PropertyUpdate": {"properties": {"address": {"anyOf": [{"$ref": "#/components/schemas/AddressUpdate"}, {"type": "null"}]}, "type": {"anyOf": [{"$ref": "#/components/schemas/PropertyType"}, {"type": "null"}]}, "tenureType": {"anyOf": [{"$ref": "#/components/schemas/PropertyTenureType"}, {"type": "null"}]}}, "type": "object", "title": "PropertyUpdate"}, "ResponseMessage": {"properties": {"content": {"type": "string", "title": "Content"}, "type": {"type": "string", "enum": ["text", "diagnostic_report"], "title": "Type"}, "additionalData": {"$ref": "#/components/schemas/ResponseMessageMetadata"}}, "type": "object", "required": ["content", "type", "additionalData"], "title": "ResponseMessage"}, "ResponseMessageMetadata": {"properties": {"timestamp": {"type": "string", "format": "date-time", "title": "Timestamp"}, "category": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Category"}, "confidence": {"anyOf": [{"type": "number"}, {"type": "null"}], "title": "Confidence"}, "imageUrls": {"anyOf": [{}, {"type": "null"}], "title": "<PERSON><PERSON><PERSON>"}, "imageClickableUrls": {"anyOf": [{}, {"type": "null"}], "title": "Imageclickableurls"}, "suggestedActions": {"anyOf": [{}, {"type": "null"}], "title": "Suggestedactions"}, "jobSummary": {"anyOf": [{}, {"type": "null"}], "title": "Jobsummary"}}, "type": "object", "required": ["timestamp"], "title": "ResponseMessageMetadata"}, "SendMessage": {"properties": {"content": {"type": "string", "title": "Content"}, "type": {"type": "string", "const": "text", "title": "Type", "default": "text"}, "additionalData": {"$ref": "#/components/schemas/SendMessageMetadata"}}, "type": "object", "required": ["content", "additionalData"], "title": "SendMessage"}, "SendMessageMetadata": {"properties": {"device": {"type": "string", "title": "<PERSON><PERSON>"}, "location": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Location"}}, "type": "object", "required": ["device"], "title": "SendMessageMetadata"}, "SendMessageRequest": {"properties": {"chatId": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "<PERSON><PERSON><PERSON>"}, "message": {"$ref": "#/components/schemas/SendMessage"}, "attachments": {"anyOf": [{"items": {"$ref": "#/components/schemas/Attachment"}, "type": "array"}, {"type": "null"}], "title": "Attachments"}}, "type": "object", "required": ["message"], "title": "SendMessageRequest"}, "SendMessageResponse": {"properties": {"chatId": {"type": "integer", "title": "<PERSON><PERSON><PERSON>"}, "userMessageId": {"type": "integer", "title": "Usermessageid"}, "systemMessageId": {"type": "integer", "title": "Systemmessageid"}, "message": {"$ref": "#/components/schemas/ResponseMessage"}, "attachments": {"items": {"$ref": "#/components/schemas/Attachment"}, "type": "array", "title": "Attachments"}}, "type": "object", "required": ["chatId", "userMessageId", "systemMessageId", "message", "attachments"], "title": "SendMessageResponse"}, "UserDetails": {"properties": {"id": {"type": "integer", "title": "Id"}, "clerkId": {"type": "string", "title": "<PERSON><PERSON>"}, "email": {"type": "string", "title": "Email"}, "firstName": {"type": "string", "title": "Firstname"}, "lastName": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Lastname"}, "phoneNumber": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Phonenumber"}, "mainUsage": {"anyOf": [{"$ref": "#/components/schemas/UserPropertyRelationType"}, {"type": "null"}]}}, "type": "object", "required": ["id", "clerkId", "email", "firstName", "lastName", "phoneNumber", "mainUsage"], "title": "UserDetails"}, "UserPropertyRelationType": {"type": "string", "enum": ["ownerAndOccupier", "landlord", "tenant", "managingProfessional"], "title": "UserPropertyRelationType"}, "UserUpdate": {"properties": {"mainUsage": {"$ref": "#/components/schemas/UserPropertyRelationType"}}, "type": "object", "required": ["mainUsage"], "title": "UserUpdate"}, "ValidationError": {"properties": {"loc": {"items": {"anyOf": [{"type": "string"}, {"type": "integer"}]}, "type": "array", "title": "Location"}, "msg": {"type": "string", "title": "Message"}, "type": {"type": "string", "title": "Error Type"}}, "type": "object", "required": ["loc", "msg", "type"], "title": "ValidationError"}}, "securitySchemes": {"HTTPBearer": {"type": "http", "scheme": "bearer"}}}}