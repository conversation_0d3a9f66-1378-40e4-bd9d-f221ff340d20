name: Deploy to production
on:
  workflow_dispatch:
    inputs:
      version:
        description: 'Version of the image to deploy. "latest" by default.'
        required: true
        default: 'latest'
        type: string

permissions:
  id-token: write # This is required for requesting the JWT
  contents: read # This is required for actions/checkout

jobs:
  deploy-production:
    runs-on: ubuntu-latest
    steps:
      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          role-to-assume: ${{ vars.AWS_OICD_ROLE_ARN }}
          aws-region: ${{ vars.AWS_REGION }}
          mask-aws-account-id: false

      - name: Login to Amazon ECR
        id: login-ecr
        uses: aws-actions/amazon-ecr-login@v2

      - name: Set vars
        id: vars
        env:
          ECR_REGISTRY: ${{ steps.login-ecr.outputs.registry }}
        run: |
          git_commit="${GITHUB_SHA:0:7}";
          service_name="${GITHUB_REPOSITORY##*/}";
          ecr_repo_name="${ECR_REGISTRY}/${service_name}";
          
          echo "ecr_repo_name=${ecr_repo_name}" >> $GITHUB_OUTPUT;
          echo "service_name=$service_name" >> $GITHUB_OUTPUT;

      - name: Tag the selected image version with production tag
        env:
          ECR_REPO_NAME: ${{ steps.vars.outputs.ecr_repo_name }}
          SELECTED_VERSION: ${{ inputs.version }}
        run: |
          docker buildx imagetools create "${ECR_REPO_NAME}:${SELECTED_VERSION}" --tag "${ECR_REPO_NAME}:production"

      - name: Redeploy the ECS service
        run: |
          aws ecs update-service --cluster ${{ secrets.SERVICES_ECS_CLUSTER_NAME }} --service ${{ secrets.BACKEND_ECS_SERVICE_NAME }} --force-new-deployment