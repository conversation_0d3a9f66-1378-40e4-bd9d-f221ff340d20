import abc
from datetime import datetime, date
from typing import Optional, Any, Literal, List, Union

from pydantic import BaseModel, EmailStr, ConfigDict, field_validator, model_validator, constr, Field
from src.db_models.entity_link import LinkedEntityType
from src.db_models.todo import TodoType

from src.db_models.document import DocumentCategoryType, DocumentStatusType
from src.db_models.property import (
    PropertyType,
    PropertyTenureType,
    PropertySubgroupType,
    PropertyEpcRatingType,
    PropertyConservationStatusType,
    PropertyConditionType,
)
from src.db_models.relationships import UserPropertyRelationType
from src.db_models.source_link import SourceType
from src.integrations.idealpostcodes import FindAddressesHit


class UserTypeCreate(BaseModel):
    type: str


class UserType(BaseModel):
    model_config = ConfigDict(from_attributes=True)

    id: int
    type: str


class UserCreate(BaseModel):
    firstName: str
    clerkId: str
    email: EmailStr
    user_type_ids: list[int] | None = None


class UserUpdate(BaseModel):
    mainUsage: UserPropertyRelationType


class UserDetails(BaseModel):
    model_config = ConfigDict(from_attributes=True)

    id: int
    clerkId: str
    email: str
    firstName: str
    lastName: Optional[str]
    phoneNumber: Optional[str]
    mainUsage: Optional[UserPropertyRelationType]


class BookingCreate(BaseModel):
    time: datetime
    address_id: int


class Booking(BaseModel):
    model_config = ConfigDict(from_attributes=True)

    id: int
    time: datetime
    address_id: int


class OSDataAddressCreate(BaseModel):
    UPRN: str
    BUILDING_NUMBER: str
    THOROUGHFARE_NAME: str  # street name
    POST_TOWN: str
    POSTCODE: str
    X_COORDINATE: float  # EPSG:27700 OSGB36/British National Grid-needs conversion to std lat/lon(EPSG:4326 WGS 84)
    Y_COORDINATE: float  # EPSG:27700 OSGB36/British National Grid-needs conversion to std lat/lon(EPSG:4326 WGS 84)
    CLASSIFICATION_CODE: str  # property type: Semi-Detached etc.
    COUNTRY_CODE: str  # country: E for England, W for Wales, S for Scotland, N for Northern Ireland

    @field_validator("UPRN")
    @classmethod
    def check_uprn(cls, value: Any) -> Any:
        try:
            int(value)
        except ValueError:
            raise ValueError("UPRN must be an integer")
        return value


class ManualAddressCreate(BaseModel):
    streetLine1: str
    streetLine2: Optional[str] = None
    townOrCity: str
    postcode: constr(to_upper=True)
    country: str = "UK"


class PropertyCreate(BaseModel):
    osDataAddress: Optional[OSDataAddressCreate] = None
    manualAddress: Optional[ManualAddressCreate] = None
    idealPostcodesAddressId: str = None
    type: Optional[PropertyType] = None
    tenureType: Optional[PropertyTenureType] = None
    userPropertyRelationshipType: Optional[UserPropertyRelationType] = None

    @model_validator(mode="after")
    def check_if_any_address_provided(self) -> "PropertyCreate":
        if not self.osDataAddress and not self.manualAddress and not self.idealPostcodesAddressId:
            raise ValueError("Either osDataAddress or manualAddress or idealPostcodesAddressId must be provided")
        return self


class Address(BaseModel):
    model_config = ConfigDict(from_attributes=True)

    id: Optional[int] = None  # ai engine does not to provide this
    streetLine1: str
    streetLine2: Optional[str] = None
    townOrCity: str
    postcode: str
    houseAccess: Optional[str] = None
    parkingInstructions: Optional[str] = None
    country: str


class PropertyDetailsData(BaseModel):
    tenureType: Optional[PropertyTenureType] = None
    type: Optional[PropertyType] = None
    subgroupType: Optional[PropertySubgroupType] = None
    numberOfBedrooms: Optional[int] = None
    numberOfBathrooms: Optional[int] = None
    numberOfFloors: Optional[int] = None
    sizeInSqft: Optional[int] = None
    hasBalconyTerrace: Optional[bool] = None
    hasGarden: Optional[bool] = None
    hasSwimmingPool: Optional[bool] = None
    onFloorLevel: Optional[int] = None
    userPropertyRelationshipType: Optional[UserPropertyRelationType] = None
    balconyTerraceDetails: Optional[str] = None
    gardenDetails: Optional[str] = None
    swimmingPoolDetails: Optional[str] = None
    lastSoldPriceInGbp: Optional[int] = None
    condition: Optional[PropertyConditionType] = None
    yearsOfOwnership: Optional[int] = None
    architecturalType: Optional[str] = None
    valuationInGbp: Optional[int] = None
    conservationStatus: Optional[PropertyConservationStatusType] = None
    typeOfLock: Optional[str] = None
    typeOfConstruction: Optional[str] = None
    proportionOfFlatRoof: Optional[int] = None
    epcRating: Optional[PropertyEpcRatingType] = None


class AddressUpdate(Address):
    streetLine1: Optional[str] = None
    streetLine2: Optional[str] = None
    townOrCity: Optional[str] = None
    postcode: Optional[str] = None
    houseAccess: Optional[str] = None
    parkingInstructions: Optional[str] = None
    country: Optional[str] = None


class PropertyUpdate(PropertyDetailsData):
    address: Optional[AddressUpdate] = None


class PropertyInfo(PropertyUpdate):
    model_config = ConfigDict(from_attributes=True)

    id: int
    address: Optional[Address] = None


class MeanHouseHoldIncomeResponse(BaseModel):
    class MeanHouseHoldIncome(BaseModel):
        totalMeanAnnualIncome: int
        totalMeanAnnualIncomeRank: int

    data: MeanHouseHoldIncome
    dataDate: str
    refreshedDate: str


class AverageHousePriceForPostcodeResponse(BaseModel):
    postcode: str
    avg: Optional[int]


class AverageHousePriceForStreetResponse(BaseModel):
    street: str
    town: str
    postcode: str
    avg: Optional[int]


class AverageHousePriceForFlatForTownResponse(BaseModel):
    town: str
    avg: Optional[int]


class AverageHousePriceChangeOverYearsForTownResponse(BaseModel):
    town: str
    changeInPercent: Optional[int]
    years: int


class MostExpensivePostcodesForTownResponse(BaseModel):
    class AverageHousePriceForPostcode(BaseModel):
        postcode: str
        avg: int
        numberOfTransactions: int

    town: str
    postcodes: list[AverageHousePriceForPostcode]


class FindAddressesResponse(BaseModel):
    hits: list[FindAddressesHit]


class AbstractMessage(BaseModel, abc.ABC):
    content: str
    type: Any
    additionalData: Any


class SendMessageMetadata(BaseModel):
    device: str
    location: Optional[str] = None


class SendMessage(AbstractMessage):
    type: Literal["text"] = "text"
    additionalData: SendMessageMetadata


class ResponseMessageMetadata(BaseModel):
    timestamp: datetime
    category: Optional[str] = None
    confidence: Optional[float] = None
    imageUrls: Optional[Any] = None
    imageClickableUrls: Optional[Any] = None
    suggestedActions: Optional[Any] = None
    jobSummary: Optional[Any] = None


class ResponseMessage(AbstractMessage):
    type: Literal["text", "diagnostic_report"]
    additionalData: ResponseMessageMetadata


class Attachment(BaseModel):
    documentId: int


class SendMessageRequest(BaseModel):
    chatId: Optional[int] = None
    message: SendMessage
    attachments: Optional[list[Attachment]] = None


class SendMessageResponse(BaseModel):
    chatId: int
    userMessageId: int
    systemMessageId: int
    message: ResponseMessage
    attachments: list[Attachment]


class StreamSendMessageResponse(BaseModel):
    model_config = ConfigDict(from_attributes=True)

    chatId: int
    userMessageId: int
    systemMessageId: int


class ChatInfo(BaseModel):
    model_config = ConfigDict(from_attributes=True)

    id: int
    title: str
    status: Literal["active", "closed"]


class DocumentInfo(BaseModel):
    model_config = ConfigDict(from_attributes=True)

    id: int
    originalFileName: str = Field(serialization_alias="fileName")
    sizeInKiloBytes: int
    browserMimeType: Optional[str]
    created_at: datetime = Field(serialization_alias="createdAt")
    uploadContext: Optional[str] = None
    status: DocumentStatusType
    category: Optional[DocumentCategoryType] = None
    label: Optional[str] = None
    hasStatusBeenDisplayed: bool


class Message(BaseModel):
    model_config = ConfigDict(from_attributes=True)

    id: int
    content: str
    type: str
    senderType: str
    timestamp: datetime
    additionalData: Optional[dict[str, Any]]
    attachments: Optional[list[DocumentInfo]] = Field(alias="documents")


class DocumentAiUpdate(BaseModel):
    status: DocumentStatusType
    aiGeneratedFileName: Optional[str] = None
    category: Optional[DocumentCategoryType] = None
    label: Optional[str] = None
    errorMessage: Optional[str] = None


class JobInfo(BaseModel):
    model_config = ConfigDict(from_attributes=True)

    id: int
    headline: str
    subTitle: str
    details: str
    urgency: str
    availability: Optional[str]
    status: str
    timestamp: datetime


class B2BDemoRequest(BaseModel):
    companyName: str
    businessEmail: EmailStr
    numberOfPropertiesManaged: str


class ApplianceInfo(BaseModel):
    model_config = ConfigDict(from_attributes=True)

    id: int
    type: str
    brand: Optional[str]
    model: Optional[str]
    serialNumber: Optional[str]
    warranty: Optional[str]
    dateOfPurchase: Optional[date]
    otherDetails: Optional[str]
    propertyId: int
    invoiceReceiptDocumentId: Optional[int]
    documents: Optional[List[DocumentInfo]]


class ApplianceUpdate(BaseModel):
    type: Optional[str] = None
    brand: Optional[str] = None
    model: Optional[str] = None
    serialNumber: Optional[str] = None
    warranty: Optional[str] = None
    dateOfPurchase: Optional[date] = None
    otherDetails: Optional[str] = None
    invoiceReceiptDocumentId: Optional[int] = None


class ApplianceCreate(ApplianceUpdate):
    type: str
    propertyId: int


class ApplianceCreateWithOptionalProperty(ApplianceCreate):
    propertyId: Optional[int] = None
    userId: Optional[int] = None

    @model_validator(mode="after")
    def check_if_any_id_provided(self) -> "ApplianceCreateWithOptionalProperty":
        if not self.propertyId and not self.userId:
            raise ValueError("Either propertyId or userId must be provided")
        if self.propertyId and self.userId:
            raise ValueError("Only one of propertyId or userId should be provided, not both")
        return self


class ApplianceAiCreate(BaseModel):
    appliance: ApplianceCreateWithOptionalProperty
    srcType: SourceType
    srcId: int
    userId: int


class ApplianceAiUpdate(BaseModel):
    appliance: ApplianceUpdate
    srcType: SourceType
    srcId: int


class ChatSource(BaseModel):
    srcType: Literal["chat"] = "chat"
    id: int
    title: str


class DocumentSource(BaseModel):
    srcType: Literal["document"] = "document"
    id: int


class UserSource(BaseModel):
    srcType: Literal["userInput"] = "userInput"
    id: int


class FieldSource(BaseModel):
    destinationField: str
    source: Union[ChatSource, DocumentSource, UserSource] = Field(discriminator="srcType")


class LinkedEntity(BaseModel):
    entityType: LinkedEntityType
    id: int


class LinkedEntityInfo(BaseModel):
    entityType: LinkedEntityType
    id: int


class UserLinkedEntityInfo(LinkedEntityInfo):
    model_config = ConfigDict(from_attributes=True)

    entityType: Literal[LinkedEntityType.users] = LinkedEntityType.users
    firstName: str
    lastName: Optional[str]


class JobLinkedEntityInfo(LinkedEntityInfo):
    model_config = ConfigDict(from_attributes=True)

    entityType: Literal[LinkedEntityType.jobs] = LinkedEntityType.jobs
    headline: str


class ApplianceLinkedEntityInfo(LinkedEntityInfo):
    model_config = ConfigDict(from_attributes=True)

    entityType: Literal[LinkedEntityType.appliances] = LinkedEntityType.appliances
    type: str
    brand: Optional[str]
    model: Optional[str]


class ChatLinkedEntityInfo(LinkedEntityInfo):
    model_config = ConfigDict(from_attributes=True)

    entityType: Literal[LinkedEntityType.chats] = LinkedEntityType.chats
    title: str


class DocumentLinkedEntityInfo(LinkedEntityInfo):
    model_config = ConfigDict(from_attributes=True)

    entityType: Literal[LinkedEntityType.documents] = LinkedEntityType.documents
    originalFileName: str


class InsuranceLinkedEntityInfo(LinkedEntityInfo):
    model_config = ConfigDict(from_attributes=True)

    entityType: Literal[LinkedEntityType.insurances] = LinkedEntityType.insurances
    policyProvider: str
    policyNumber: str


class LegalLinkedEntityInfo(LinkedEntityInfo):
    model_config = ConfigDict(from_attributes=True)

    entityType: Literal[LinkedEntityType.legals] = LinkedEntityType.legals
    type: str
    aiShortSummary: str


class ProjectLinkedEntityInfo(LinkedEntityInfo):
    model_config = ConfigDict(from_attributes=True)

    entityType: Literal[LinkedEntityType.projects] = LinkedEntityType.projects
    headline: str


class TodoLinkedEntityInfo(LinkedEntityInfo):
    model_config = ConfigDict(from_attributes=True)

    entityType: Literal[LinkedEntityType.todos] = LinkedEntityType.todos
    name: str


class TodoUpdate(BaseModel):
    name: Optional[str] = None
    description: Optional[str] = None
    dueDate: Optional[datetime] = None
    doneDate: Optional[datetime] = None


class TodoShortInfo(TodoUpdate):
    id: int
    type: TodoType
    deletedDate: Optional[datetime] = None


class TodoFullInfo(TodoShortInfo):
    sources: list[FieldSource]


class TodoCreate(TodoUpdate):
    pass


class TodoAiCreate(BaseModel):
    todo: TodoCreate
    srcType: SourceType
    srcId: int


class TodoAiUpdate(BaseModel):
    todo: TodoUpdate
    srcType: SourceType
    srcId: int


class PropertyAiUpdate(BaseModel):
    propertyDetails: PropertyDetailsData
    srcType: SourceType = SourceType.document
    srcId: int
