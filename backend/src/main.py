import logging
from contextlib import asynccontextmanager
from os import environ

import sentry_sdk
import uvicorn
from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from fastapi_pagination import add_pagination

from src.database import sessionmanager, run_migrations
from src.dependencies import crystal_roof_api, land_registry_api, os_data_places_api, idealpostcodes_api, ai_engine_api
from src.routers import (
    external_datasources,
    healthcheck,
    user,
    properties,
    webhooks,
    messages,
    chats,
    documents,
    jobs,
    appliances,
    ai_engine,
    admin,
    todos,
    entity_links,
)

SENTRY_ENVIRONMENT = environ.get("SENTRY_ENVIRONMENT")
SENTRY_DSN = environ.get("SENTRY_DSN")
CORS_ALLOWED_ORIGINS = environ.get("CORS_ALLOWED_ORIGINS", "").split(",")
CORS_ALLOWED_ORIGIN_REGEX = environ.get("CORS_ALLOWED_ORIGIN_REGEX", None)

logger = logging.getLogger("uvicorn")

if SENTRY_ENVIRONMENT and SENTRY_DSN:
    sentry_sdk.init(
        dsn=SENTRY_DSN,
        # Set traces_sample_rate to 1.0 to capture 100%
        # of transactions for tracing.
        traces_sample_rate=1.0,
        # Set profiles_sample_rate to 1.0 to profile 100%
        # of sampled transactions.
        # We recommend adjusting this value in production.
        profiles_sample_rate=1.0,
        environment=SENTRY_ENVIRONMENT,
    )
else:
    logger.warning("Sentry environment variables not set")


class HealthCheckLogsFilter(logging.Filter):
    def filter(self, record: logging.LogRecord) -> bool:
        return record.getMessage().find("/healthcheck") == -1


@asynccontextmanager
async def lifespan(fastapi_app: FastAPI):
    logger = logging.getLogger("uvicorn")
    console_formatter = uvicorn.logging.DefaultFormatter(
        "{asctime} {levelname} {filename}:{lineno} {message}", style="{"
    )
    access_logger = logging.getLogger("uvicorn.access")
    access_logger.addFilter(HealthCheckLogsFilter())
    if logger.handlers:
        logger.handlers[0].setFormatter(console_formatter)
    logger.info("Running alembic upgrade head")
    await run_migrations()
    logger.info("Alembic upgrade head done")
    yield
    if sessionmanager.is_engine_initialized:
        # Close the DB connection
        await sessionmanager.close()
    await crystal_roof_api.close()
    await land_registry_api.close()
    await os_data_places_api.close()
    await idealpostcodes_api.close()
    await ai_engine_api.close()


app = FastAPI(lifespan=lifespan)

if CORS_ALLOWED_ORIGIN_REGEX:
    app.add_middleware(
        CORSMiddleware,
        allow_origin_regex=CORS_ALLOWED_ORIGIN_REGEX,
        allow_credentials=True,
        allow_methods=["*"],
        allow_headers=["*"],
    )
elif CORS_ALLOWED_ORIGINS:
    app.add_middleware(
        CORSMiddleware,
        allow_origins=CORS_ALLOWED_ORIGINS,
        allow_credentials=True,
        allow_methods=["*"],
        allow_headers=["*"],
    )
else:
    raise ValueError("CORS_ALLOWED_ORIGINS or CORS_ALLOWED_ORIGIN_REGEX must be set")
app.include_router(messages.router)
app.include_router(chats.router)
app.include_router(documents.router)
app.include_router(properties.router)
app.include_router(user.router)
app.include_router(jobs.router)
app.include_router(appliances.router)
app.include_router(external_datasources.router)
app.include_router(webhooks.router)
app.include_router(ai_engine.router)
app.include_router(healthcheck.router)
app.include_router(admin.router)
app.include_router(todos.router)
app.include_router(entity_links.router)

add_pagination(app)

if __name__ == "__main__":
    uvicorn.run(app, host="0.0.0.0", port=8000)
