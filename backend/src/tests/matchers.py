from pydantic import BaseModel
from sqlalchemy import Sequence, RowMapping

from src.database import Base


def __single_model_dump(model: BaseModel | Base, casted_to: type[BaseModel] | None):
    if casted_to is not None:
        model = casted_to.model_validate(model)
    if isinstance(model, BaseModel):
        return model.model_dump()
    elif isinstance(model, Base):
        return model.to_dict()
    else:
        raise ValueError(f"Model must be BaseModel or Base. Was: {type(model)}")


def model_dump(value: list[BaseModel] | BaseModel | Base | Sequence, casted_to: type[BaseModel | None] = None):
    if isinstance(value, Sequence):
        value = list(value)
    if isinstance(value, list):
        return [__single_model_dump(m, casted_to) for m in value]
    return __single_model_dump(value, casted_to)
