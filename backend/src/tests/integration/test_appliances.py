import datetime
from datetime import date

import pytest
from sqlalchemy.ext.asyncio import AsyncSession

from src.db_models.document import Document, DocumentStatusType
from src.db_models.source_link import SourceType
from src.db_models.user import User
from src.schemas import (
    ApplianceCreate,
    PropertyCreate,
    ApplianceUpdate,
    ApplianceAiCreate,
    ApplianceAiUpdate,
    ApplianceCreateWithOptionalProperty,
)
from src.services.appliances import ApplianceService, PropertyDoesNotExist, ApplianceDoesNotExist
from src.services.properties import PropertyService
from src.services.source_links import SourceLinksService
from src.tests.matchers import model_dump


@pytest.fixture
def source_link_service(async_db_session: AsyncSession):
    return SourceLinksService(async_db_session)


@pytest.fixture
async def user1(async_db_session: AsyncSession):
    """Create a test user fixture."""
    user = User(
        id=1,
        email="<EMAIL>",
        clerkId="clerk_user_id",
        firstName="Test",
        lastName="User",
    )
    async_db_session.add(user)
    await async_db_session.flush()
    return user


@pytest.fixture
async def user2(async_db_session: AsyncSession):
    """Create another test user fixture for multi-user tests."""
    user = User(
        id=2,
        email="<EMAIL>",
        clerkId="clerk_user_id2",
        firstName="Test2",
        lastName="User2",
    )
    async_db_session.add(user)
    await async_db_session.flush()
    return user


@pytest.mark.asyncio
async def test_create_appliance_for_user(
    async_db_session: AsyncSession,
    property_service: PropertyService,
    source_link_service: SourceLinksService,
    user1: User,
):
    property_create = PropertyCreate(idealPostcodesAddressId="ID12345")
    property = await property_service.create_property_for_user(user1, property_create)
    await async_db_session.flush()

    appliance_service = ApplianceService(async_db_session, property_service, source_link_service)
    appliance_data = ApplianceCreate(
        propertyId=property.id,
        type="Washing Machine",
        brand="Bosch",
        model="Series 6",
        serialNumber="WM123456",
        dateOfPurchase=date(2025, 1, 15),
    )

    appliance = await appliance_service.create_appliance_by_user(appliance_data, user1)

    assert appliance is not None
    assert appliance.type == "Washing Machine"
    assert appliance.brand == "Bosch"
    assert appliance.model == "Series 6"
    assert appliance.serialNumber == "WM123456"
    assert appliance.propertyId == property.id
    assert len(appliance.documents) == 0

    source_links = await source_link_service.get_source_links(appliance)
    assert model_dump(source_links) == [
        {"destinationField": "brand", "source": {"id": 1, "srcType": "userInput"}},
        {"destinationField": "dateOfPurchase", "source": {"id": 1, "srcType": "userInput"}},
        {"destinationField": "model", "source": {"id": 1, "srcType": "userInput"}},
        {"destinationField": "propertyId", "source": {"id": 1, "srcType": "userInput"}},
        {"destinationField": "serialNumber", "source": {"id": 1, "srcType": "userInput"}},
        {"destinationField": "type", "source": {"id": 1, "srcType": "userInput"}},
    ]


@pytest.mark.asyncio
async def test_create_appliance_for_nonexistent_property(
    async_db_session: AsyncSession,
    property_service: PropertyService,
    source_link_service: SourceLinksService,
    user1: User,
):
    appliance_service = ApplianceService(async_db_session, property_service, source_link_service)
    appliance_data = ApplianceCreate(
        propertyId=999,  # Non-existent property ID
        type="Dishwasher",
        brand="Miele",
        model="G 7000",
        serialNumber="DW123456",
        dateOfPurchase=date(2025, 1, 15),
    )

    with pytest.raises(PropertyDoesNotExist):
        await appliance_service.create_appliance_by_user(appliance_data, user1)


@pytest.mark.asyncio
async def test_update_appliance_by_user(
    async_db_session: AsyncSession,
    property_service: PropertyService,
    source_link_service: SourceLinksService,
    user1: User,
):
    property_create = PropertyCreate(idealPostcodesAddressId="ID12345")
    property = await property_service.create_property_for_user(user1, property_create)
    await async_db_session.flush()

    appliance_service = ApplianceService(async_db_session, property_service, source_link_service)
    appliance_data = ApplianceCreate(
        propertyId=property.id,
        type="Refrigerator",
        brand="Samsung",
        model="RF23M8070",
    )

    appliance = await appliance_service.create_appliance_by_user(appliance_data, user1)
    await async_db_session.commit()

    update_data = ApplianceUpdate(
        model="RF23M8070 Updated",
        serialNumber="RF123456789",
        dateOfPurchase=date(2025, 1, 15),
    )

    updated_appliance = await appliance_service.update_appliance_by_user(appliance.id, update_data, user1)
    source_links = await source_link_service.get_source_links(appliance)

    assert updated_appliance is not None
    assert updated_appliance.model == "RF23M8070 Updated"
    assert updated_appliance.serialNumber == "RF123456789"
    assert updated_appliance.dateOfPurchase == datetime.datetime(2025, 1, 15)

    await async_db_session.flush()
    updated_source_links = await source_link_service.get_source_links(appliance)

    assert source_links == updated_source_links


@pytest.mark.asyncio
async def test_update_appliance_by_another_user(
    async_db_session: AsyncSession,
    property_service: PropertyService,
    source_link_service: SourceLinksService,
    user1: User,
    user2: User,
):
    property_create = PropertyCreate(idealPostcodesAddressId="ID12345")
    property = await property_service.create_property_for_user(user1, property_create)
    await async_db_session.flush()

    appliance_service = ApplianceService(async_db_session, property_service, source_link_service)
    appliance_data = ApplianceCreate(
        propertyId=property.id,
        type="Air Conditioner",
        brand="LG",
        model="Dual Inverter",
    )

    appliance = await appliance_service.create_appliance_by_user(appliance_data, user1)
    await async_db_session.flush()

    update_data = ApplianceUpdate(
        model="New Model",
    )

    with pytest.raises(ApplianceDoesNotExist):
        await appliance_service.update_appliance_by_user(appliance.id, update_data, user2)


@pytest.mark.asyncio
async def test_that_ai_cannot_override_appliances_set_by_user(
    async_db_session: AsyncSession,
    property_service: PropertyService,
    source_link_service: SourceLinksService,
    user1: User,
):
    """Test that AI cannot override appliance fields that were set by user input."""
    property_create = PropertyCreate(idealPostcodesAddressId="ID12345")
    property = await property_service.create_property_for_user(user1, property_create)
    await async_db_session.flush()

    appliance_service = ApplianceService(async_db_session, property_service, source_link_service)

    # Create appliance with user input
    appliance_data = ApplianceCreate(
        propertyId=property.id,
        type="Washing Machine",
        brand="Bosch",
        model="Series 6",
        serialNumber="WM123456",
        dateOfPurchase=date(2024, 5, 20),
    )

    appliance = await appliance_service.create_appliance_by_user(appliance_data, user1)
    await async_db_session.commit()

    # Create a document for AI update source
    document = Document(
        id=1,
        userId=user1.id,
        source="user",
        originalFileName="manual.pdf",
        fileExtension="pdf",
        sizeInKiloBytes=2,
        browserMimeType="application/pdf",
        s3Key=f"user={user1.id}/manual.pdf",
        s3Bucket="test-bucket",
        status=DocumentStatusType.processingCompleted,
    )
    async_db_session.add(document)
    await async_db_session.flush()

    # Try to update appliance fields via AI - should not override user-set values
    ai_update_data = ApplianceAiUpdate(
        appliance=ApplianceUpdate(
            type="Dryer",  # AI trying to change type set by user
            brand="LG",  # AI trying to change brand set by user
            model="Series 8",  # AI trying to change model set by user
            serialNumber="WM789012",  # AI trying to change serial number set by user
            dateOfPurchase=date(2025, 1, 15),  # AI trying to change date set by user
        ),
        srcType=SourceType.document,
        srcId=document.id,
    )

    updated_appliance = await appliance_service.update_appliance_by_ai(appliance.id, ai_update_data)
    await async_db_session.commit()

    # Verify that user-set values were not overridden by AI
    assert updated_appliance.type == "Washing Machine"  # Should remain user value
    assert updated_appliance.brand == "Bosch"  # Should remain user value
    assert updated_appliance.model == "Series 6"  # Should remain user value
    assert updated_appliance.serialNumber == "WM123456"  # Should remain user value
    assert updated_appliance.dateOfPurchase == datetime.datetime(2024, 5, 20)  # Should remain user value

    # Verify source links still point to user input for protected fields
    source_links = await source_link_service.get_source_links(appliance)
    user_input_links = [sl for sl in source_links if sl.source.srcType == "userInput"]
    document_links = [sl for sl in source_links if sl.source.srcType == "document"]

    # All fields should still have userInput source links since AI couldn't override them
    user_input_fields = {sl.destinationField for sl in user_input_links}
    assert "type" in user_input_fields
    assert "brand" in user_input_fields
    assert "model" in user_input_fields
    assert "serialNumber" in user_input_fields
    assert "dateOfPurchase" in user_input_fields

    # No document source links should exist since AI updates were rejected
    assert len(document_links) == 0


@pytest.mark.asyncio
async def test_does_appliance_belong_to_user(
    async_db_session: AsyncSession,
    property_service: PropertyService,
    source_link_service: SourceLinksService,
    user1: User,
    user2: User,
):
    property_create = PropertyCreate(idealPostcodesAddressId="ID12345")
    property = await property_service.create_property_for_user(user1, property_create)
    await async_db_session.flush()

    appliance_service = ApplianceService(async_db_session, property_service, source_link_service)
    appliance_data = ApplianceCreate(
        propertyId=property.id,
        type="Microwave",
        brand="Panasonic",
        model="NN-SN966S",
    )

    appliance = await appliance_service.create_appliance_by_user(appliance_data, user1)
    await async_db_session.flush()

    user1_appliance = await appliance_service.get_appliance_by_user(appliance.id, user1)
    assert user1_appliance is not None

    user2_appliance = await appliance_service.get_appliance_by_user(appliance.id, user2)
    assert user2_appliance is None


@pytest.mark.asyncio
async def test_create_appliance_by_ai(
    async_db_session: AsyncSession,
    property_service: PropertyService,
    source_link_service: SourceLinksService,
    user1: User,
):
    property_create = PropertyCreate(idealPostcodesAddressId="ID12345")
    property = await property_service.create_property_for_user(user1, property_create)
    await async_db_session.flush()

    appliance_service = ApplianceService(async_db_session, property_service, source_link_service)
    appliance_data = ApplianceAiCreate(
        appliance=ApplianceCreateWithOptionalProperty(
            propertyId=property.id,
            type="Smart Refrigerator",
            brand="LG",
            model="InstaView",
        ),
        srcType=SourceType.userInput,
        srcId=user1.id,
        userId=user1.id,
    )

    appliance = await appliance_service.create_appliance_by_ai(appliance_data)

    assert appliance is not None
    assert appliance.type == "Smart Refrigerator"
    assert appliance.brand == "LG"
    assert appliance.model == "InstaView"
    assert appliance.propertyId == property.id
    assert len(appliance.documents) == 0

    source_links = await source_link_service.get_source_links(appliance)
    assert model_dump(source_links) == [
        {"destinationField": "brand", "source": {"id": 1, "srcType": "userInput"}},
        {"destinationField": "model", "source": {"id": 1, "srcType": "userInput"}},
        {"destinationField": "propertyId", "source": {"id": 1, "srcType": "userInput"}},
        {"destinationField": "type", "source": {"id": 1, "srcType": "userInput"}},
    ]


@pytest.mark.asyncio
async def test_create_appliance_by_ai_for_user_without_property(
    async_db_session: AsyncSession,
    property_service: PropertyService,
    source_link_service: SourceLinksService,
    user1: User,
):
    appliance_service = ApplianceService(async_db_session, property_service, source_link_service)
    appliance_data = ApplianceAiCreate(
        appliance=ApplianceCreateWithOptionalProperty(
            type="Smart Kettle",
            brand="Breville",
            model="BKE820XL",
            userId=user1.id,
        ),
        srcType=SourceType.userInput,
        srcId=user1.id,
        userId=user1.id,
    )

    appliance = await appliance_service.create_appliance_by_ai(appliance_data)

    assert appliance is not None
    assert appliance.type == "Smart Kettle"
    assert appliance.brand == "Breville"
    assert appliance.model == "BKE820XL"
    assert appliance.propertyId is not None  # Should create a property for the user
    assert await property_service.does_property_belong_to_user(appliance.propertyId, user1)


async def test_update_appliance_by_ai(
    async_db_session: AsyncSession,
    property_service: PropertyService,
    source_link_service: SourceLinksService,
    user1: User,
):
    property_create = PropertyCreate(idealPostcodesAddressId="ID12345")
    property = await property_service.create_property_for_user(user1, property_create)
    await async_db_session.flush()

    appliance_service = ApplianceService(async_db_session, property_service, source_link_service)
    appliance_data = ApplianceCreate(
        propertyId=property.id,
        type="Oven",
        brand="GE",
        model="Profile",
        dateOfPurchase=date(2024, 5, 20),
        serialNumber="GE123456789old",
    )

    appliance = await appliance_service.create_appliance_by_user(appliance_data, user1)
    await async_db_session.commit()

    document = Document(
        id=1,
        userId=user1.id,
        source="user",
        originalFileName="test.pdf",
        fileExtension="pdf",
        sizeInKiloBytes=2,
        browserMimeType="application/pdf",
        s3Key=f"user={user1.id}/test.pdf",
        s3Bucket="test-bucket",
        status=DocumentStatusType.processingCompleted,
    )
    async_db_session.add(document)
    await async_db_session.flush()

    update_data = ApplianceAiUpdate(
        appliance=ApplianceUpdate(
            model="Profile Updated", serialNumber="GE123456789", otherDetails="some other details"
        ),
        srcType=SourceType.document,
        srcId=document.id,
    )

    updated_appliance = await appliance_service.update_appliance_by_ai(appliance.id, update_data)

    assert updated_appliance is not None
    assert updated_appliance.model == "Profile"  # note that AI hasn't updated the field set by the user
    assert updated_appliance.serialNumber == "GE123456789old"  # note that AI hasn't updated the field set by the user
    assert updated_appliance.otherDetails == "some other details"

    source_links = await source_link_service.get_source_links(appliance)
    assert model_dump(source_links) == [
        {"destinationField": "brand", "source": {"srcType": "userInput", "id": 1}},
        {"destinationField": "dateOfPurchase", "source": {"srcType": "userInput", "id": 1}},
        {"destinationField": "model", "source": {"srcType": "userInput", "id": 1}},
        {"destinationField": "otherDetails", "source": {"srcType": "document", "id": 1}},
        {"destinationField": "propertyId", "source": {"srcType": "userInput", "id": 1}},
        {"destinationField": "serialNumber", "source": {"srcType": "userInput", "id": 1}},
        {"destinationField": "type", "source": {"srcType": "userInput", "id": 1}},
    ]

    update_data.srcId = 2  # Change srcId to a non-existent user ID
    with pytest.raises(ValueError, match="Source of type SourceType.document with ID 2 does not exist."):
        await appliance_service.update_appliance_by_ai(appliance.id, update_data)

    update_data.srcType = SourceType.userInput
    update_data.appliance.model = None
    with pytest.raises(ValueError, match="Source of type SourceType.userInput with ID 2 does not exist."):
        await appliance_service.update_appliance_by_ai(appliance.id, update_data)
