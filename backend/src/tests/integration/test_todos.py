import datetime

import pytest
from sqlalchemy import select
from sqlalchemy.ext.asyncio import AsyncSession

from src.db_models.chat import Chat
from src.db_models.document import DocumentStatusType, Document
from src.db_models.source_link import SourceType
from src.db_models.todo import Todo, TodoType
from src.db_models.user import User
from src.schemas import TodoCreate, TodoUpdate, TodoAiCreate, TodoAiUpdate
from src.services.source_links import SourceLinksService
from src.services.todos import TodoService, TodoDoesNotExist
from src.tests.matchers import model_dump


@pytest.fixture
def source_link_service(async_db_session: AsyncSession):
    return SourceLinksService(async_db_session)


@pytest.fixture
async def user1(async_db_session: AsyncSession):
    user = User(
        id=1,
        email="<EMAIL>",
        clerkId="clerk_user_id_1",
        firstName="User",
        lastName="One",
    )
    async_db_session.add(user)
    return user


@pytest.fixture
async def user2(async_db_session: AsyncSession):
    user = User(
        id=2,
        email="<EMAIL>",
        clerkId="clerk_user_id_2",
        firstName="User",
        lastName="Two",
    )
    async_db_session.add(user)
    return user


@pytest.fixture
async def document(async_db_session: AsyncSession, user1: User):
    document = Document(
        id=1,
        type="test",
        source="system",
        s3Key="test-key",
        s3Bucket="test-bucket",
        fileExtension="pdf",
        sizeInKiloBytes=100,
        originalFileName="test.pdf",
        status=DocumentStatusType.processingCompleted,
        userId=user1.id,
    )
    async_db_session.add(document)
    return document


@pytest.mark.asyncio
async def test_create_todo_for_user(async_db_session: AsyncSession, source_link_service: SourceLinksService, user1):
    todo_service = TodoService(async_db_session, source_link_service)
    todo_data = TodoCreate(
        name="Some todo",
        description="What needs to be done?",
        dueDate=datetime.datetime(2025, 1, 15),
    )

    todo = await todo_service.create_todo_by_user(todo_data, user1)

    assert todo is not None
    assert todo.name == "Some todo"
    assert todo.description == "What needs to be done?"
    assert todo.dueDate == datetime.datetime(2025, 1, 15)

    query = todo_service.get_todos_for_user_query(user1)
    result = await async_db_session.execute(query)
    todos = result.scalars().all()

    assert len(todos) == 1
    assert todos[0].id == todo.id
    assert todos[0].name == todo.name
    assert todos[0].type == TodoType.userCreated


@pytest.mark.asyncio
async def test_create_todo_for_ai(async_db_session, source_link_service, user1, document):
    todo_service = TodoService(async_db_session, source_link_service)
    todo_data = TodoAiCreate(
        todo=TodoCreate(
            name="Some todo",
            description="What needs to be done?",
            dueDate=datetime.datetime(2025, 1, 15),
        ),
        srcType=SourceType.document,
        srcId=document.id,
    )

    todo = await todo_service.create_todo_by_ai(todo_data, user1)

    assert todo is not None
    assert todo.name == "Some todo"
    assert todo.description == "What needs to be done?"
    assert todo.dueDate == datetime.datetime(2025, 1, 15)

    query = todo_service.get_todos_for_user_query(user1)
    result = await async_db_session.execute(query)
    todos = result.scalars().all()

    assert len(todos) == 1
    assert todos[0].id == todo.id
    assert todos[0].name == todo.name
    assert todos[0].type == TodoType.systemCreated

    source_links = await source_link_service.get_source_links(todos[0])
    assert model_dump(source_links) == [
        {"destinationField": "description", "source": {"id": 1, "srcType": "document"}},
        {"destinationField": "dueDate", "source": {"id": 1, "srcType": "document"}},
        {"destinationField": "name", "source": {"id": 1, "srcType": "document"}},
    ]


@pytest.mark.asyncio
async def test_update_todo_by_user(async_db_session: AsyncSession, source_link_service: SourceLinksService, user1):
    todo_service = TodoService(async_db_session, source_link_service)
    todo_data = TodoCreate(
        name="Some todo",
        description="What needs to be done?",
        dueDate=datetime.datetime(2025, 1, 15),
    )

    todo = await todo_service.create_todo_by_user(todo_data, user1)
    await async_db_session.commit()

    update_data = TodoUpdate(
        name="Updated todo",
        description="Updated description",
        dueDate=datetime.datetime(2025, 2, 15),
    )

    updated_todo = await todo_service.update_todo_by_user(todo.id, update_data, user1)

    assert updated_todo is not None
    assert updated_todo.name == "Updated todo"
    assert updated_todo.description == "Updated description"
    assert updated_todo.dueDate == datetime.datetime(2025, 2, 15)

    todo = await async_db_session.scalar(select(Todo).where(Todo.id == todo.id))
    assert todo.type == TodoType.userCreated

    source_links = await source_link_service.get_source_links(todo)
    assert model_dump(source_links) == [
        {"destinationField": "description", "source": {"id": 1, "srcType": "userInput"}},
        {"destinationField": "dueDate", "source": {"id": 1, "srcType": "userInput"}},
        {"destinationField": "name", "source": {"id": 1, "srcType": "userInput"}},
    ]


@pytest.mark.asyncio
async def test_create_todo_by_ai_update_by_user(async_db_session: AsyncSession, source_link_service, user1, document):
    todo_service = TodoService(async_db_session, source_link_service)
    todo_data = TodoAiCreate(
        todo=TodoCreate(
            name="Some todo",
            description="What needs to be done?",
            dueDate=datetime.datetime(2025, 1, 15),
        ),
        srcType=SourceType.document,
        srcId=document.id,
    )

    todo = await todo_service.create_todo_by_ai(todo_data, user1)

    update_data = TodoUpdate(
        name="Updated todo",
    )

    updated_todo = await todo_service.update_todo_by_user(todo.id, update_data, user1)

    assert updated_todo is not None
    assert updated_todo.name == "Updated todo"
    assert updated_todo.description == "What needs to be done?"
    assert updated_todo.dueDate == datetime.datetime(2025, 1, 15)

    todo = await async_db_session.scalar(select(Todo).where(Todo.id == todo.id))
    assert todo.type == TodoType.systemCreated

    source_links = await source_link_service.get_source_links(todo)
    assert model_dump(source_links) == [
        {"destinationField": "description", "source": {"id": 1, "srcType": "document"}},
        {"destinationField": "dueDate", "source": {"id": 1, "srcType": "document"}},
        {"destinationField": "name", "source": {"id": 1, "srcType": "userInput"}},
    ]


@pytest.mark.asyncio
async def test_update_todo_by_different_user_raises_exception(
    async_db_session: AsyncSession, source_link_service: SourceLinksService, user1, user2
):
    todo_service = TodoService(async_db_session, source_link_service)
    todo_data = TodoCreate(
        name="Some todo",
        description="What needs to be done?",
        dueDate=datetime.datetime(2025, 1, 15),
    )

    todo = await todo_service.create_todo_by_user(todo_data, user1)
    await async_db_session.commit()

    update_data = TodoUpdate(
        name="Updated todo",
        description="Updated description",
        dueDate=datetime.datetime(2025, 2, 15),
    )

    # Try to update the todo with user2 - should raise TodoDoesNotExist
    with pytest.raises(TodoDoesNotExist, match="The ToDo does not exist."):
        await todo_service.update_todo_by_user(todo.id, update_data, user2)


@pytest.mark.asyncio
async def test_delete_todo_by_user_success(
    async_db_session: AsyncSession, source_link_service: SourceLinksService, user1
):
    todo_service = TodoService(async_db_session, source_link_service)
    todo_data = TodoCreate(
        name="Todo to delete",
        description="This will be deleted",
        dueDate=datetime.datetime(2025, 1, 15),
    )

    todo = await todo_service.create_todo_by_user(todo_data, user1)
    await async_db_session.commit()

    # Verify todo exists before deletion and has correct type
    result = await async_db_session.scalar(select(Todo).where(Todo.id == todo.id))
    assert result is not None
    assert result.type == TodoType.userCreated

    # Delete the todo
    await todo_service.delete_todo_by_user(todo.id, user1)
    await async_db_session.commit()

    # Verify todo no longer exists in database
    result = await async_db_session.scalar(select(Todo).where(Todo.id == todo.id))
    assert result is None


@pytest.mark.asyncio
async def test_delete_todo_by_different_user_raises_exception(
    async_db_session: AsyncSession, source_link_service: SourceLinksService, user1, user2
):
    todo_service = TodoService(async_db_session, source_link_service)
    todo_data = TodoCreate(
        name="Todo to delete",
        description="This should not be deleted by user2",
        dueDate=datetime.datetime(2025, 1, 15),
    )

    todo = await todo_service.create_todo_by_user(todo_data, user1)
    await async_db_session.commit()

    # Try to delete the todo with user2 - should raise TodoDoesNotExist
    with pytest.raises(TodoDoesNotExist, match="The ToDo does not exist."):
        await todo_service.delete_todo_by_user(todo.id, user2)

    # Verify todo still exists in database after failed deletion attempt
    result = await async_db_session.scalar(select(Todo).where(Todo.id == todo.id))
    assert result is not None
    assert result.type == TodoType.userCreated


@pytest.mark.asyncio
async def test_update_todo_by_ai_cannot_update_fields_set_by_user(
    async_db_session, source_link_service, user1, document
):
    todo_service = TodoService(async_db_session, source_link_service)
    todo_data = TodoCreate(
        name="Some todo",
        dueDate=datetime.datetime(2025, 1, 15),
    )
    todo = await todo_service.create_todo_by_user(todo_data, user1)

    todo = await async_db_session.scalar(select(Todo).where(Todo.id == todo.id))
    source_links = await source_link_service.get_source_links(todo)
    assert model_dump(source_links) == [
        {"destinationField": "dueDate", "source": {"id": 1, "srcType": "userInput"}},
        {"destinationField": "name", "source": {"id": 1, "srcType": "userInput"}},
    ]
    assert todo.type == TodoType.userCreated

    update_data = TodoAiUpdate(
        todo=TodoUpdate(name="Updated todo", description="Updated description"),
        srcType=SourceType.document,
        srcId=document.id,
    )

    updated_todo = await todo_service.update_todo_by_ai(todo.id, update_data)

    assert updated_todo is not None
    assert updated_todo.name == "Some todo"  # note the field set initially by the user hasn't been updated
    assert updated_todo.description == "Updated description"
    assert updated_todo.dueDate == datetime.datetime(2025, 1, 15)

    todo = await async_db_session.scalar(select(Todo).where(Todo.id == todo.id))
    source_links = await source_link_service.get_source_links(todo)
    assert model_dump(source_links) == [
        {"destinationField": "description", "source": {"id": 1, "srcType": "document"}},
        {"destinationField": "dueDate", "source": {"id": 1, "srcType": "userInput"}},
        {"destinationField": "name", "source": {"id": 1, "srcType": "userInput"}},
    ]

    # Verify that type field remains unchanged after AI update
    assert todo.type == TodoType.userCreated


@pytest.mark.asyncio
async def test_update_todo_by_ai_with_chat_source(
    async_db_session: AsyncSession, source_link_service: SourceLinksService, user1
):
    todo_service = TodoService(async_db_session, source_link_service)
    todo_data = TodoCreate(
        name="Some todo",
    )
    todo = await todo_service.create_todo_by_user(todo_data, user1)

    todo = await async_db_session.scalar(select(Todo).where(Todo.id == todo.id))
    source_links = await source_link_service.get_source_links(todo)
    assert model_dump(source_links) == [{"destinationField": "name", "source": {"id": 1, "srcType": "userInput"}}]
    assert todo.type == TodoType.userCreated

    chat = Chat(id=1, title="Planning Chat", status="active", userId=user1.id)
    async_db_session.add(chat)
    await async_db_session.commit()

    update_data = TodoAiUpdate(
        todo=TodoUpdate(
            description="Updated description via chat",
        ),
        srcType=SourceType.chat,
        srcId=chat.id,
    )

    updated_todo = await todo_service.update_todo_by_ai(todo.id, update_data)

    assert updated_todo is not None
    assert updated_todo.name == "Some todo"
    assert updated_todo.description == "Updated description via chat"

    todo = await async_db_session.scalar(select(Todo).where(Todo.id == todo.id))
    source_links = await source_link_service.get_source_links(todo)
    assert model_dump(source_links) == [
        {"destinationField": "description", "source": {"id": 1, "srcType": "chat", "title": "Planning Chat"}},
        {"destinationField": "name", "source": {"id": 1, "srcType": "userInput"}},
    ]

    # Verify that type field remains unchanged after AI update
    assert todo.type == TodoType.userCreated


@pytest.mark.asyncio
async def test_accept_todo_by_user_success(async_db_session, source_link_service, user1, document):
    todo_service = TodoService(async_db_session, source_link_service)

    todo_data = TodoAiCreate(
        todo=TodoCreate(
            name="System created todo",
            description="This was created by AI",
            dueDate=datetime.datetime(2025, 1, 15),
        ),
        srcType=SourceType.document,
        srcId=document.id,
    )

    todo = await todo_service.create_todo_by_ai(todo_data, user1)

    result = await async_db_session.scalar(select(Todo).where(Todo.id == todo.id))
    assert result.type == TodoType.systemCreated

    await todo_service.accept_todo_by_user(todo.id, user1)

    # Verify the todo type has been changed to systemAccepted
    result = await async_db_session.scalar(select(Todo).where(Todo.id == todo.id))
    assert result.type == TodoType.systemAccepted

    # Verify that source links have been updated to include user acceptance
    source_links = await source_link_service.get_source_links(result)
    type_source_links = [sl for sl in source_links if sl.destinationField == "type"]
    assert len(type_source_links) > 0
    assert any(sl.source.srcType == "userInput" and sl.source.id == user1.id for sl in type_source_links)


@pytest.mark.asyncio
async def test_accept_todo_by_user_non_system_created_raises_exception(
    async_db_session: AsyncSession, source_link_service: SourceLinksService, user1
):
    todo_service = TodoService(async_db_session, source_link_service)

    # Create a user-created todo (not system-created)
    todo_data = TodoCreate(
        name="User created todo",
        description="This was created by user",
        dueDate=datetime.datetime(2025, 1, 15),
    )

    todo = await todo_service.create_todo_by_user(todo_data, user1)
    await async_db_session.commit()

    # Verify the todo is userCreated
    result = await async_db_session.scalar(select(Todo).where(Todo.id == todo.id))
    assert result.type == TodoType.userCreated

    # Try to accept a user-created todo - should raise TodoDoesNotExist
    with pytest.raises(TodoDoesNotExist):
        await todo_service.accept_todo_by_user(todo.id, user1)


@pytest.mark.asyncio
async def test_accept_todo_by_different_user_raises_exception(
    async_db_session: AsyncSession, source_link_service: SourceLinksService, user1, user2
):
    todo_service = TodoService(async_db_session, source_link_service)

    # Create a system-created todo for user1
    todo_data = TodoCreate(
        name="System created todo",
        description="This was created by AI for user1",
        dueDate=datetime.datetime(2025, 1, 15),
    )

    todo = await todo_service.create_todo_by_user(todo_data, user1)
    await async_db_session.commit()

    # Try to accept the todo with user2 - should raise TodoDoesNotExist
    with pytest.raises(TodoDoesNotExist):
        await todo_service.accept_todo_by_user(todo.id, user2)

    # Verify todo type remains unchanged
    result = await async_db_session.scalar(select(Todo).where(Todo.id == todo.id))
    assert result.type == TodoType.userCreated


@pytest.mark.asyncio
async def test_get_todo_success(async_db_session: AsyncSession, source_link_service: SourceLinksService, user1):
    todo_service = TodoService(async_db_session, source_link_service)
    todo_data = TodoCreate(
        name="Get todo test",
        description="Testing get_todo method",
        dueDate=datetime.datetime(2025, 1, 20),
    )
    created_todo = await todo_service.create_todo_by_user(todo_data, user1)
    retrieved_todo = await todo_service.get_todo(created_todo.id, user1)

    assert retrieved_todo is not None
    assert retrieved_todo.id == created_todo.id
    assert retrieved_todo.name == "Get todo test"
    assert retrieved_todo.description == "Testing get_todo method"
    assert retrieved_todo.dueDate == datetime.datetime(2025, 1, 20)
    assert retrieved_todo.type == TodoType.userCreated

    assert hasattr(retrieved_todo, "sources")
    assert retrieved_todo.sources is not None
    assert model_dump(retrieved_todo.sources) == [
        {"destinationField": "description", "source": {"id": 1, "srcType": "userInput"}},
        {"destinationField": "dueDate", "source": {"id": 1, "srcType": "userInput"}},
        {"destinationField": "name", "source": {"id": 1, "srcType": "userInput"}},
    ]


@pytest.mark.asyncio
async def test_get_todo_different_user_raises_exception(
    async_db_session: AsyncSession, source_link_service: SourceLinksService, user1, user2
):
    todo_service = TodoService(async_db_session, source_link_service)
    todo_data = TodoCreate(
        name="Private todo",
        description="This belongs to user1",
        dueDate=datetime.datetime(2025, 1, 20),
    )

    created_todo = await todo_service.create_todo_by_user(todo_data, user1)
    await async_db_session.commit()

    with pytest.raises(TodoDoesNotExist):
        await todo_service.get_todo(created_todo.id, user2)


@pytest.mark.asyncio
async def test_get_todo_nonexistent_todo_raises_exception(
    async_db_session: AsyncSession, source_link_service: SourceLinksService, user1
):
    todo_service = TodoService(async_db_session, source_link_service)

    with pytest.raises(TodoDoesNotExist):
        await todo_service.get_todo(999999, user1)


@pytest.mark.asyncio
async def test_get_todo_with_ai_created_todo(
    async_db_session: AsyncSession, source_link_service: SourceLinksService, user1, document
):
    todo_service = TodoService(async_db_session, source_link_service)
    todo_data = TodoAiCreate(
        todo=TodoCreate(
            name="AI created todo",
            description="This was created by AI",
            dueDate=datetime.datetime(2025, 1, 20),
        ),
        srcType=SourceType.document,
        srcId=document.id,
    )

    created_todo = await todo_service.create_todo_by_ai(todo_data, user1)
    await async_db_session.commit()

    retrieved_todo = await todo_service.get_todo(created_todo.id, user1)
    assert retrieved_todo is not None
    assert retrieved_todo.id == created_todo.id
    assert retrieved_todo.name == "AI created todo"
    assert retrieved_todo.description == "This was created by AI"
    assert retrieved_todo.dueDate == datetime.datetime(2025, 1, 20)
    assert retrieved_todo.type == TodoType.systemCreated

    assert hasattr(retrieved_todo, "sources")
    assert retrieved_todo.sources is not None
    assert model_dump(retrieved_todo.sources) == [
        {"destinationField": "description", "source": {"id": 1, "srcType": "document"}},
        {"destinationField": "dueDate", "source": {"id": 1, "srcType": "document"}},
        {"destinationField": "name", "source": {"id": 1, "srcType": "document"}},
    ]
