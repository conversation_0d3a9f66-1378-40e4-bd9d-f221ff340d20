import pytest
from sqlalchemy.ext.asyncio import AsyncSession

from src.db_models.appliance import Appliance
from src.db_models.chat import Chat
from src.db_models.entity_link import LinkedEntityType
from src.db_models.job import Job
from src.db_models.project import Project
from src.db_models.todo import Todo, TodoType
from src.db_models.user import User
from src.schemas import LinkedEntity
from src.services.entity_links import EntityLinksService


@pytest.fixture
def entity_link_service(async_db_session: AsyncSession):
    return EntityLinksService(async_db_session)


@pytest.fixture
async def user(async_db_session: AsyncSession):
    user = User(clerkId="test_clerk_id", email="<EMAIL>", firstName="Test", lastName="User")
    async_db_session.add(user)
    await async_db_session.commit()
    await async_db_session.refresh(user)
    return user


@pytest.fixture
async def chat(async_db_session: AsyncSession, user: User):
    chat = Chat(title="Dummy", userId=user.id)
    async_db_session.add(chat)
    await async_db_session.commit()
    await async_db_session.refresh(chat)
    return chat


@pytest.fixture
async def todo(async_db_session: AsyncSession, user: User):
    todo = Todo(name="Test Todo", type=TodoType.userCreated, userId=user.id)
    async_db_session.add(todo)
    await async_db_session.commit()
    await async_db_session.refresh(todo)
    return todo


@pytest.fixture
async def project(async_db_session: AsyncSession, user: User, chat: Chat):
    project = Project(
        id=1,
        headline="Dummy Project",
        subTitle="Dummy Subtitle",
        details="Some details about the dummy project",
        urgency="High",
        chatId=chat.id,
        userId=user.id,
    )
    async_db_session.add(project)
    await async_db_session.commit()
    return project


@pytest.fixture
async def job(async_db_session: AsyncSession, user: User, chat: Chat, project: Project):
    job = Job(
        id=1,
        headline="Test Headline 1",
        subTitle="Test Subtitle 1",
        details="Test details 1",
        urgency="Medium",
        availability="Weekdays",
        status="created",
        projectId=project.id,
        chatId=chat.id,
        userId=user.id,
        hubspotId="old_hubspot_id",
    )
    async_db_session.add(job)
    await async_db_session.commit()
    await async_db_session.refresh(job)
    return job


@pytest.fixture
async def property(async_db_session: AsyncSession):
    from src.db_models.property import Property

    prop = Property(
        sizeInSqft=1000,
        onFloorLevel=1,
        hasBalconyTerrace=False,
        hasGarden=False,
        hasSwimmingPool=False,
        numberOfBedrooms=2,
        numberOfBathrooms=1,
        numberOfFloors=1,
        yearsOfOwnership=5,
    )
    async_db_session.add(prop)
    await async_db_session.commit()
    await async_db_session.refresh(prop)
    return prop


@pytest.fixture
async def appliance(async_db_session: AsyncSession, property):
    appliance = Appliance(type="refrigerator", brand="Test Brand", model="Test Model", propertyId=property.id)
    async_db_session.add(appliance)
    await async_db_session.commit()
    await async_db_session.refresh(appliance)
    return appliance


@pytest.mark.asyncio
async def test_link_retrieval_bidirectional_user_job(
    entity_link_service: EntityLinksService, async_db_session: AsyncSession, user: User, job: Job
):
    """Test that links can be retrieved from both entities"""
    user_entity = LinkedEntity(entityType=LinkedEntityType.users, id=user.id)
    job_entity = LinkedEntity(entityType=LinkedEntityType.jobs, id=job.id)

    # Create link
    await entity_link_service.link(user_entity, job_entity)
    await async_db_session.commit()

    # Test that the link can be retrieved by user_entity
    user_links = await entity_link_service.get_links(user_entity)
    assert len(user_links) == 1
    assert user_links[0].entityType == LinkedEntityType.jobs
    assert user_links[0].id == job.id
    # Check JobLinkedEntityInfo fields
    assert user_links[0].headline == "Test Headline 1"

    # Test that the link can also be retrieved by job_entity
    job_links = await entity_link_service.get_links(job_entity)
    assert len(job_links) == 1
    assert job_links[0].entityType == LinkedEntityType.users
    assert job_links[0].id == user.id
    assert job_links[0].firstName == "Test"
    assert job_links[0].lastName == "User"


@pytest.mark.asyncio
async def test_link_job_to_appliance(
    entity_link_service: EntityLinksService, async_db_session: AsyncSession, job: Job, appliance: Appliance
):
    """Test linking a job to an appliance"""
    job_entity = LinkedEntity(entityType=LinkedEntityType.jobs, id=job.id)
    appliance_entity = LinkedEntity(entityType=LinkedEntityType.appliances, id=appliance.id)

    # Create link
    await entity_link_service.link(job_entity, appliance_entity)
    await async_db_session.commit()

    # Test bidirectional retrieval
    job_links = await entity_link_service.get_links(job_entity)

    assert len(job_links) == 1
    assert job_links[0].entityType == LinkedEntityType.appliances
    assert job_links[0].id == appliance.id
    # Check ApplianceLinkedEntityInfo fields
    assert job_links[0].type == "refrigerator"
    assert job_links[0].brand == "Test Brand"
    assert job_links[0].model == "Test Model"

    appliance_links = await entity_link_service.get_links(appliance_entity)
    assert len(appliance_links) == 1
    assert appliance_links[0].entityType == LinkedEntityType.jobs
    assert appliance_links[0].id == job.id
    assert appliance_links[0].headline == "Test Headline 1"


@pytest.mark.asyncio
async def test_link_nonexistent_entities(entity_link_service: EntityLinksService, async_db_session: AsyncSession):
    nonexistent_user = LinkedEntity(entityType=LinkedEntityType.users, id=99999)
    nonexistent_job = LinkedEntity(entityType=LinkedEntityType.jobs, id=99999)

    with pytest.raises(ValueError, match="User with id 99999 not found"):
        await entity_link_service.link(nonexistent_user, nonexistent_job)

    with pytest.raises(ValueError, match="Job with id 99999 not found"):
        await entity_link_service.link(nonexistent_job, nonexistent_user)

    links = await entity_link_service.get_links(nonexistent_user)
    assert len(links) == 0

    links = await entity_link_service.get_links(nonexistent_job)
    assert len(links) == 0


@pytest.mark.asyncio
async def test_unlink_entities(
    entity_link_service: EntityLinksService, async_db_session: AsyncSession, user: User, job: Job
):
    """Test unlinking entities"""
    user_entity = LinkedEntity(entityType=LinkedEntityType.users, id=user.id)
    job_entity = LinkedEntity(entityType=LinkedEntityType.jobs, id=job.id)

    # Create link first
    await entity_link_service.link(user_entity, job_entity)
    await async_db_session.commit()

    # Unlink
    await entity_link_service.unlink(user_entity, job_entity)
    await async_db_session.commit()

    # Verify link is removed
    links = await entity_link_service.get_links(user_entity)
    assert len(links) == 0


@pytest.mark.asyncio
async def test_unlink_entities_reverse_order(
    entity_link_service: EntityLinksService,
    async_db_session: AsyncSession,
    todo: Todo,
    chat: Chat,
):
    todo_entity = LinkedEntity(entityType=LinkedEntityType.todos, id=todo.id)
    chat_entity = LinkedEntity(entityType=LinkedEntityType.chats, id=chat.id)

    await entity_link_service.link(todo_entity, chat_entity)
    await async_db_session.commit()

    # verify the link exists for chat and for todo (and assert the values)
    todo_links = await entity_link_service.get_links(todo_entity)
    assert len(todo_links) == 1
    assert todo_links[0].entityType == LinkedEntityType.chats
    assert todo_links[0].id == chat.id
    # Check ChatLinkedEntityInfo fields
    assert todo_links[0].title == "Dummy"

    chat_links = await entity_link_service.get_links(chat_entity)
    assert len(chat_links) == 1
    assert chat_links[0].entityType == LinkedEntityType.todos
    assert chat_links[0].id == todo.id
    # Check TodoLinkedEntityInfo fields
    assert chat_links[0].name == "Test Todo"

    # Unlink
    await entity_link_service.unlink(chat_entity, todo_entity)
    await async_db_session.commit()

    links = await entity_link_service.get_links(todo_entity)
    assert len(links) == 0

    links = await entity_link_service.get_links(chat_entity)
    assert len(links) == 0


@pytest.mark.asyncio
async def test_unlink_bidirectional(
    entity_link_service: EntityLinksService, async_db_session: AsyncSession, user: User, job: Job
):
    """Test that unlink works bidirectionally"""
    user_entity = LinkedEntity(entityType=LinkedEntityType.users, id=user.id)
    job_entity = LinkedEntity(entityType=LinkedEntityType.jobs, id=job.id)

    # Create link
    await entity_link_service.link(user_entity, job_entity)
    await async_db_session.commit()

    # Unlink in reverse order
    await entity_link_service.unlink(job_entity, user_entity)
    await async_db_session.commit()

    # Verify link is removed
    user_links = await entity_link_service.get_links(user_entity)
    job_links = await entity_link_service.get_links(job_entity)

    assert len(user_links) == 0
    assert len(job_links) == 0


@pytest.mark.asyncio
async def test_unlink_all(
    entity_link_service: EntityLinksService, async_db_session: AsyncSession, user: User, job: Job, appliance: Appliance
):
    """Test unlinking all connections for an entity"""
    user_entity = LinkedEntity(entityType=LinkedEntityType.users, id=user.id)
    job_entity = LinkedEntity(entityType=LinkedEntityType.jobs, id=job.id)
    appliance_entity = LinkedEntity(entityType=LinkedEntityType.appliances, id=appliance.id)

    # Create multiple links
    await entity_link_service.link(user_entity, job_entity)
    await entity_link_service.link(user_entity, appliance_entity)
    await async_db_session.commit()

    # Verify multiple links exist and check their fields
    links = await entity_link_service.get_links(user_entity)
    assert len(links) == 2

    # Sort links by entity type for consistent testing
    links.sort(key=lambda x: x.entityType.value)

    # First link should be appliance
    assert links[0].entityType == LinkedEntityType.appliances
    assert links[0].id == appliance.id
    assert links[0].type == "refrigerator"
    assert links[0].brand == "Test Brand"
    assert links[0].model == "Test Model"

    # Second link should be job
    assert links[1].entityType == LinkedEntityType.jobs
    assert links[1].id == job.id
    assert links[1].headline == "Test Headline 1"

    # Unlink all
    await entity_link_service.unlink_all(user_entity)
    await async_db_session.commit()

    # Verify all links are removed
    links = await entity_link_service.get_links(user_entity)
    assert len(links) == 0

    # Verify other entities also have no links to the user
    job_links = await entity_link_service.get_links(job_entity)
    appliance_links = await entity_link_service.get_links(appliance_entity)

    assert len(job_links) == 0
    assert len(appliance_links) == 0


@pytest.mark.asyncio
async def test_duplicate_links_ignored(
    entity_link_service: EntityLinksService, async_db_session: AsyncSession, user: User, job: Job
):
    """Test that duplicate links are ignored"""
    user_entity = LinkedEntity(entityType=LinkedEntityType.users, id=user.id)
    job_entity = LinkedEntity(entityType=LinkedEntityType.jobs, id=job.id)

    # Create same link multiple times
    await entity_link_service.link(user_entity, job_entity)
    await entity_link_service.link(user_entity, job_entity)
    await entity_link_service.link(job_entity, user_entity)
    await async_db_session.commit()

    # Should only have one link
    user_links = await entity_link_service.get_links(user_entity)
    assert len(user_links) == 1
    assert user_links[0].entityType == LinkedEntityType.jobs
    assert user_links[0].id == job.id
    assert user_links[0].headline == "Test Headline 1"

    job_links = await entity_link_service.get_links(job_entity)
    assert len(job_links) == 1
    assert job_links[0].entityType == LinkedEntityType.users
    assert job_links[0].id == user.id
    assert job_links[0].firstName == "Test"
    assert job_links[0].lastName == "User"


async def test_link_user_to_todo(entity_link_service, user, todo):
    """Test linking a user to a todo"""
    # Create LinkedEntity objects
    user_entity = LinkedEntity(entityType=LinkedEntityType.users, id=user.id)
    todo_entity = LinkedEntity(entityType=LinkedEntityType.todos, id=todo.id)

    # Link user to todo
    await entity_link_service.link(user_entity, todo_entity)

    # Verify link exists in both directions
    user_links = await entity_link_service.get_links(user_entity)
    assert len(user_links) == 1
    assert user_links[0].entityType == LinkedEntityType.todos
    assert user_links[0].id == todo.id

    todo_links = await entity_link_service.get_links(todo_entity)
    assert len(todo_links) == 1
    assert todo_links[0].entityType == LinkedEntityType.users
    assert todo_links[0].id == user.id


async def test_link_project_to_chat(entity_link_service, project, chat):
    """Test linking a project to a chat"""
    # Create LinkedEntity objects
    project_entity = LinkedEntity(entityType=LinkedEntityType.projects, id=project.id)
    chat_entity = LinkedEntity(entityType=LinkedEntityType.chats, id=chat.id)

    # Link project to chat
    await entity_link_service.link(project_entity, chat_entity)

    # Verify bidirectional linking
    project_links = await entity_link_service.get_links(project_entity)
    assert len(project_links) == 1
    assert project_links[0].entityType == LinkedEntityType.chats
    assert project_links[0].id == chat.id

    chat_links = await entity_link_service.get_links(chat_entity)
    assert len(chat_links) == 1
    assert chat_links[0].entityType == LinkedEntityType.projects
    assert chat_links[0].id == project.id


async def test_link_todo_to_project(entity_link_service, todo, project):
    """Test linking a todo to a project"""
    # Create LinkedEntity objects
    todo_entity = LinkedEntity(entityType=LinkedEntityType.todos, id=todo.id)
    project_entity = LinkedEntity(entityType=LinkedEntityType.projects, id=project.id)

    # Link todo to project
    await entity_link_service.link(todo_entity, project_entity)

    # Verify bidirectional linking
    todo_links = await entity_link_service.get_links(todo_entity)
    assert len(todo_links) == 1
    assert todo_links[0].entityType == LinkedEntityType.projects
    assert todo_links[0].id == project.id

    project_links = await entity_link_service.get_links(project_entity)
    assert len(project_links) == 1
    assert project_links[0].entityType == LinkedEntityType.todos
    assert project_links[0].id == todo.id


async def test_link_chat_to_user(entity_link_service, chat, user):
    """Test linking a chat to a user"""
    # Create LinkedEntity objects
    chat_entity = LinkedEntity(entityType=LinkedEntityType.chats, id=chat.id)
    user_entity = LinkedEntity(entityType=LinkedEntityType.users, id=user.id)

    # Link chat to user
    await entity_link_service.link(chat_entity, user_entity)

    # Verify bidirectional linking
    chat_links = await entity_link_service.get_links(chat_entity)
    assert len(chat_links) == 1
    assert chat_links[0].entityType == LinkedEntityType.users
    assert chat_links[0].id == user.id

    user_links = await entity_link_service.get_links(user_entity)
    assert len(user_links) == 1
    assert user_links[0].entityType == LinkedEntityType.chats
    assert user_links[0].id == chat.id


async def test_multiple_links_same_entity(entity_link_service, user, job, project, todo):
    """Test that one entity can be linked to multiple other entities"""
    # Create LinkedEntity objects
    user_entity = LinkedEntity(entityType=LinkedEntityType.users, id=user.id)
    job_entity = LinkedEntity(entityType=LinkedEntityType.jobs, id=job.id)
    project_entity = LinkedEntity(entityType=LinkedEntityType.projects, id=project.id)
    todo_entity = LinkedEntity(entityType=LinkedEntityType.todos, id=todo.id)

    # Link user to multiple entities
    await entity_link_service.link(user_entity, job_entity)
    await entity_link_service.link(user_entity, project_entity)
    await entity_link_service.link(user_entity, todo_entity)

    # Verify user has links to all three entities
    user_links = await entity_link_service.get_links(user_entity)
    assert len(user_links) == 3

    # Check that all expected entity types are linked
    linked_types = {link.entityType for link in user_links}
    assert linked_types == {LinkedEntityType.jobs, LinkedEntityType.projects, LinkedEntityType.todos}

    # Verify each linked entity has a link back to the user
    job_links = await entity_link_service.get_links(job_entity)
    assert len(job_links) == 1
    assert job_links[0].entityType == LinkedEntityType.users

    project_links = await entity_link_service.get_links(project_entity)
    assert len(project_links) == 1
    assert project_links[0].entityType == LinkedEntityType.users

    todo_links = await entity_link_service.get_links(todo_entity)
    assert len(todo_links) == 1
    assert todo_links[0].entityType == LinkedEntityType.users


async def test_complex_entity_network(entity_link_service, user, job, project, todo, chat, appliance):
    """Test creating a complex network of entity links"""
    # Create LinkedEntity objects
    user_entity = LinkedEntity(entityType=LinkedEntityType.users, id=user.id)
    job_entity = LinkedEntity(entityType=LinkedEntityType.jobs, id=job.id)
    project_entity = LinkedEntity(entityType=LinkedEntityType.projects, id=project.id)
    todo_entity = LinkedEntity(entityType=LinkedEntityType.todos, id=todo.id)
    chat_entity = LinkedEntity(entityType=LinkedEntityType.chats, id=chat.id)
    appliance_entity = LinkedEntity(entityType=LinkedEntityType.appliances, id=appliance.id)

    # Create a network: user -> job -> project -> todo -> chat -> appliance
    await entity_link_service.link(user_entity, job_entity)
    await entity_link_service.link(job_entity, project_entity)
    await entity_link_service.link(project_entity, todo_entity)
    await entity_link_service.link(todo_entity, chat_entity)
    await entity_link_service.link(chat_entity, appliance_entity)

    # Verify each entity has the expected number of links
    assert len(await entity_link_service.get_links(user_entity)) == 1
    assert len(await entity_link_service.get_links(job_entity)) == 2  # linked to user and project
    assert len(await entity_link_service.get_links(project_entity)) == 2  # linked to job and todo
    assert len(await entity_link_service.get_links(todo_entity)) == 2  # linked to project and chat
    assert len(await entity_link_service.get_links(chat_entity)) == 2  # linked to todo and appliance
    assert len(await entity_link_service.get_links(appliance_entity)) == 1


async def test_unlink_multiple_entity_types(entity_link_service, user, job, project, todo):
    """Test unlinking works correctly with multiple entity types"""
    # Create LinkedEntity objects
    user_entity = LinkedEntity(entityType=LinkedEntityType.users, id=user.id)
    job_entity = LinkedEntity(entityType=LinkedEntityType.jobs, id=job.id)
    project_entity = LinkedEntity(entityType=LinkedEntityType.projects, id=project.id)
    todo_entity = LinkedEntity(entityType=LinkedEntityType.todos, id=todo.id)

    # Create multiple links
    await entity_link_service.link(user_entity, job_entity)
    await entity_link_service.link(user_entity, project_entity)
    await entity_link_service.link(user_entity, todo_entity)

    # Verify all links exist
    assert len(await entity_link_service.get_links(user_entity)) == 3

    # Unlink one relationship
    await entity_link_service.unlink(user_entity, project_entity)

    # Verify only the specific link was removed
    user_links = await entity_link_service.get_links(user_entity)
    assert len(user_links) == 2

    linked_types = {link.entityType for link in user_links}
    assert linked_types == {LinkedEntityType.jobs, LinkedEntityType.todos}

    # Verify the project no longer has a link to the user
    project_links = await entity_link_service.get_links(project_entity)
    assert len(project_links) == 0
