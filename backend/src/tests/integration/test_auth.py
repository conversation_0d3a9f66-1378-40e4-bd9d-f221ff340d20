import os

import pytest
from fastapi import FastAPI
from jwt import InvalidTokenError

app = FastAPI()


@pytest.mark.filterwarnings("ignore:authenticate_request method is applicable")
def test_invalid_token(client, monkeypatch):
    def mock_decode(token, key, algorithms, audience, options, leeway):
        raise InvalidTokenError

    monkeypatch.setattr("jwt.decode", mock_decode)
    os.environ["CLERK_PEM_PUBLIC_KEY"] = "test_key"

    response = client.get("/user/", headers={"Authorization": "Bearer invalid_token"})
    assert response.status_code == 401
    assert response.json() == {"detail": "Could not validate credentials"}
