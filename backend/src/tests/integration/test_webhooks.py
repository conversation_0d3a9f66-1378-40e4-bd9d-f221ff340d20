import base64
import hashlib
import hmac
import json
import os
from asyncio import sleep
from datetime import datetime
from math import floor
from unittest.mock import AsyncMock

import pytest
from fastapi.testclient import TestClient
from sqlalchemy import select
from sqlalchemy.ext.asyncio import AsyncSession

from src.db_models.user import User
from src.integrations.clerk import ClerkAP<PERSON>
from src.services.users import UserService

svix_secret = os.environ["CLERK_WEBHOOK_SECRET"]


def test_receive_clerk_event_signature_verification_failure(client: TestClient):
    response = client.post(
        "/webhooks/clerk",
        headers={
            "svix-id": "1",
            "svix-timestamp": str(floor(datetime.now().timestamp())),
            "svix-signature": "invalid_signature",
        },
        json={"data": {}, "object": "event", "type": "user.created"},
    )
    assert response.status_code == 400


@pytest.mark.filterwarnings("ignore:authenticate_request method is applicable")
async def test_receive_clerk_event_user_created_updated_deleted_and_recreated(
    async_db_session: AsyncSession, client: TestClient, monkeypatch
):
    timestamp = datetime.now()

    def post_webhook(data: dict):
        def sign(msg_id: str, timestamp: datetime, data: str) -> str:
            timestamp_str = str(floor(timestamp.timestamp()))
            to_sign = f"{msg_id}.{timestamp_str}.{data}".encode()
            signature = hmac.new(base64.b64decode(svix_secret.split("_")[1]), to_sign, hashlib.sha256).digest()
            return f"v1,{base64.b64encode(signature).decode('utf-8')}"

        body = json.dumps(data)
        signature = sign("1", timestamp, body)
        return client.post(
            "/webhooks/clerk",
            headers={
                "svix-id": "1",
                "svix-timestamp": str(floor(timestamp.timestamp())),
                "svix-signature": signature,
            },
            content=body,
        )

    response = post_webhook(
        {
            "data": {
                "id": "clerk_user_id",
                "email_addresses": [
                    {"id": "email_id_1", "email_address": "<EMAIL>", "verification": {"status": "verified"}}
                ],
                "primary_email_address_id": "email_id_1",
                "first_name": "Test",
                "last_name": "User",
                "phone_numbers": [
                    {"id": "phone_id_1", "phone_number": "+1234567890", "verification": {"status": "verified"}}
                ],
                "primary_phone_number_id": "phone_id_1",
            },
            "object": "event",
            "type": "user.created",
        }
    )

    assert response.status_code == 204

    async_db_session.expire_all()

    mock_sendgrid_service = AsyncMock()

    user_service = UserService(
        async_db_session,
        clerk_api=ClerkAPI(api_key="TEST", permitted_origins=["localhost:8000"], public_key="test"),
        sendgrid_service=mock_sendgrid_service,
        hubspot_sync_service=AsyncMock(),
    )
    await sleep(0)

    user = await user_service.get_user_by_clerk_id("clerk_user_id")
    assert user is not None
    assert user.email == "<EMAIL>"
    assert user.isEmailVerified
    assert user.phoneNumber == "+1234567890"
    assert user.isPhoneNumberVerified
    # assert user.isWelcomeEmailSent
    assert not mock_sendgrid_service.send_welcome_email.called  # because the user was already created earlier

    def mock_decode(token, key, algorithms, audience, options, leeway):
        return {"sub": "clerk_user_id", "azp": "http://invalid-azp:8000"}

    monkeypatch.setattr("jwt.decode", mock_decode)
    os.environ["CLERK_PEM_PUBLIC_KEY"] = "test_key"

    response = client.get("/user/", headers={"Authorization": "Bearer test_token"})
    assert response.status_code == 401

    def mock_decode(token, key, algorithms, audience, options, leeway):
        return {"sub": "clerk_user_id", "azp": "http://localhost:8000"}

    monkeypatch.setattr("jwt.decode", mock_decode)
    os.environ["CLERK_PEM_PUBLIC_KEY"] = "test_key"

    response = client.get("/user/", headers={"Authorization": "Bearer test_token"})
    assert response.status_code == 200
    assert response.json() == {
        "id": 1,
        "clerkId": "clerk_user_id",
        "firstName": "Test",
        "lastName": "User",
        "email": "<EMAIL>",
        "phoneNumber": "+1234567890",
        "mainUsage": None,
    }

    response = post_webhook(
        {
            "data": {
                "id": "clerk_user_id",
                "email_addresses": [
                    {
                        "id": "email_id_1",
                        "email_address": "<EMAIL>",
                        "verification": {"status": "unverified"},
                    }
                ],
                "primary_email_address_id": "email_id_1",
                "first_name": "Updated",
                "last_name": "User",
                "phone_numbers": [],
                "primary_phone_number_id": None,
            },
            "object": "event",
            "type": "user.updated",
        }
    )

    assert response.status_code == 204

    async_db_session.expire_all()

    user = await user_service.get_user_by_clerk_id("clerk_user_id")

    assert user is not None
    assert user.email == "<EMAIL>"
    assert not user.isEmailVerified

    response = post_webhook(
        {
            "data": {
                "id": "clerk_user_id",
                "email_addresses": [
                    {"id": "email_id_1", "email_address": "<EMAIL>", "verification": None}
                ],
                "primary_email_address_id": "email_id_1",
                "first_name": "Updated",
                "last_name": "User",
                "phone_numbers": [{"id": "phone_id_1", "phone_number": "+1234567890", "verification": None}],
                "primary_phone_number_id": "phone_id_1",
            },
            "object": "event",
            "type": "user.updated",
        }
    )

    assert response.status_code == 204

    async_db_session.expire_all()

    user = await user_service.get_user_by_clerk_id("clerk_user_id")

    assert user is not None
    assert user.email == "<EMAIL>"
    assert not user.isEmailVerified
    assert user.phoneNumber == "+1234567890"
    assert not user.isPhoneNumberVerified

    response = post_webhook(
        {
            "data": {"deleted": True, "id": "clerk_user_id", "object": "user"},
            "object": "event",
            "type": "user.deleted",
        }
    )

    assert response.status_code == 204
    async_db_session.expire_all()

    user = await user_service.get_user_by_clerk_id("clerk_user_id")
    assert user is None

    user_from_db = await async_db_session.scalar(select(User).where(User.clerkId == "clerk_user_id"))
    assert user_from_db is not None

    response = post_webhook(
        {
            "data": {
                "id": "clerk_user_id_2",
                "email_addresses": [
                    {
                        "id": "email_id_2",
                        "email_address": "<EMAIL>",
                        "verification": {"status": "verified"},
                    }
                ],
                "primary_email_address_id": "email_id_2",
                "first_name": "Test",
                "last_name": "User",
                "phone_numbers": [
                    {"id": "phone_id_1", "phone_number": "+1234567890", "verification": {"status": "verified"}}
                ],
                "primary_phone_number_id": "phone_id_1",
            },
            "object": "event",
            "type": "user.created",
        }
    )

    assert response.status_code == 204
    async_db_session.expire_all()

    user = await user_service.get_user_by_clerk_id("clerk_user_id_2")
    assert user is not None
    assert user.email == "<EMAIL>"
    assert user.isEmailVerified
    assert user.phoneNumber == "+1234567890"
    assert user.isPhoneNumberVerified


async def test_receive_clerk_event_unknown_event_type(client: TestClient):
    response = client.post(
        "/webhooks/clerk",
        headers={"svix-signature": "valid_signature"},
        json={"data": {}, "object": "event", "type": "unknown.event"},
    )
    assert response.status_code == 400
