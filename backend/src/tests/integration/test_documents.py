import os
from unittest.mock import AsyncMock

import aioboto3
import pytest
from sqlalchemy.ext.asyncio import AsyncSession

from src.db_models.document import DocumentStatusType, DocumentCategoryType
from src.db_models.user import User
from src.schemas import DocumentAiUpdate
from src.services.documents import (
    DocumentService,
    DocumentSizeLimitExceeded,
    UserDocumentSizeLimitExceeded,
    UnsupportedDocumentType,
    DocumentDoesNotExist,
)


@pytest.mark.asyncio(loop_scope="session")
class TestDocuments:
    @pytest.fixture(scope="session")
    async def mock_boto(self):
        from moto.server import ThreadedMotoServer

        server = ThreadedMotoServer(port=0)

        server.start()
        port = server._server.socket.getsockname()[1]
        os.environ["AWS_ENDPOINT_URL"] = f"http://127.0.0.1:{port}"
        os.environ["AWS_DEFAULT_REGION"] = "us-east-1"
        os.environ["AWS_ACCESS_KEY_ID"] = "test"
        os.environ["AWS_SECRET_ACCESS_KEY"] = "test"

        yield

        del os.environ["AWS_ENDPOINT_URL"]
        server.stop()

    @pytest.fixture
    async def aioboto3_session(self):
        aioboto3_session = aioboto3.Session()
        async with aioboto3_session.client("s3") as s3:
            await s3.create_bucket(Bucket="test-bucket")
        return aioboto3_session

    @pytest.fixture
    def s3_client(self, mocker):
        s3_client = AsyncMock()
        mocker.patch("src.services.documents.aioboto3.Session.client", return_value=s3_client)
        return s3_client

    @pytest.fixture
    def ai_engine(self):
        ai_engine = AsyncMock()
        ai_engine.parse_file.return_value = None
        return ai_engine

    @pytest.mark.asyncio
    async def test_documents(self, async_db_session: AsyncSession, mock_boto, aioboto3_session, ai_engine):
        user1 = User(
            id=1,
            email="<EMAIL>",
            clerkId="clerk_user_id",
            firstName="Test",
            lastName="User",
        )
        async_db_session.add(user1)
        user2 = User(
            id=2,
            email="<EMAIL>",
            clerkId="clerk_user_id2",
            firstName="Test2",
            lastName="User2",
        )
        async_db_session.add(user1)
        async_db_session.add(user2)
        await async_db_session.flush()

        mock_property_service = AsyncMock()
        mock_property_service.get_first_property_for_user.return_value = None
        document_service = DocumentService(async_db_session, mock_property_service, aioboto3_session, ai_engine)
        document = AsyncMock()
        document.content_type = "application/pdf"
        document.size = 1500  # 1.5 KB
        document.file = b"test file content"
        document.filename = "test.pdf"

        db_document = await document_service.save_document(document, user1)

        assert db_document is not None
        assert db_document.userId == user1.id
        assert db_document.sizeInKiloBytes == 2
        assert db_document.originalFileName == "test.pdf"
        assert db_document.browserMimeType == "application/pdf"
        assert db_document.s3Key.startswith(f"user={user1.id}/")
        assert db_document.s3Bucket == "test-bucket"
        assert db_document.uploadContext is None
        assert db_document.status == DocumentStatusType.saved
        async with aioboto3_session.client("s3") as s3:
            response = await s3.get_object(Bucket=db_document.s3Bucket, Key=db_document.s3Key)
            assert (await response["Body"].read()) == b"test file content"

        received_content = b""
        async for chunk in document_service.get_document_content_from_s3(db_document.s3Bucket, db_document.s3Key):
            received_content += chunk

        assert received_content == b"test file content"

        document.size = 50_000_000  # 50 MB
        with pytest.raises(DocumentSizeLimitExceeded):
            await document_service.save_document(document, user1)

        document.size = None  # 50 MB
        with pytest.raises(DocumentSizeLimitExceeded):
            await document_service.save_document(document, user1)

        assert (
            len((await async_db_session.execute(document_service.list_documents_for_user_query(user1))).scalars().all())
            == 1
        )

        assert (
            len(
                (
                    await async_db_session.execute(
                        document_service.list_documents_for_user_query(user1, status=DocumentStatusType.saved)
                    )
                )
                .scalars()
                .all()
            )
            == 1
        )

        assert (
            len(
                (
                    await async_db_session.execute(
                        document_service.list_documents_for_user_query(
                            user1, status=DocumentStatusType.processingCompleted
                        )
                    )
                )
                .scalars()
                .all()
            )
            == 0
        )

        assert (
            len(
                (
                    await async_db_session.execute(
                        document_service.list_documents_for_user_query(user1, upload_context="filesPage")  # include all
                    )
                )
                .scalars()
                .all()
            )
            == 1
        )

        document.size = 1
        db_document = await document_service.save_document(document, user1, upload_context="appliancesPage")

        assert (
            len((await async_db_session.execute(document_service.list_documents_for_user_query(user1))).scalars().all())
            == 2
        )

        assert (
            len(
                (
                    await async_db_session.execute(
                        document_service.list_documents_for_user_query(user1, status=DocumentStatusType.saved)
                    )
                )
                .scalars()
                .all()
            )
            == 2
        )

        assert (
            len(
                (
                    await async_db_session.execute(
                        document_service.list_documents_for_user_query(
                            user1, status=DocumentStatusType.processingCompleted
                        )
                    )
                )
                .scalars()
                .all()
            )
            == 0
        )

        assert (
            len(
                (
                    await async_db_session.execute(
                        document_service.list_documents_for_user_query(user1, upload_context="appliancesPage")
                    )
                )
                .scalars()
                .all()
            )
            == 1
        )

        db_document.status = DocumentStatusType.error
        await async_db_session.flush()

        assert (
            len(
                (
                    await async_db_session.execute(
                        document_service.list_documents_for_user_query(user1, upload_context="appliancesPage")
                    )
                )
                .scalars()
                .all()
            )
            == 1
        )
        await document_service.mark_documents_status_displayed(user1, upload_context="filesPage")  # clears all errors
        assert (
            len(
                (
                    await async_db_session.execute(
                        document_service.list_documents_for_user_query(user1, upload_context="appliancesPage")
                    )
                )
                .scalars()
                .all()
            )
            == 0
        )

        db_document.hasStatusBeenDisplayed = False
        await async_db_session.flush()
        await document_service.mark_documents_status_displayed(user1, upload_context="appliancesPage")
        assert (
            len(
                (
                    await async_db_session.execute(
                        document_service.list_documents_for_user_query(user1, upload_context="appliancesPage")
                    )
                )
                .scalars()
                .all()
            )
            == 0
        )

        assert (
            len(
                (
                    await async_db_session.execute(
                        document_service.list_documents_for_user_query(
                            user1, upload_context="appliancesPage", exclude_displayed_errors=False
                        )
                    )
                )
                .scalars()
                .all()
            )
            == 1
        )

        db_document.hasStatusBeenDisplayed = False
        await async_db_session.flush()
        await document_service.mark_documents_status_displayed(user1)
        assert (
            len(
                (
                    await async_db_session.execute(
                        document_service.list_documents_for_user_query(user1, upload_context="appliancesPage")
                    )
                )
                .scalars()
                .all()
            )
            == 0
        )

        db_document.hasStatusBeenDisplayed = False
        db_document_files_page = await document_service.save_document(document, user1, upload_context="filesPage")
        await async_db_session.flush()

        assert (
            len(
                (
                    await async_db_session.execute(
                        document_service.list_documents_for_user_query(user1, upload_context="filesPage")  # include all
                    )
                )
                .scalars()
                .all()
            )
            == 2
        )

        await document_service.mark_documents_status_displayed(user1, upload_context="appliancesPage")
        assert (
            len(
                (
                    await async_db_session.execute(
                        document_service.list_documents_for_user_query(user1, upload_context="appliancesPage")
                    )
                )
                .scalars()
                .all()
            )
            == 0
        )

        assert (
            len(
                (
                    await async_db_session.execute(
                        document_service.list_documents_for_user_query(user1, upload_context="filesPage")  # include all
                    )
                )
                .scalars()
                .all()
            )
            == 2
        )

        document.size = 20_000_000  # 20 MB - MAX
        large_documents = []
        for _ in range(24):
            large_documents.append(await document_service.save_document(document, user1))

        assert (
            len(
                (
                    await async_db_session.execute(
                        document_service.list_documents_for_user_query(user1, upload_context="appliancesPage")
                    )
                )
                .scalars()
                .all()
            )
            == 0
        )

        with pytest.raises(UserDocumentSizeLimitExceeded):
            await document_service.save_document(document, user1)

        with pytest.raises(DocumentDoesNotExist):
            await document_service.delete_document(large_documents[0].id, user2)

        await document_service.delete_document(large_documents[0].id, user1)

        with pytest.raises(DocumentDoesNotExist):
            await document_service.get_document_for_user(large_documents[0].id, user1)

        await document_service.save_document(document, user1)

        document.size = 1500
        document.filename = "test.docx"
        with pytest.raises(UnsupportedDocumentType):
            await document_service.save_document(document, user1)

        assert await document_service.get_document_for_user(large_documents[1].id, user1) == large_documents[1]

        with pytest.raises(DocumentDoesNotExist):
            await document_service.get_document_for_user(large_documents[1].id, user2)

        assert (
            await async_db_session.execute(document_service.list_documents_for_user_query(user2))
        ).scalars().all() == []

        assert (
            len((await async_db_session.execute(document_service.list_documents_for_user_query(user1))).scalars().all())
            == 26
        )

        # test filename case insensitivity
        document.filename = "test.JPG"
        await document_service.save_document(document, user1)

        document.filename = "test.Jpg"
        await document_service.save_document(document, user1)

        document.filename = "test"
        with pytest.raises(UnsupportedDocumentType):
            await document_service.save_document(document, user1)

    @pytest.mark.asyncio
    async def test_update_document_by_ai(self, async_db_session: AsyncSession, mock_boto, aioboto3_session, ai_engine):
        # Setup test user and document service
        user = User(
            id=100,
            email="<EMAIL>",
            clerkId="clerk_ai_user",
            firstName="AI",
            lastName="Tester",
        )
        async_db_session.add(user)
        await async_db_session.flush()

        mock_property_service = AsyncMock()
        mock_property_service.get_first_property_for_user.return_value = None
        document_service = DocumentService(async_db_session, mock_property_service, aioboto3_session, ai_engine)

        # Create a test document
        document = AsyncMock()
        document.content_type = "application/pdf"
        document.size = 1500
        document.file = b"test ai document content"
        document.filename = "ai_test.pdf"

        db_document = await document_service.save_document(document, user)
        assert db_document.status == DocumentStatusType.saved

        document_update = DocumentAiUpdate(
            status=DocumentStatusType.processingCompleted,
            aiGeneratedFileName="test.docx",
            category=DocumentCategoryType.propertyDetails,
            label="foo",
        )
        updated_document = await document_service.update_document_by_ai(db_document.id, document_update)

        await async_db_session.flush()
        await async_db_session.refresh(updated_document)

        assert updated_document.status == document_update.status
        assert updated_document.aiGeneratedFileName == document_update.aiGeneratedFileName
        assert updated_document.category == document_update.category
        assert updated_document.label == document_update.label

        with pytest.raises(DocumentDoesNotExist):
            await document_service.update_document_by_ai(999999, document_update)

    @pytest.mark.asyncio
    async def test_timeout_documents(self, async_db_session: AsyncSession, mock_boto, aioboto3_session, ai_engine):
        from datetime import datetime, timedelta
        from src.db_models.document import Document

        # Setup test user
        user = User(
            id=200,
            email="<EMAIL>",
            clerkId="clerk_timeout_user",
            firstName="Timeout",
            lastName="Tester",
        )
        async_db_session.add(user)
        await async_db_session.flush()

        mock_property_service = AsyncMock()
        mock_property_service.get_first_property_for_user.return_value = None
        document_service = DocumentService(async_db_session, mock_property_service, aioboto3_session, ai_engine)

        # Create test documents with different statuses and timestamps
        old_timestamp = datetime.now() - timedelta(minutes=20)  # 20 minutes ago (should timeout)
        recent_timestamp = datetime.now() - timedelta(minutes=5)  # 5 minutes ago (should not timeout)

        # Document 1: Old and in "saved" status (should timeout)
        doc1 = Document(
            userId=user.id,
            source="user",
            sizeInKiloBytes=100,
            s3Key="test1.pdf",
            s3Bucket="test-bucket",
            fileExtension="pdf",
            originalFileName="test1.pdf",
            status=DocumentStatusType.saved,
            created_at=old_timestamp,
            updated_at=old_timestamp,
        )

        # Document 2: Old and in "processing" status (should timeout)
        doc2 = Document(
            userId=user.id,
            source="user",
            sizeInKiloBytes=100,
            s3Key="test2.pdf",
            s3Bucket="test-bucket",
            fileExtension="pdf",
            originalFileName="test2.pdf",
            status=DocumentStatusType.processing,
            created_at=old_timestamp,
            updated_at=old_timestamp,
        )

        # Document 3: Recent and in "saved" status (should not timeout)
        doc3 = Document(
            userId=user.id,
            source="user",
            sizeInKiloBytes=100,
            s3Key="test3.pdf",
            s3Bucket="test-bucket",
            fileExtension="pdf",
            originalFileName="test3.pdf",
            status=DocumentStatusType.saved,
            created_at=recent_timestamp,
            updated_at=recent_timestamp,
        )

        # Document 4: Old but already completed (should not timeout)
        doc4 = Document(
            userId=user.id,
            source="user",
            sizeInKiloBytes=100,
            s3Key="test4.pdf",
            s3Bucket="test-bucket",
            fileExtension="pdf",
            originalFileName="test4.pdf",
            status=DocumentStatusType.processingCompleted,
            created_at=old_timestamp,
            updated_at=old_timestamp,
        )

        async_db_session.add_all([doc1, doc2, doc3, doc4])
        await async_db_session.flush()

        # Execute timeout_documents
        await document_service.timeout_documents()

        # Refresh documents to get updated values
        await async_db_session.refresh(doc1)
        await async_db_session.refresh(doc2)
        await async_db_session.refresh(doc3)
        await async_db_session.refresh(doc4)

        # Verify that old documents in saved/processing status were timed out
        assert doc1.status == DocumentStatusType.error
        assert doc1.errorMessage == "Processing timeout"

        assert doc2.status == DocumentStatusType.error
        assert doc2.errorMessage == "Processing timeout"

        # Verify that recent or already completed documents were not affected
        assert doc3.status == DocumentStatusType.saved
        assert doc3.errorMessage is None

        assert doc4.status == DocumentStatusType.processingCompleted
        assert doc4.errorMessage is None

    @pytest.mark.asyncio
    async def test_get_presigned_urls_of_chat_documents(self, async_db_session: AsyncSession, ai_engine, mocker):
        from src.db_models.message import Message
        from src.db_models.chat import Chat
        from src.db_models.document import Document
        from unittest.mock import AsyncMock, patch

        # Setup test user
        user = User(
            id=300,
            email="<EMAIL>",
            clerkId="clerk_chat_user",
            firstName="Chat",
            lastName="Tester",
        )
        async_db_session.add(user)
        await async_db_session.flush()

        # Mock the static method get_presigned_url_from_s3
        with patch.object(
            DocumentService,
            "get_presigned_url_from_s3",
            return_value="https://test-bucket.s3.amazonaws.com/presigned-url",
        ) as mock_get_presigned_url:
            # Setup document service
            mock_aioboto3_session = AsyncMock()
            mock_property_service = AsyncMock()
            mock_property_service.get_first_property_for_user.return_value = None
            document_service = DocumentService(
                async_db_session, mock_property_service, mock_aioboto3_session, ai_engine
            )

            # Create a test chat
            chat = Chat(id=1, userId=user.id, title="Test Chat")
            async_db_session.add(chat)
            await async_db_session.flush()

            # Create a test message first
            message = Message(
                chatId=chat.id, userId=user.id, content="Test message with documents", type="text", senderType="user"
            )
            async_db_session.add(message)
            await async_db_session.flush()

            # Create test documents and associate them with the message
            db_document1 = Document(
                userId=user.id,
                source="user",
                sizeInKiloBytes=2,
                s3Key="user=300/test1.pdf",
                s3Bucket="test-bucket",
                fileExtension="pdf",
                originalFileName="test1.pdf",
                browserMimeType="application/pdf",
                status=DocumentStatusType.saved,
                messageId=message.id,
            )

            db_document2 = Document(
                userId=user.id,
                source="user",
                sizeInKiloBytes=2,
                s3Key="user=300/test2.jpg",
                s3Bucket="test-bucket",
                fileExtension="jpg",
                originalFileName="test2.jpg",
                browserMimeType="image/jpeg",
                status=DocumentStatusType.saved,
                messageId=message.id,
            )

            async_db_session.add_all([db_document1, db_document2])
            await async_db_session.flush()

            # Test getting presigned URLs for chat documents
            presigned_urls = await document_service.get_presigned_urls_of_chat_documents(chat.id)

            # Verify that we got URLs for both documents
            assert len(presigned_urls) == 2
            assert all(isinstance(url, str) for url in presigned_urls)
            assert all(url == "https://test-bucket.s3.amazonaws.com/presigned-url" for url in presigned_urls)

            # Verify static method was called correctly
            assert mock_get_presigned_url.call_count == 2

    @pytest.mark.asyncio
    async def test_get_presigned_urls_of_chat_documents_empty_chat(
        self, async_db_session: AsyncSession, ai_engine, mocker
    ):
        from src.db_models.chat import Chat
        from unittest.mock import AsyncMock, patch

        # Setup test user
        user = User(
            id=301,
            email="<EMAIL>",
            clerkId="clerk_empty_chat_user",
            firstName="Empty",
            lastName="Chat",
        )
        async_db_session.add(user)
        await async_db_session.flush()

        # Mock the static method get_presigned_url_from_s3
        with patch.object(DocumentService, "get_presigned_url_from_s3") as mock_get_presigned_url:
            # Setup document service
            mock_aioboto3_session = AsyncMock()
            mock_property_service = AsyncMock()
            mock_property_service.get_first_property_for_user.return_value = None
            document_service = DocumentService(
                async_db_session, mock_property_service, mock_aioboto3_session, ai_engine
            )

            # Create a test chat with no documents
            chat = Chat(id=2, userId=user.id, title="Empty Chat")
            async_db_session.add(chat)
            await async_db_session.flush()

            # Test getting presigned URLs for chat with no documents
            presigned_urls = await document_service.get_presigned_urls_of_chat_documents(chat.id)

            # Verify that we got an empty list
            assert presigned_urls == []

            # Verify static method was not called
            mock_get_presigned_url.assert_not_called()

    @pytest.mark.asyncio
    async def test_get_presigned_urls_of_chat_documents_with_s3_error(
        self, async_db_session: AsyncSession, ai_engine, mocker
    ):
        from src.db_models.message import Message
        from src.db_models.chat import Chat
        from src.db_models.document import Document
        from unittest.mock import AsyncMock, patch

        # Setup test user
        user = User(
            id=302,
            email="<EMAIL>",
            clerkId="clerk_s3_error_user",
            firstName="S3Error",
            lastName="Tester",
        )
        async_db_session.add(user)
        await async_db_session.flush()

        # Mock the static method to simulate S3 error
        with patch.object(
            DocumentService, "get_presigned_url_from_s3", side_effect=Exception("S3 connection failed")
        ) as mock_get_presigned_url:
            # Setup document service
            mock_aioboto3_session = AsyncMock()
            mock_property_service = AsyncMock()
            mock_property_service.get_first_property_for_user.return_value = None
            document_service = DocumentService(
                async_db_session, mock_property_service, mock_aioboto3_session, ai_engine
            )

            # Create a test chat
            chat = Chat(id=3, userId=user.id, title="S3 Error Chat")
            async_db_session.add(chat)
            await async_db_session.flush()

            # Create a test message first
            message = Message(
                chatId=chat.id,
                userId=user.id,
                content="Test message with document that will fail",
                type="text",
                senderType="user",
            )
            async_db_session.add(message)
            await async_db_session.flush()

            # Create a test document and associate it with the message
            db_document = Document(
                userId=user.id,
                source="user",
                sizeInKiloBytes=100,
                s3Key="user=302/error-test.pdf",
                s3Bucket="test-bucket",
                fileExtension="pdf",
                originalFileName="error-test.pdf",
                browserMimeType="application/pdf",
                status=DocumentStatusType.saved,
                messageId=message.id,
            )
            async_db_session.add(db_document)
            await async_db_session.flush()

            # Mock logger to verify error logging
            mock_logger = mocker.patch("src.services.documents.logger")
            mock_sentry = mocker.patch("src.services.documents.sentry_capture_exception")

            # Test getting presigned URLs when S3 fails
            presigned_urls = await document_service.get_presigned_urls_of_chat_documents(chat.id)

            # Verify that we got an empty list due to S3 error
            assert presigned_urls == []

            # Verify that error was logged
            mock_logger.error.assert_called_once()
            assert "Failed to generate presigned URL for document" in mock_logger.error.call_args[0][0]

            # Verify that exception was sent to Sentry
            mock_sentry.assert_called_once()

            # Verify static method was called
            mock_get_presigned_url.assert_called_once()

    @pytest.mark.asyncio
    async def test_get_presigned_urls_of_chat_documents_nonexistent_chat(
        self, async_db_session: AsyncSession, ai_engine, mocker
    ):
        from unittest.mock import AsyncMock, patch

        # Mock the static method get_presigned_url_from_s3
        with patch.object(DocumentService, "get_presigned_url_from_s3") as mock_get_presigned_url:
            # Setup document service
            mock_aioboto3_session = AsyncMock()
            mock_property_service = AsyncMock()
            mock_property_service.get_first_property_for_user.return_value = None
            document_service = DocumentService(
                async_db_session, mock_property_service, mock_aioboto3_session, ai_engine
            )

            # Test getting presigned URLs for non-existent chat
            presigned_urls = await document_service.get_presigned_urls_of_chat_documents(99999)

            # Verify that we got an empty list
            assert presigned_urls == []

            # Verify static method was not called
            mock_get_presigned_url.assert_not_called()
