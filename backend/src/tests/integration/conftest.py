from os import environ

environ["CRYSTAL_ROOF_API_KEY"] = "DEMO"
environ["OS_DATA_PLACES_API_KEY"] = "TEST"
environ["IDEALPOSTCODES_API_KEY"] = "TEST"
environ["CLERK_PEM_PUBLIC_KEY"] = "TEST"
environ["CLERK_API_KEY"] = "TEST"
environ["CLERK_WEBHOOK_SECRET"] = "whsec_TEST"
environ["CLERK_PERMITTED_ORIGINS"] = "http://localhost:8000"
environ["AI_ENGINE_API_KEY"] = "TEST"
environ["ADMIN_API_KEY"] = "TEST_ADMIN_API_KEY"
environ["AI_ENGINE_API_BASE_URL"] = "http://localhost:8000"
environ["DOCUMENTS_S3_BUCKET_NAME"] = "test-bucket"
environ["SENDGRID_API_KEY"] = "TEST"
environ["SENDGRID_DEFAULT_FROM_EMAIL"] = "<EMAIL>"
environ["STAGE"] = "test"
environ["HUBSPOT_API_KEY"] = "hubspot-test"
DATABASE_URL = environ.get("DATABASE_URL")
if not DATABASE_URL:
    DATABASE_URL = "test-user:password@127.0.0.1:5433/test_db"
    environ["DATABASE_URL"] = DATABASE_URL
SQLALCHEMY_DATABASE_URL = f"postgresql+psycopg://{DATABASE_URL}"

from src.services.source_links import SourceLinksService
from src.integrations.idealpostcodes import ResolvedAddress
from src.services.coordinates import CoordinateTransformer
from src.services.properties import PropertyService
from typing import Any, Callable, AsyncGenerator, Generator
from unittest.mock import AsyncMock
import pytest
import pytest_asyncio
from faker import Faker
from fastapi.testclient import TestClient
from sqlalchemy import text
from sqlalchemy.ext.asyncio import (
    AsyncSession,
    create_async_engine,
    AsyncConnection,
)
from src.dependencies import get_sendgrid_service
from src.db_models import BaseModel
from src.main import app

fake = Faker()


@pytest.fixture(scope="session")
def client() -> Generator[TestClient, Any, None]:
    with TestClient(app) as _client:
        app.dependency_overrides[get_sendgrid_service] = lambda: AsyncMock()
        yield _client
    app.dependency_overrides = {}


def override_dependency(dependency: Callable[..., Any], mocked_response: Any) -> None:
    app.dependency_overrides[dependency] = lambda: mocked_response


@pytest_asyncio.fixture
async def engine():
    engine = create_async_engine(SQLALCHEMY_DATABASE_URL)
    yield engine
    await engine.dispose()


@pytest_asyncio.fixture
async def connection(engine) -> AsyncGenerator[AsyncConnection, None]:
    async with engine.connect() as connection:
        yield connection


@pytest_asyncio.fixture
async def create(engine):
    async with engine.begin() as conn:
        await conn.run_sync(BaseModel.metadata.create_all)
    yield
    async with engine.begin() as conn:
        await conn.run_sync(BaseModel.metadata.drop_all)

        def drop_tables(c):
            c.execute(text("DROP TABLE IF EXISTS alembic_version"))

        await conn.run_sync(drop_tables)


@pytest_asyncio.fixture
async def async_db_session(connection: AsyncConnection, create) -> AsyncGenerator[AsyncSession, None]:
    async_session = AsyncSession(bind=connection, join_transaction_mode="create_savepoint", expire_on_commit=False)

    yield async_session
    await async_session.close()


@pytest_asyncio.fixture
async def property_service(async_db_session: AsyncSession, source_link_service: SourceLinksService):
    coordinate_transformer = CoordinateTransformer()
    land_registry_api_mock = AsyncMock()
    land_registry_api_mock.get_property_type.return_value = "flat-maisonette"
    idealpostcodes_api_mock = AsyncMock()
    idealpostcodes_api_mock.resolve_address.return_value = (
        ResolvedAddress(
            id="paf_23833521",
            line_1="22 Eardley Crescent",
            line_2="",
            line_3="",
            post_town="London",
            postcode="SW5 9JZ",
            county="London",
            country="England",
            uprn="217025320",
            udprn=23833521,
            thoroughfare="Eardley Crescent",
            building_number="22",
            building_name="",
            sub_building_name="",
            latitude=51.4885502,
            longitude=-0.1953659,
            umprn=23833522,
        ),
        {
            "postcode": "SW5 9JZ",
            "postcode_inward": "9JZ",
            "postcode_outward": "SW5",
            "post_town": "London",
            "dependant_locality": "",
            "double_dependant_locality": "",
            "thoroughfare": "Eardley Crescent",
            "dependant_thoroughfare": "",
            "building_number": "22",
            "building_name": "",
            "sub_building_name": "",
            "po_box": "",
            "department_name": "",
            "organisation_name": "",
            "udprn": 23833521,
            "postcode_type": "S",
            "su_organisation_indicator": "",
            "delivery_point_suffix": "1J",
            "line_1": "22 Eardley Crescent",
            "line_2": "",
            "line_3": "",
            "premise": "22",
            "longitude": -0.1953659,
            "latitude": 51.4885502,
            "eastings": 525390,
            "northings": 178166,
            "country": "England",
            "traditional_county": "Greater London",
            "administrative_county": "",
            "postal_county": "London",
            "county": "London",
            "district": "Kensington and Chelsea",
            "ward": "Earl's Court",
            "uprn": "217025320",
            "id": "paf_23833521",
            "country_iso": "GBR",
            "country_iso_2": "GB",
            "county_code": "",
            "language": "en",
            "umprn": "23833522",
            "dataset": "paf",
        },
    )
    return PropertyService(
        async_db_session,
        coordinate_transformer,
        idealpostcodes_api=idealpostcodes_api_mock,
        land_registry_api=land_registry_api_mock,
        source_link_service=source_link_service,
    )


@pytest.fixture
async def source_link_service(async_db_session: AsyncSession):
    return SourceLinksService(async_db_session)
