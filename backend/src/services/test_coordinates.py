from typing import Tuple

import pytest

from src.services.coordinates import CoordinateTransformer


@pytest.mark.parametrize(
    "test_input,expected",
    [
        ((391654.39, 174902.5), (51.4730217, -2.1215437)),
        ((503545, 307663), (52.65683459, -0.47062593)),
        ((560489, 148573), (51.21380819, 0.29644525)),
        ((369404, 90881), (50.71673613, -2.43474621)),
        ((385487, 168785), (51.41788945, -2.21008703)),
    ],
)
def test_transform_from_27700_to_4326(test_input: <PERSON><PERSON>[float, float], expected: <PERSON><PERSON>[float, float]):
    result = CoordinateTransformer.transform_from_27700_to_4326(*test_input)
    assert result == pytest.approx(expected, rel=1e-4)
