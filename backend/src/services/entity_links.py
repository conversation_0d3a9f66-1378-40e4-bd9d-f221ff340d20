from typing import Type, TypeVar
from sqlalchemy import delete, or_, and_, select
from sqlalchemy.dialects.postgresql import insert
from sqlalchemy.ext.asyncio import AsyncSession

from src.db_models.appliance import Appliance
from src.db_models.chat import Chat
from src.db_models.document import Document
from src.db_models.entity_link import EntityLink
from src.db_models.insurance import Insurance
from src.db_models.job import Job
from src.db_models.legal import Legal
from src.db_models.project import Project
from src.db_models.todo import Todo
from src.db_models.user import User
from src.schemas import (
    LinkedEntity,
    LinkedEntityInfo,
    LinkedEntityType,
    UserLinkedEntityInfo,
    ChatLinkedEntityInfo,
    ProjectLinkedEntityInfo,
    DocumentLinkedEntityInfo,
    TodoLinkedEntityInfo,
    ApplianceLinkedEntityInfo,
    InsuranceLinkedEntityInfo,
    JobLinkedEntityInfo,
    LegalLinkedEntityInfo,
)

T = TypeVar("T")


class EntityLinksService:
    """
    Links entities together (unidirectionally).
    """

    def __init__(self, db: AsyncSession):
        self._db = db

    @staticmethod
    def _order_entities(a: LinkedEntity, b: LinkedEntity) -> tuple[str, int, str, int]:
        if (a.entityType.value, a.id) <= (b.entityType.value, b.id):
            return a.entityType.value, a.id, b.entityType.value, b.id
        else:
            return b.entityType.value, b.id, a.entityType.value, a.id

    async def _validate_entity_exists(self, entity: LinkedEntity):
        """Validate that an entity exists in the database"""
        match entity.entityType:
            case LinkedEntityType.users:
                await self._get_entity_or_raise(User, entity.id)
            case LinkedEntityType.chats:
                await self._get_entity_or_raise(Chat, entity.id)
            case LinkedEntityType.projects:
                await self._get_entity_or_raise(Project, entity.id)
            case LinkedEntityType.documents:
                await self._get_entity_or_raise(Document, entity.id)
            case LinkedEntityType.todos:
                await self._get_entity_or_raise(Todo, entity.id)
            case LinkedEntityType.appliances:
                await self._get_entity_or_raise(Appliance, entity.id)
            case LinkedEntityType.insurances:
                await self._get_entity_or_raise(Insurance, entity.id)
            case LinkedEntityType.jobs:
                await self._get_entity_or_raise(Job, entity.id)
            case LinkedEntityType.legals:
                await self._get_entity_or_raise(Legal, entity.id)
            case _:
                raise Exception(f"Unsupported entity type: {entity.entityType}")

    async def link(self, a: LinkedEntity, b: LinkedEntity):
        await self._validate_entity_exists(a)
        await self._validate_entity_exists(b)
        type_a, id_a, type_b, id_b = self._order_entities(a, b)
        await self._db.execute(
            insert(EntityLink).values(typeA=type_a, idA=id_a, typeB=type_b, idB=id_b).on_conflict_do_nothing()
        )

    async def unlink(self, a: LinkedEntity, b: LinkedEntity):
        type_a, id_a, type_b, id_b = self._order_entities(a, b)

        await self._db.execute(
            delete(EntityLink).where(
                and_(
                    EntityLink.typeA == type_a,
                    EntityLink.idA == id_a,
                    EntityLink.typeB == type_b,
                    EntityLink.idB == id_b,
                )
            )
        )

    async def unlink_all(self, entity: LinkedEntity):
        await self._db.execute(
            delete(EntityLink).where(
                or_(
                    and_(
                        EntityLink.typeA == entity.entityType.value,
                        EntityLink.idA == entity.id,
                    ),
                    and_(EntityLink.typeB == entity.entityType.value, EntityLink.idB == entity.id),
                )
            )
        )

    async def _get_entity_or_raise(self, model_class: Type[T], entity_id: int) -> T:
        entity = await self._db.scalar(select(model_class).where(entity_id == model_class.id))
        if entity is None:
            raise ValueError(f"{model_class.__name__} with id {entity_id} not found")
        return entity

    async def _get_linked_entity_info(self, linked_type: LinkedEntityType, linked_id: int) -> LinkedEntityInfo:
        match linked_type:
            case LinkedEntityType.users:
                user = await self._get_entity_or_raise(User, linked_id)
                return UserLinkedEntityInfo(id=linked_id, firstName=user.firstName, lastName=user.lastName)
            case LinkedEntityType.chats:
                chat = await self._get_entity_or_raise(Chat, linked_id)
                return ChatLinkedEntityInfo(id=linked_id, title=chat.title)
            case LinkedEntityType.projects:
                project = await self._get_entity_or_raise(Project, linked_id)
                return ProjectLinkedEntityInfo(id=linked_id, headline=project.headline)
            case LinkedEntityType.documents:
                document = await self._get_entity_or_raise(Document, linked_id)
                return DocumentLinkedEntityInfo(id=linked_id, originalFileName=document.originalFileName)
            case LinkedEntityType.todos:
                todo = await self._get_entity_or_raise(Todo, linked_id)
                return TodoLinkedEntityInfo(id=linked_id, name=todo.name)
            case LinkedEntityType.appliances:
                appliance = await self._get_entity_or_raise(Appliance, linked_id)
                return ApplianceLinkedEntityInfo(
                    id=linked_id, type=appliance.type, brand=appliance.brand, model=appliance.model
                )
            case LinkedEntityType.insurances:
                insurance = await self._get_entity_or_raise(Insurance, linked_id)
                return InsuranceLinkedEntityInfo(
                    id=linked_id, policyProvider=insurance.policyProvider, policyNumber=insurance.policyNumber
                )
            case LinkedEntityType.jobs:
                job = await self._get_entity_or_raise(Job, linked_id)
                return JobLinkedEntityInfo(id=linked_id, headline=job.headline)
            case LinkedEntityType.legals:
                legal = await self._get_entity_or_raise(Legal, linked_id)
                return LegalLinkedEntityInfo(id=linked_id, type=legal.type, aiShortSummary=legal.aiShortSummary)
            case _:
                raise Exception(f"Unsupported entity type: {linked_type}")

    async def _to_linked_entity_info(self, base_entity: LinkedEntity, link: EntityLink) -> LinkedEntityInfo:
        if link.typeA == base_entity.entityType.value and link.idA == base_entity.id:
            linked_type = LinkedEntityType(link.typeB)
            linked_id = link.idB
        else:
            linked_type = LinkedEntityType(link.typeA)
            linked_id = link.idA
        return await self._get_linked_entity_info(linked_type, linked_id)

    async def get_links(self, entity: LinkedEntity) -> list[LinkedEntityInfo]:
        results = await self._db.scalars(
            select(EntityLink).where(
                or_(
                    and_(
                        EntityLink.typeA == entity.entityType.value,
                        EntityLink.idA == entity.id,
                    ),
                    and_(EntityLink.typeB == entity.entityType.value, EntityLink.idB == entity.id),
                )
            )
        )
        return [await self._to_linked_entity_info(entity, link) for link in results]
