import logging
from typing import TypeVar, <PERSON>, Any

from sqlalchemy import func, select
from sqlalchemy.dialects.postgresql import insert
from sqlalchemy.ext.asyncio import AsyncSession

from src.db_models import BaseModel
from src.db_models.appliance import Appliance
from src.db_models.chat import Chat
from src.db_models.document import Document
from src.db_models.source_link import SourceLink, SourceType
from src.db_models.user import User
from src.schemas import FieldSource, ChatSource, DocumentSource, UserSource

logger = logging.getLogger("uvicorn")

Destination = TypeVar("Destination", bound=BaseModel)


class SourceLinksService:
    def __init__(self, db: AsyncSession):
        self._db = db

    @staticmethod
    def __select(src_type: SourceType, src_id: int):
        match src_type:
            case SourceType.chat:
                return select(Chat).where(Chat.id == src_id)
            case SourceType.document:
                return select(Document).where(Document.id == src_id)
            case SourceType.userInput:
                return select(User).where(User.id == src_id)
            case _:
                raise ValueError(f"Unsupported source type: {src_type}")

    async def __does_source_exist(self, src_type: SourceType, src_id: int) -> bool:
        return (await self._db.execute(self.__select(src_type, src_id))).scalar() is not None

    async def link_creation_from_source(
        self, destination: Destination, source_type: SourceType, source_id: int, create_command: dict[str, Any]
    ):
        await self.__link_command(destination, source_type, source_id, create_command)

    async def filter_command_from_source(
        self, destination: Destination, source_type: SourceType, source_id: int, command: dict[str, Any]
    ) -> dict[str, Any]:
        if not await self.__does_source_exist(source_type, source_id):
            raise ValueError(f"Source of type {source_type} with ID {source_id} does not exist.")

        if source_type == SourceType.chat or source_type == SourceType.document:
            user_input_fields = (
                await self._db.scalars(
                    select(SourceLink).where(
                        SourceLink.destTable == destination.__tablename__,
                        SourceLink.destId == destination.id,
                        SourceLink.srcType == SourceType.userInput,
                    )
                )
            ).all()
            for user_input_field in user_input_fields:
                if user_input_field.destField in command:
                    logger.info(f"User input field {user_input_field.destField} will not be overwritten.")
                    del command[user_input_field.destField]

        await self.__link_command(destination, source_type, source_id, command)
        return command

    async def __link_command(
        self, destination: Destination, source_type: SourceType, source_id: int, command: dict[str, Any]
    ):
        destination_fields = command.keys()
        for field in destination_fields:
            await self._db.execute(
                insert(SourceLink)
                .values(
                    destTable=destination.__tablename__,
                    destId=destination.id,
                    destField=field,
                    srcType=source_type,
                    srcId=source_id,
                )
                .on_conflict_do_update(
                    index_elements=["destTable", "destId", "destField"],
                    set_={"srcType": source_type, "srcId": source_id, "updated_at": func.now()},
                )
            )

    @staticmethod
    def __to_source_schema(
        source: Union[Chat, Document, User, Appliance],
    ) -> Union[ChatSource, DocumentSource, UserSource]:
        match source:
            case Chat():
                return ChatSource(id=source.id, title=source.title)
            case Document():
                return DocumentSource(id=source.id)
            case User():
                return UserSource(id=source.id)
            case _:
                raise ValueError(f"Unsupported source type: {type(source)}")

    async def get_source_links(self, destination: Destination) -> list[FieldSource]:
        source_links = await self._db.scalars(
            select(SourceLink).where(
                SourceLink.destTable == destination.__tablename__, SourceLink.destId == destination.id
            )
        )
        sources: dict[str, Union[Chat, Document, User, Appliance]] = {}
        result: list[FieldSource] = []
        for source_link in source_links.all():
            key = f"{source_link.srcType}_{source_link.srcId}"
            source = (
                sources[key]
                if key in sources
                else (await self._db.execute(self.__select(source_link.srcType, source_link.srcId))).scalar()
            )
            sources[key] = source
            result.append(FieldSource(destinationField=source_link.destField, source=self.__to_source_schema(source)))
        return result
