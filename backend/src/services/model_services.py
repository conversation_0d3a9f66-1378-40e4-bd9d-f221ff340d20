import logging
from typing import <PERSON><PERSON>

from sqlalchemy import select, Select
from sqlalchemy.ext.asyncio import AsyncSession

from src.db_models.booking import Booking
from src.db_models.user_type import UserType
from src.schemas import BookingCreate, UserTypeCreate

logger = logging.getLogger("uvicorn")


class UserTypeService:
    def __init__(self, db: AsyncSession):
        self._db = db

    def get_user_types(self) -> <PERSON><PERSON>[Select[tuple[UserType]], AsyncSession]:
        return select(UserType), self._db

    async def create_user_type(self, user_type: UserTypeCreate) -> UserType:
        db_user_type = UserType(name=user_type.type)
        self._db.add(db_user_type)
        return db_user_type


class BookingService:
    def __init__(self, db: AsyncSession):
        self._db = db

    def get_bookings(self) -> <PERSON><PERSON>[Select[tuple[Booking]], AsyncSession]:
        return select(Booking), self._db

    async def create_booking(self, booking: BookingCreate) -> Booking:
        db_booking = Booking(time=booking.time, address_id=booking.address_id)
        self._db.add(db_booking)
        return db_booking
