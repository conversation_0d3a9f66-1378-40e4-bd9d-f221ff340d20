import logging
from os import environ
from typing import Annotated

from fastapi import Depends, HTTPException, status, Request
from fastapi.security import HTTPAuthorizationCredentials, HTTPBearer

from src.db_models.user import User
from src.dependencies import get_user_service, get_clerk_api
from src.integrations.clerk import Clerk<PERSON><PERSON>, InvalidTokenError
from src.services.users import UserService

security = HTTPBearer()

logger = logging.getLogger("uvicorn")

AI_ENGINE_API_KEY = environ["AI_ENGINE_API_KEY"]
ADMIN_API_KEY = environ["ADMIN_API_KEY"]


def validate_jwt_token_and_get_users_clerk_id(
    credentials: Annotated[HTTPAuthorizationCredentials, Depends(security)],
    request: Request,
    clerk_api: ClerkAPI = Depends(get_clerk_api),
) -> str:
    try:
        token_payload = clerk_api.validate_request(request)
        return token_payload["sub"]
    except InvalidTokenError as e:
        logger.warning(f"Error decoding JWT token: {e}")
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Could not validate credentials",
            headers={"WWW-Authenticate": "Bearer"},
        )


def validate_jwt_token(token: str = Depends(validate_jwt_token_and_get_users_clerk_id)) -> None:
    return


async def get_current_user(
    user_clerk_id: str = Depends(validate_jwt_token_and_get_users_clerk_id),
    user_service: UserService = Depends(get_user_service),
) -> User:
    return await user_service.get_or_create_user_by_clerk_id(user_clerk_id)


def validate_ai_engine_token(
    credentials: Annotated[HTTPAuthorizationCredentials, Depends(security)],
) -> None:
    if credentials.credentials != AI_ENGINE_API_KEY:
        logger.warning("Invalid AI Engine API key provided.")
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid API key",
            headers={"WWW-Authenticate": "Bearer"},
        )
    return


def validate_admin_token(
    credentials: Annotated[HTTPAuthorizationCredentials, Depends(security)],
) -> None:
    if credentials.credentials != ADMIN_API_KEY:
        logger.warning("Invalid Admin API key provided.")
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid API key",
            headers={"WWW-Authenticate": "Bearer"},
        )
    return
