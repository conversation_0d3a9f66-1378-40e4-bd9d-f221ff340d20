from sqlalchemy import select
from sqlalchemy.ext.asyncio import AsyncSession

from src.db_models.appliance import Appliance
from src.db_models.relationships import UsersProperties
from src.db_models.source_link import SourceType
from src.db_models.user import User
from src.schemas import ApplianceCreate, ApplianceUpdate, ApplianceAiCreate, ApplianceAiUpdate
from src.services.properties import PropertyService
from src.services.source_links import SourceLinksService


class PropertyDoesNotExist(Exception):
    pass


class ApplianceDoesNotExist(Exception):
    pass


class ApplianceService:
    def __init__(self, db: AsyncSession, property_service: PropertyService, source_link_service: SourceLinksService):
        self._db = db
        self._property_service = property_service
        self._source_link_service = source_link_service

    @staticmethod
    def get_appliances_for_user_query(user: User):
        return (
            select(Appliance)
            .join(UsersProperties, Appliance.propertyId == UsersProperties.propertyId)
            .where(UsersProperties.userId == user.id)
        )

    async def get_appliance_by_user(self, appliance_id: int, user: User) -> Appliance:
        return await self._db.scalar(
            select(Appliance)
            .join(UsersProperties, Appliance.propertyId == UsersProperties.propertyId)
            .where(Appliance.id == appliance_id, UsersProperties.userId == user.id)
        )

    async def __get_appliance_by_id(self, appliance_id: int) -> Appliance:
        return await self._db.scalar(
            select(Appliance)
            .join(UsersProperties, Appliance.propertyId == UsersProperties.propertyId)
            .where(Appliance.id == appliance_id)
        )

    async def create_appliance_by_user(self, appliance_data: ApplianceCreate, user: User) -> Appliance:
        if not await self._property_service.does_property_belong_to_user(appliance_data.propertyId, user):
            raise PropertyDoesNotExist("The property does not exist.")

        appliance_dict = appliance_data.model_dump(exclude_unset=True)
        return await self.__create_appliance(appliance_dict, SourceType.userInput, user.id)

    async def __create_appliance(self, appliance_dict: dict, source_type: SourceType, source_id: int) -> Appliance:
        appliance = Appliance(**appliance_dict)
        self._db.add(appliance)
        await self._db.flush()
        await self._db.refresh(appliance)
        await self._source_link_service.link_creation_from_source(
            destination=appliance,
            source_type=source_type,
            source_id=source_id,
            create_command=appliance_dict,
        )
        return appliance

    async def create_appliance_by_ai(self, appliance_create: ApplianceAiCreate):
        prop = await self._property_service.get_first_or_create_empty_property_for_user(appliance_create.userId)
        appliance_dict = appliance_create.appliance.model_dump(exclude_unset=True, exclude={"userId"})
        appliance_dict["propertyId"] = prop.id
        return await self.__create_appliance(appliance_dict, appliance_create.srcType, appliance_create.srcId)

    async def update_appliance_by_user(self, appliance_id: int, command: ApplianceUpdate, user: User) -> Appliance:
        appliance = await self.get_appliance_by_user(appliance_id, user)
        if not appliance:
            raise ApplianceDoesNotExist("The appliance does not exist.")

        update_dict = command.model_dump(exclude_unset=True)
        return await self.__update_appliance(appliance, SourceType.userInput, user.id, update_dict)

    async def update_appliance_by_ai(self, appliance_id: int, command: ApplianceAiUpdate) -> Appliance:
        appliance = await self.__get_appliance_by_id(appliance_id)
        if not appliance:
            raise ApplianceDoesNotExist(f"Appliance id {appliance_id} does not exist.")

        update_dict = command.appliance.model_dump(exclude_unset=True)
        return await self.__update_appliance(appliance, command.srcType, command.srcId, update_dict)

    async def __update_appliance(
        self, appliance: Appliance, source_type: SourceType, source_id: int, update_dict: dict
    ) -> Appliance:
        update_data = await self._source_link_service.filter_command_from_source(
            destination=appliance, source_type=source_type, source_id=source_id, command=update_dict
        )
        for key, value in update_data.items():
            setattr(appliance, key, value)

        await self._db.flush()
        await self._db.refresh(appliance)

        return appliance
