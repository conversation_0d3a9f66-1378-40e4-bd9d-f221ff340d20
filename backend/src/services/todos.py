from sqlalchemy import select, delete
from sqlalchemy.ext.asyncio import AsyncSession

from src.db_models.source_link import SourceType
from src.db_models.todo import Todo, TodoType
from src.db_models.user import User
from src.schemas import TodoCreate, TodoShortInfo, TodoUpdate, TodoAiCreate, TodoAiUpdate, TodoFullInfo
from src.services.source_links import SourceLinksService


class TodoDoesNotExist(Exception):
    pass


class TodoService:
    def __init__(self, db: AsyncSession, source_link_service: SourceLinksService):
        self._db = db
        self._source_link_service = source_link_service

    @staticmethod
    def get_todos_for_user_query(user: User):
        return select(Todo).where(Todo.userId == user.id)

    async def __get_todo_by_user(self, todo_id: int, user: User) -> Todo:
        return await self._db.scalar(select(Todo).where(Todo.userId == user.id, Todo.id == todo_id))

    async def __create_todo(
        self, todo_dict: dict, user: User, todo_type: TodoType, source_type: SourceType, source_id: int
    ) -> Todo:
        todo = Todo(**todo_dict, userId=user.id, type=todo_type)
        self._db.add(todo)
        await self._db.flush()
        await self._db.refresh(todo)

        await self._source_link_service.link_creation_from_source(
            destination=todo,
            source_type=source_type,
            source_id=source_id,
            create_command=todo_dict,
        )

        return todo

    async def create_todo_by_user(self, todo_data: TodoCreate, user: User) -> Todo:
        todo_dict = todo_data.model_dump(exclude_unset=True)
        return await self.__create_todo(
            todo_dict=todo_dict,
            user=user,
            todo_type=TodoType.userCreated,
            source_type=SourceType.userInput,
            source_id=user.id,
        )

    async def create_todo_by_ai(self, todo_data: TodoAiCreate, user: User) -> Todo:
        todo_dict = todo_data.todo.model_dump(exclude_unset=True)
        return await self.__create_todo(
            todo_dict=todo_dict,
            user=user,
            todo_type=TodoType.systemCreated,
            source_type=todo_data.srcType,
            source_id=todo_data.srcId,
        )

    async def __update_todo(self, todo: Todo, source_type: SourceType, source_id: int, command_dict: dict) -> Todo:
        command = await self._source_link_service.filter_command_from_source(
            destination=todo,
            source_type=source_type,
            source_id=source_id,
            command=command_dict,
        )
        for key, value in command.items():
            setattr(todo, key, value)
        await self._db.flush()
        return todo

    async def update_todo_by_user(self, todo_id: int, todo_update: TodoUpdate, user: User) -> Todo:
        todo = await self.__get_todo_by_user(todo_id, user)
        if not todo:
            raise TodoDoesNotExist("The ToDo does not exist.")
        return await self.__update_todo(
            todo=todo,
            source_type=SourceType.userInput,
            source_id=user.id,
            command_dict=todo_update.model_dump(exclude_unset=True),
        )

    async def update_todo_by_ai(self, todo_id: int, todo_update: TodoAiUpdate) -> Todo:
        todo = await self._db.scalar(select(Todo).where(Todo.id == todo_id))
        return await self.__update_todo(
            todo=todo,
            source_type=todo_update.srcType,
            source_id=todo_update.srcId,
            command_dict=todo_update.todo.model_dump(exclude_unset=True),
        )

    async def delete_todo_by_user(self, todo_id: int, user: User):
        if not await self.__get_todo_by_user(todo_id, user):
            raise TodoDoesNotExist("The ToDo does not exist.")

        await self._db.execute(delete(Todo).where(Todo.id == todo_id))

    async def accept_todo_by_user(self, todo_id: int, user: User):
        todo = await self._db.scalar(
            select(Todo).where(Todo.id == todo_id, Todo.userId == user.id, Todo.type == TodoType.systemCreated)
        )
        if not todo:
            raise TodoDoesNotExist(f"A system created ToDo of id {todo_id} does not exist.")
        command = await self._source_link_service.filter_command_from_source(
            destination=todo,
            source_type=SourceType.userInput,
            source_id=user.id,
            command={"type": TodoType.systemAccepted},
        )
        todo.type = command["type"]
        await self._db.flush()

    async def get_todo(self, todo_id: int, user: User) -> TodoFullInfo:
        todo = await self.__get_todo_by_user(todo_id, user)
        if not todo:
            raise TodoDoesNotExist(f"A ToDo of id {todo_id} does not exist.")
        sources = await self._source_link_service.get_source_links(todo)
        todo_data = TodoShortInfo.model_validate(todo, from_attributes=True)
        return TodoFullInfo(**todo_data.model_dump(), sources=sources)
