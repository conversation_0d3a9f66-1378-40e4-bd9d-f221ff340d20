from os import environ

import aioboto3
from fastapi import Depends
from sqlalchemy.ext.asyncio import AsyncSession

from src.database import sessionmanager
from src.integrations.aiengine import AIEngineAPI
from src.integrations.clerk import <PERSON><PERSON><PERSON>
from src.integrations.crystalroof import <PERSON><PERSON><PERSON><PERSON><PERSON>
from src.integrations.hubspot import Hub<PERSON><PERSON><PERSON><PERSON>
from src.integrations.idealpostcodes import IdealP<PERSON><PERSON><PERSON><PERSON>
from src.integrations.landregistry import <PERSON>RegistryAPI
from src.integrations.osdataplaces import OSDataPlacesAPI
from src.integrations.sendgrid import SendgridService
from src.services.appliances import ApplianceService
from src.services.coordinates import CoordinateTransformer
from src.services.documents import DocumentService
from src.services.entity_links import EntityLinksService
from src.services.hubspot_sync import HubSpotSyncService
from src.services.jobs import JobService
from src.services.messages import MessageService
from src.services.properties import PropertyService
from src.services.source_links import SourceLinksService
from src.services.todos import TodoService
from src.services.users import UserService


async def get_db_session():
    async with sessionmanager.session() as session:
        yield session


crystal_roof_api = CrystalRoofAPI(environ["CRYSTAL_ROOF_API_KEY"])


async def get_crystalroof_api():
    return crystal_roof_api


land_registry_api = LandRegistryAPI()


async def get_land_registry_api():
    return land_registry_api


os_data_places_api = OSDataPlacesAPI(environ["OS_DATA_PLACES_API_KEY"])


async def get_os_data_places_api():
    return os_data_places_api


idealpostcodes_api = IdealPostcodesAPI(environ["IDEALPOSTCODES_API_KEY"])


async def get_idealpostcodes_api():
    return idealpostcodes_api


clerk_api = ClerkAPI(
    api_key=environ["CLERK_API_KEY"],
    public_key=environ["CLERK_PEM_PUBLIC_KEY"],
    permitted_origins=(
        environ["CLERK_PERMITTED_ORIGINS"].split(",") if environ.get("CLERK_PERMITTED_ORIGINS") else None
    ),
    permitted_origin_regex=environ.get("CLERK_PERMITTED_ORIGIN_REGEX", None),
)


async def get_clerk_api():
    return clerk_api


sendgrid_service = SendgridService(
    environ["SENDGRID_API_KEY"], environ["SENDGRID_DEFAULT_FROM_EMAIL"], environ["STAGE"]
)


def get_sendgrid_service():
    return sendgrid_service


hubspot_api = HubSpotAPI(environ["HUBSPOT_API_KEY"])


def get_hubspot_api():
    return hubspot_api


def get_source_link_service(
    db: AsyncSession = Depends(get_db_session),
) -> SourceLinksService:
    return SourceLinksService(db=db)


ai_engine_api = AIEngineAPI(environ["AI_ENGINE_API_KEY"], environ["AI_ENGINE_API_BASE_URL"])


def get_ai_engine_api():
    return ai_engine_api


def get_source_links_service(
    db: AsyncSession = Depends(get_db_session),
) -> SourceLinksService:
    return SourceLinksService(db=db)


def get_property_service(
    db: AsyncSession = Depends(get_db_session),
    my_land_registry_api=Depends(get_land_registry_api),
    my_idealpostcodes_api=Depends(get_idealpostcodes_api),
    source_link_service: SourceLinksService = Depends(get_source_links_service),
) -> PropertyService:
    return PropertyService(
        db=db,
        coordinate_transformer=CoordinateTransformer(),
        land_registry_api=my_land_registry_api,
        idealpostcodes_api=my_idealpostcodes_api,
        source_link_service=get_source_link_service(db),
    )


def get_message_service(
    db: AsyncSession = Depends(get_db_session),
    my_property_service: PropertyService = Depends(get_property_service),
    my_ai_engine: AIEngineAPI = Depends(get_ai_engine_api),
) -> MessageService:
    return MessageService(db=db, property_service=my_property_service, ai_engine_api=my_ai_engine)


aioboto3_session = aioboto3.Session()


def get_aioboto3_session():
    return aioboto3_session


def get_document_service(
    db: AsyncSession = Depends(get_db_session),
    my_property_service: PropertyService = Depends(get_property_service),
    my_ai_engine: AIEngineAPI = Depends(get_ai_engine_api),
    my_aioboto3_session=Depends(get_aioboto3_session),
) -> DocumentService:
    return DocumentService(
        db=db, property_service=my_property_service, ai_engine=my_ai_engine, aioboto3_session=my_aioboto3_session
    )


def get_hubspot_sync_service(
    db: AsyncSession = Depends(get_db_session),
    document_service: DocumentService = Depends(get_document_service),
) -> HubSpotSyncService:
    return HubSpotSyncService(db=db, hubspot_api=hubspot_api, document_service=document_service)


def get_user_service(
    db: AsyncSession = Depends(get_db_session),
    my_clerk_api: ClerkAPI = Depends(get_clerk_api),
    my_sendgrid_service=Depends(get_sendgrid_service),
    hubspot_sync_service: HubSpotSyncService = Depends(get_hubspot_sync_service),
) -> UserService:
    return UserService(
        db=db,
        clerk_api=my_clerk_api,
        sendgrid_service=my_sendgrid_service,
        hubspot_sync_service=hubspot_sync_service,
    )


def get_job_service(
    db: AsyncSession = Depends(get_db_session),
    my_property_service: PropertyService = Depends(get_property_service),
    my_document_service: DocumentService = Depends(get_document_service),
    my_sendgrid_service=Depends(get_sendgrid_service),
    hubspot_sync_service: HubSpotSyncService = Depends(get_hubspot_sync_service),
) -> JobService:
    return JobService(
        db=db,
        property_service=my_property_service,
        document_service=my_document_service,
        sendgrid_service=my_sendgrid_service,
        hubspot_sync_service=hubspot_sync_service,
    )


def get_appliance_service(
    db: AsyncSession = Depends(get_db_session),
    my_property_service: PropertyService = Depends(get_property_service),
    my_source_link_service: SourceLinksService = Depends(get_source_links_service),
) -> ApplianceService:
    return ApplianceService(db=db, property_service=my_property_service, source_link_service=my_source_link_service)


def get_todo_service(
    db: AsyncSession = Depends(get_db_session),
    my_source_link_service: SourceLinksService = Depends(get_source_links_service),
) -> TodoService:
    return TodoService(db=db, source_link_service=my_source_link_service)


def get_entity_link_service(
    db: AsyncSession = Depends(get_db_session),
) -> EntityLinksService:
    return EntityLinksService(db=db)
