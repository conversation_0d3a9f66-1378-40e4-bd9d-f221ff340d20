from enum import Enum

from sqlalchemy import Integer, UniqueConstraint, Index, String
from sqlalchemy.orm import mapped_column, Mapped

from .base import BaseModel


class LinkedEntityType(Enum):
    users = "users"
    jobs = "jobs"
    appliances = "appliances"
    chats = "chats"
    documents = "documents"
    insurances = "insurances"
    legals = "legals"
    projects = "projects"
    todos = "todos"


class EntityLink(BaseModel):
    __tablename__ = "entity_links"

    id: Mapped[int] = mapped_column(Integer, primary_key=True, index=True)
    typeA: Mapped[LinkedEntityType] = mapped_column(String, index=True, nullable=False)
    idA: Mapped[int] = mapped_column(Integer, index=True, nullable=False)
    typeB: Mapped[LinkedEntityType] = mapped_column(String, index=True, nullable=False)
    idB: Mapped[int] = mapped_column(Integer, index=True, nullable=False)
    __table_args__ = (UniqueConstraint("typeA", "idA", "typeB", "idB"),
                      Index("tablesIds", "typeA", "idA", "typeB", "idB"),
                      Index("typeIdB", "typeB", "idB"),
                      Index("typeIdA", "typeA", "idA"))
