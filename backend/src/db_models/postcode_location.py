from sqlalchemy import Integer, Numeric
from sqlalchemy.orm import Mapped, mapped_column

from .base import BaseModel
from decimal import Decimal


class PostcodeLocation(BaseModel):
    __tablename__ = "postcode_locations"

    id: Mapped[int] = mapped_column(Integer, primary_key=True, index=True)
    postcode: Mapped[str] = mapped_column(unique=True, index=True)
    latitude: Mapped[Decimal] = mapped_column(Numeric(precision=8, scale=6))
    longitude: Mapped[Decimal] = mapped_column(Numeric(precision=8, scale=6))
