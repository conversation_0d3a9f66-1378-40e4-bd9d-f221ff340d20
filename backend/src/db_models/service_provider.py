from typing import List, Optional

from sqlalchemy import Integer
from sqlalchemy.orm import mapped_column, Mapped, relationship

from .base import BaseModel


class ServiceProvider(BaseModel):
    __tablename__ = "service_providers"

    id: Mapped[int] = mapped_column(Integer, primary_key=True, index=True)
    name: Mapped[str]
    onboardingStage: Mapped[str]
    haRelationship: Mapped[str]
    businessAddress: Mapped[str]
    postCode: Mapped[str]
    companyType: Mapped[str]
    insuranceDetails: Mapped[str]
    qualifications: Mapped[list[str]]
    idCheck: Mapped[str]

    quotes: Mapped[List["Quote"]] = relationship(back_populates="serviceProvider")
    jobs: Mapped[List["Job"]] = relationship(back_populates="serviceProvider")
    professionals: Mapped[List["Professional"]] = relationship(back_populates="serviceProvider")

    # the fields below should be calculated dynamically
    # noOfQuotesForHa: Mapped[int]
    # noOfJobsCompletedWithHa: Mapped[int]
    # haUserFeedbackAverageScore: Mapped[float]
    # haRating: Mapped[float]
    # timeToQuote: Mapped[float]
    # numberOfComplaintsAndDisputes: Mapped[int]
    email: Mapped[Optional[str]]
    phoneNumber: Mapped[Optional[str]]
    nextSteps: Mapped[Optional[str]]
    source: Mapped[Optional[str]]
    mainServiceCategories: Mapped[Optional[list[str]]]
    serviceSubCategories: Mapped[Optional[list[str]]]
    urgentOohService: Mapped[Optional[str]]
    postcodesServiced: Mapped[Optional[list[str]]]
    areasServiced: Mapped[Optional[list[str]]]
    companyName: Mapped[Optional[str]]
    teamSize: Mapped[Optional[int]]
    website: Mapped[Optional[str]]
    availability: Mapped[Optional[str]]
    phoneNumber: Mapped[Optional[str]]
    email: Mapped[Optional[str]]
    mainContactName: Mapped[Optional[str]]
    mainContactEmail: Mapped[Optional[str]]
    mainContactPhoneNumber: Mapped[Optional[str]]
    preferredCommunication: Mapped[Optional[str]]
    googleRating: Mapped[Optional[float]]
    tradingStandardsRating: Mapped[Optional[float]]
    trustpilotScore: Mapped[Optional[float]]
    whichTrustedRating: Mapped[Optional[float]]
    barkRating: Mapped[Optional[float]]
    trustatraderRating: Mapped[Optional[float]]
    mybuilderRating: Mapped[Optional[float]]
    trustmarkRating: Mapped[Optional[float]]
    checkatradeRating: Mapped[Optional[float]]
    checkatradeProfile: Mapped[Optional[str]]
    qualifications: Mapped[Optional[list[str]]]
    workGuarantee: Mapped[Optional[str]]
    paymentTerms: Mapped[Optional[str]]
    fees: Mapped[Optional[str]]
    notes: Mapped[Optional[str]]
    plumbing: Mapped[Optional[str]]
    plumbingFees: Mapped[Optional[str]]
    handyperson: Mapped[Optional[str]]
    handypersonFees: Mapped[Optional[str]]
    gasAndHeatingEngineer: Mapped[Optional[str]]
    gasAndHeatingFees: Mapped[Optional[str]]
    electrician: Mapped[Optional[str]]
    electricianFees: Mapped[Optional[str]]
    gardeningAndLandscaping: Mapped[Optional[str]]
    gardeningFees: Mapped[Optional[str]]
    cleaner: Mapped[Optional[str]]
    cleaningFees: Mapped[Optional[str]]
    propertySurvey: Mapped[Optional[str]]
    surveyFees: Mapped[Optional[str]]
