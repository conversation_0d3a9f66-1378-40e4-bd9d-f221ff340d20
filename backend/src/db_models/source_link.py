from enum import Enum
from typing import Optional

from sqlalchemy import Integer, UniqueConstraint, Index, String
from sqlalchemy.orm import mapped_column, Mapped

from .base import BaseModel


class SourceType(Enum):
    chat = "chat"
    document = "document"
    userInput = "userInput"


class SourceLink(BaseModel):
    __tablename__ = "source_links"

    id: Mapped[int] = mapped_column(Integer, primary_key=True, index=True)
    destTable: Mapped[str] = mapped_column(String, index=True, nullable=False)
    destId: Mapped[int] = mapped_column(Integer, index=True, nullable=False)
    destField: Mapped[str] = mapped_column(String, index=True, nullable=False)
    srcType: Mapped[SourceType]
    srcId: Mapped[Optional[int]]
    __table_args__ = (UniqueConstraint("destTable", "destId", "destField"), Index("destTable", "destId", "destField"))
