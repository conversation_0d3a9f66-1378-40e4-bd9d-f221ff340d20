from typing import List

from sqlalchemy import Integer, String
from sqlalchemy.orm import relationship, mapped_column, Mapped

from .base import BaseModel
from .relationships import users_types


class UserType(BaseModel):
    __tablename__ = "user_types"

    id: Mapped[int] = mapped_column(Integer, primary_key=True, index=True)
    name: Mapped[str] = mapped_column(String, unique=True, nullable=False, index=True)
    users: Mapped[List["User"]] = relationship("User", secondary=users_types, back_populates="types")
