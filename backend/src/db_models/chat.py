from typing import List, Optional, Any

from sqlalchemy import Enum, <PERSON><PERSON><PERSON>, Integer
from sqlalchemy.orm import relationship, mapped_column, Mapped

from .base import BaseModel


class Chat(BaseModel):
    __tablename__ = "chats"

    id: Mapped[int] = mapped_column(Integer, primary_key=True, index=True)
    title: Mapped[str]
    status: Mapped[str] = mapped_column(Enum("active", "closed", name="chat_status"), default="active")
    category: Mapped[Optional[str]]
    additionalData: Mapped[Optional[dict[str, Any]]]
    userId: Mapped[int] = mapped_column(ForeignKey("users.id"))
    user: Mapped["User"] = relationship(back_populates="chats")
    propertyId: Mapped[Optional[int]] = mapped_column(ForeignKey("properties.id"))
    property: Mapped[Optional["Property"]] = relationship(back_populates="chats")
    messages: Mapped[List["Message"]] = relationship(back_populates="chat", cascade="all, delete-orphan")
    projects: Mapped[List["Project"]] = relationship(back_populates="chat")
    jobs: Mapped[List["Job"]] = relationship(back_populates="chat")
