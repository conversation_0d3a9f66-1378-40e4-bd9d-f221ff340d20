"""remove type and tenureType from property

Revision ID: f8e7bdd54e2e
Revises: 9081b26a02c4
Create Date: 2025-05-14 15:08:48.054747

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision: str = 'f8e7bdd54e2e'
down_revision: Union[str, None] = '9081b26a02c4'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('properties', 'type')
    op.drop_column('properties', 'tenureType')
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('properties', sa.Column('tenureType', postgresql.ENUM('freehold', 'leasehold', 'commonhold', name='propertytenuretype'), autoincrement=False, nullable=True))
    op.add_column('properties', sa.Column('type', postgresql.ENUM('detached', 'semi_detached', 'terraced', 'flat', 'other', name='propertytype'), autoincrement=False, nullable=True))
    # ### end Alembic commands ###
