"""add isEmailVerified and isPhoneNumberVerified

Revision ID: 2f68d50695e4
Revises: 168677549ac1
Create Date: 2025-01-09 16:20:46.213959

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '2f68d50695e4'
down_revision: Union[str, None] = '168677549ac1'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('users', sa.Column('isEmailVerified', sa.<PERSON>(), server_default=sa.text('false'), nullable=False))
    op.add_column('users', sa.Column('isPhoneNumberVerified', sa.<PERSON>(), nullable=True))
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('users', 'isPhoneNumberVerified')
    op.drop_column('users', 'isEmailVerified')
    # ### end Alembic commands ###
