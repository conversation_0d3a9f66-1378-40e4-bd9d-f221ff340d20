"""Merge multiple heads

Revision ID: 9cf7f5ddb6c3
Revises: 2c10ed3f2109, 5cb0fbe50cc0
Create Date: 2025-06-25 22:34:55.610165

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '9cf7f5ddb6c3'
down_revision: Union[str, None] = ('2c10ed3f2109', '5cb0fbe50cc0')
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    pass


def downgrade() -> None:
    pass
