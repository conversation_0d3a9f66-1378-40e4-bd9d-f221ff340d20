"""add fields to document

Revision ID: cb0d8fb426bc
Revises: 8cd5a95b1016
Create Date: 2025-06-02 16:08:18.228583

"""

from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = "cb0d8fb426bc"
down_revision: Union[str, None] = "8cd5a95b1016"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column("documents", sa.Column("uploadContext", sa.String(), nullable=True))
    op.execute('UPDATE documents SET "uploadContext" = \'chat\' WHERE "uploadContext" IS NULL')
    op.add_column(
        "documents", sa.Column("hasStatusBeenDisplayed", sa.<PERSON>(), server_default="false", nullable=False)
    )

    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column("documents", "hasStatusBeenDisplayed")
    op.drop_column("documents", "uploadContext")
    # ### end Alembic commands ###
