"""modify documents

Revision ID: 58d12686a186
Revises: af6bf85a3acf
Create Date: 2025-01-30 20:14:39.981082

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '58d12686a186'
down_revision: Union[str, None] = 'af6bf85a3acf'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('documents', sa.Column('fileExtension', sa.String(), nullable=False))
    op.add_column('documents', sa.Column('sizeInKiloBytes', sa.Integer(), nullable=False))
    op.add_column('documents', sa.Column('originalFileName', sa.String(), nullable=False))
    op.alter_column('documents', 'type',
               existing_type=sa.VARCHAR(),
               nullable=True)
    op.create_unique_constraint(None, 'documents', ['s3Key'])
    op.drop_column('documents', 'extension')
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('documents', sa.Column('extension', sa.VARCHAR(), autoincrement=False, nullable=False))
    op.drop_constraint(None, 'documents', type_='unique')
    op.alter_column('documents', 'type',
               existing_type=sa.VARCHAR(),
               nullable=False)
    op.drop_column('documents', 'originalFileName')
    op.drop_column('documents', 'sizeInKiloBytes')
    op.drop_column('documents', 'fileExtension')
    # ### end Alembic commands ###
