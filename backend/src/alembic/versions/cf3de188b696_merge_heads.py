"""merge heads

Revision ID: cf3de188b696
Revises: 57a07bcd5cc5, acebfc2ba127
Create Date: 2025-06-18 13:23:10.615391

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'cf3de188b696'
down_revision: Union[str, None] = ('57a07bcd5cc5', 'acebfc2ba127')
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    pass


def downgrade() -> None:
    pass
