"""job code

Revision ID: 5cb0fbe50cc0
Revises: 9bd483ce0441
Create Date: 2025-06-25 21:10:51.017415

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '5cb0fbe50cc0'
down_revision: Union[str, None] = '9bd483ce0441'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('jobs', sa.Column('jobCode', sa.String(length=10), server_default=sa.text("UPPER(LEFT(TRANSLATE(MD5(RANDOM()::TEXT || CLOCK_TIMESTAMP()::TEXT), '0o', ''), 10))"), nullable=False))
    op.create_index(op.f('ix_jobs_jobCode'), 'jobs', ['jobCode'], unique=True)
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f('ix_jobs_jobCode'), table_name='jobs')
    op.drop_column('jobs', 'jobCode')
    # ### end Alembic commands ###
