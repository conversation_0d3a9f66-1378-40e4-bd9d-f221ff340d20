"""add unique constraint to source links

Revision ID: e12d9d828262
Revises: ed09e52882d7
Create Date: 2025-05-28 14:37:23.051245

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'e12d9d828262'
down_revision: Union[str, None] = 'ed09e52882d7'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_index('destTable', 'source_links', ['destId', 'destField'], unique=False)
    op.create_unique_constraint(None, 'source_links', ['destTable', 'destId', 'destField'])
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint(None, 'source_links', type_='unique')
    op.drop_index('destTable', table_name='source_links')
    # ### end Alembic commands ###
