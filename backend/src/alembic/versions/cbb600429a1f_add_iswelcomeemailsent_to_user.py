"""add isWelcomeEmailSent to user

Revision ID: cbb600429a1f
Revises: 60870e5c96ca
Create Date: 2025-02-11 13:12:41.941616

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'cbb600429a1f'
down_revision: Union[str, None] = '60870e5c96ca'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('users', sa.Column('isWelcomeEmailSent', sa.<PERSON>(), server_default=sa.text('false'), nullable=False))
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('users', 'isWelcomeEmailSent')
    # ### end Alembic commands ###
