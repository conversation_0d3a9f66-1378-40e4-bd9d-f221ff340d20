"""loosen hubspot id

Revision ID: d4f5a13d07db
Revises: cf3de188b696
Create Date: 2025-06-18 13:27:06.594289

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'd4f5a13d07db'
down_revision: Union[str, None] = 'cf3de188b696'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index('ix_users_hubspotId', table_name='users')
    op.create_index(op.f('ix_users_hubspotId'), 'users', ['hubspotId'], unique=False)
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f('ix_users_hubspotId'), table_name='users')
    op.create_index('ix_users_hubspotId', 'users', ['hubspotId'], unique=True)
    # ### end Alembic commands ###
