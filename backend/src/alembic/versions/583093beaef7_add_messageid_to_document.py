"""add messageId to document

Revision ID: 583093beaef7
Revises: cbb600429a1f
Create Date: 2025-02-12 14:51:27.243854

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '583093beaef7'
down_revision: Union[str, None] = 'cbb600429a1f'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('documents', sa.Column('messageId', sa.Integer(), nullable=True))
    op.create_foreign_key(None, 'documents', 'messages', ['messageId'], ['id'])
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint(None, 'documents', type_='foreignkey')
    op.drop_column('documents', 'messageId')
    # ### end Alembic commands ###
