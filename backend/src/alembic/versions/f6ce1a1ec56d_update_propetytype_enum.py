"""update propetytype enum

Revision ID: f6ce1a1ec56d
Revises: 583093beaef7
Create Date: 2025-03-19 11:06:02.949009

"""

from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = "f6ce1a1ec56d"
down_revision: Union[str, None] = "583093beaef7"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    op.execute("ALTER TYPE propertytype ADD VALUE 'other'")


def downgrade() -> None:
    pass
