"""add jobs and projects

Revision ID: 1424876f0541
Revises: f6ce1a1ec56d
Create Date: 2025-03-21 12:27:20.589414

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision: str = '1424876f0541'
down_revision: Union[str, None] = 'f6ce1a1ec56d'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('service_providers',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('name', sa.String(), nullable=False),
    sa.Column('onboardingStage', sa.String(), nullable=False),
    sa.Column('haRelationship', sa.String(), nullable=False),
    sa.Column('businessAddress', sa.String(), nullable=False),
    sa.Column('postCode', sa.String(), nullable=False),
    sa.Column('companyType', sa.String(), nullable=False),
    sa.Column('insuranceDetails', sa.String(), nullable=False),
    sa.Column('qualifications', postgresql.ARRAY(sa.String()), nullable=True),
    sa.Column('idCheck', sa.String(), nullable=False),
    sa.Column('email', sa.String(), nullable=True),
    sa.Column('phoneNumber', sa.String(), nullable=True),
    sa.Column('nextSteps', sa.String(), nullable=True),
    sa.Column('source', sa.String(), nullable=True),
    sa.Column('mainServiceCategories', postgresql.ARRAY(sa.String()), nullable=True),
    sa.Column('serviceSubCategories', postgresql.ARRAY(sa.String()), nullable=True),
    sa.Column('urgentOohService', sa.String(), nullable=True),
    sa.Column('postcodesServiced', postgresql.ARRAY(sa.String()), nullable=True),
    sa.Column('areasServiced', postgresql.ARRAY(sa.String()), nullable=True),
    sa.Column('companyName', sa.String(), nullable=True),
    sa.Column('teamSize', sa.Integer(), nullable=True),
    sa.Column('website', sa.String(), nullable=True),
    sa.Column('availability', sa.String(), nullable=True),
    sa.Column('mainContactName', sa.String(), nullable=True),
    sa.Column('mainContactEmail', sa.String(), nullable=True),
    sa.Column('mainContactPhoneNumber', sa.String(), nullable=True),
    sa.Column('preferredCommunication', sa.String(), nullable=True),
    sa.Column('googleRating', sa.Float(), nullable=True),
    sa.Column('tradingStandardsRating', sa.Float(), nullable=True),
    sa.Column('trustpilotScore', sa.Float(), nullable=True),
    sa.Column('whichTrustedRating', sa.Float(), nullable=True),
    sa.Column('barkRating', sa.Float(), nullable=True),
    sa.Column('trustatraderRating', sa.Float(), nullable=True),
    sa.Column('mybuilderRating', sa.Float(), nullable=True),
    sa.Column('trustmarkRating', sa.Float(), nullable=True),
    sa.Column('checkatradeRating', sa.Float(), nullable=True),
    sa.Column('checkatradeProfile', sa.String(), nullable=True),
    sa.Column('workGuarantee', sa.String(), nullable=True),
    sa.Column('paymentTerms', sa.String(), nullable=True),
    sa.Column('fees', sa.String(), nullable=True),
    sa.Column('notes', sa.String(), nullable=True),
    sa.Column('plumbing', sa.String(), nullable=True),
    sa.Column('plumbingFees', sa.String(), nullable=True),
    sa.Column('handyperson', sa.String(), nullable=True),
    sa.Column('handypersonFees', sa.String(), nullable=True),
    sa.Column('gasAndHeatingEngineer', sa.String(), nullable=True),
    sa.Column('gasAndHeatingFees', sa.String(), nullable=True),
    sa.Column('electrician', sa.String(), nullable=True),
    sa.Column('electricianFees', sa.String(), nullable=True),
    sa.Column('gardeningAndLandscaping', sa.String(), nullable=True),
    sa.Column('gardeningFees', sa.String(), nullable=True),
    sa.Column('cleaner', sa.String(), nullable=True),
    sa.Column('cleaningFees', sa.String(), nullable=True),
    sa.Column('propertySurvey', sa.String(), nullable=True),
    sa.Column('surveyFees', sa.String(), nullable=True),
    sa.Column('created_at', sa.DateTime(), server_default=sa.text('now()'), nullable=False),
    sa.Column('updated_at', sa.DateTime(), server_default=sa.text('now()'), nullable=False),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_service_providers_id'), 'service_providers', ['id'], unique=False)
    op.create_table('professionals',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('firstName', sa.String(), nullable=False),
    sa.Column('lastName', sa.String(), nullable=True),
    sa.Column('serviceProviderId', sa.Integer(), nullable=False),
    sa.Column('created_at', sa.DateTime(), server_default=sa.text('now()'), nullable=False),
    sa.Column('updated_at', sa.DateTime(), server_default=sa.text('now()'), nullable=False),
    sa.ForeignKeyConstraint(['serviceProviderId'], ['service_providers.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_professionals_id'), 'professionals', ['id'], unique=False)
    op.create_table('projects',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('headline', sa.String(), nullable=False),
    sa.Column('subTitle', sa.String(), nullable=False),
    sa.Column('details', sa.String(), nullable=False),
    sa.Column('urgency', sa.String(), nullable=False),
    sa.Column('timestamp', sa.DateTime(), server_default=sa.text('now()'), nullable=False),
    sa.Column('chatId', sa.Integer(), nullable=False),
    sa.Column('userId', sa.Integer(), nullable=False),
    sa.Column('propertyId', sa.Integer(), nullable=True),
    sa.Column('created_at', sa.DateTime(), server_default=sa.text('now()'), nullable=False),
    sa.Column('updated_at', sa.DateTime(), server_default=sa.text('now()'), nullable=False),
    sa.ForeignKeyConstraint(['chatId'], ['chats.id'], ),
    sa.ForeignKeyConstraint(['propertyId'], ['properties.id'], ),
    sa.ForeignKeyConstraint(['userId'], ['users.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_projects_id'), 'projects', ['id'], unique=False)
    op.create_table('jobs',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('headline', sa.String(), nullable=False),
    sa.Column('subTitle', sa.String(), nullable=False),
    sa.Column('details', sa.String(), nullable=False),
    sa.Column('urgency', sa.String(), nullable=False),
    sa.Column('availability', sa.String(), nullable=True),
    sa.Column('status', sa.Enum('incomplete', 'created', 'user_accepted', 'quoting_started', 'quoting_completed', 'more_quotes_requested', 'booked', 'completed', 'disputed', 'paid', 'cancelled', name='job_status'), nullable=False),
    sa.Column('timestamp', sa.DateTime(), server_default=sa.text('now()'), nullable=False),
    sa.Column('projectId', sa.Integer(), nullable=False),
    sa.Column('chatId', sa.Integer(), nullable=False),
    sa.Column('userId', sa.Integer(), nullable=False),
    sa.Column('propertyId', sa.Integer(), nullable=True),
    sa.Column('serviceProviderId', sa.Integer(), nullable=True),
    sa.Column('created_at', sa.DateTime(), server_default=sa.text('now()'), nullable=False),
    sa.Column('updated_at', sa.DateTime(), server_default=sa.text('now()'), nullable=False),
    sa.ForeignKeyConstraint(['chatId'], ['chats.id'], ),
    sa.ForeignKeyConstraint(['projectId'], ['projects.id'], ),
    sa.ForeignKeyConstraint(['propertyId'], ['properties.id'], ),
    sa.ForeignKeyConstraint(['serviceProviderId'], ['service_providers.id'], ),
    sa.ForeignKeyConstraint(['userId'], ['users.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_jobs_id'), 'jobs', ['id'], unique=False)
    op.create_table('jobs_professionals',
    sa.Column('job_id', sa.Integer(), nullable=False),
    sa.Column('professional_id', sa.Integer(), nullable=False),
    sa.ForeignKeyConstraint(['job_id'], ['jobs.id'], ),
    sa.ForeignKeyConstraint(['professional_id'], ['professionals.id'], ),
    sa.PrimaryKeyConstraint('job_id', 'professional_id')
    )
    op.create_table('quotes',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('costLowest', sa.Integer(), nullable=False),
    sa.Column('costHighest', sa.Integer(), nullable=False),
    sa.Column('availability', sa.String(), nullable=True),
    sa.Column('status', sa.Enum('open', 'accepted', 'rejected', 'cancelled', name='quote_status'), nullable=False),
    sa.Column('timestamp', sa.DateTime(), server_default=sa.text('now()'), nullable=False),
    sa.Column('jobId', sa.Integer(), nullable=False),
    sa.Column('serviceProviderId', sa.Integer(), nullable=False),
    sa.Column('created_at', sa.DateTime(), server_default=sa.text('now()'), nullable=False),
    sa.Column('updated_at', sa.DateTime(), server_default=sa.text('now()'), nullable=False),
    sa.ForeignKeyConstraint(['jobId'], ['jobs.id'], ),
    sa.ForeignKeyConstraint(['serviceProviderId'], ['service_providers.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_quotes_id'), 'quotes', ['id'], unique=False)
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f('ix_quotes_id'), table_name='quotes')
    op.drop_table('quotes')
    op.drop_table('jobs_professionals')
    op.drop_index(op.f('ix_jobs_id'), table_name='jobs')
    op.drop_table('jobs')
    op.drop_index(op.f('ix_projects_id'), table_name='projects')
    op.drop_table('projects')
    op.drop_index(op.f('ix_professionals_id'), table_name='professionals')
    op.drop_table('professionals')
    op.drop_index(op.f('ix_service_providers_id'), table_name='service_providers')
    op.drop_table('service_providers')
    # ### end Alembic commands ###
