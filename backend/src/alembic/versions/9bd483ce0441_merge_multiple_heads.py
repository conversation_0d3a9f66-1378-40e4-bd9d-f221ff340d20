"""merge multiple heads

Revision ID: 9bd483ce0441
Revises: 9e4cd810a4e4, d4f5a13d07db, ad357555d218
Create Date: 2025-06-18 15:41:21.091131

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '9bd483ce0441'
down_revision: Union[str, None] = ('9e4cd810a4e4', 'd4f5a13d07db', 'ad357555d218')
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    pass


def downgrade() -> None:
    pass
