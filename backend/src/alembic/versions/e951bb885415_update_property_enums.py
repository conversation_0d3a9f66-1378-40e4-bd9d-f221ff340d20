"""update property enums

Revision ID: e951bb885415
Revises: 049a7f067184
Create Date: 2025-05-15 17:00:15.089317

"""

from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = "e951bb885415"
down_revision: Union[str, None] = "049a7f067184"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    op.execute("ALTER TYPE propertytype RENAME VALUE 'detached' TO 'house'")
    op.execute("ALTER TYPE propertytype RENAME VALUE 'semi_detached' TO 'office'")
    op.execute("ALTER TYPE propertytype RENAME VALUE 'terraced' TO 'retail'")


def downgrade() -> None:
    pass
