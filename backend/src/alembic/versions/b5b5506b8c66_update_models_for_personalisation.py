"""update models for personalisation

Revision ID: b5b5506b8c66
Revises: 4d7d06229038
Create Date: 2025-05-23 11:03:22.156689

"""

from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision: str = "b5b5506b8c66"
down_revision: Union[str, None] = "4d7d06229038"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table(
        "source_links",
        sa.Column("id", sa.Integer(), nullable=False),
        sa.Column("destTable", sa.String(), nullable=False),
        sa.Column("destId", sa.Integer(), nullable=False),
        sa.Column("destField", sa.String(), nullable=False),
        sa.Column("srcType", sa.Enum("userInput", "chat", "document", name="sourcetype"), nullable=False),
        sa.Column("srcId", sa.Integer(), nullable=False),
        sa.Column("created_at", sa.DateTime(), server_default=sa.text("now()"), nullable=False),
        sa.Column("updated_at", sa.DateTime(), server_default=sa.text("now()"), nullable=False),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_index(op.f("ix_source_links_id"), "source_links", ["id"], unique=False)
    op.create_table(
        "buildings",
        sa.Column("id", sa.Integer(), nullable=False),
        sa.Column("name", sa.String(), nullable=True),
        sa.Column(
            "numberOfFlats",
            sa.Enum("two", "threeToTen", "elevenToFourtyNine", "fiftyPlus", name="numberofflatstype"),
            nullable=True,
        ),
        sa.Column("freeholder", sa.String(), nullable=True),
        sa.Column("hasConcierge", sa.Boolean(), nullable=True),
        sa.Column("hasSwimmingPool", sa.Boolean(), nullable=True),
        sa.Column("hasMailRoom", sa.Boolean(), nullable=True),
        sa.Column("hasBicycleStore", sa.Boolean(), nullable=True),
        sa.Column("hasGym", sa.Boolean(), nullable=True),
        sa.Column("otherDetails", sa.String(), nullable=True),
        sa.Column("managingAgentId", sa.Integer(), nullable=True),
        sa.Column("created_at", sa.DateTime(), server_default=sa.text("now()"), nullable=False),
        sa.Column("updated_at", sa.DateTime(), server_default=sa.text("now()"), nullable=False),
        sa.ForeignKeyConstraint(
            ["managingAgentId"],
            ["users.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_index(op.f("ix_buildings_id"), "buildings", ["id"], unique=False)
    op.create_table(
        "bills",
        sa.Column("id", sa.Integer(), nullable=False),
        sa.Column(
            "type",
            sa.Enum(
                "energyGas",
                "energyElectricity",
                "energyGasAndElectricity",
                "water",
                "securitySystem",
                "councilTax",
                "mortgage",
                "rent",
                "serviceCharge",
                "groundRent",
                "tv",
                "internet",
                "mobile",
                "other",
                name="billtype",
            ),
            nullable=False,
        ),
        sa.Column("provider", sa.String(), nullable=True),
        sa.Column("accountNumber", sa.String(), nullable=True),
        sa.Column("contractDates", sa.String(), nullable=True),
        sa.Column("tariff", sa.String(), nullable=True),
        sa.Column("estimatedMonthlyUsage", sa.String(), nullable=True),
        sa.Column("estimatedMonthlyCost", sa.Numeric(), nullable=True),
        sa.Column(
            "paymentMethod",
            sa.Enum("directDebit", "debitCreditCard", "standingOrder", "prepayment", "other", name="paymentmethodtype"),
            nullable=True,
        ),
        sa.Column("otherDetails", sa.String(), nullable=True),
        sa.Column("isRecentBill", sa.Boolean(), nullable=False),
        sa.Column("userId", sa.Integer(), nullable=False),
        sa.Column("propertyId", sa.Integer(), nullable=True),
        sa.Column("documentId", sa.Integer(), nullable=True),
        sa.Column("created_at", sa.DateTime(), server_default=sa.text("now()"), nullable=False),
        sa.Column("updated_at", sa.DateTime(), server_default=sa.text("now()"), nullable=False),
        sa.ForeignKeyConstraint(
            ["documentId"],
            ["documents.id"],
        ),
        sa.ForeignKeyConstraint(
            ["propertyId"],
            ["properties.id"],
        ),
        sa.ForeignKeyConstraint(
            ["userId"],
            ["users.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_index(op.f("ix_bills_id"), "bills", ["id"], unique=False)
    op.create_table(
        "insurances",
        sa.Column("id", sa.Integer(), nullable=False),
        sa.Column(
            "type",
            sa.Enum(
                "buildingsOnly",
                "contentsOnly",
                "buildingsAndContents",
                "landlords",
                "tenants",
                "other",
                name="insurancetype",
            ),
            nullable=False,
        ),
        sa.Column("policyProvider", sa.String(), nullable=False),
        sa.Column("policyNumber", sa.String(), nullable=False),
        sa.Column("renewalDate", sa.DateTime(), nullable=True),
        sa.Column("annualPremium", sa.Numeric(), nullable=True),
        sa.Column("limits", sa.String(), nullable=True),
        sa.Column("excessDeductible", sa.String(), nullable=True),
        sa.Column("whatsCovered", sa.String(), nullable=True),
        sa.Column("exclusions", sa.String(), nullable=True),
        sa.Column("userId", sa.Integer(), nullable=False),
        sa.Column("propertyId", sa.Integer(), nullable=True),
        sa.Column("documentId", sa.Integer(), nullable=True),
        sa.Column("created_at", sa.DateTime(), server_default=sa.text("now()"), nullable=False),
        sa.Column("updated_at", sa.DateTime(), server_default=sa.text("now()"), nullable=False),
        sa.ForeignKeyConstraint(
            ["documentId"],
            ["documents.id"],
        ),
        sa.ForeignKeyConstraint(
            ["propertyId"],
            ["properties.id"],
        ),
        sa.ForeignKeyConstraint(
            ["userId"],
            ["users.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_index(op.f("ix_insurances_id"), "insurances", ["id"], unique=False)
    op.create_table(
        "legals",
        sa.Column("id", sa.Integer(), nullable=False),
        sa.Column(
            "type",
            sa.Enum(
                "tenancyAgreement",
                "leaseholdAgreement",
                "sharedOwnershipAgreement",
                "planningApplicationsAndPermissions",
                "partyWallAgreements",
                "wills",
                "deeds",
                "contracts",
                "stampDutyReceipt",
                "landTitle",
                "other",
                name="legaltype",
            ),
            nullable=False,
        ),
        sa.Column("aiShortSummary", sa.String(), nullable=False),
        sa.Column("aiDetailedSummary", sa.String(), nullable=True),
        sa.Column("generatedPrompts", sa.String(), nullable=True),
        sa.Column("userId", sa.Integer(), nullable=False),
        sa.Column("propertyId", sa.Integer(), nullable=True),
        sa.Column("documentId", sa.Integer(), nullable=True),
        sa.Column("created_at", sa.DateTime(), server_default=sa.text("now()"), nullable=False),
        sa.Column("updated_at", sa.DateTime(), server_default=sa.text("now()"), nullable=False),
        sa.ForeignKeyConstraint(
            ["documentId"],
            ["documents.id"],
        ),
        sa.ForeignKeyConstraint(
            ["propertyId"],
            ["properties.id"],
        ),
        sa.ForeignKeyConstraint(
            ["userId"],
            ["users.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_index(op.f("ix_legals_id"), "legals", ["id"], unique=False)
    op.add_column("appliances", sa.Column("serialNumber", sa.String(), nullable=True))
    op.add_column("appliances", sa.Column("dateOfPurchase", sa.DateTime(), nullable=True))
    op.add_column("appliances", sa.Column("otherDetails", sa.String(), nullable=True))
    op.add_column("appliances", sa.Column("invoiceReceiptDocumentId", sa.Integer(), nullable=True))
    op.alter_column(
        "appliances", "warranty", existing_type=postgresql.TIMESTAMP(), type_=sa.String(), existing_nullable=True
    )
    op.create_foreign_key(None, "appliances", "documents", ["invoiceReceiptDocumentId"], ["id"])
    op.add_column("documents", sa.Column("aiGeneratedFileName", sa.String(), nullable=True))
    op.add_column("properties", sa.Column("sizeInSqft", sa.Integer(), nullable=True))
    op.add_column("properties", sa.Column("onFloorLevel", sa.Integer(), nullable=True))
    op.add_column("properties", sa.Column("hasBalconyTerrace", sa.Boolean(), nullable=True))
    op.add_column("properties", sa.Column("balconyTerraceDetails", sa.String(), nullable=True))
    op.add_column("properties", sa.Column("hasGarden", sa.Boolean(), nullable=True))
    op.add_column("properties", sa.Column("gardenDetails", sa.String(), nullable=True))
    op.add_column("properties", sa.Column("hasSwimmingPool", sa.Boolean(), nullable=True))
    op.add_column("properties", sa.Column("swimmingPoolDetails", sa.String(), nullable=True))
    op.add_column("properties", sa.Column("numberOfBedrooms", sa.Integer(), nullable=True))
    op.add_column("properties", sa.Column("numberOfBathrooms", sa.Integer(), nullable=True))
    op.add_column("properties", sa.Column("numberOfFloors", sa.Integer(), nullable=True))
    op.add_column("properties", sa.Column("lastSoldPriceInGbp", sa.Integer(), nullable=True))
    propertyconditiontype_enum = postgresql.ENUM(
        "excellent", "good", "fair", "poor", name="propertyconditiontype", create_type=False
    )
    propertyconditiontype_enum.create(op.get_bind(), checkfirst=True)
    op.add_column(
        "properties",
        sa.Column("condition", propertyconditiontype_enum, nullable=True),
    )
    op.add_column("properties", sa.Column("yearsOfOwnership", sa.Integer(), nullable=True))
    op.add_column("properties", sa.Column("architecturalType", sa.String(), nullable=True))
    op.add_column("properties", sa.Column("valuationInGbp", sa.Integer(), nullable=True))
    propertyconservationstatustype_enum = postgresql.ENUM(
        "gradeI",
        "gradeIIstar",
        "gradeII",
        "conservationArea",
        "buildingPreservationNotice",
        "localHeritageList",
        "other",
        name="propertyconservationstatustype",
        create_type=False,
    )
    propertyconservationstatustype_enum.create(op.get_bind(), checkfirst=True)
    op.add_column(
        "properties",
        sa.Column(
            "conservationStatus",
            propertyconservationstatustype_enum,
            nullable=True,
        ),
    )
    op.add_column("properties", sa.Column("typeOfLock", sa.String(), nullable=True))
    op.add_column("properties", sa.Column("typeOfConstruction", sa.String(), nullable=True))
    op.add_column("properties", sa.Column("proportionOfFlatRoof", sa.Integer(), nullable=True))
    propertyepcratingtype_enum = postgresql.ENUM(
        "a", "b", "c", "d", "e", "f", "g", name="propertyepcratingtype", create_type=False
    )
    propertyepcratingtype_enum.create(op.get_bind(), checkfirst=True)
    op.add_column(
        "properties",
        sa.Column("epcRating", propertyepcratingtype_enum, nullable=True),
    )
    op.add_column("properties", sa.Column("floorPlanDocumentId", sa.Integer(), nullable=True))
    op.add_column("properties", sa.Column("propertySurveyDocumentId", sa.Integer(), nullable=True))
    op.add_column("properties", sa.Column("epcCertificateDocumentId", sa.Integer(), nullable=True))
    op.add_column("properties", sa.Column("buildingId", sa.Integer(), nullable=True))
    op.create_foreign_key(None, "properties", "buildings", ["buildingId"], ["id"])
    op.drop_column("properties", "salePrice")
    op.drop_column("properties", "transferDate")
    op.add_column("users", sa.Column("diyProficiency", sa.String(), nullable=True))
    op.add_column("users", sa.Column("projectManagementAbilityAndWillingness", sa.String(), nullable=True))
    op.add_column("users", sa.Column("propertyImprovementInterest", sa.String(), nullable=True))
    op.add_column("users", sa.Column("preferenceForDIYOrProfessionals", sa.String(), nullable=True))
    op.add_column("users", sa.Column("aestheticPreferences", sa.String(), nullable=True))
    op.add_column("users", sa.Column("householdPeople", sa.String(), nullable=True))
    op.add_column("users", sa.Column("householdChildren", sa.String(), nullable=True))
    op.add_column("users", sa.Column("householdPets", sa.String(), nullable=True))
    op.add_column("users", sa.Column("vehicle", sa.String(), nullable=True))
    op.add_column("users", sa.Column("ecoConsciousness", sa.String(), nullable=True))
    op.add_column("users", sa.Column("budgetConsciousness", sa.String(), nullable=True))
    op.add_column("users", sa.Column("brandAffinity", sa.String(), nullable=True))
    op.add_column("users", sa.Column("occupation", sa.String(), nullable=True))
    op.add_column("users", sa.Column("dailyWorkRoutineGeneralAvailability", sa.String(), nullable=True))
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column("users", "dailyWorkRoutineGeneralAvailability")
    op.drop_column("users", "occupation")
    op.drop_column("users", "brandAffinity")
    op.drop_column("users", "budgetConsciousness")
    op.drop_column("users", "ecoConsciousness")
    op.drop_column("users", "vehicle")
    op.drop_column("users", "householdPets")
    op.drop_column("users", "householdChildren")
    op.drop_column("users", "householdPeople")
    op.drop_column("users", "aestheticPreferences")
    op.drop_column("users", "preferenceForDIYOrProfessionals")
    op.drop_column("users", "propertyImprovementInterest")
    op.drop_column("users", "projectManagementAbilityAndWillingness")
    op.drop_column("users", "diyProficiency")
    op.add_column("properties", sa.Column("transferDate", postgresql.TIMESTAMP(), autoincrement=False, nullable=True))
    op.add_column("properties", sa.Column("salePrice", sa.INTEGER(), autoincrement=False, nullable=True))
    op.drop_constraint(None, "properties", type_="foreignkey")
    op.drop_column("properties", "buildingId")
    op.drop_column("properties", "epcCertificateDocumentId")
    op.drop_column("properties", "propertySurveyDocumentId")
    op.drop_column("properties", "floorPlanDocumentId")
    op.drop_column("properties", "epcRating")
    op.drop_column("properties", "proportionOfFlatRoof")
    op.drop_column("properties", "typeOfConstruction")
    op.drop_column("properties", "typeOfLock")
    op.drop_column("properties", "conservationStatus")
    op.drop_column("properties", "valuationInGbp")
    op.drop_column("properties", "architecturalType")
    op.drop_column("properties", "yearsOfOwnership")
    op.drop_column("properties", "condition")
    op.drop_column("properties", "lastSoldPriceInGbp")
    op.drop_column("properties", "numberOfFloors")
    op.drop_column("properties", "numberOfBathrooms")
    op.drop_column("properties", "numberOfBedrooms")
    op.drop_column("properties", "swimmingPoolDetails")
    op.drop_column("properties", "hasSwimmingPool")
    op.drop_column("properties", "gardenDetails")
    op.drop_column("properties", "hasGarden")
    op.drop_column("properties", "balconyTerraceDetails")
    op.drop_column("properties", "hasBalconyTerrace")
    op.drop_column("properties", "onFloorLevel")
    op.drop_column("properties", "sizeInSqft")
    op.drop_column("documents", "aiGeneratedFileName")
    op.drop_constraint(None, "appliances", type_="foreignkey")
    op.alter_column(
        "appliances", "warranty", existing_type=sa.String(), type_=postgresql.TIMESTAMP(), existing_nullable=True
    )
    op.drop_column("appliances", "invoiceReceiptDocumentId")
    op.drop_column("appliances", "otherDetails")
    op.drop_column("appliances", "dateOfPurchase")
    op.drop_column("appliances", "serialNumber")
    op.drop_index(op.f("ix_legals_id"), table_name="legals")
    op.drop_table("legals")
    op.drop_index(op.f("ix_insurances_id"), table_name="insurances")
    op.drop_table("insurances")
    op.drop_index(op.f("ix_bills_id"), table_name="bills")
    op.drop_table("bills")
    op.drop_index(op.f("ix_buildings_id"), table_name="buildings")
    op.drop_table("buildings")
    op.drop_index(op.f("ix_source_links_id"), table_name="source_links")
    op.drop_table("source_links")
    # ### end Alembic commands ###
