"""add indexes to source links

Revision ID: 7d55940206ea
Revises: e12d9d828262
Create Date: 2025-05-29 11:24:39.263358

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '7d55940206ea'
down_revision: Union[str, None] = 'e12d9d828262'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_index(op.f('ix_source_links_destField'), 'source_links', ['destField'], unique=False)
    op.create_index(op.f('ix_source_links_destId'), 'source_links', ['destId'], unique=False)
    op.create_index(op.f('ix_source_links_destTable'), 'source_links', ['destTable'], unique=False)
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f('ix_source_links_destTable'), table_name='source_links')
    op.drop_index(op.f('ix_source_links_destId'), table_name='source_links')
    op.drop_index(op.f('ix_source_links_destField'), table_name='source_links')
    # ### end Alembic commands ###
