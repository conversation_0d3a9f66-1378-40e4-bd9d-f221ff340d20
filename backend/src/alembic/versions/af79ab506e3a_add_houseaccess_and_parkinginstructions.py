"""add houseAccess and parkingInstructions

Revision ID: af79ab506e3a
Revises: 1424876f0541
Create Date: 2025-03-24 12:53:42.669316

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'af79ab506e3a'
down_revision: Union[str, None] = '1424876f0541'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('addresses', sa.Column('houseAccess', sa.String(), nullable=True))
    op.add_column('addresses', sa.Column('parkingInstructions', sa.String(), nullable=True))
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('addresses', 'parkingInstructions')
    op.drop_column('addresses', 'houseAccess')
    # ### end Alembic commands ###
