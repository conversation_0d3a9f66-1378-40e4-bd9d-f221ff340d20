"""make src id optional in sourcelink

Revision ID: ed09e52882d7
Revises: b5b5506b8c66
Create Date: 2025-05-28 10:59:43.434542

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'ed09e52882d7'
down_revision: Union[str, None] = 'b5b5506b8c66'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('source_links', 'srcId',
               existing_type=sa.INTEGER(),
               nullable=True)
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('source_links', 'srcId',
               existing_type=sa.INTEGER(),
               nullable=False)
    # ### end Alembic commands ###
