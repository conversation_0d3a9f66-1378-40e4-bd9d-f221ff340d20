"""merge heads

Revision ID: ad357555d218
Revises: 57a07bcd5cc5, acebfc2ba127
Create Date: 2025-06-18 12:57:52.258283

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'ad357555d218'
down_revision: Union[str, None] = ('57a07bcd5cc5', 'acebfc2ba127')
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    pass


def downgrade() -> None:
    pass
