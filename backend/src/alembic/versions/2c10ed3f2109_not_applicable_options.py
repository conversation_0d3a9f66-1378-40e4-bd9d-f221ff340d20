"""not applicable options

Revision ID: 2c10ed3f2109
Revises: 9bd483ce0441
Create Date: 2025-06-25 09:14:41.366229

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '2c10ed3f2109'
down_revision: Union[str, None] = '9bd483ce0441'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    op.execute("ALTER TYPE propertytenuretype ADD VALUE IF NOT EXISTS 'NOT_APPLICABLE'")
    op.execute("ALTER TYPE propertysubgrouptype ADD VALUE IF NOT EXISTS 'NOT_APPLICABLE'")
    # ### end Alembic commands ###


def downgrade() -> None:
    pass
    # ### end Alembic commands ###
