"""jobCode to reference

Revision ID: f4e137b6307b
Revises: 9cf7f5ddb6c3
Create Date: 2025-06-27 14:33:04.262559

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'f4e137b6307b'
down_revision: Union[str, None] = '9cf7f5ddb6c3'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('jobs', sa.Column('reference', sa.String(length=10), server_default=sa.text("UPPER(LEFT(TRANSLATE(MD5(RANDOM()::TEXT || CLOCK_TIMESTAMP()::TEXT), '0o', ''), 8))"), nullable=False))
    op.drop_index('ix_jobs_jobCode', table_name='jobs')
    op.create_index(op.f('ix_jobs_reference'), 'jobs', ['reference'], unique=True)
    op.drop_column('jobs', 'jobCode')
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('jobs', sa.Column('jobCode', sa.VARCHAR(length=10), server_default=sa.text('upper("left"(translate(md5(((random())::text || (clock_timestamp())::text)), \'0o\'::text, \'\'::text), 10))'), autoincrement=False, nullable=False))
    op.drop_index(op.f('ix_jobs_reference'), table_name='jobs')
    op.create_index('ix_jobs_jobCode', 'jobs', ['jobCode'], unique=True)
    op.drop_column('jobs', 'reference')
    # ### end Alembic commands ###
