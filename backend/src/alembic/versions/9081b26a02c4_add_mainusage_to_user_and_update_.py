"""add mainUsage to user and update relationType

Revision ID: 9081b26a02c4
Revises: 90459f886613
Create Date: 2025-05-12 15:24:38.714511

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '9081b26a02c4'
down_revision: Union[str, None] = '90459f886613'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('users', sa.Column('mainUsage', sa.Enum('ownerAndOccupier', 'landlord', 'tenant', 'managingProfessional', name='userpropertyrelationtype'), nullable=True))
    op.add_column('users_properties', sa.Column('relationType', sa.Enum('ownerAndOccupier', 'landlord', 'tenant', 'managingProfessional', name='userpropertyrelationtype'), nullable=True))
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('users_properties', 'relationType')
    op.drop_column('users', 'mainUsage')
    # ### end Alembic commands ###
