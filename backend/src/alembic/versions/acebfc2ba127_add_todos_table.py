"""add_todos_table

Revision ID: acebfc2ba127
Revises: 9ef4170c9f70
Create Date: 2025-06-16 23:27:31.998857

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'acebfc2ba127'
down_revision: Union[str, None] = '9ef4170c9f70'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('entity_links',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('typeA', sa.String(), nullable=False),
    sa.Column('idA', sa.Integer(), nullable=False),
    sa.Column('typeB', sa.String(), nullable=False),
    sa.Column('idB', sa.Integer(), nullable=False),
    sa.Column('created_at', sa.DateTime(), server_default=sa.text('now()'), nullable=False),
    sa.Column('updated_at', sa.DateTime(), server_default=sa.text('now()'), nullable=False),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('typeA', 'idA', 'typeB', 'idB')
    )
    op.create_index(op.f('ix_entity_links_id'), 'entity_links', ['id'], unique=False)
    op.create_index(op.f('ix_entity_links_idA'), 'entity_links', ['idA'], unique=False)
    op.create_index(op.f('ix_entity_links_idB'), 'entity_links', ['idB'], unique=False)
    op.create_index(op.f('ix_entity_links_typeA'), 'entity_links', ['typeA'], unique=False)
    op.create_index(op.f('ix_entity_links_typeB'), 'entity_links', ['typeB'], unique=False)
    op.create_index('tablesIds', 'entity_links', ['typeA', 'idA', 'typeB', 'idB'], unique=False)
    op.create_index('typeIdA', 'entity_links', ['typeA', 'idA'], unique=False)
    op.create_index('typeIdB', 'entity_links', ['typeB', 'idB'], unique=False)
    op.create_table('todos',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('type', sa.Enum('userCreated', 'systemCreated', 'systemAccepted', name='todotype'), nullable=False),
    sa.Column('name', sa.String(), nullable=False),
    sa.Column('description', sa.String(), nullable=True),
    sa.Column('dueDate', sa.DateTime(), nullable=True),
    sa.Column('doneDate', sa.DateTime(), nullable=True),
    sa.Column('deletedDate', sa.DateTime(), nullable=True),
    sa.Column('userId', sa.Integer(), nullable=False),
    sa.Column('created_at', sa.DateTime(), server_default=sa.text('now()'), nullable=False),
    sa.Column('updated_at', sa.DateTime(), server_default=sa.text('now()'), nullable=False),
    sa.ForeignKeyConstraint(['userId'], ['users.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_todos_id'), 'todos', ['id'], unique=False)
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f('ix_todos_id'), table_name='todos')
    op.drop_table('todos')
    op.drop_index('typeIdB', table_name='entity_links')
    op.drop_index('typeIdA', table_name='entity_links')
    op.drop_index('tablesIds', table_name='entity_links')
    op.drop_index(op.f('ix_entity_links_typeB'), table_name='entity_links')
    op.drop_index(op.f('ix_entity_links_typeA'), table_name='entity_links')
    op.drop_index(op.f('ix_entity_links_idB'), table_name='entity_links')
    op.drop_index(op.f('ix_entity_links_idA'), table_name='entity_links')
    op.drop_index(op.f('ix_entity_links_id'), table_name='entity_links')
    op.drop_table('entity_links')
    # ### end Alembic commands ###
