"""remove old relationType from UsersProperties relationship

Revision ID: 90459f886613
Revises: af79ab506e3a
Create Date: 2025-05-12 15:22:58.021703

"""

from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision: str = "90459f886613"
down_revision: Union[str, None] = "af79ab506e3a"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column("users_properties", "relationType")
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column(
        "users_properties",
        sa.Column(
            "relationType",
            postgresql.ENUM("owner", "tenant", "guarantor", name="userpropertyrelationtype"),
            autoincrement=False,
            nullable=True,
        ),
    )
    # ### end Alembic commands ###
