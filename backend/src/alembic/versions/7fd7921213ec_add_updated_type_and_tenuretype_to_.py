"""add updated type and tenureType to property

Revision ID: 7fd7921213ec
Revises: f8e7bdd54e2e
Create Date: 2025-05-14 15:10:19.194391

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '7fd7921213ec'
down_revision: Union[str, None] = 'f8e7bdd54e2e'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('properties', sa.Column('type', sa.Enum('house', 'flat', 'office', 'retail', name='propertytype'), nullable=True))
    op.add_column('properties', sa.Column('tenureType', sa.Enum('freehold', 'leasehold', 'shareOfFreehold', name='propertytenuretype'), nullable=True))
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('properties', 'tenureType')
    op.drop_column('properties', 'type')
    # ### end Alembic commands ###
