"""user deletion

Revision ID: 57a07bcd5cc5
Revises: 9ef4170c9f70
Create Date: 2025-06-17 23:02:44.994926

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '57a07bcd5cc5'
down_revision: Union[str, None] = '9ef4170c9f70'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('users', sa.Column('deletedAt', sa.DateTime(), nullable=True))
    op.drop_index('ix_users_email', table_name='users')
    op.create_index(op.f('ix_users_email'), 'users', ['email'], unique=False)
    op.create_unique_constraint('uq_email_deletedAt', 'users', ['email', 'deletedAt'])
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint('uq_email_deletedAt', 'users', type_='unique')
    op.drop_index(op.f('ix_users_email'), table_name='users')
    op.create_index('ix_users_email', 'users', ['email'], unique=True)
    op.drop_column('users', 'deletedAt')
    # ### end Alembic commands ###
