"""change default status for document

Revision ID: a738975afbee
Revises: cb0d8fb426bc
Create Date: 2025-06-02 16:14:44.665786

"""

from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = "a738975afbee"
down_revision: Union[str, None] = "cb0d8fb426bc"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    op.alter_column("documents", "status", server_default=sa.text("'saved'"))


def downgrade() -> None:
    op.alter_column("documents", "status", server_default=sa.text("'processingCompleted'"))
