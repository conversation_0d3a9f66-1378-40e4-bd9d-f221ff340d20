"""Add hubspot

Revision ID: f4326be0dce6
Revises: 8cd5a95b1016
Create Date: 2025-06-04 23:14:04.453623

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'f4326be0dce6'
down_revision: Union[str, None] = '8cd5a95b1016'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('jobs', sa.Column('hubspotId', sa.String(), nullable=True))
    op.add_column('jobs', sa.Column('hubspotSyncAt', sa.DateTime(), nullable=True))
    op.create_index(op.f('ix_jobs_hubspotId'), 'jobs', ['hubspotId'], unique=True)
    op.add_column('users', sa.Column('hubspotId', sa.String(), nullable=True))
    op.add_column('users', sa.Column('hubspotSyncAt', sa.DateTime(), nullable=True))
    op.create_index(op.f('ix_users_hubspotId'), 'users', ['hubspotId'], unique=True)
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f('ix_users_hubspotId'), table_name='users')
    op.drop_column('users', 'hubspotSyncAt')
    op.drop_column('users', 'hubspotId')
    op.drop_index(op.f('ix_jobs_hubspotId'), table_name='jobs')
    op.drop_column('jobs', 'hubspotSyncAt')
    op.drop_column('jobs', 'hubspotId')
    # ### end Alembic commands ###
