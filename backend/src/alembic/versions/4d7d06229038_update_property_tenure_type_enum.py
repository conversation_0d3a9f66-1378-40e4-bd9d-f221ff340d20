"""update property tenure type enum

Revision ID: 4d7d06229038
Revises: e951bb885415
Create Date: 2025-05-15 17:04:33.438507

"""

from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = "4d7d06229038"
down_revision: Union[str, None] = "e951bb885415"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    op.execute("ALTER TYPE propertytenuretype RENAME VALUE 'commonhold' TO 'shareOfFreehold'")


def downgrade() -> None:
    pass
