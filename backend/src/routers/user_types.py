from fastapi import APIRouter, Depends, status
from fastapi_pagination import Page
from fastapi_pagination.ext.sqlalchemy import paginate
from sqlalchemy.ext.asyncio import AsyncSession

from src.dependencies import get_db_session
from src.schemas import UserType, UserTypeCreate
from src.services.model_services import UserTypeService

router = APIRouter(
    prefix="/user_types",
    tags=["user_types"],
)


def get_user_type_service(db: AsyncSession = Depends(get_db_session)) -> UserTypeService:
    return UserTypeService(db=db)


@router.get("/", response_model=Page[UserType])
async def get_user_types_endpoint(user_type_service: UserTypeService = Depends(get_user_type_service)):
    user_types_query, session = user_type_service.get_user_types()
    return await paginate(query=user_types_query, conn=session)


@router.post("/", response_model=UserType, status_code=status.HTTP_201_CREATED)
async def create_user_type_endpoint(
    user_type: UserTypeCreate, user_type_service: UserTypeService = Depends(get_user_type_service)
):
    return await user_type_service.create_user_type(user_type=user_type)
