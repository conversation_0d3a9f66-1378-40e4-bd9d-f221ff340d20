from fastapi import APIRouter, Depends, status
from fastapi_pagination import Page
from fastapi_pagination.ext.sqlalchemy import paginate
from sqlalchemy.ext.asyncio import AsyncSession

from src.dependencies import get_db_session
from src.schemas import Booking, BookingCreate
from src.services.model_services import BookingService

router = APIRouter(
    prefix="/bookings",
    tags=["bookings"],
)


def get_booking_service(db: AsyncSession = Depends(get_db_session)) -> BookingService:
    return BookingService(db=db)


@router.get("/", response_model=Page[Booking])
async def get_bookings_endpoint(booking_service: BookingService = Depends(get_booking_service)):
    bookings_query, session = booking_service.get_bookings()
    return await paginate(query=bookings_query, conn=session)


@router.post("/", response_model=BookingCreate, status_code=status.HTTP_201_CREATED)
async def create_booking_endpoint(
    booking: BookingCreate, booking_service: BookingService = Depends(get_booking_service)
):
    return await booking_service.create_booking(booking=booking)
