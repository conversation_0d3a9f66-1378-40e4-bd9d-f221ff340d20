import logging
from typing import Annotated

from fastapi import Depends, APIRouter, status, HTTPException
from fastapi_pagination import Page
from fastapi_pagination.ext.sqlalchemy import paginate
from sqlalchemy.ext.asyncio import AsyncSession

from src.db_models.user import User
from src.dependencies import get_job_service, get_db_session
from src.schemas import JobInfo
from src.services.auth import get_current_user
from src.services.jobs import JobDoesNotExist, OperationNotAllowed, JobService

logger = logging.getLogger("uvicorn")

router = APIRouter(
    prefix="/jobs",
    tags=["jobs"],
)


@router.get("/", response_model=Page[JobInfo])
async def list_of_jobs_for_user(
    current_user: Annotated[User, Depends(get_current_user)],
    job_service: JobService = Depends(get_job_service),
    db_session: AsyncSession = Depends(get_db_session),
):
    return await paginate(query=job_service.list_jobs_by_user_query(current_user), conn=db_session)


@router.get("/{job_id}", response_model=JobInfo)
async def get_job_by_id(
    current_user: Annotated[User, Depends(get_current_user)],
    job_id: int,
    job_service: JobService = Depends(get_job_service),
):
    job = await job_service.get_job_by_id(job_id, current_user)
    if not job:
        raise HTTPException(status_code=404, detail="Job does not exist")
    return job


@router.post("/{job_id}/user-accept/", status_code=status.HTTP_202_ACCEPTED)
async def user_accept_job(
    current_user: Annotated[User, Depends(get_current_user)],
    job_id: int,
    job_service: JobService = Depends(get_job_service),
):
    logger.info(f"Received job {job_id} accept request from user {current_user.id}.")

    try:
        await job_service.accept_job_by_user(job_id, current_user)
    except OperationNotAllowed as e:
        raise HTTPException(status_code=400, detail=str(e))
    except JobDoesNotExist:
        raise HTTPException(status_code=404, detail="Job does not exist")
