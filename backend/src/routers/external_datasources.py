from fastapi import APIRouter, Depends

from src.dependencies import get_crystalroof_api, get_land_registry_api, get_os_data_places_api, get_idealpostcodes_api
from src.integrations.crystalroof import CrystalRoofAPI, Postcode
from src.integrations.idealpostcodes import IdealPostcodesAP<PERSON>
from src.integrations.landregistry import LandRegistryAPI
from src.integrations.osdataplaces import OSDataPlacesAPI
from src.schemas import (
    MeanHouseHoldIncomeResponse,
    AverageHousePriceForPostcodeResponse,
    AverageHousePriceForStreetResponse,
    AverageHousePriceForFlatForTownResponse,
    AverageHousePriceChangeOverYearsForTownResponse,
    MostExpensivePostcodesForTownResponse,
    FindAddressesResponse,
)
from src.services.auth import validate_jwt_token

router = APIRouter(
    prefix="/external-datasources",
    tags=["external-datasources"],
)


@router.get(
    "/mean-household-income-postcode",
    response_model=MeanHouseHoldIncomeResponse,
    dependencies=[Depends(validate_jwt_token)],
)
async def get_mean_household_income_endpoint(
    postcode: str,
    crystalroof_api: CrystalRoofAPI = Depends(get_crystalroof_api),
):
    response = await crystalroof_api.get_mean_household_income(Postcode(postcode))
    return response


@router.get(
    "/average-price-postcode",
    response_model=AverageHousePriceForPostcodeResponse,
    dependencies=[Depends(validate_jwt_token)],
)
async def get_average_price_for_postcode_endpoint(
    postcode: str,
    land_registry_api: LandRegistryAPI = Depends(get_land_registry_api),
):
    response = {"postcode": postcode, "avg": await land_registry_api.get_average_price_paid_for_postcode(postcode)}
    return response


@router.get(
    "/average-price-street",
    response_model=AverageHousePriceForStreetResponse,
    dependencies=[Depends(validate_jwt_token)],
)
async def get_average_price_for_street_endpoint(
    postcode: str,
    town: str,
    street: str,
    land_registry_api: LandRegistryAPI = Depends(get_land_registry_api),
):
    response = {
        "postcode": postcode,
        "town": town,
        "street": street,
        "avg": await land_registry_api.get_average_price_paid_for_street(postcode, town, street),
    }
    return response


@router.get(
    "/average-flat-price-town",
    response_model=AverageHousePriceForFlatForTownResponse,
    dependencies=[Depends(validate_jwt_token)],
)
async def get_average_price_for_flat_in_town_endpoint(
    town: str,
    land_registry_api: LandRegistryAPI = Depends(get_land_registry_api),
):
    response = {"town": town, "avg": await land_registry_api.get_average_price_paid_for_flat_for_town(town)}
    return response


@router.get(
    "/average-price-changed-over-years-town",
    response_model=AverageHousePriceChangeOverYearsForTownResponse,
    dependencies=[Depends(validate_jwt_token)],
)
async def get_average_price_change_for_town_endpoint(
    town: str,
    years: int = 5,
    land_registry_api: LandRegistryAPI = Depends(get_land_registry_api),
):
    response = {
        "town": town,
        "years": years,
        "changeInPercent": await land_registry_api.get_average_price_paid_change_over_years_for_town(town, years),
    }
    return response


@router.get(
    "/most-expensive-postcodes-for-town",
    response_model=MostExpensivePostcodesForTownResponse,
    dependencies=[Depends(validate_jwt_token)],
)
async def get_most_expensive_postcodes_for_town_endpoint(
    town: str,
    land_registry_api: LandRegistryAPI = Depends(get_land_registry_api),
):
    response = {
        "town": town,
        "postcodes": [
            {"postcode": postcode[0], "avg": postcode[1], "numberOfTransactions": postcode[2]}
            for postcode in await land_registry_api.get_most_expensive_postcodes_for_town(town)
        ],
    }
    return response


@router.get("/find-place", response_model=dict, dependencies=[Depends(validate_jwt_token)])
async def find_place_endpoint(
    name: str,
    max_results: int = 5,
    os_data_places_api: OSDataPlacesAPI = Depends(get_os_data_places_api),
):
    places = await os_data_places_api.find_places_by_name(name, max_results)
    return {
        "places": places,
    }


@router.get("/find-address", response_model=FindAddressesResponse, dependencies=[Depends(validate_jwt_token)])
async def find_address_endpoint(
    query: str, limit: int = 10, ideal_postcodes_api: IdealPostcodesAPI = Depends(get_idealpostcodes_api)
):
    addresses = await ideal_postcodes_api.find_addresses(query, limit)
    return {
        "hits": addresses,
    }
