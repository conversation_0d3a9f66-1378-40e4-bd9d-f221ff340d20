import logging

from fastapi import APIRouter, Depends, HTTPException

from src.dependencies import get_document_service, get_hubspot_sync_service
from src.services.auth import validate_admin_token
from src.services.documents import DocumentService
from src.services.hubspot_sync import HubSpotSyncService

logger = logging.getLogger("uvicorn")

router = APIRouter(
    prefix="/admin",
    tags=["admin"],
)


@router.post("/pulse", status_code=200, dependencies=[Depends(validate_admin_token)])
async def admin_pulse(
    document_service: DocumentService = Depends(get_document_service),
    hubspot_sync_service: HubSpotSyncService = Depends(get_hubspot_sync_service),
):
    logger.info("Received admin pulse")
    try:
        logger.info("Timeout documents")
        await document_service.timeout_documents()
        logger.info("Sync users")
        await hubspot_sync_service.sync_users()
        logger.info("Sync jobs")
        await hubspot_sync_service.sync_jobs()
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))
