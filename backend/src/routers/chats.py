import logging
from typing import Annotated

from fastapi import Depends, APIRouter
from fastapi_pagination import Page
from fastapi_pagination.ext.sqlalchemy import paginate
from sqlalchemy.ext.asyncio import AsyncSession

from src.db_models.user import User
from src.dependencies import get_message_service, get_db_session
from src.schemas import Message, ChatInfo
from src.services.auth import get_current_user
from src.services.messages import MessageService

logger = logging.getLogger("uvicorn")

router = APIRouter(
    prefix="/chats",
    tags=["chats"],
)


@router.get("/", response_model=Page[ChatInfo])
async def list_of_chats_for_user(
    current_user: Annotated[User, Depends(get_current_user)],
    message_service: MessageService = Depends(get_message_service),
    db_session: AsyncSession = Depends(get_db_session),
):
    return await paginate(query=message_service.list_chats_for_user_query(current_user), conn=db_session)


@router.get("/{chat_id}/messages/", response_model=Page[Message])
async def list_messages_for_chat(
    chat_id: int,
    current_user: Annotated[User, Depends(get_current_user)],
    message_service: MessageService = Depends(get_message_service),
    db_session: AsyncSession = Depends(get_db_session),
):
    return await paginate(query=message_service.list_messages_for_chat_query(chat_id, current_user), conn=db_session)
