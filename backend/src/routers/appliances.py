from typing import Annotated

from fastapi import APIRouter, Depends, HTTPException
from fastapi_pagination import Page
from fastapi_pagination.ext.sqlalchemy import paginate
from sqlalchemy.ext.asyncio import AsyncSession

from src.db_models.user import User
from src.dependencies import get_db_session, get_appliance_service
from src.schemas import ApplianceInfo, ApplianceCreate, ApplianceUpdate
from src.services.appliances import ApplianceService, PropertyDoesNotExist, ApplianceDoesNotExist
from src.services.auth import get_current_user

router = APIRouter(
    prefix="/appliances",
    tags=["appliances"],
)


@router.get("/", response_model=Page[ApplianceInfo])
async def get_appliances_endpoint(
    current_user: Annotated[User, Depends(get_current_user)],
    appliance_service: ApplianceService = Depends(get_appliance_service),
    db_session: AsyncSession = Depends(get_db_session),
):
    return await paginate(query=appliance_service.get_appliances_for_user_query(current_user), conn=db_session)


@router.post("/", response_model=ApplianceInfo)
async def create_appliance_endpoint(
    current_user: Annotated[User, Depends(get_current_user)],
    appliance_data: ApplianceCreate,
    appliance_service: ApplianceService = Depends(get_appliance_service),
):
    try:
        return await appliance_service.create_appliance_by_user(appliance_data, current_user)
    except PropertyDoesNotExist as e:
        raise HTTPException(status_code=400, detail=str(e))


@router.patch("/{appliance_id}", response_model=ApplianceInfo)
async def update_appliance_endpoint(
    current_user: Annotated[User, Depends(get_current_user)],
    appliance_id: int,
    appliance_data: ApplianceUpdate,
    appliance_service: ApplianceService = Depends(get_appliance_service),
):
    try:
        return await appliance_service.update_appliance_by_user(appliance_id, appliance_data, current_user)
    except ApplianceDoesNotExist as e:
        raise HTTPException(status_code=404, detail=str(e))
