import pytest
from aioresponses import aioresponses

from .osdataplaces import OSDataPlacesAPI


@pytest.mark.asyncio(loop_scope="session")
class TestOSDataPlacesAPI:
    api_key = "TEST"

    @pytest.fixture(scope="class")
    def api(self):
        return OSDataPlacesAPI(api_key=self.api_key)

    @pytest.mark.asyncio
    async def test_find_place_by_name_success(self, api):
        mock_response = {
            "results": [
                {
                    "DPA": {
                        "UPRN": "100023336956",
                        "ADDRESS": "LONDON, LONDON",
                        "BUILDING_NUMBER": "",
                        "THOROUGHFARE_NAME": "",
                        "POST_TOWN": "LONDON",
                        "POSTCODE": "SW1A 2AA",
                        "RPC": "1",
                        "X_COORDINATE": 530047,
                        "Y_COORDINATE": 179951,
                        "STATUS": "APPROVED",
                        "LOGICAL_STATUS_CODE": "1",
                        "CLASSIFICATION_CODE": "PP",
                        "CLASSIFICATION_CODE_DESCRIPTION": "Property Shell",
                        "LOCAL_CUSTODIAN_CODE": 7655,
                        "LOCAL_CUSTODIAN_CODE_DESCRIPTION": "CITY OF WESTMINSTER",
                        "POSTAL_ADDRESS_CODE": "D",
                        "POSTAL_ADDRESS_CODE_DESCRIPTION": "A record which is linked to PAF",
                        "BLPU_STATE_CODE": "2",
                        "BLPU_STATE_CODE_DESCRIPTION": "In use",
                        "TOPOGRAPHY_LAYER_TOID": "osgb1000002148122894",
                        "PARENT_UPRN": "100023336955",
                        "LAST_UPDATE_DATE": "10/02/2016",
                        "ENTRY_DATE": "12/10/2005",
                        "LANGUAGE": "EN",
                        "MATCH": 1.0,
                        "MATCH_DESCRIPTION": "EXACT",
                    }
                }
            ]
        }

        with aioresponses() as m:
            m.get(
                f"{api._base_url}/find?key={api._api_key}&maxresults=5&query=London", status=200, payload=mock_response
            )

            places = await api.find_places_by_name("London")
            assert places == [mock_response["results"][0]["DPA"]]

    @pytest.mark.asyncio
    async def test_find_place_by_name_error(self, api):
        with aioresponses() as m:
            m.get(f"{api._base_url}find?key={api._api_key}&query=NonexistentPlace&maxresults=1", status=404)

            with pytest.raises(Exception):
                await api.find_places_by_name("NonexistentPlace")
