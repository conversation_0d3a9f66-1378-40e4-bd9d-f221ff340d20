import pytest
from aioresponses import aioresponses

from .idealpostcodes import IdealPostcodesAPI, FindAddressesHit, ResolvedAddress


@pytest.mark.asyncio(loop_scope="session")
class TestLandRegistryAPI:
    @pytest.fixture
    def api(self):
        return IdealPostcodesAPI(api_key="test")

    async def test_find_addresses(self, api):
        query = "22 Eardley Crescent"
        mock_response = {
            "result": {
                "hits": [
                    {
                        "id": "paf_23833521",
                        "suggestion": "22 Eardley Crescent, London, SW5",
                        "udprn": 23833521,
                        "urls": {"udprn": "/v1/udprn/23833521"},
                    }
                ]
            },
            "code": 2000,
            "message": "Success",
        }
        url = f"{api._base_url}/v1/autocomplete/addresses?api_key=test&query=22+Eardley+Crescent&limit=10&context=GBR"
        with aioresponses() as m:
            m.get(url, payload=mock_response, status=200)
            result = await api.find_addresses(query)
            m.assert_called_once()
            assert result == [
                FindAddressesHit(
                    id="paf_23833521",
                    suggestion="22 Eardley Crescent, London, SW5",
                    udprn=23833521,
                )
            ]

    async def test_resolve_address(self, api):
        address_id = "paf_23833521"
        mock_response = {
            "result": {
                "postcode": "SW5 9JZ",
                "postcode_inward": "9JZ",
                "postcode_outward": "SW5",
                "post_town": "London",
                "dependant_locality": "",
                "double_dependant_locality": "",
                "thoroughfare": "Eardley Crescent",
                "dependant_thoroughfare": "",
                "building_number": "22",
                "building_name": "",
                "sub_building_name": "",
                "po_box": "",
                "department_name": "",
                "organisation_name": "",
                "udprn": 23833521,
                "postcode_type": "S",
                "su_organisation_indicator": "",
                "delivery_point_suffix": "1J",
                "line_1": "22 Eardley Crescent",
                "line_2": "",
                "line_3": "",
                "premise": "22",
                "longitude": -0.1953659,
                "latitude": 51.4885502,
                "eastings": 525390,
                "northings": 178166,
                "country": "England",
                "traditional_county": "Greater London",
                "administrative_county": "",
                "postal_county": "London",
                "county": "London",
                "district": "Kensington and Chelsea",
                "ward": "Earl's Court",
                "uprn": "217025320",
                "id": "paf_23833521",
                "country_iso": "GBR",
                "country_iso_2": "GB",
                "county_code": "",
                "language": "en",
                "umprn": "",
                "dataset": "paf",
            },
            "code": 2000,
            "message": "Success",
        }
        url = f"{api._base_url}/v1/autocomplete/addresses/paf_23833521/gbr?api_key=test"
        with aioresponses() as m:
            m.get(url, payload=mock_response, status=200)
            result, raw_result = await api.resolve_address(address_id)
            m.assert_called_once()
            assert result == (
                ResolvedAddress(
                    id="paf_23833521",
                    line_1="22 Eardley Crescent",
                    line_2="",
                    line_3="",
                    post_town="London",
                    postcode="SW5 9JZ",
                    county="London",
                    country="England",
                    uprn="217025320",
                    udprn=23833521,
                    thoroughfare="Eardley Crescent",
                    building_number="22",
                    building_name="",
                    sub_building_name="",
                    latitude=51.4885502,
                    longitude=-0.1953659,
                    umprn=None,
                )
            )
            assert raw_result == mock_response["result"]
