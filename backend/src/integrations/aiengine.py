import asyncio
import json
import logging
from typing import Literal, Any, Optional, AsyncGenerator
from urllib.parse import urljoin

import aiohttp
from fastapi import HTTPException
from pydantic import BaseModel
from sentry_sdk import capture_exception as sentry_capture_exception

from src.db_models.document import Document
from src.integrations.base import BaseAPI
from src.schemas import SendMessageRequest

logger = logging.getLogger("uvicorn")


class AttachmentDetails(BaseModel):
    documentId: int
    documentS3Url: str
    userId: int
    documentS3Key: str
    documentS3Bucket: str
    # also check _ALLOWED_DOCUMENT_EXTENSIONS in DocumentService
    type: Literal["png", "jpg", "jpeg", "webp", "gif", "pdf", "heic", "heif"]


class UserMessage(SendMessageRequest):
    userId: int
    chatId: int
    messageId: int
    attachments: Optional[list[AttachmentDetails]] = None


class Message(BaseModel):
    content: str
    type: Literal["text", "diagnostic_report"]


class ResponseData(BaseModel):
    chatId: int
    response: Message
    additionalData: Optional[dict[str, Any]]


class AIMessageResponse(BaseModel):
    status: Literal["success", "error"]
    data: ResponseData


class JobSummary(BaseModel):
    jobId: Optional[int] = None
    jobHeadline: str
    jobSubTitle: str
    jobDetails: str
    jobDate: str
    jobTimeOfDay: str


class ParseFileRequest(BaseModel):
    documentId: int
    documentS3Url: str
    userId: int
    documentS3Key: str
    documentS3Bucket: str
    type: str


class AIEngineAPI(BaseAPI):
    def __init__(self, api_key: str, base_url: str):
        self._api_key = api_key
        self._base_url = base_url

    async def generate_response(self, to_message: UserMessage) -> AIMessageResponse:
        ai_response_json = await self._post_message(to_message)
        ai_response = AIMessageResponse.model_validate(ai_response_json)
        return ai_response

    async def generate_response_stream(self, to_message: UserMessage) -> AsyncGenerator[str, None]:
        await self._ensure_session()
        logger.info(f"Posting message to AI engine stream endpoint for chat {to_message.chatId}.")
        url = urljoin(self._base_url, "message/stream")
        headers = {"Authorization": f"Bearer {self._api_key}", "Accept": "text/event-stream"}
        payload = to_message.model_dump(exclude_none=True)

        try:
            timeout = aiohttp.ClientTimeout(total=300)  # 5 minutes  timeout
            async with self._session.post(url, headers=headers, json=payload, timeout=timeout) as response:
                if response.status != 200:
                    error_detail = f"AI Engine stream returned status {response.status}"
                    try:
                        error_body = await response.text()
                        error_detail += f": {error_body}"
                    except Exception as e:
                        logger.error(f"Unexpected error during reading body: {str(e)}", exc_info=True)
                        sentry_capture_exception(e)

                    if response.status == 529:
                        logger.error(f"AI Engine overloaded (529): {error_detail}")
                        raise HTTPException(status_code=529, detail="AI Engine unavailable")
                    elif response.status >= 500:
                        logger.error(f"AI Engine server error ({response.status}): {error_detail}")
                        raise HTTPException(status_code=500, detail="AI Internal server error occurred")
                    else:
                        logger.error(f"AI Engine request error ({response.status}): {error_detail}")
                        raise HTTPException(status_code=500, detail="Failed to communicate with AI")

                async for line_bytes in response.content:
                    yield line_bytes.decode("utf-8")

        except asyncio.TimeoutError:
            logger.error(f"Timeout connecting to or streaming from AI Engine: {url}")
            sentry_capture_exception()
            error_payload = {"code": "AI_ENGINE_TIMEOUT", "message": "Request to AI service timed out."}
            yield f"event: error\ndata: {json.dumps(error_payload)}\n\n"
            yield "event: end\ndata: Stream finished with timeout error\n\n"

        except aiohttp.ClientError as e:
            logger.error(f"AIOHTTP client error during AI engine stream: {e}", exc_info=True)
            sentry_capture_exception(e)
            error_payload = {"code": "AI_ENGINE_CONNECTION_ERROR", "message": "Could not connect to AI service."}
            yield f"event: error\ndata: {json.dumps(error_payload)}\n\n"
            yield "event: end\ndata: Stream finished with connection error\n\n"

        except HTTPException as http_exc:
            # HTTP exceptions (like 529)
            logger.error(
                f"HTTP Exception caught during AI engine stream setup: {http_exc.status_code} - {http_exc.detail}"
            )
            error_payload = {"code": f"AI_ENGINE_HTTP_{http_exc.status_code}", "message": http_exc.detail}
            yield f"event: error\ndata: {json.dumps(error_payload)}\n\n"
            yield "event: end\ndata: Stream finished with HTTP error\n\n"

        except Exception as e:
            # Unexpected errors
            logger.error(f"Unexpected error during AI engine stream: {str(e)}", exc_info=True)
            sentry_capture_exception(e)
            error_payload = {
                "code": "AI_ENGINE_STREAM_ERROR",
                "message": "An unexpected error occurred while processing the AI response.",
            }
            yield f"event: error\ndata: {json.dumps(error_payload)}\n\n"
            yield "event: end\ndata: Stream finished with unexpected error\n\n"

        finally:
            logger.info(f"Finished streaming from AI engine for chat {to_message.chatId}.")

    async def parse_file(self, document: Document) -> None:
        await self._ensure_session()
        logger.debug("Sending parse_file request to AI engine.")
        request = ParseFileRequest(
            documentId=document.id,
            documentS3Url="",
            userId=document.userId,
            documentS3Key=document.s3Key,
            documentS3Bucket=document.s3Bucket,
            type=document.fileExtension,
        )
        try:
            async with self._session.post(
                urljoin(self._base_url, "parse_file/"),
                headers={"Authorization": f"Bearer {self._api_key}"},
                json=request.model_dump(),
            ) as response:
                response.raise_for_status()
        except Exception as e:
            logger.error(f"Error in AI engine communication: {str(e)}", exc_info=True)
            sentry_capture_exception(e)

    async def _post_message(self, message: UserMessage) -> dict:
        await self._ensure_session()
        logger.debug("Posting message to AI engine.")
        try:
            async with self._session.post(
                urljoin(self._base_url, "message/"),
                headers={"Authorization": f"Bearer {self._api_key}"},
                json=message.model_dump(),
            ) as response:
                # Check specifically for 529 error code
                if response.status == 529:
                    error_data = await response.json()
                    error_message = error_data.get("detail", "AI Engine unavailable")
                    # Propagate the 529 error with its original message
                    raise HTTPException(status_code=529, detail=error_message)

                # For 500 errors, raise with default  message
                if response.status == 500:
                    raise HTTPException(status_code=500, detail="Internal server error occurred")

                # For other errors, raise generic exception
                response.raise_for_status()
                return await response.json()
        except HTTPException as http_exc:
            # Log the HTTP exception with traceback before re-raising
            logger.error(
                f"HTTP Exception in AI engine communication: {http_exc.status_code} - {http_exc.detail}", exc_info=True
            )
            raise
        except Exception as e:
            # Log the general exception with full stacktrace
            logger.error(f"Error in AI engine communication: {str(e)}", exc_info=True)
            # All other exceptions get converted to 500 with default message
            raise HTTPException(status_code=500, detail="Internal server error occurred")
