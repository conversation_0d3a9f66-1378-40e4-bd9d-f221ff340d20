import logging

from src.integrations.base import BaseAPI


class OSDataPlacesAPI(BaseAPI):
    _base_url = "https://api.os.uk/search/places/v1"

    def __init__(self, api_key: str):
        self._api_key = api_key

    async def find_places_by_name(self, name: str, max_results: int = 5) -> list:
        url = f"{self._base_url}/find"
        params = {
            "key": self._api_key,
            "query": name,
            "maxresults": max_results,
        }
        response = await self._fetch(url, params)
        places = [result["DPA"] for result in response.get("results", [])]
        return places

    async def _fetch(self, url: str, params: dict):
        await self._ensure_session()
        logging.debug(f"Fetching {url}")
        async with self._session.get(url, params=params) as response:
            response.raise_for_status()
            return await response.json()
