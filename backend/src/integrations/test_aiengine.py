import pytest
from aioresponses import aioresponses

from .aiengine import AIEngineAPI, UserMessage, Message, AIMessageResponse, ResponseData
from ..schemas import SendMessage, SendMessageMetadata


@pytest.mark.asyncio(loop_scope="session")
class TestAIEngineAPI:
    @pytest.fixture
    def api(self):
        return AIEngineAPI(api_key="test", base_url="https://aiengine.localhost")

    async def test_generate_response(self, api):
        user_message = UserMessage(
            userId=1,
            chatId=1,
            message=SendMessage(
                content="Hello, how are you?", type="text", additionalData=SendMessageMetadata(device="browser")
            ),
            messageId=1,
        )
        mock_response = {
            "status": "success",
            "data": {
                "chatId": 1,
                "response": {"content": "This is an example response", "type": "text"},
                "additionalData": {
                    "timestamp": "2025-01-24T10:20:40.402850",
                    "category": "general",
                    "confidence": 0.95,
                    "suggested_actions": [{"type": "button", "label": "Click me", "action": "click"}],
                    "imageUrls": None,
                },
            },
            "timestamp": "2025-01-24T10:20:40.402850",
        }
        url = f"{api._base_url}/message/"
        with aioresponses() as m:
            m.post(url, payload=mock_response, status=200)
            result = await api.generate_response(user_message)
            m.assert_called_once()
            print(result)
            assert result == AIMessageResponse(
                status="success",
                data=ResponseData(
                    chatId=1,
                    response=Message(content="This is an example response", type="text"),
                    additionalData={
                        "timestamp": "2025-01-24T10:20:40.402850",
                        "category": "general",
                        "confidence": 0.95,
                        "suggested_actions": [{"type": "button", "label": "Click me", "action": "click"}],
                        "imageUrls": None,
                    },
                ),
            )
