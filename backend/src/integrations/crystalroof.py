import logging
from abc import ABC, abstractmethod
from os import environ

from src.integrations.base import BaseAPI
from src.schemas import MeanHouseHoldIncomeResponse


class Location(ABC):
    @abstractmethod
    def get_identifier(self):
        pass


class Postcode(Location):
    def __init__(self, postcode):
        self.postcode = postcode

    def get_identifier(self):
        return self.postcode


class Coordinates(Location):
    def __init__(self, latitude, longitude):
        self.latitude = latitude
        self.longitude = longitude

    def get_identifier(self):
        return f"{self.latitude},{self.longitude}"


class UPRN(Location):
    def __init__(self, uprn):
        self.uprn = uprn

    def get_identifier(self):
        return f"uprn{self.uprn}"


class CrystalRoofAPI(BaseAPI):
    def __init__(self, api_key: str, base_url: str = None):
        self._api_key = api_key
        if base_url:
            self._base_url = base_url
        else:
            self._base_url = environ.get("CRYSTAL_ROOF_API_BASE_URL", "https://crystalroof.co.uk/customer-api")

    async def _fetch(self, url):
        await self._ensure_session()
        logging.debug(f"Fetching {url}")
        params = {"api_key": self._api_key}
        async with self._session.get(url, params=params) as response:
            response.raise_for_status()
            return await response.json()

    async def get_mean_household_income(self, location: Location) -> MeanHouseHoldIncomeResponse:
        identifier = location.get_identifier()
        url = f"{self._base_url}/income/mean-household-income/postcode/v1/{identifier}"
        response = await self._fetch(url)
        return MeanHouseHoldIncomeResponse.model_validate(response)

    async def get_accommodation_type(self, location: Location):
        identifier = location.get_identifier()
        url = f"{self._base_url}/housing/accommodation-type/postcode/v1/{identifier}"
        return await self._fetch(url)
