# Backend
FastAPI backend

At the moment very basic CRUD implementation with a SQLite database just to get things started and set up the project.

## Requirements
- Python 3.12
- Pipenv https://pipenv.pypa.io/en/latest/installation.html

## Installation
1. Create a virtual environment
2. Install the requirements
3. Run the server

```bash
pipenv install
pipenv shell
fastapi dev src/main.py
```

Access OpenAPI docs at http://127.0.0.1:8000/docs

## Local environment with PostgreSQL
### Requirements
- Docker
- Docker Compose
```bash
docker-compose start
```
Access OpenAPI docs at http://127.0.0.1:8000/docs

## Alembic migrations
To apply existing migrations:
```bash
alembic upgrade head
```
To create a new migration:
```bash
alembic revision --autogenerate -m "migration message"
```

## Integration tests with code coverage, formatting check
```bash
docker-compose build backend-tests
docker-compose up backend-tests
```

## Commiting changes
Use semantic commit messages. For example:
- feat(someFeature): add new feature
- fix(endpoint): fix a bug
- refactor(validators): code refactoring