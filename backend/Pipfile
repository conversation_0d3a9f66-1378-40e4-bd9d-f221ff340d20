[[source]]
url = "https://pypi.org/simple"
verify_ssl = true
name = "pypi"

[packages]
fastapi = {extras = ["standard"] }
fastapi-pagination = "*"
email-validator = "*"
uvicorn = "*"
alembic = "*"
asyncpg = "*"
greenlet = "*"
sqlalchemy = {extras = ["asyncio"] }
sentry-sdk = {extras = ["fastapi"], version = "*"}
aiohttp = "*"
pyproj = "*"
pyjwt = {extras = ["crypto"], version = "*"}
svix = "*"
sendgrid = "*"
clerk-backend-api = "*"
aioboto3 = "*"

[dev-packages]
pytest = "*"
faker = "*"
black = "*"
flake8 = "*"
pytest-cov = "*"
tox = "*"
psycopg = {extras = ["binary"], version = "*"}
aioresponses = "*"
pytest-asyncio = "*"
pytest-mock = "*"
moto = {extras = ["server"], version = "*"}

[requires]
python_version = "3.12"
python_full_version = "3.12.6"
