# syntax=docker.io/docker/dockerfile:1

FROM node:20-alpine

ARG NEXT_PUBLIC_VERCEL_ENV
ARG CLERK_SECRET_KEY
ARG NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY
ARG NEXT_PUBLIC_TEMP_TOKEN
ARG NEXT_PUBLIC_CUSTOM_API_URL

ENV NEXT_PUBLIC_VERCEL_ENV=$NEXT_PUBLIC_VERCEL_ENV
ENV CLERK_SECRET_KEY=$CLERK_SECRET_KEY
ENV NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY=$NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY
ENV NEXT_PUBLIC_TEMP_TOKEN=$NEXT_PUBLIC_TEMP_TOKEN
ENV NEXT_PUBLIC_CUSTOM_API_URL=$NEXT_PUBLIC_CUSTOM_API_URL

ENV NEXT_TELEMETRY_DISABLED=1
ENV PORT=3000
ENV HOSTNAME="0.0.0.0"

# Check https://github.com/nodejs/docker-node/tree/b4117f9333da4138b03a546ec926ef50a31506c3#nodealpine to understand why libc6-compat might be needed.
RUN apk add --no-cache libc6-compat
WORKDIR /app

COPY package.json package-lock.json .npmrc .storybook storybook-static ./
RUN npm ci
COPY . .
RUN npm run build

RUN addgroup --system --gid 1001 nodejs
RUN adduser --system --uid 1001 nextjs
USER nextjs

EXPOSE 3000

CMD ["npm", "run", "start"]
