import type { Config } from 'tailwindcss';

export default {
  darkMode: 'class',
  content: [
    './src/pages/**/*.{js,ts,jsx,tsx,mdx}',
    './src/components/**/*.{js,ts,jsx,tsx,mdx}',
    './src/app/**/*.{js,ts,jsx,tsx,mdx}',
    './src/**/*.{html,js,ts,jsx,tsx}',
    './src/components/ui/*.{js,ts,jsx,tsx,mdx}',
    './pages/**/*.{ts,tsx}',
    './components/**/*.{ts,tsx}',
    './@/components/**/*.{ts,tsx}',
    './app/**/*.{ts,tsx}',
    './src/**/*.{ts,tsx}',
  ],
  theme: {
    extend: {
      colors: {
        background: 'hsl(var(--background))',
        foreground: 'hsl(var(--foreground))',
        'colors-black': 'var(--colors-black)',
        'colors-blue-100': 'var(--colors-blue-100)',
        'colors-blue-200': 'var(--colors-blue-200)',
        'colors-blue-300': 'var(--colors-blue-300)',
        'colors-blue-400': 'var(--colors-blue-400)',
        'colors-blue-50': 'var(--colors-blue-50)',
        'colors-blue-500': 'var(--colors-blue-500)',
        'colors-blue-600': 'var(--colors-blue-600)',
        'colors-blue-700': 'var(--colors-blue-700)',
        'colors-blue-800': 'var(--colors-blue-800)',
        'colors-blue-900': 'var(--colors-blue-900)',
        'colors-gray-100': 'var(--colors-gray-100)',
        'colors-gray-200': 'var(--colors-gray-200)',
        'colors-gray-300': 'var(--colors-gray-300)',
        'colors-gray-400': 'var(--colors-gray-400)',
        'colors-gray-50': 'var(--colors-gray-50)',
        'colors-gray-500': 'var(--colors-gray-500)',
        'colors-gray-600': 'var(--colors-gray-600)',
        'colors-gray-700': 'var(--colors-gray-700)',
        'colors-gray-800': 'var(--colors-gray-800)',
        'colors-gray-900': 'var(--colors-gray-900)',
        'colors-green-100': 'var(--colors-green-100)',
        'colors-green-200': 'var(--colors-green-200)',
        'colors-green-300': 'var(--colors-green-300)',
        'colors-green-400': 'var(--colors-green-400)',
        'colors-green-50': 'var(--colors-green-50)',
        'colors-green-500': 'var(--colors-green-500)',
        'colors-green-600': 'var(--colors-green-600)',
        'colors-green-700': 'var(--colors-green-700)',
        'colors-green-800': 'var(--colors-green-800)',
        'colors-green-900': 'var(--colors-green-900)',
        'colors-indigo-100': 'var(--colors-indigo-100)',
        'colors-indigo-200': 'var(--colors-indigo-200)',
        'colors-indigo-300': 'var(--colors-indigo-300)',
        'colors-indigo-400': 'var(--colors-indigo-400)',
        'colors-indigo-50': 'var(--colors-indigo-50)',
        'colors-indigo-500': 'var(--colors-indigo-500)',
        'colors-indigo-600': 'var(--colors-indigo-600)',
        'colors-indigo-700': 'var(--colors-indigo-700)',
        'colors-indigo-800': 'var(--colors-indigo-800)',
        'colors-indigo-900': 'var(--colors-indigo-900)',
        'colors-neutral-100': 'var(--colors-neutral-100)',
        'colors-neutral-200': 'var(--colors-neutral-200)',
        'colors-neutral-300': 'var(--colors-neutral-300)',
        'colors-neutral-400': 'var(--colors-neutral-400)',
        'colors-neutral-50': 'var(--colors-neutral-50)',
        'colors-neutral-500': 'var(--colors-neutral-500)',
        'colors-neutral-600': 'var(--colors-neutral-600)',
        'colors-neutral-700': 'var(--colors-neutral-700)',
        'colors-neutral-800': 'var(--colors-neutral-800)',
        'colors-neutral-900': 'var(--colors-neutral-900)',
        'colors-orange-100': 'var(--colors-orange-100)',
        'colors-orange-200': 'var(--colors-orange-200)',
        'colors-orange-300': 'var(--colors-orange-300)',
        'colors-orange-400': 'var(--colors-orange-400)',
        'colors-orange-50': 'var(--colors-orange-50)',
        'colors-orange-500': 'var(--colors-orange-500)',
        'colors-orange-600': 'var(--colors-orange-600)',
        'colors-orange-700': 'var(--colors-orange-700)',
        'colors-orange-800': 'var(--colors-orange-800)',
        'colors-orange-900': 'var(--colors-orange-900)',
        'colors-pink-100': 'var(--colors-pink-100)',
        'colors-pink-200': 'var(--colors-pink-200)',
        'colors-pink-300': 'var(--colors-pink-300)',
        'colors-pink-400': 'var(--colors-pink-400)',
        'colors-pink-50': 'var(--colors-pink-50)',
        'colors-pink-500': 'var(--colors-pink-500)',
        'colors-pink-600': 'var(--colors-pink-600)',
        'colors-pink-700': 'var(--colors-pink-700)',
        'colors-pink-800': 'var(--colors-pink-800)',
        'colors-pink-900': 'var(--colors-pink-900)',
        'colors-purple-100': 'var(--colors-purple-100)',
        'colors-purple-200': 'var(--colors-purple-200)',
        'colors-purple-300': 'var(--colors-purple-300)',
        'colors-purple-400': 'var(--colors-purple-400)',
        'colors-purple-50': 'var(--colors-purple-50)',
        'colors-purple-500': 'var(--colors-purple-500)',
        'colors-purple-600': 'var(--colors-purple-600)',
        'colors-purple-700': 'var(--colors-purple-700)',
        'colors-purple-800': 'var(--colors-purple-800)',
        'colors-purple-900': 'var(--colors-purple-900)',
        'colors-red-100': 'var(--colors-red-100)',
        'colors-red-200': 'var(--colors-red-200)',
        'colors-red-300': 'var(--colors-red-300)',
        'colors-red-400': 'var(--colors-red-400)',
        'colors-red-50': 'var(--colors-red-50)',
        'colors-red-500': 'var(--colors-red-500)',
        'colors-red-600': 'var(--colors-red-600)',
        'colors-red-700': 'var(--colors-red-700)',
        'colors-red-800': 'var(--colors-red-800)',
        'colors-red-900': 'var(--colors-red-900)',
        'colors-teal-100': 'var(--colors-teal-100)',
        'colors-teal-200': 'var(--colors-teal-200)',
        'colors-teal-300': 'var(--colors-teal-300)',
        'colors-teal-400': 'var(--colors-teal-400)',
        'colors-teal-50': 'var(--colors-teal-50)',
        'colors-teal-500': 'var(--colors-teal-500)',
        'colors-teal-600': 'var(--colors-teal-600)',
        'colors-teal-700': 'var(--colors-teal-700)',
        'colors-teal-800': 'var(--colors-teal-800)',
        'colors-teal-900': 'var(--colors-teal-900)',
        'colors-white': 'var(--colors-white)',
        'colors-yellow-100': 'var(--colors-yellow-100)',
        'colors-yellow-200': 'var(--colors-yellow-200)',
        'colors-yellow-300': 'var(--colors-yellow-300)',
        'colors-yellow-400': 'var(--colors-yellow-400)',
        'colors-yellow-50': 'var(--colors-yellow-50)',
        'colors-yellow-500': 'var(--colors-yellow-500)',
        'colors-yellow-600': 'var(--colors-yellow-600)',
        'colors-yellow-700': 'var(--colors-yellow-700)',
        'colors-yellow-800': 'var(--colors-yellow-800)',
        'colors-yellow-900': 'var(--colors-yellow-900)',
        card: {
          DEFAULT: 'hsl(var(--card))',
          foreground: 'hsl(var(--card-foreground))',
        },
        popover: {
          DEFAULT: 'hsl(var(--popover))',
          foreground: 'hsl(var(--popover-foreground))',
        },
        primary: {
          DEFAULT: 'hsl(var(--primary))',
          foreground: 'hsl(var(--primary-foreground))',
        },
        secondary: {
          DEFAULT: 'hsl(var(--secondary))',
          foreground: 'hsl(var(--secondary-foreground))',
        },
        muted: {
          DEFAULT: 'hsl(var(--muted))',
          foreground: 'hsl(var(--muted-foreground))',
        },
        accent: {
          DEFAULT: 'hsl(var(--accent))',
          foreground: 'hsl(var(--accent-foreground))',
        },
        destructive: {
          DEFAULT: 'hsl(var(--destructive))',
          foreground: 'hsl(var(--destructive-foreground))',
        },
        border: 'hsl(var(--border))',
        input: 'hsl(var(--input))',
        ring: 'hsl(var(--ring))',
        chart: {
          '1': 'hsl(var(--chart-1))',
          '2': 'hsl(var(--chart-2))',
          '3': 'hsl(var(--chart-3))',
          '4': 'hsl(var(--chart-4))',
          '5': 'hsl(var(--chart-5))',
        },
      },
      borderRadius: {
        lg: 'var(--radius)',
        md: 'calc(var(--radius) - 2px)',
        sm: 'calc(var(--radius) - 4px)',
      },
    },
  },
  plugins: [require('tailwindcss-animate')],
} satisfies Config;
