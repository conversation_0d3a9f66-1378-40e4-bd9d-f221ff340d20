{"generatedAt": 1731950943496, "hasCustomBabel": false, "hasCustomWebpack": false, "hasStaticDirs": true, "hasStorybookEslint": true, "refCount": 0, "metaFramework": {"name": "Next", "packageName": "next", "version": "15.0.3"}, "testPackages": {}, "packageManager": {"type": "npm", "version": "10.9.0"}, "preview": {"usesGlobals": false}, "framework": {"name": "@storybook/nextjs", "options": {}}, "builder": "@storybook/builder-webpack5", "renderer": "@storybook/react", "portableStoriesFileCount": 0, "storybookVersion": "8.4.4", "storybookVersionSpecifier": "^8.4.4", "language": "typescript", "storybookPackages": {"@storybook/blocks": {"version": "8.4.4"}, "@storybook/nextjs": {"version": "8.4.4"}, "@storybook/react": {"version": "8.4.4"}, "@storybook/test": {"version": "8.4.4"}, "eslint-plugin-storybook": {"version": "0.11.0"}, "storybook": {"version": "8.4.4"}}, "addons": {"@storybook/addon-onboarding": {"version": "8.4.4"}, "@storybook/addon-essentials": {"version": "8.4.4"}, "@chromatic-com/storybook": {"version": "3.2.2"}, "@storybook/addon-interactions": {"version": "8.4.4"}}}