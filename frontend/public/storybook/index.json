{"v": 5, "entries": {"configure-your-project--docs": {"id": "configure-your-project--docs", "title": "Configure your project", "name": "Docs", "importPath": "./src/stories/Configure.mdx", "storiesImports": [], "type": "docs", "tags": ["dev", "test", "unattached-mdx"]}, "example-button--docs": {"id": "example-button--docs", "title": "Example/Button", "name": "Docs", "importPath": "./src/stories/Button.stories.ts", "type": "docs", "tags": ["dev", "test", "autodocs"], "storiesImports": []}, "example-button--primary": {"type": "story", "id": "example-button--primary", "name": "Primary", "title": "Example/Button", "importPath": "./src/stories/Button.stories.ts", "componentPath": "./src/stories/Button.tsx", "tags": ["dev", "test", "autodocs"]}, "example-button--secondary": {"type": "story", "id": "example-button--secondary", "name": "Secondary", "title": "Example/Button", "importPath": "./src/stories/Button.stories.ts", "componentPath": "./src/stories/Button.tsx", "tags": ["dev", "test", "autodocs"]}, "example-button--large": {"type": "story", "id": "example-button--large", "name": "Large", "title": "Example/Button", "importPath": "./src/stories/Button.stories.ts", "componentPath": "./src/stories/Button.tsx", "tags": ["dev", "test", "autodocs"]}, "example-button--small": {"type": "story", "id": "example-button--small", "name": "Small", "title": "Example/Button", "importPath": "./src/stories/Button.stories.ts", "componentPath": "./src/stories/Button.tsx", "tags": ["dev", "test", "autodocs"]}, "example-header--docs": {"id": "example-header--docs", "title": "Example/Header", "name": "Docs", "importPath": "./src/stories/Header.stories.ts", "type": "docs", "tags": ["dev", "test", "autodocs"], "storiesImports": []}, "example-header--logged-in": {"type": "story", "id": "example-header--logged-in", "name": "Logged In", "title": "Example/Header", "importPath": "./src/stories/Header.stories.ts", "componentPath": "./src/stories/Header.tsx", "tags": ["dev", "test", "autodocs"]}, "example-header--logged-out": {"type": "story", "id": "example-header--logged-out", "name": "Logged Out", "title": "Example/Header", "importPath": "./src/stories/Header.stories.ts", "componentPath": "./src/stories/Header.tsx", "tags": ["dev", "test", "autodocs"]}, "example-page--logged-out": {"type": "story", "id": "example-page--logged-out", "name": "Logged Out", "title": "Example/Page", "importPath": "./src/stories/Page.stories.ts", "componentPath": "./src/stories/Page.tsx", "tags": ["dev", "test"]}, "example-page--logged-in": {"type": "story", "id": "example-page--logged-in", "name": "Logged In", "title": "Example/Page", "importPath": "./src/stories/Page.stories.ts", "componentPath": "./src/stories/Page.tsx", "tags": ["dev", "test", "play-fn"]}}}