import type { StorybookConfig } from "@storybook/nextjs";
import path from "path";

const config: StorybookConfig = {
  stories: ["../src/**/*.mdx", "../src/**/*.stories.@(js|jsx|mjs|ts|tsx)"],
  addons: [
    "@storybook/addon-links",
    "@storybook/addon-essentials",
    "@storybook/addon-onboarding",
    "@storybook/addon-interactions",
  ],
  framework: {
    name: "@storybook/nextjs",
    options: {},
  },
  docs: {
    autodocs: "tag",
  },
  staticDirs: ['../public'],
  webpackFinal: async (config: any) => {
    // Initialize module rules if they don't exist
    if (!config.module?.rules) {
      config.module = {
        ...config.module,
        rules: [],
      };
    }

    // Remove any existing rules for SCSS files
    config.module.rules = config.module.rules.filter(
      (rule: any) => 
        !(rule.test instanceof RegExp && rule.test.test('.scss'))
    );

    // Add rule for font files
    config.module.rules.push({
      test: /\.(woff|woff2|eot|ttf|otf)$/,
      type: 'asset/resource',
      generator: {
        filename: (pathData: any) => {
          const relativePath = pathData.filename.replace(/^\//, '');
          return `static/media/${relativePath}`;
        }
      }
    });

    // Add new rule for SCSS modules
    config.module.rules.push({
      test: /\.scss$/,
      use: [
        'style-loader',
        {
          loader: 'css-loader',
          options: {
            importLoaders: 1,
            modules: {
              auto: /\.module\.scss$/,
              localIdentName: '[name]__[local]--[hash:base64:5]',
            },
          },
        },
        'postcss-loader',
        {
          loader: 'sass-loader',
          options: {
            sourceMap: true,
            implementation: require('sass'),
            sassOptions: {
              includePaths: [path.resolve(__dirname, '../public')]
            }
          },
        },
      ],
    });

    // Add @ alias and public path alias
    if (!config.resolve) {
      config.resolve = {};
    }

    config.resolve.alias = {
      ...config.resolve.alias,
      '@': path.resolve(__dirname, '../src'),
      '/fonts': path.resolve(__dirname, '../public/fonts'),
    };

    return config;
  },
};

export default config;
