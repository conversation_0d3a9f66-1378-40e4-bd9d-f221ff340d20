import React from 'react';
import type { Preview } from '@storybook/react';
import { <PERSON><PERSON><PERSON><PERSON> } from '@clerk/nextjs';
import '@/styles/main.css';
import '@/styles/storybook.scss';
import '@/styles/tailwind.css';

const preview: Preview = {
  decorators: [
    (Story) => (
      <ClerkProvider>
        <div className="storybook-container">
          <Story />
        </div>
      </ClerkProvider>
    ),
  ],
  parameters: {
    actions: { argTypesRegex: '^on[A-Z].*' },
    controls: {
      matchers: {
        color: /(background|color)$/i,
        date: /Date$/i,
      },
    },
    backgrounds: {
      default: 'light',
      values: [
        {
          name: 'light',
          value: '#FFFFFF',
        },
      ],
    },
    nextjs: {
      appDirectory: true,
    },
  },
};

export default preview;
