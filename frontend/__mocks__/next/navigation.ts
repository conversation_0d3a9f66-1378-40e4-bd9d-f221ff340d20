export const useRouter = vi.fn(() => ({
  push: vi.fn(),
  replace: vi.fn(),
  refresh: vi.fn(),
  back: vi.fn(),
  forward: vi.fn(),
}));

export const usePathname = vi.fn(() => '/');

export const useSearchParams = vi.fn(() => ({
  get: vi.fn(() => null),
  has: vi.fn(() => false),
  getAll: vi.fn(() => []),
  keys: vi.fn(() => []),
  values: vi.fn(() => []),
  entries: vi.fn(() => []),
  forEach: vi.fn(),
  append: vi.fn(),
  delete: vi.fn(),
  set: vi.fn(),
  sort: vi.fn(),
  toString: vi.fn(() => ''),
}));

export const useParams = vi.fn(() => ({
  chatId: 'test-chat-id',
}));

export const redirect = vi.fn();
export const notFound = vi.fn();
