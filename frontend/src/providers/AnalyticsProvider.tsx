'use client';

import React, { useEffect, Suspense } from 'react';
import { usePathname, useSearchParams } from 'next/navigation';
import { trackPageView, getIsProduction } from '@/utils/gtm';

// Client-side only component that handles the actual tracking
const PageViewTracker = () => {
  const pathname = usePathname();
  const searchParams = useSearchParams();

  // Track page views when the route changes
  useEffect(() => {
    if (pathname && getIsProduction()) {
      // Get the page title from the document if available
      const pageTitle = document.title || 'Hey Alfie';
      
      // Track the page view
      trackPageView(pathname, pageTitle);
    }
  }, [pathname, searchParams]);

  return null;
};

interface AnalyticsProviderProps {
  children: React.ReactNode;
}

export const AnalyticsProvider: React.FC<AnalyticsProviderProps> = ({ children }) => {
  // Only include the tracker in production
  const shouldTrack = getIsProduction();

  return (
    <>
      {shouldTrack && (
        <Suspense fallback={null}>
          <PageViewTracker />
        </Suspense>
      )}
      {children}
    </>
  );
};

export default AnalyticsProvider; 