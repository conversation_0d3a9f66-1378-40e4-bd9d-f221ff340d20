.container {
  display: flex;
  flex-direction: column;
  width: 100%;
}

.subtitle {
  font-family: 'Quasimoda', Helvetica;
  font-size: 16px;
  font-weight: bold;
  line-height: 150%;
  margin-bottom: 12px;
  color: var(--colors-gray-900);
  display: block;
}

.inputContainer {
  margin-bottom: 24px;
}

.inputContainer :global([class*='MultiFieldInput_container']) {
  padding: 0 !important;
}

.roleSelectionContainer {
  display: flex;
  flex-direction: column;
}

.roleSelectionTitle {
  font-size: 16px;
  font-weight: 700;
  color: var(--colors-gray-900);
  margin-bottom: 4px;
}

.roleSelectionSubtitle {
  font-size: 14px;
  font-weight: 400;
  color: var(--colors-gray-500);
  margin-bottom: 12px;
}

.roleOptionsContainer {
  display: flex;
  flex-direction: column;
  gap: 0;
}

.radioOption {
  border: 1px solid var(--colors-gray-200);
  border-radius: 8px;
  padding: 16px;
  cursor: pointer;
  transition: border-color 0.2s ease;
  margin-bottom: 8px;

  &:hover {
    border-color: var(--colors-gray-300);
  }

  &.selected {
    border: 1px solid #1b6e5a;
  }
}

.radioOption :global([class*='content']) {
  align-items: center;
}
