export type AddressType =
  | string
  | {
      line1: string;
      line2: string;
      city: string;
      postcode: string;
    };

export interface UserRoleOption {
  id: string;
  title: string;
  description: string;
}

export interface AddressRoleFormProps {
  selectedAddress?: AddressType | null;
  selectedRole?: string | null;
  onAddressChange?: (address: AddressType, displayAddress?: string) => void;
  onRoleChange?: (role: string) => void;
  forceDropdownPosition?: 'top' | 'bottom';
  className?: string;
}
