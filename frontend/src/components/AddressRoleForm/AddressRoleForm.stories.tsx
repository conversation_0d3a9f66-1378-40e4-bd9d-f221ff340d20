import type { Meta, StoryObj } from '@storybook/react';
import { AddressRoleForm } from './AddressRoleForm';

const meta: Meta<typeof AddressRoleForm> = {
  title: 'Components/AddressRoleForm',
  component: AddressRoleForm,
  parameters: {
    layout: 'padded',
  },
  argTypes: {
    onAddressChange: { action: 'addressChanged' },
    onRoleChange: { action: 'roleChanged' },
  },
};

export default meta;
type Story = StoryObj<typeof AddressRoleForm>;

export const Default: Story = {
  args: {
    selectedAddress: null,
    selectedRole: null,
  },
};

export const WithSelectedRole: Story = {
  args: {
    selectedAddress: null,
    selectedRole: 'owner',
  },
};

export const WithAddress: Story = {
  args: {
    selectedAddress: '123 Main Street, London, SW1A 1AA',
    selectedRole: 'tenant',
  },
};

export const WithManualAddress: Story = {
  args: {
    selectedAddress: {
      line1: '123 Main Street',
      line2: 'Apartment 4B',
      city: 'London',
      postcode: 'SW1A 1AA',
    },
    selectedRole: 'landlord',
  },
};
