import React from 'react';
import { render, screen, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { vi } from 'vitest';
import { FormDropdown } from '../FormDropdown';

export default describe('FormDropdown', () => {
  const defaultProps = {
    value: '',
    onChange: vi.fn(),
    options: ['Option 1', 'Option 2', 'Option 3'],
    placeholder: 'Select an option',
  };

  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('renders with placeholder when no value is selected', () => {
    render(<FormDropdown {...defaultProps} />);
    expect(screen.getByText('Select an option')).toBeInTheDocument();
  });

  it('renders with selected value when value is provided', () => {
    render(<FormDropdown {...defaultProps} value="Option 2" />);
    expect(screen.getByText('Option 2')).toBeInTheDocument();
  });

  it('opens dropdown when trigger is clicked', async () => {
    const user = userEvent.setup();
    render(<FormDropdown {...defaultProps} />);

    const trigger = screen.getByRole('button');
    await user.click(trigger);

    expect(screen.getByText('Option 1')).toBeInTheDocument();
    expect(screen.getByText('Option 2')).toBeInTheDocument();
    expect(screen.getByText('Option 3')).toBeInTheDocument();
  });

  it('closes dropdown when option is selected', async () => {
    const user = userEvent.setup();
    const mockOnChange = vi.fn();
    render(<FormDropdown {...defaultProps} onChange={mockOnChange} />);

    const trigger = screen.getByRole('button');
    await user.click(trigger);

    const option = screen.getByText('Option 2');
    await user.click(option);

    expect(mockOnChange).toHaveBeenCalledWith('Option 2');
    await waitFor(() => {
      expect(screen.queryByText('Option 1')).not.toBeInTheDocument();
    });
  });

  it('closes dropdown when clicking outside', async () => {
    const user = userEvent.setup();
    render(
      <div>
        <FormDropdown {...defaultProps} />
        <div data-testid="outside">Outside element</div>
      </div>
    );

    const trigger = screen.getByRole('button');
    await user.click(trigger);

    expect(screen.getByText('Option 1')).toBeInTheDocument();

    const outsideElement = screen.getByTestId('outside');
    await user.click(outsideElement);

    await waitFor(() => {
      expect(screen.queryByText('Option 1')).not.toBeInTheDocument();
    });
  });

  it('does not open dropdown when disabled', async () => {
    const user = userEvent.setup();
    render(<FormDropdown {...defaultProps} disabled />);

    const trigger = screen.getByRole('button');
    await user.click(trigger);

    expect(screen.queryByText('Option 1')).not.toBeInTheDocument();
  });

  it('applies disabled styling when disabled', () => {
    render(<FormDropdown {...defaultProps} disabled />);
    const trigger = screen.getByRole('button');
    expect(trigger).toBeDisabled();
  });

  it('applies custom className', () => {
    const { container } = render(<FormDropdown {...defaultProps} className="custom-class" />);
    expect(container.firstChild).toHaveClass('custom-class');
  });

  it('handles empty options array', () => {
    render(<FormDropdown {...defaultProps} options={[]} />);
    expect(screen.getByText('Select an option')).toBeInTheDocument();
  });

  it('rotates arrow icon when dropdown is open', async () => {
    const user = userEvent.setup();
    const { container } = render(<FormDropdown {...defaultProps} />);

    const trigger = screen.getByRole('button');

    await user.click(trigger);

    // Check that the arrow container has the rotated class (CSS modules will hash the class name)
    const arrowContainer = container.querySelector('[class*="rotated"]');
    expect(arrowContainer).toBeInTheDocument();
  });

  it('handles keyboard navigation', async () => {
    const user = userEvent.setup();
    render(<FormDropdown {...defaultProps} />);

    const trigger = screen.getByRole('button');
    await user.click(trigger);

    // Test that options are clickable
    const firstOption = screen.getByText('Option 1');
    expect(firstOption).toBeInTheDocument();
  });

  it('calls onChange with correct value when option is selected', async () => {
    const user = userEvent.setup();
    const mockOnChange = vi.fn();
    render(<FormDropdown {...defaultProps} onChange={mockOnChange} />);

    const trigger = screen.getByRole('button');
    await user.click(trigger);

    const option = screen.getByText('Option 3');
    await user.click(option);

    expect(mockOnChange).toHaveBeenCalledTimes(1);
    expect(mockOnChange).toHaveBeenCalledWith('Option 3');
  });

  it('maintains focus management correctly', async () => {
    const user = userEvent.setup();
    const { container } = render(<FormDropdown {...defaultProps} />);

    const trigger = screen.getByRole('button');
    await user.click(trigger);

    // Check that the trigger has the active class (CSS modules will hash the class name)
    const activeTrigger = container.querySelector('[class*="active"]');
    expect(activeTrigger).toBeInTheDocument();
  });
});
