// Vitest Snapshot v1, https://vitest.dev/guide/snapshot.html

exports[`FormDropdown Snapshots > matches default dropdown snapshot 1`] = `
<div>
  <div
    class="_container_604974"
  >
    <button
      class="_trigger_604974"
      type="button"
    >
      <span>
        Select an option
      </span>
      <div
        class="_icon_053e64 _arrow_604974"
      >
        <svg
          color="currentColor"
          fill="none"
          height="20"
          stroke-width="0"
          viewBox="0 0 24 24"
          width="20"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M5.99977 9.00005L11.9998 15L17.9998 9"
            stroke="currentColor"
            stroke-linecap="round"
            stroke-linejoin="round"
            stroke-width="1.5"
          />
        </svg>
      </div>
    </button>
  </div>
</div>
`;

exports[`FormDropdown Snapshots > matches disabled dropdown snapshot 1`] = `
<div>
  <div
    class="_container_604974"
  >
    <button
      class="_trigger_604974"
      disabled=""
      type="button"
    >
      <span>
        Select an option
      </span>
      <div
        class="_icon_053e64 _arrow_604974"
      >
        <svg
          color="currentColor"
          fill="none"
          height="20"
          stroke-width="0"
          viewBox="0 0 24 24"
          width="20"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M5.99977 9.00005L11.9998 15L17.9998 9"
            stroke="currentColor"
            stroke-linecap="round"
            stroke-linejoin="round"
            stroke-width="1.5"
          />
        </svg>
      </div>
    </button>
  </div>
</div>
`;

exports[`FormDropdown Snapshots > matches dropdown with custom className snapshot 1`] = `
<div>
  <div
    class="_container_604974 custom-dropdown"
  >
    <button
      class="_trigger_604974"
      type="button"
    >
      <span>
        Select an option
      </span>
      <div
        class="_icon_053e64 _arrow_604974"
      >
        <svg
          color="currentColor"
          fill="none"
          height="20"
          stroke-width="0"
          viewBox="0 0 24 24"
          width="20"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M5.99977 9.00005L11.9998 15L17.9998 9"
            stroke="currentColor"
            stroke-linecap="round"
            stroke-linejoin="round"
            stroke-width="1.5"
          />
        </svg>
      </div>
    </button>
  </div>
</div>
`;

exports[`FormDropdown Snapshots > matches dropdown with custom placeholder snapshot 1`] = `
<div>
  <div
    class="_container_604974"
  >
    <button
      class="_trigger_604974"
      type="button"
    >
      <span>
        Choose your option
      </span>
      <div
        class="_icon_053e64 _arrow_604974"
      >
        <svg
          color="currentColor"
          fill="none"
          height="20"
          stroke-width="0"
          viewBox="0 0 24 24"
          width="20"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M5.99977 9.00005L11.9998 15L17.9998 9"
            stroke="currentColor"
            stroke-linecap="round"
            stroke-linejoin="round"
            stroke-width="1.5"
          />
        </svg>
      </div>
    </button>
  </div>
</div>
`;

exports[`FormDropdown Snapshots > matches dropdown with empty options snapshot 1`] = `
<div>
  <div
    class="_container_604974"
  >
    <button
      class="_trigger_604974"
      type="button"
    >
      <span>
        Select an option
      </span>
      <div
        class="_icon_053e64 _arrow_604974"
      >
        <svg
          color="currentColor"
          fill="none"
          height="20"
          stroke-width="0"
          viewBox="0 0 24 24"
          width="20"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M5.99977 9.00005L11.9998 15L17.9998 9"
            stroke="currentColor"
            stroke-linecap="round"
            stroke-linejoin="round"
            stroke-width="1.5"
          />
        </svg>
      </div>
    </button>
  </div>
</div>
`;

exports[`FormDropdown Snapshots > matches dropdown with long options snapshot 1`] = `
<div>
  <div
    class="_container_604974"
  >
    <button
      class="_trigger_604974"
      type="button"
    >
      <span>
        Select an option
      </span>
      <div
        class="_icon_053e64 _arrow_604974"
      >
        <svg
          color="currentColor"
          fill="none"
          height="20"
          stroke-width="0"
          viewBox="0 0 24 24"
          width="20"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M5.99977 9.00005L11.9998 15L17.9998 9"
            stroke="currentColor"
            stroke-linecap="round"
            stroke-linejoin="round"
            stroke-width="1.5"
          />
        </svg>
      </div>
    </button>
  </div>
</div>
`;

exports[`FormDropdown Snapshots > matches dropdown with many options snapshot 1`] = `
<div>
  <div
    class="_container_604974"
  >
    <button
      class="_trigger_604974"
      type="button"
    >
      <span>
        Select an option
      </span>
      <div
        class="_icon_053e64 _arrow_604974"
      >
        <svg
          color="currentColor"
          fill="none"
          height="20"
          stroke-width="0"
          viewBox="0 0 24 24"
          width="20"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M5.99977 9.00005L11.9998 15L17.9998 9"
            stroke="currentColor"
            stroke-linecap="round"
            stroke-linejoin="round"
            stroke-width="1.5"
          />
        </svg>
      </div>
    </button>
  </div>
</div>
`;

exports[`FormDropdown Snapshots > matches dropdown with selected value snapshot 1`] = `
<div>
  <div
    class="_container_604974"
  >
    <button
      class="_trigger_604974"
      type="button"
    >
      <span>
        Option 2
      </span>
      <div
        class="_icon_053e64 _arrow_604974"
      >
        <svg
          color="currentColor"
          fill="none"
          height="20"
          stroke-width="0"
          viewBox="0 0 24 24"
          width="20"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M5.99977 9.00005L11.9998 15L17.9998 9"
            stroke="currentColor"
            stroke-linecap="round"
            stroke-linejoin="round"
            stroke-width="1.5"
          />
        </svg>
      </div>
    </button>
  </div>
</div>
`;

exports[`FormDropdown Snapshots > matches ownership type dropdown snapshot 1`] = `
<div>
  <div
    class="_container_604974"
  >
    <button
      class="_trigger_604974"
      type="button"
    >
      <span>
        Select ownership type
      </span>
      <div
        class="_icon_053e64 _arrow_604974"
      >
        <svg
          color="currentColor"
          fill="none"
          height="20"
          stroke-width="0"
          viewBox="0 0 24 24"
          width="20"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M5.99977 9.00005L11.9998 15L17.9998 9"
            stroke="currentColor"
            stroke-linecap="round"
            stroke-linejoin="round"
            stroke-width="1.5"
          />
        </svg>
      </div>
    </button>
  </div>
</div>
`;

exports[`FormDropdown Snapshots > matches property type dropdown snapshot 1`] = `
<div>
  <div
    class="_container_604974"
  >
    <button
      class="_trigger_604974"
      type="button"
    >
      <span>
        House
      </span>
      <div
        class="_icon_053e64 _arrow_604974"
      >
        <svg
          color="currentColor"
          fill="none"
          height="20"
          stroke-width="0"
          viewBox="0 0 24 24"
          width="20"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M5.99977 9.00005L11.9998 15L17.9998 9"
            stroke="currentColor"
            stroke-linecap="round"
            stroke-linejoin="round"
            stroke-width="1.5"
          />
        </svg>
      </div>
    </button>
  </div>
</div>
`;
