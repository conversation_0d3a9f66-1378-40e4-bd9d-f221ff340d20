import React from 'react';
import { render } from '@testing-library/react';
import { vi } from 'vitest';
import { FormDropdown } from '../FormDropdown';

export default describe('FormDropdown Snapshots', () => {
  const defaultProps = {
    value: '',
    onChange: vi.fn(),
    options: ['Option 1', 'Option 2', 'Option 3'],
    placeholder: 'Select an option',
  };

  it('matches default dropdown snapshot', () => {
    const { container } = render(<FormDropdown {...defaultProps} />);
    expect(container).toMatchSnapshot();
  });

  it('matches dropdown with selected value snapshot', () => {
    const { container } = render(<FormDropdown {...defaultProps} value="Option 2" />);
    expect(container).toMatchSnapshot();
  });

  it('matches disabled dropdown snapshot', () => {
    const { container } = render(<FormDropdown {...defaultProps} disabled />);
    expect(container).toMatchSnapshot();
  });

  it('matches dropdown with custom placeholder snapshot', () => {
    const { container } = render(
      <FormDropdown {...defaultProps} placeholder="Choose your option" />
    );
    expect(container).toMatchSnapshot();
  });

  it('matches dropdown with long options snapshot', () => {
    const { container } = render(
      <FormDropdown
        {...defaultProps}
        options={[
          'Very long option that might overflow the container',
          'Another extremely long option with lots of text',
          'Short option',
        ]}
      />
    );
    expect(container).toMatchSnapshot();
  });

  it('matches dropdown with many options snapshot', () => {
    const { container } = render(
      <FormDropdown
        {...defaultProps}
        options={Array.from({ length: 10 }, (_, i) => `Option ${i + 1}`)}
      />
    );
    expect(container).toMatchSnapshot();
  });

  it('matches dropdown with custom className snapshot', () => {
    const { container } = render(<FormDropdown {...defaultProps} className="custom-dropdown" />);
    expect(container).toMatchSnapshot();
  });

  it('matches dropdown with empty options snapshot', () => {
    const { container } = render(<FormDropdown {...defaultProps} options={[]} />);
    expect(container).toMatchSnapshot();
  });

  it('matches property type dropdown snapshot', () => {
    const { container } = render(
      <FormDropdown
        {...defaultProps}
        options={['House', 'Flat', 'Office', 'Retail']}
        placeholder="Select property type"
        value="House"
      />
    );
    expect(container).toMatchSnapshot();
  });

  it('matches ownership type dropdown snapshot', () => {
    const { container } = render(
      <FormDropdown
        {...defaultProps}
        options={['Freehold', 'Leasehold', 'Share of freehold']}
        placeholder="Select ownership type"
      />
    );
    expect(container).toMatchSnapshot();
  });
});
