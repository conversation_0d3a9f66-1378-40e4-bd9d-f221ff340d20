import React, { useState, useRef, useEffect } from 'react';
import classNames from 'classnames';
import { HugeiconsIcon } from '../HugeiconsIcon';
import { ArrowDown01Icon } from '@hugeicons-pro/core-stroke-standard';
import { IconSvgObject } from '@hugeicons/react';
import { FormDropdownProps } from './FormDropdown.types';
import styles from './FormDropdown.module.scss';

export const FormDropdown: React.FC<FormDropdownProps> = ({
  value,
  onChange,
  options,
  placeholder = 'Select an option',
  disabled = false,
  className,
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const containerRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (containerRef.current && !containerRef.current.contains(event.target as Node)) {
        setIsOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  const handleToggle = () => {
    if (!disabled) {
      setIsOpen(!isOpen);
    }
  };

  const handleOptionSelect = (option: string) => {
    onChange(option);
    setIsOpen(false);
  };

  return (
    <div className={classNames(styles.container, className)} ref={containerRef}>
      <button
        type="button"
        className={classNames(styles.trigger, { [styles.active]: isOpen })}
        onClick={handleToggle}
        disabled={disabled}
      >
        <span>{value || placeholder}</span>
        <HugeiconsIcon
          icon={ArrowDown01Icon as unknown as IconSvgObject}
          size={20}
          className={classNames(styles.arrow, { [styles.rotated]: isOpen })}
        />
      </button>

      {isOpen && (
        <div className={styles.dropdown}>
          {options.map((option) => (
            <div key={option} className={styles.option} onClick={() => handleOptionSelect(option)}>
              {option}
            </div>
          ))}
        </div>
      )}
    </div>
  );
};
