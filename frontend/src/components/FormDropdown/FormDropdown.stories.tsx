import type { Meta, StoryObj } from '@storybook/react';
import { useState } from 'react';
import { FormDropdown } from './FormDropdown';

const meta: Meta<typeof FormDropdown> = {
  title: 'Components/FormDropdown',
  component: FormDropdown,
  parameters: {
    layout: 'padded',
    docs: {
      description: {
        component: `
## FormDropdown

A custom dropdown component designed for forms with consistent styling and behavior.

### Features
- Custom styling with CSS variables
- Keyboard navigation support
- Click outside to close
- Disabled state support
- Responsive design

### Usage

\`\`\`jsx
import { FormDropdown } from '@/components/FormDropdown';

const [value, setValue] = useState('');

<FormDropdown
  value={value}
  onChange={setValue}
  options={['Option 1', 'Option 2', 'Option 3']}
  placeholder="Select an option"
/>
\`\`\`
        `,
      },
    },
  },
  tags: ['autodocs'],
  argTypes: {
    value: {
      control: 'text',
      description: 'Currently selected value',
    },
    onChange: {
      action: 'changed',
      description: 'Callback fired when selection changes',
    },
    options: {
      control: 'object',
      description: 'Array of available options',
    },
    placeholder: {
      control: 'text',
      description: 'Placeholder text when no option is selected',
    },
    disabled: {
      control: 'boolean',
      description: 'Whether the dropdown is disabled',
    },
    className: {
      control: 'text',
      description: 'Additional CSS classes',
    },
  },
  decorators: [
    (Story) => (
      <div style={{ width: '400px', padding: '20px' }}>
        <Story />
      </div>
    ),
  ],
} satisfies Meta<typeof FormDropdown>;

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
  args: {
    value: '',
    options: ['Option 1', 'Option 2', 'Option 3'],
    placeholder: 'Select an option',
    disabled: false,
  },
};

export const WithSelectedValue: Story = {
  args: {
    ...Default.args,
    value: 'Option 2',
  },
};

export const Disabled: Story = {
  args: {
    ...Default.args,
    disabled: true,
  },
};

export const LongOptions: Story = {
  args: {
    ...Default.args,
    options: [
      'Very long option that might overflow the container',
      'Another extremely long option with lots of text',
      'Short option',
      'Medium length option here',
    ],
  },
};

export const ManyOptions: Story = {
  args: {
    ...Default.args,
    options: Array.from({ length: 20 }, (_, i) => `Option ${i + 1}`),
  },
};

export const PropertyTypes: Story = {
  args: {
    ...Default.args,
    options: ['House', 'Flat', 'Office', 'Retail'],
    placeholder: 'Select property type',
  },
};

export const Interactive: Story = {
  render: () => {
    const [value, setValue] = useState('');

    return (
      <div className="space-y-4">
        <FormDropdown
          value={value}
          onChange={setValue}
          options={['Freehold', 'Leasehold', 'Share of freehold']}
          placeholder="Select ownership type"
        />
        <p className="text-sm text-gray-600">Selected value: {value || 'None'}</p>
      </div>
    );
  },
};

export const AllStates: Story = {
  render: () => {
    const [values, setValues] = useState({
      normal: '',
      disabled: 'Disabled option',
    });

    return (
      <div className="space-y-6">
        <div>
          <h3 className="text-sm font-medium mb-2">Normal State</h3>
          <FormDropdown
            value={values.normal}
            onChange={(newValue) => setValues((prev) => ({ ...prev, normal: newValue }))}
            options={['Option 1', 'Option 2', 'Option 3']}
            placeholder="Select an option"
          />
        </div>

        <div>
          <h3 className="text-sm font-medium mb-2">Disabled State</h3>
          <FormDropdown
            value={values.disabled}
            onChange={(newValue) => setValues((prev) => ({ ...prev, disabled: newValue }))}
            options={['Option 1', 'Option 2', 'Option 3']}
            placeholder="Select an option"
            disabled
          />
        </div>
      </div>
    );
  },
};
