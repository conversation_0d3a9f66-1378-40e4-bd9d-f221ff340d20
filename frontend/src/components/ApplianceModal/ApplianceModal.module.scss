.modalOverlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background: rgba(0, 0, 0, 0.18);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.modal {
  background: var(--colors-white);
  border-radius: 16px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
  padding: 32px 32px 24px 32px;
  min-width: 400px;
  max-width: 95vw;
  position: relative;
  display: flex;
  flex-direction: column;
}

.title {
  font-family: Quasimoda, Arial, sans-serif;
  font-size: 24px;
  font-weight: bold;
  margin-top: 8px;
  margin-bottom: 20px;
  color: var(--colors-black);
}

.formGroup {
  display: flex;
  flex-direction: column;
  margin-bottom: 20px;
}

.label {
  font-size: 16px;
  font-weight: bold;
  margin-bottom: 8px;
  color: var(--colors-black);
}

.input {
  font-family: Quasimoda, <PERSON><PERSON>, sans-serif;
  font-size: 16px;
  padding: 12px 8px;
  border: 1px solid var(--colors-gray-200);
  border-radius: 8px;
  border-color: var(--colors-gray-200);
  transition: border-color 0.2s ease;

  &:focus {
    outline: none;
    border-color: var(--colors-green-600);
  }
}
