import React, { useState } from 'react';
import { Meta, StoryFn } from '@storybook/react';
import { ApplianceModal } from './ApplianceModal';
import { Button } from '../Button';
import { ButtonSize, ButtonType } from '../Button/Button.types';
import { ButtonColor } from '../Button/Button.types';

const meta = {
  title: 'Components/ApplianceModal',
  component: ApplianceModal,
  parameters: {
    layout: 'centered',
  },
  tags: ['autodocs'],
} satisfies Meta<typeof ApplianceModal>;

export default meta;

export const Default: StoryFn = () => {
  const [open, setOpen] = useState(false);
  return (
    <div>
      <Button
        onClick={() => setOpen(true)}
        size={ButtonSize.L}
        color={ButtonColor.GREEN_PRIMARY}
        type={ButtonType.TERTIARY}
      >
        Add manually
      </Button>
      <ApplianceModal
        open={open}
        onClose={() => setOpen(false)}
        onSave={(data) => {
          setOpen(false);
          alert('Appliance saved: ' + JSON.stringify(data, null, 2));
        }}
      />
    </div>
  );
};
