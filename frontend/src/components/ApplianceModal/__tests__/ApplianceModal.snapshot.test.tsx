import React, { ReactNode } from 'react';
import { render } from '@testing-library/react';
import { ApplianceModal } from '../ApplianceModal';

interface MockModalProps {
  children: ReactNode;
  open: boolean;
  actionButtons?: ReactNode;
  title?: string;
}

interface MockButtonProps {
  children: ReactNode;
  disabled?: boolean;
  loading?: boolean;
}

interface MockApplianceFormProps {
  showValidation?: boolean;
}

vi.mock('@/components/Modal', () => ({
  Modal: ({ children, open, actionButtons, title }: MockModalProps) =>
    open ? (
      <div data-testid="mock-modal">
        {title && <h2 className="_title_3da5cd">{title}</h2>}
        {children}
        <div data-testid="action-buttons">{actionButtons}</div>
      </div>
    ) : null,
}));

vi.mock('@/components/Button', () => ({
  Button: ({ children, disabled, loading }: MockButtonProps) => (
    <button disabled={disabled || loading} data-testid={loading ? 'loading-button' : 'mock-button'}>
      {children}
    </button>
  ),
}));

vi.mock('../ApplianceForm', () => ({
  ApplianceForm: ({ showValidation }: MockApplianceFormProps) => (
    <div data-testid="mock-appliance-form">
      {showValidation && <div data-testid="validation-message">Please fill required fields</div>}
    </div>
  ),
}));

describe('ApplianceModal Snapshots', () => {
  const defaultProps = {
    open: true,
    onClose: vi.fn(),
    onSave: vi.fn(),
    loading: false,
  };

  it('matches snapshot when open', () => {
    const { container } = render(<ApplianceModal {...defaultProps} />);
    expect(container).toMatchSnapshot();
  });

  it('matches snapshot when closed', () => {
    const { container } = render(<ApplianceModal {...defaultProps} open={false} />);
    expect(container).toMatchSnapshot();
  });

  it('matches snapshot when loading', () => {
    const { container } = render(<ApplianceModal {...defaultProps} loading={true} />);
    expect(container).toMatchSnapshot();
  });

  it('matches snapshot with validation state', () => {
    const { container, getByText } = render(<ApplianceModal {...defaultProps} />);

    getByText('Save').click();

    expect(container).toMatchSnapshot();
  });
});
