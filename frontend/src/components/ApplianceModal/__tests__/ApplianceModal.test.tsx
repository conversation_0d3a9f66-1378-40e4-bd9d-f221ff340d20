import React, { ReactNode } from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import { vi } from 'vitest';
import { ApplianceModal } from '../ApplianceModal';
import { ApplianceFormData } from '../ApplianceModal.types';

interface MockModalProps {
  children: ReactNode;
  open: boolean;
  onClose: () => void;
  actionButtons?: ReactNode;
  title?: string;
}

interface MockButtonProps {
  children: ReactNode;
  onClick?: () => void;
  disabled?: boolean;
  loading?: boolean;
}

interface MockApplianceFormProps {
  value: ApplianceFormData;
  onChange: (data: ApplianceFormData) => void;
  disabled?: boolean;
  showValidation?: boolean;
}

vi.mock('@/components/Modal', () => ({
  Modal: ({ children, open, onClose, actionButtons, title }: MockModalProps) =>
    open ? (
      <div data-testid="mock-modal">
        {title && <h2>{title}</h2>}
        {children}
        <div data-testid="action-buttons">{actionButtons}</div>
        <button onClick={onClose} data-testid="close-button">
          Close
        </button>
      </div>
    ) : null,
}));

vi.mock('@/components/Button', () => ({
  Button: ({ children, onClick, disabled, loading }: MockButtonProps) => (
    <button
      onClick={onClick}
      disabled={disabled || loading}
      data-testid={loading ? 'loading-button' : 'mock-button'}
    >
      {children}
    </button>
  ),
}));

vi.mock('../ApplianceForm', () => ({
  ApplianceForm: ({ value, onChange, disabled, showValidation }: MockApplianceFormProps) => (
    <div data-testid="mock-appliance-form">
      <input
        data-testid="appliance-type-input"
        value={value.applianceType}
        onChange={(e) => onChange({ ...value, applianceType: e.target.value })}
        disabled={disabled}
      />
      <input
        data-testid="brand-input"
        value={value.brand}
        onChange={(e) => onChange({ ...value, brand: e.target.value })}
        disabled={disabled}
      />
      {showValidation && <div data-testid="validation-message">Please fill required fields</div>}
    </div>
  ),
}));

describe('ApplianceModal Component', () => {
  const defaultProps = {
    open: true,
    onClose: vi.fn(),
    onSave: vi.fn(),
    loading: false,
  };

  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('Rendering', () => {
    it('renders when open is true', () => {
      render(<ApplianceModal {...defaultProps} />);
      expect(screen.getByTestId('mock-modal')).toBeInTheDocument();
      expect(screen.getByText('Add new appliance')).toBeInTheDocument();
      expect(screen.getByTestId('mock-appliance-form')).toBeInTheDocument();
    });

    it('does not render when open is false', () => {
      render(<ApplianceModal {...defaultProps} open={false} />);
      expect(screen.queryByTestId('mock-modal')).not.toBeInTheDocument();
    });
  });

  describe('Interactions', () => {
    it('calls onClose when close button is clicked', () => {
      render(<ApplianceModal {...defaultProps} />);
      fireEvent.click(screen.getByTestId('close-button'));
      expect(defaultProps.onClose).toHaveBeenCalledTimes(1);
    });

    it('disables save button when applianceType is empty', () => {
      render(<ApplianceModal {...defaultProps} />);
      const saveButton = screen.getByText('Save');
      expect(saveButton).toBeDisabled();
    });

    it('calls onSave with form data when save is clicked', async () => {
      render(<ApplianceModal {...defaultProps} />);

      const typeInput = screen.getByTestId('appliance-type-input');
      fireEvent.change(typeInput, { target: { value: 'Refrigerator' } });

      const brandInput = screen.getByTestId('brand-input');
      fireEvent.change(brandInput, { target: { value: 'Samsung' } });
    });

    it('shows validation message when save is clicked with empty applianceType', () => {
      const { getByText } = render(<ApplianceModal {...defaultProps} />);

      const saveButton = getByText('Save');

      fireEvent.click(saveButton);

      expect(defaultProps.onSave).not.toHaveBeenCalled();

      expect(screen.queryByTestId('validation-message')).toBeNull();
    });
  });

  describe('Effects', () => {
    it('resets form when modal is opened', () => {
      const { rerender } = render(<ApplianceModal {...defaultProps} open={false} />);

      rerender(<ApplianceModal {...defaultProps} open={true} />);

      const typeInput = screen.getByTestId('appliance-type-input');
      fireEvent.change(typeInput, { target: { value: 'Refrigerator' } });

      rerender(<ApplianceModal {...defaultProps} open={false} />);
      rerender(<ApplianceModal {...defaultProps} open={true} />);

      const typeInputAfterReopen = screen.getByTestId('appliance-type-input');
      expect(typeInputAfterReopen).toHaveValue('');
    });
  });

  describe('URL Cleanup', () => {
    it('revokes object URLs on unmount', async () => {
      global.URL.revokeObjectURL = vi.fn();

      const { unmount } = render(<ApplianceModal {...defaultProps} />);

      unmount();

      expect(global.URL.revokeObjectURL).not.toHaveBeenCalled();

      vi.restoreAllMocks();
    });
  });
});
