import React from 'react';
import { Input } from '../Input';
import { InputSize, InputState } from '../Input/Input.types';
import styles from './ApplianceModal.module.scss';
import { ApplianceFormData } from './ApplianceModal.types';

interface ApplianceFormProps {
  value: ApplianceFormData;
  onChange: (value: ApplianceFormData) => void;
  disabled?: boolean;
  showValidation?: boolean;
}

export const ApplianceForm: React.FC<ApplianceFormProps> = ({
  value,
  onChange,
  disabled,
  showValidation,
}) => {
  const handleFieldChange = (field: keyof ApplianceFormData, newValue: string) => {
    onChange({ ...value, [field]: newValue });
  };

  return (
    <form className={styles.form} autoComplete="off" onSubmit={(e) => e.preventDefault()}>
      <div className={styles.formGroup}>
        <div className={styles.label}>Appliance type*</div>
        <Input
          labelText=""
          placeholderText="e.g. Fridge"
          value={value.applianceType}
          onChange={(newValue) => handleFieldChange('applianceType', newValue)}
          size={InputSize.LARGE}
          state={
            showValidation && !value.applianceType.trim()
              ? InputState.ERROR
              : disabled
                ? InputState.DISABLED
                : InputState.NORMAL
          }
          showLabel={false}
          showLeftIcon={false}
          showHelperText={false}
          fullWidth
          greenBorder
          disableClear
        />
      </div>
      <div className={styles.formGroup}>
        <div className={styles.label}>Brand</div>
        <Input
          labelText=""
          placeholderText="e.g. Samsung"
          value={value.brand}
          onChange={(newValue) => handleFieldChange('brand', newValue)}
          size={InputSize.LARGE}
          state={disabled ? InputState.DISABLED : InputState.NORMAL}
          showLabel={false}
          showLeftIcon={false}
          showHelperText={false}
          fullWidth
          greenBorder
          disableClear
        />
      </div>
      <div className={styles.formGroup}>
        <div className={styles.label}>Model</div>
        <Input
          labelText=""
          placeholderText="e.g. Series 6 SpaceMax"
          value={value.model}
          onChange={(newValue) => handleFieldChange('model', newValue)}
          size={InputSize.LARGE}
          state={disabled ? InputState.DISABLED : InputState.NORMAL}
          showLabel={false}
          showLeftIcon={false}
          showHelperText={false}
          fullWidth
          greenBorder
          disableClear
        />
      </div>
      <div className={styles.formGroup}>
        <div className={styles.label}>Serial number</div>
        <Input
          labelText=""
          placeholderText="e.g. AB1234CDEFG"
          value={value.serialNumber}
          onChange={(newValue) => handleFieldChange('serialNumber', newValue)}
          size={InputSize.LARGE}
          state={disabled ? InputState.DISABLED : InputState.NORMAL}
          showLabel={false}
          showLeftIcon={false}
          showHelperText={false}
          fullWidth
          greenBorder
          disableClear
        />
      </div>
      <div className={styles.formGroup}>
        <div className={styles.label}>Warranty</div>
        <Input
          labelText=""
          placeholderText="e.g. 5 years"
          value={value.warranty}
          onChange={(newValue) => handleFieldChange('warranty', newValue)}
          size={InputSize.LARGE}
          state={disabled ? InputState.DISABLED : InputState.NORMAL}
          showLabel={false}
          showLeftIcon={false}
          showHelperText={false}
          fullWidth
          greenBorder
          disableClear
        />
      </div>
    </form>
  );
};
