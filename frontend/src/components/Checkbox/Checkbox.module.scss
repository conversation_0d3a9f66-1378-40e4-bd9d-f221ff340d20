.checkbox-field {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-3);
  cursor: pointer;

  &.disabled {
    cursor: not-allowed;
  }
}

.content {
  display: flex;
  align-items: flex-start;
  gap: var(--spacing-2);
  cursor: inherit;
}

.checkbox-wrapper {
    display: flex;
  position: relative;
  width: var(--spacing-4);
  height: var(--spacing-4);
  cursor: inherit;

  svg {
    position: absolute;
    top: 3px;
    left: 3px;
    pointer-events: none;
  }
}

.checkbox {
  appearance: none;
  width: var(--spacing-4);
  height: var(--spacing-4);
  border: 0.5px solid var(--colors-gray-300);
  border-radius: var(--border-radius-rounded);
  background-color: var(--colors-gray-50);
  cursor: inherit;
  position: relative;
  margin: 0;

  &:checked {
    border-color: var(--colors-blue-700);
    background-color: var(--colors-green-500);
  }

  &.error {
    border-color: var(--colors-red-500);
  }

  &.disabled {
    background-color: var(--colors-gray-100);
    border-color: var(--colors-gray-300);
  }
}

.label-helper {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-1);
  cursor: inherit;
}

.label {
  color: var(--colors-gray-900);
  font-family: "Quasimoda", Helvetica;
  font-size: 16px;
  font-weight: 400;
  line-height: 16px;
  text-align: left;
}

.caption {
  color: var(--colors-gray-500);
  font-family: "Quasimoda", Helvetica;
  font-size: 12px;
  font-weight: 400;
  line-height: 16px;
  text-align: left;

  &.disabled {
    color: var(--colors-gray-400);
  }
}

.error-container {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
}

.error-icon {
  color: var(--colors-red-500);
}

.error-text {
  color: var(--colors-red-500);
  font-family: "Quasimoda", Helvetica;
  font-size: 16px;
  font-weight: 400;
  line-height: 16px;
  text-align: left;
} 