import type { <PERSON><PERSON>, <PERSON>Obj } from "@storybook/react";
import { useState } from "react";
import { Checkbox } from "./Checkbox";
import { CheckboxState } from "./Checkbox.types";

const meta = {
  title: "Components/Checkbox",
  component: Checkbox,
  parameters: {
    layout: "centered",
  },
  tags: ["autodocs"],
  argTypes: {
    state: {
      control: "select",
      options: Object.values(CheckboxState),
      defaultValue: CheckboxState.NORMAL,
    },
  },
} satisfies Meta<typeof Checkbox>;

export default meta;
type Story = StoryObj<typeof meta>;

const InteractiveCheckbox = (args: Story["args"]) => {
  const [isChecked, setIsChecked] = useState(args?.checked || false);
  return (
    <Checkbox
      {...args}
      checked={isChecked}
      onChange={(checked) => setIsChecked(checked)}
    />
  );
};

export const Default: Story = {
  args: {
    labelText: "Checkbox label",
    helperText: "Helper text",
  },
  render: (args) => <InteractiveCheckbox {...args} />,
};

export const InitiallyChecked: Story = {
  args: {
    ...Default.args,
    checked: true,
  },
  render: (args) => <InteractiveCheckbox {...args} />,
};

export const WithoutHelperText: Story = {
  args: {
    labelText: "Checkbox label",
  },
  render: (args) => <InteractiveCheckbox {...args} />,
};

export const Error: Story = {
  args: {
    ...Default.args,
    state: CheckboxState.ERROR,
    errorText: "Error message",
  },
  render: (args) => <InteractiveCheckbox {...args} />,
};

export const Disabled: Story = {
  args: {
    ...Default.args,
    state: CheckboxState.DISABLED,
  },
};

export const Group: Story = {
  render: () => {
    const [checkedItems, setCheckedItems] = useState<Record<string, boolean>>(
      {}
    );

    return (
      <div style={{ display: "flex", flexDirection: "column", gap: "8px" }}>
        <Checkbox
          labelText="Option 1"
          helperText="Helper text 1"
          name="group"
          value="1"
          checked={checkedItems["1"]}
          onChange={(checked) =>
            setCheckedItems((prev) => ({ ...prev, "1": checked }))
          }
        />
        <Checkbox
          labelText="Option 2"
          helperText="Helper text 2"
          name="group"
          value="2"
          checked={checkedItems["2"]}
          onChange={(checked) =>
            setCheckedItems((prev) => ({ ...prev, "2": checked }))
          }
        />
        <Checkbox
          labelText="Option 3"
          helperText="Helper text 3"
          name="group"
          value="3"
          checked={checkedItems["3"]}
          onChange={(checked) =>
            setCheckedItems((prev) => ({ ...prev, "3": checked }))
          }
        />
      </div>
    );
  },
};
