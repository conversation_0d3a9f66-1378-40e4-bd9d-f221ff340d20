import React from "react";
import { render, fireEvent } from "@testing-library/react";
import { vi } from "vitest";
import { Checkbox } from "../Checkbox";
import { CheckboxState } from "../Checkbox.types";
import styles from "../Checkbox.module.scss";

export default describe("Checkbox Component", () => {
  const defaultProps = {
    labelText: "Test Label",
    helperText: "Test Helper Text",
  };

  // Basic Rendering Tests
  it("renders without crashing", () => {
    const { container } = render(<Checkbox {...defaultProps} />);
    expect(container).toBeInTheDocument();
  });

  it("renders label text correctly", () => {
    const { getByText } = render(<Checkbox {...defaultProps} />);
    expect(getByText("Test Label")).toBeInTheDocument();
  });

  it("renders helper text when provided", () => {
    const { getByText } = render(<Checkbox {...defaultProps} />);
    expect(getByText("Test Helper Text")).toBeInTheDocument();
  });

  // State Variants Tests
  describe("State Variants", () => {
    it.each(Object.values(CheckboxState))(
      "renders %s state correctly",
      (state) => {
        const { container } = render(
          <Checkbox
            {...defaultProps}
            state={state}
            checked={state === CheckboxState.CHECKED}
          />
        );
        const checkbox = container.querySelector("input[type='checkbox']");
        expect(checkbox).toHaveClass(styles.checkbox);
        if (state !== CheckboxState.NORMAL) {
          expect(checkbox).toHaveClass(styles[state.toLowerCase()]);
        }
      }
    );
  });

  // Interaction Tests
  describe("Interactions", () => {
    it("calls onChange when clicked", () => {
      const handleChange = vi.fn();
      const { container } = render(
        <Checkbox {...defaultProps} onChange={handleChange} />
      );
      const checkbox = container.querySelector("input[type='checkbox']");
      fireEvent.click(checkbox!);
      expect(handleChange).toHaveBeenCalledWith(true);
    });

    it("doesn't call onChange when disabled", () => {
      const handleChange = vi.fn();
      const { container } = render(
        <Checkbox
          {...defaultProps}
          state={CheckboxState.DISABLED}
          onChange={handleChange}
        />
      );
      const checkbox = container.querySelector("input[type='checkbox']");
      fireEvent.click(checkbox!);
      expect(handleChange).not.toHaveBeenCalled();
    });
  });

  // Error State Tests
  describe("Error State", () => {
    it("renders error message when in error state", () => {
      const errorText = "Error Message";
      const { getByText } = render(
        <Checkbox
          {...defaultProps}
          state={CheckboxState.ERROR}
          errorText={errorText}
        />
      );
      expect(getByText(errorText)).toBeInTheDocument();
    });

    it("doesn't render error message in normal state", () => {
      const errorText = "Error Message";
      const { queryByText } = render(
        <Checkbox {...defaultProps} errorText={errorText} />
      );
      expect(queryByText(errorText)).not.toBeInTheDocument();
    });
  });

  // Check Icon Tests
  describe("Check Icon", () => {
    it("shows check icon when checked", () => {
      const { container } = render(<Checkbox {...defaultProps} checked />);
      const svg = container.querySelector("svg");
      expect(svg).toBeInTheDocument();
    });

    it("hides check icon when unchecked", () => {
      const { container } = render(<Checkbox {...defaultProps} />);
      const svg = container.querySelector("svg");
      expect(svg).not.toBeInTheDocument();
    });
  });
});
