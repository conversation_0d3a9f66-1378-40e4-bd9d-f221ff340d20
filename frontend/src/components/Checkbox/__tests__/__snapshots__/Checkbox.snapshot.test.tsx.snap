// Vitest Snapshot v1, https://vitest.dev/guide/snapshot.html

exports[`Checkbox Snapshots > matches checkbox with error text snapshot 1`] = `
<div>
  <label
    class="_checkbox-field_cd53e0 _error_cd53e0"
  >
    <div
      class="_content_cd53e0"
    >
      <div
        class="_checkbox-wrapper_cd53e0"
      >
        <input
          class="_checkbox_cd53e0 _error_cd53e0"
          type="checkbox"
          value=""
        />
      </div>
      <div
        class="_label-helper_cd53e0"
      >
        <div
          class="_label_cd53e0"
        >
          Test Label
        </div>
        <div
          class="_caption_cd53e0 _error_cd53e0"
        >
          Test Helper Text
        </div>
      </div>
    </div>
    <div
      class="_error-container_cd53e0"
    >
      <svg
        class="_error-icon_cd53e0"
        color="currentColor"
        fill="none"
        height="16"
        viewBox="0 0 24 24"
        width="16"
        xmlns="http://www.w3.org/2000/svg"
      >
        <circle
          cx="12"
          cy="12"
          r="10"
          stroke="currentColor"
          stroke-width="1.5"
        />
        <path
          d="M11.992 15H12.001"
          stroke="currentColor"
          stroke-linecap="round"
          stroke-linejoin="round"
          stroke-width="2"
        />
        <path
          d="M12 12L12 8"
          stroke="currentColor"
          stroke-linecap="round"
          stroke-linejoin="round"
          stroke-width="1.5"
        />
      </svg>
      <span
        class="_error-text_cd53e0"
      >
        Test Error Text
      </span>
    </div>
  </label>
</div>
`;

exports[`Checkbox Snapshots > matches checkbox without helper text snapshot 1`] = `
<div>
  <label
    class="_checkbox-field_cd53e0 _normal_cd53e0"
  >
    <div
      class="_content_cd53e0"
    >
      <div
        class="_checkbox-wrapper_cd53e0"
      >
        <input
          class="_checkbox_cd53e0"
          type="checkbox"
          value=""
        />
      </div>
      <div
        class="_label-helper_cd53e0"
      >
        <div
          class="_label_cd53e0"
        >
          Test Label
        </div>
      </div>
    </div>
  </label>
</div>
`;

exports[`Checkbox Snapshots > matches checked checkbox snapshot 1`] = `
<div>
  <label
    class="_checkbox-field_cd53e0 _normal_cd53e0"
  >
    <div
      class="_content_cd53e0"
    >
      <div
        class="_checkbox-wrapper_cd53e0"
      >
        <input
          checked=""
          class="_checkbox_cd53e0 _checked_cd53e0"
          type="checkbox"
          value=""
        />
        <svg
          fill="none"
          height="10"
          viewBox="0 0 10 10"
          width="10"
          xmlns="http://www.w3.org/2000/svg"
        >
          <g
            clip-path="url(#clip0_73_127954)"
          >
            <mask
              fill="white"
              id="path-1-inside-1_73_127954"
            >
              <path
                d="M3.5777 9.16666C3.4145 9.16744 3.25752 9.09079 3.14033 8.95308L0.18872 5.48123C0.129889 5.4116 0.082947 5.32862 0.0505741 5.237C0.0182012 5.14539 0.00103144 5.04694 4.5112e-05 4.94729C-0.00194686 4.74602 0.0621007 4.55204 0.178098 4.40801C0.294096 4.26399 0.452541 4.18172 0.618578 4.1793C0.784615 4.17689 0.944643 4.25453 1.06346 4.39514L3.5802 7.35424L8.9361 1.04901C9.05508 0.908398 9.21526 0.83084 9.38142 0.833397C9.54757 0.835954 9.70608 0.918415 9.82208 1.06264C9.93808 1.20687 10.0021 1.40104 9.99995 1.60245C9.99784 1.80386 9.92981 1.996 9.81083 2.13661L4.01507 8.95308C3.89789 9.09079 3.74091 9.16744 3.5777 9.16666Z"
              />
            </mask>
            <path
              d="M3.5777 9.16666C3.4145 9.16744 3.25752 9.09079 3.14033 8.95308L0.18872 5.48123C0.129889 5.4116 0.082947 5.32862 0.0505741 5.237C0.0182012 5.14539 0.00103144 5.04694 4.5112e-05 4.94729C-0.00194686 4.74602 0.0621007 4.55204 0.178098 4.40801C0.294096 4.26399 0.452541 4.18172 0.618578 4.1793C0.784615 4.17689 0.944643 4.25453 1.06346 4.39514L3.5802 7.35424L8.9361 1.04901C9.05508 0.908398 9.21526 0.83084 9.38142 0.833397C9.54757 0.835954 9.70608 0.918415 9.82208 1.06264C9.93808 1.20687 10.0021 1.40104 9.99995 1.60245C9.99784 1.80386 9.92981 1.996 9.81083 2.13661L4.01507 8.95308C3.89789 9.09079 3.74091 9.16744 3.5777 9.16666Z"
              fill="#1F2A37"
            />
            <path
              d="M3.5777 9.16666L3.58488 7.66668L3.5777 7.66665L3.57052 7.66668L3.5777 9.16666ZM3.14033 8.95308L1.99751 9.92466L1.99797 9.92519L3.14033 8.95308ZM0.18872 5.48123L-0.957019 6.44937L-0.954101 6.4528L0.18872 5.48123ZM4.5112e-05 4.94729L-1.49988 4.96213L4.5112e-05 4.94729ZM1.06346 4.39514L-0.0822825 5.36328L-0.0791645 5.36695L1.06346 4.39514ZM3.5802 7.35424L2.43758 8.32605L3.58092 9.67035L4.72343 8.32534L3.5802 7.35424ZM8.9361 1.04901L10.0793 2.02011L10.0812 2.01793L8.9361 1.04901ZM9.81083 2.13661L10.9536 3.10826L10.9559 3.10554L9.81083 2.13661ZM4.01507 8.95308L5.15744 9.92519L5.15783 9.92472L4.01507 8.95308ZM3.57052 7.66668C3.90071 7.6651 4.14814 7.82284 4.2827 7.98097L1.99797 9.92519C2.3669 10.3587 2.92828 10.6698 3.58488 10.6666L3.57052 7.66668ZM4.28316 7.9815L1.33154 4.50965L-0.954101 6.4528L1.99751 9.92466L4.28316 7.9815ZM1.33445 4.51309C1.40028 4.59099 1.44081 4.66915 1.46487 4.73724L-1.36372 5.73676C-1.27492 5.98808 -1.1405 6.23222 -0.957014 6.44937L1.33445 4.51309ZM1.46487 4.73724C1.48898 4.80546 1.49937 4.87115 1.49997 4.93244L-1.49988 4.96213C-1.4973 5.22273 -1.45257 5.48532 -1.36372 5.73676L1.46487 4.73724ZM1.49997 4.93244C1.50109 5.04566 1.468 5.19781 1.34632 5.3489L-0.990121 3.46713C-1.3438 3.90626 -1.50499 4.44638 -1.49988 4.96213L1.49997 4.93244ZM1.34632 5.3489C1.2183 5.50784 0.974902 5.67428 0.64039 5.67914L0.596766 2.67946C-0.0698197 2.68916 -0.63011 3.02013 -0.990121 3.46713L1.34632 5.3489ZM0.64039 5.67914C0.304767 5.68403 0.0531651 5.52356 -0.0822766 5.36327L2.20919 3.427C1.83612 2.98549 1.26446 2.66975 0.596766 2.67946L0.64039 5.67914ZM-0.0791645 5.36695L2.43758 8.32605L4.72282 6.38243L2.20608 3.42333L-0.0791645 5.36695ZM4.72343 8.32534L10.0793 2.02011L7.79287 0.0779092L2.43698 6.38314L4.72343 8.32534ZM10.0812 2.01793C9.94559 2.17816 9.69386 2.33838 9.35834 2.33322L9.4045 -0.666425C8.73666 -0.676702 8.16456 -0.361362 7.79103 0.0800814L10.0812 2.01793ZM9.35834 2.33322C9.02399 2.32807 8.78094 2.16153 8.65322 2.00273L10.9909 0.122552C10.6312 -0.324702 10.0712 -0.656167 9.4045 -0.666425L9.35834 2.33322ZM8.65322 2.00273C8.53181 1.85177 8.49885 1.69982 8.50003 1.58674L11.4999 1.61816C11.5053 1.10226 11.3443 0.561961 10.9909 0.122552L8.65322 2.00273ZM8.50003 1.58674C8.50122 1.47377 8.53745 1.31933 8.66577 1.16769L10.9559 3.10554C11.3222 2.67267 11.4945 2.13395 11.4999 1.61816L8.50003 1.58674ZM8.66807 1.16497L2.87231 7.98144L5.15783 9.92472L10.9536 3.10826L8.66807 1.16497ZM2.87271 7.98097C3.00727 7.82284 3.25469 7.6651 3.58488 7.66668L3.57052 10.6666C4.22713 10.6698 4.78851 10.3587 5.15744 9.92519L2.87271 7.98097Z"
              fill="white"
              mask="url(#path-1-inside-1_73_127954)"
            />
          </g>
          <defs>
            <clippath
              id="clip0_73_127954"
            >
              <rect
                fill="white"
                height="10"
                width="10"
              />
            </clippath>
          </defs>
        </svg>
      </div>
      <div
        class="_label-helper_cd53e0"
      >
        <div
          class="_label_cd53e0"
        >
          Test Label
        </div>
        <div
          class="_caption_cd53e0"
        >
          Test Helper Text
        </div>
      </div>
    </div>
  </label>
</div>
`;

exports[`Checkbox Snapshots > matches checked state snapshot 1`] = `
<div>
  <label
    class="_checkbox-field_cd53e0 _checked_cd53e0"
  >
    <div
      class="_content_cd53e0"
    >
      <div
        class="_checkbox-wrapper_cd53e0"
      >
        <input
          class="_checkbox_cd53e0"
          type="checkbox"
          value=""
        />
      </div>
      <div
        class="_label-helper_cd53e0"
      >
        <div
          class="_label_cd53e0"
        >
          Test Label
        </div>
        <div
          class="_caption_cd53e0 _checked_cd53e0"
        >
          Test Helper Text
        </div>
      </div>
    </div>
  </label>
</div>
`;

exports[`Checkbox Snapshots > matches default checkbox snapshot 1`] = `
<div>
  <label
    class="_checkbox-field_cd53e0 _normal_cd53e0"
  >
    <div
      class="_content_cd53e0"
    >
      <div
        class="_checkbox-wrapper_cd53e0"
      >
        <input
          class="_checkbox_cd53e0"
          type="checkbox"
          value=""
        />
      </div>
      <div
        class="_label-helper_cd53e0"
      >
        <div
          class="_label_cd53e0"
        >
          Test Label
        </div>
        <div
          class="_caption_cd53e0"
        >
          Test Helper Text
        </div>
      </div>
    </div>
  </label>
</div>
`;

exports[`Checkbox Snapshots > matches disabled state snapshot 1`] = `
<div>
  <label
    class="_checkbox-field_cd53e0 _disabled_cd53e0"
  >
    <div
      class="_content_cd53e0"
    >
      <div
        class="_checkbox-wrapper_cd53e0"
      >
        <input
          class="_checkbox_cd53e0 _disabled_cd53e0"
          disabled=""
          type="checkbox"
          value=""
        />
      </div>
      <div
        class="_label-helper_cd53e0"
      >
        <div
          class="_label_cd53e0"
        >
          Test Label
        </div>
        <div
          class="_caption_cd53e0 _disabled_cd53e0"
        >
          Test Helper Text
        </div>
      </div>
    </div>
  </label>
</div>
`;

exports[`Checkbox Snapshots > matches error state snapshot 1`] = `
<div>
  <label
    class="_checkbox-field_cd53e0 _error_cd53e0"
  >
    <div
      class="_content_cd53e0"
    >
      <div
        class="_checkbox-wrapper_cd53e0"
      >
        <input
          class="_checkbox_cd53e0 _error_cd53e0"
          type="checkbox"
          value=""
        />
      </div>
      <div
        class="_label-helper_cd53e0"
      >
        <div
          class="_label_cd53e0"
        >
          Test Label
        </div>
        <div
          class="_caption_cd53e0 _error_cd53e0"
        >
          Test Helper Text
        </div>
      </div>
    </div>
  </label>
</div>
`;

exports[`Checkbox Snapshots > matches normal state snapshot 1`] = `
<div>
  <label
    class="_checkbox-field_cd53e0 _normal_cd53e0"
  >
    <div
      class="_content_cd53e0"
    >
      <div
        class="_checkbox-wrapper_cd53e0"
      >
        <input
          class="_checkbox_cd53e0"
          type="checkbox"
          value=""
        />
      </div>
      <div
        class="_label-helper_cd53e0"
      >
        <div
          class="_label_cd53e0"
        >
          Test Label
        </div>
        <div
          class="_caption_cd53e0"
        >
          Test Helper Text
        </div>
      </div>
    </div>
  </label>
</div>
`;
