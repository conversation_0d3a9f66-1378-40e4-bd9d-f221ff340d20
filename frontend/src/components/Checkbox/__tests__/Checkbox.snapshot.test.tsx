import React from "react";
import { render } from "@testing-library/react";
import { Checkbox } from "../Checkbox";
import { CheckboxState } from "../Checkbox.types";

export default describe("Checkbox Snapshots", () => {
  const defaultProps = {
    labelText: "Test Label",
    helperText: "Test Helper Text",
  };

  it("matches default checkbox snapshot", () => {
    const { container } = render(<Checkbox {...defaultProps} />);
    expect(container).toMatchSnapshot();
  });

  it.each(Object.values(CheckboxState))(
    "matches %s state snapshot",
    (state) => {
      const { container } = render(
        <Checkbox {...defaultProps} state={state} />
      );
      expect(container).toMatchSnapshot();
    }
  );

  it("matches checkbox with error text snapshot", () => {
    const { container } = render(
      <Checkbox
        {...defaultProps}
        state={CheckboxState.ERROR}
        errorText="Test Error Text"
      />
    );
    expect(container).toMatchSnapshot();
  });

  it("matches checked checkbox snapshot", () => {
    const { container } = render(<Checkbox {...defaultProps} checked />);
    expect(container).toMatchSnapshot();
  });

  it("matches checkbox without helper text snapshot", () => {
    const { container } = render(
      <Checkbox labelText={defaultProps.labelText} />
    );
    expect(container).toMatchSnapshot();
  });
});
