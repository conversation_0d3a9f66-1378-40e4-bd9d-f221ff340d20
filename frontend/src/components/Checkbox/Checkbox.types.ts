export enum CheckboxState {
  NORMAL = "normal",
  CHECKED = "checked",
  ERROR = "error",
  DISABLED = "disabled",
}

export interface CheckboxProps {
  labelText?: string;
  helperText?: string;
  errorText?: string;
  state?: CheckboxState;
  className?: string;
  labelClassName?: string;
  helperClassName?: string;
  checked?: boolean;
  onChange?: (checked: boolean) => void;
  name?: string;
  value?: string;
  customControl?: React.ReactNode;
} 