import { render, screen } from '@testing-library/react';
import { Header } from '../Header';
import React from 'react';

vi.mock('next/link', () => ({
  default: ({ children, href }: { children: React.ReactNode; href: string }) => {
    return <a href={href}>{children}</a>;
  },
}));

vi.mock('@clerk/nextjs', () => ({
  useAuth: () => ({
    isSignedIn: true,
  }),
  SignInButton: ({ children }: { children: React.ReactNode }) => <div>{children}</div>,
  UserButton: () => <div data-testid="user-button">UserButton</div>,
}));

describe('Header', () => {
  it('renders the UserButton component', () => {
    render(<Header />);

    const userButton = screen.getByTestId('user-button');
    expect(userButton).toBeInTheDocument();
  });
});
