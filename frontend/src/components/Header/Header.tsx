'use client';

import { UserButton } from '@clerk/nextjs';
import { Logo } from '@/components/Sidebar/components/Logo/Logo';
import { IconSvgObject } from '@hugeicons/react';
import { Menu01Icon } from '@hugeicons-pro/core-stroke-standard';
import { HugeiconsIcon } from '@/components/HugeiconsIcon';
import { useBreakpoint } from '@/utils/breakpointUtils';
import { useSidebar } from '@/hooks/useSidebar';
import { Button } from '@/components/Button/Button';
import { ButtonType, ButtonColor, ButtonSize } from '@/components/Button/Button.types';
import { SignInButton } from '@clerk/nextjs';
import { useAuth } from '@clerk/nextjs';
import { SignUpButton } from '@clerk/nextjs';

import styles from './Header.module.scss';

export const Header: React.FC = () => {
  const { isMobile } = useBreakpoint();
  const { toggleSidebar } = useSidebar();
  const { isSignedIn } = useAuth();

  return (
    <header className={styles.header}>
      {isMobile && (
        <div className={styles.burger} onClick={toggleSidebar}>
          <HugeiconsIcon icon={Menu01Icon as unknown as IconSvgObject} size={24} color="#6b7280" />
        </div>
      )}
      {isMobile && (
        <div className={styles.logo}>
          <Logo style={{ width: 105, height: 18 }} />
        </div>
      )}
      {isMobile && !isSignedIn && (
        <div className={styles.mobileAuthButtons}>
          <SignUpButton mode="modal">
            <Button
              type={ButtonType.PRIMARY}
              color={ButtonColor.GREEN_PRIMARY}
              size={ButtonSize.SM}
              className={styles.signupBtn}
            >
              Sign up
            </Button>
          </SignUpButton>
          <SignInButton mode="modal">
            <Button
              type={ButtonType.SECONDARY}
              color={ButtonColor.GREEN_PRIMARY}
              size={ButtonSize.SM}
              className={styles.loginBtn}
            >
              Log in
            </Button>
          </SignInButton>
        </div>
      )}
      <div className={styles.user}>
        <UserButton />
      </div>
    </header>
  );
};
