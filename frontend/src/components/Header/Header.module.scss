.header {
  display: flex;
  align-items: center;
  padding: var(--spacing-4);
  height: 64px;
  background-color: var(--colors-white);
  border-bottom: 1px solid var(--colors-gray-200);
  z-index: 20;
  position: relative;
}

.burger {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: var(--spacing-2);
  padding: var(--spacing-2);
  cursor: pointer;
  border-radius: 4px;
  transition: background-color 0.2s ease;
}

.user {
  margin-left: auto;
}

.logo {
  display: flex;
  align-items: center;
  padding-top: 2px;
  flex: 1;
}

.mobileAuthButtons {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-left: auto;
}
