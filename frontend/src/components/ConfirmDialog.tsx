import { DialogTitle } from '@radix-ui/react-dialog';
import { Button, ButtonVariants } from './ui/button';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader } from './ui/dialog';

interface IConfirmDialog {
  isOpen: boolean;
  title?: string;
  description?: string;
  okText?: string;
  cancelText?: string;
  cancelType?: ButtonVariants['variant'];
  onOk?: () => void;
  onCancel?: () => void;
}

export const ConfirmDialog = ({
  isOpen,
  title,
  description,
  okText,
  cancelText,
  cancelType = 'danger',
  onOk = () => {},
  onCancel = () => {},
}: IConfirmDialog) => {
  return (
    <Dialog open={isOpen} onOpenChange={() => {}}>
      <DialogContent
        className="!w-auto text-center pt-4 !h-auto min-w-[300px]"
        showCloseButton={false}
      >
        <DialogHeader>
          {title && (
            <DialogTitle className="text-zinc-950 !text-lg text-center font-bold">
              {title}
            </DialogTitle>
          )}
          {description && (
            <DialogDescription className="text-base text-center text-black">
              {description}
            </DialogDescription>
          )}
        </DialogHeader>
        <DialogFooter className="flex flex-row justify-center gap-2">
          {okText && (
            <Button size="lg" variant="ghost" onClick={onOk}>
              {okText}
            </Button>
          )}
          {cancelText && (
            <Button size="lg" variant={cancelType} className="" onClick={onCancel}>
              {cancelText}
            </Button>
          )}
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};
