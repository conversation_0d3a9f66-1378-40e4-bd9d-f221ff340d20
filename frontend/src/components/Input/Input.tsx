import React from 'react';
// import * as HugeIcons from '@hugeicons/react';
import classNames from 'classnames';
import { Button } from '../Button';
import { ButtonColor, ButtonSize, ButtonType } from '../Button/Button.types';
import styles from './Input.module.scss';
import { InputProps, InputSize, InputState } from './Input.types';
import { Cancel01Icon } from '@hugeicons/react';

export const Input = ({
  value,
  onChange,
  onClear,
  labelText = 'First name',
  placeholderText = 'Input text',
  helperText = "We'll never share your details. See our Privacy Policy.",
  size = InputSize.REGULAR,
  state = InputState.NORMAL,
  className,
  inputClassName,
  showLabel = true,
  showHelperText = true,
  showLeftIcon = true,
  showPlaceholder = true,
  leftIconName,
  fullWidth = false,
  width,
  disableClear = false,
  greenBorder = false,
  inputType = 'text',
}: InputProps): JSX.Element => {
  const inputFieldClasses = classNames(
    styles['input-field'],
    styles[state],
    { [styles.fullWidth]: fullWidth },
    className
  );

  const inputClasses = classNames(
    styles.input,
    styles[state.toLowerCase()],
    styles[size.toLowerCase()],
    { [styles.greenBorder]: greenBorder },
    inputClassName
  );

  const helperTextClasses = classNames(
    styles.caption,
    state !== InputState.NORMAL && styles[state.toLowerCase()]
  );

  // Dummy icon component for demonstration purposes
  const DummyIcon: React.FC<{ className: string; size: number }> = ({ className, size }) => (
    <span className={className} style={{ fontSize: size, display: 'inline-block' }}>🔲</span>
  );

  const LeftIcon = leftIconName
    ? DummyIcon
    : null;


  const showClear = value && value.length > 0;

  const getCloseButtonColor = () => {
    switch (state) {
      case InputState.ERROR:
        return ButtonColor.RED;
      case InputState.SUCCESS:
        return ButtonColor.GREEN_PRIMARY;
      default:
        return ButtonColor.GREEN_PRIMARY;
    }
  };

  return (
    <div className={inputFieldClasses} style={!fullWidth && width ? { width } : undefined}>
      {showLabel && <div className={styles.label}>{labelText}</div>}

      <div className={inputClasses}>
        <div className={styles.content}>
          {showLeftIcon && LeftIcon && <LeftIcon className={styles['user-instance']} size={16} />}

          <input
            className={styles['input-text']}
            placeholder={showPlaceholder ? placeholderText : ''}
            value={value}
            onChange={(e) => onChange?.(e.target.value)}
            disabled={state === InputState.DISABLED}
            type={inputType}
          />

          {!disableClear && showClear && state !== InputState.DISABLED && (
            <Button
              color={getCloseButtonColor()}
              iconOnly
              size={ButtonSize.XS}
              type={ButtonType.TERTIARY}
              leftIcon={Cancel01Icon}
              className={styles['clear-button']}
              onClick={onClear}
            />
          )}
        </div>
      </div>

      {showHelperText && <div className={helperTextClasses}>{helperText}</div>}
    </div>
  );
};
