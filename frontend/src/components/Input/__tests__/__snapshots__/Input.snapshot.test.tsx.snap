// Vitest Snapshot v1, https://vitest.dev/guide/snapshot.html

exports[`Input Snapshots > matches default input snapshot 1`] = `
<div>
  <div
    class="_input-field_859b20 _normal_859b20"
  >
    <div
      class="_label_859b20"
    >
      Test Label
    </div>
    <div
      class="_input_859b20 _normal_859b20 _regular_859b20"
    >
      <div
        class="_content_859b20"
      >
        <input
          class="_input-text_859b20"
          placeholder="Test Placeholder"
          type="text"
          value=""
        />
      </div>
    </div>
    <div
      class="_caption_859b20"
    >
      We'll never share your details. See our Privacy Policy.
    </div>
  </div>
</div>
`;

exports[`Input Snapshots > matches disabled state snapshot 1`] = `
<div>
  <div
    class="_input-field_859b20 _disabled_859b20"
  >
    <div
      class="_label_859b20"
    >
      Test Label
    </div>
    <div
      class="_input_859b20 _disabled_859b20 _regular_859b20"
    >
      <div
        class="_content_859b20"
      >
        <input
          class="_input-text_859b20"
          disabled=""
          placeholder="Test Placeholder"
          type="text"
          value=""
        />
      </div>
    </div>
    <div
      class="_caption_859b20 _disabled_859b20"
    >
      We'll never share your details. See our Privacy Policy.
    </div>
  </div>
</div>
`;

exports[`Input Snapshots > matches error state snapshot 1`] = `
<div>
  <div
    class="_input-field_859b20 _error_859b20"
  >
    <div
      class="_label_859b20"
    >
      Test Label
    </div>
    <div
      class="_input_859b20 _error_859b20 _regular_859b20"
    >
      <div
        class="_content_859b20"
      >
        <input
          class="_input-text_859b20"
          placeholder="Test Placeholder"
          type="text"
          value=""
        />
      </div>
    </div>
    <div
      class="_caption_859b20 _error_859b20"
    >
      We'll never share your details. See our Privacy Policy.
    </div>
  </div>
</div>
`;

exports[`Input Snapshots > matches full width input snapshot 1`] = `
<div>
  <div
    class="_input-field_859b20 _normal_859b20 _fullWidth_859b20"
  >
    <div
      class="_label_859b20"
    >
      Test Label
    </div>
    <div
      class="_input_859b20 _normal_859b20 _regular_859b20"
    >
      <div
        class="_content_859b20"
      >
        <input
          class="_input-text_859b20"
          placeholder="Test Placeholder"
          type="text"
          value=""
        />
      </div>
    </div>
    <div
      class="_caption_859b20"
    >
      We'll never share your details. See our Privacy Policy.
    </div>
  </div>
</div>
`;

exports[`Input Snapshots > matches input with helper text snapshot 1`] = `
<div>
  <div
    class="_input-field_859b20 _normal_859b20"
  >
    <div
      class="_label_859b20"
    >
      Test Label
    </div>
    <div
      class="_input_859b20 _normal_859b20 _regular_859b20"
    >
      <div
        class="_content_859b20"
      >
        <input
          class="_input-text_859b20"
          placeholder="Test Placeholder"
          type="text"
          value=""
        />
      </div>
    </div>
    <div
      class="_caption_859b20"
    >
      Test Helper Text
    </div>
  </div>
</div>
`;

exports[`Input Snapshots > matches input with left icon snapshot 1`] = `
<div>
  <div
    class="_input-field_859b20 _normal_859b20"
  >
    <div
      class="_label_859b20"
    >
      Test Label
    </div>
    <div
      class="_input_859b20 _normal_859b20 _regular_859b20"
    >
      <div
        class="_content_859b20"
      >
        <svg
          class="_user-instance_859b20"
          color="currentColor"
          fill="none"
          height="16"
          viewBox="0 0 24 24"
          width="16"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M6.57757 15.4816C5.1628 16.324 1.45336 18.0441 3.71266 20.1966C4.81631 21.248 6.04549 22 7.59087 22H16.4091C17.9545 22 19.1837 21.248 20.2873 20.1966C22.5466 18.0441 18.8372 16.324 17.4224 15.4816C14.1048 13.5061 9.89519 13.5061 6.57757 15.4816Z"
            stroke="currentColor"
            stroke-linecap="round"
            stroke-linejoin="round"
            stroke-width="1.5"
          />
          <path
            d="M16.5 6.5C16.5 8.98528 14.4853 11 12 11C9.51472 11 7.5 8.98528 7.5 6.5C7.5 4.01472 9.51472 2 12 2C14.4853 2 16.5 4.01472 16.5 6.5Z"
            stroke="currentColor"
            stroke-width="1.5"
          />
        </svg>
        <input
          class="_input-text_859b20"
          placeholder="Test Placeholder"
          type="text"
          value=""
        />
      </div>
    </div>
    <div
      class="_caption_859b20"
    >
      We'll never share your details. See our Privacy Policy.
    </div>
  </div>
</div>
`;

exports[`Input Snapshots > matches input with value and clear button snapshot 1`] = `
<div>
  <div
    class="_input-field_859b20 _normal_859b20"
  >
    <div
      class="_label_859b20"
    >
      Test Label
    </div>
    <div
      class="_input_859b20 _normal_859b20 _regular_859b20"
    >
      <div
        class="_content_859b20"
      >
        <input
          class="_input-text_859b20"
          placeholder="Test Placeholder"
          type="text"
          value="Test Value"
        />
        <button
          class="_button_f8f296 _tertiary_f8f296 _xs_f8f296 _green-primary_f8f296 _iconOnly_f8f296 _clear-button_859b20"
        >
          <svg
            class="_icon_f8f296 _icon-xs_f8f296"
            color="currentColor"
            fill="none"
            height="24"
            viewBox="0 0 24 24"
            width="24"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M19 5L5 19M5 5L19 19"
              stroke="currentColor"
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="1.5"
            />
          </svg>
        </button>
      </div>
    </div>
    <div
      class="_caption_859b20"
    >
      We'll never share your details. See our Privacy Policy.
    </div>
  </div>
</div>
`;

exports[`Input Snapshots > matches large size snapshot 1`] = `
<div>
  <div
    class="_input-field_859b20 _normal_859b20"
  >
    <div
      class="_label_859b20"
    >
      Test Label
    </div>
    <div
      class="_input_859b20 _normal_859b20 _large_859b20"
    >
      <div
        class="_content_859b20"
      >
        <input
          class="_input-text_859b20"
          placeholder="Test Placeholder"
          type="text"
          value=""
        />
      </div>
    </div>
    <div
      class="_caption_859b20"
    >
      We'll never share your details. See our Privacy Policy.
    </div>
  </div>
</div>
`;

exports[`Input Snapshots > matches normal state snapshot 1`] = `
<div>
  <div
    class="_input-field_859b20 _normal_859b20"
  >
    <div
      class="_label_859b20"
    >
      Test Label
    </div>
    <div
      class="_input_859b20 _normal_859b20 _regular_859b20"
    >
      <div
        class="_content_859b20"
      >
        <input
          class="_input-text_859b20"
          placeholder="Test Placeholder"
          type="text"
          value=""
        />
      </div>
    </div>
    <div
      class="_caption_859b20"
    >
      We'll never share your details. See our Privacy Policy.
    </div>
  </div>
</div>
`;

exports[`Input Snapshots > matches regular size snapshot 1`] = `
<div>
  <div
    class="_input-field_859b20 _normal_859b20"
  >
    <div
      class="_label_859b20"
    >
      Test Label
    </div>
    <div
      class="_input_859b20 _normal_859b20 _regular_859b20"
    >
      <div
        class="_content_859b20"
      >
        <input
          class="_input-text_859b20"
          placeholder="Test Placeholder"
          type="text"
          value=""
        />
      </div>
    </div>
    <div
      class="_caption_859b20"
    >
      We'll never share your details. See our Privacy Policy.
    </div>
  </div>
</div>
`;

exports[`Input Snapshots > matches small size snapshot 1`] = `
<div>
  <div
    class="_input-field_859b20 _normal_859b20"
  >
    <div
      class="_label_859b20"
    >
      Test Label
    </div>
    <div
      class="_input_859b20 _normal_859b20 _small_859b20"
    >
      <div
        class="_content_859b20"
      >
        <input
          class="_input-text_859b20"
          placeholder="Test Placeholder"
          type="text"
          value=""
        />
      </div>
    </div>
    <div
      class="_caption_859b20"
    >
      We'll never share your details. See our Privacy Policy.
    </div>
  </div>
</div>
`;

exports[`Input Snapshots > matches success state snapshot 1`] = `
<div>
  <div
    class="_input-field_859b20 _success_859b20"
  >
    <div
      class="_label_859b20"
    >
      Test Label
    </div>
    <div
      class="_input_859b20 _success_859b20 _regular_859b20"
    >
      <div
        class="_content_859b20"
      >
        <input
          class="_input-text_859b20"
          placeholder="Test Placeholder"
          type="text"
          value=""
        />
      </div>
    </div>
    <div
      class="_caption_859b20 _success_859b20"
    >
      We'll never share your details. See our Privacy Policy.
    </div>
  </div>
</div>
`;
