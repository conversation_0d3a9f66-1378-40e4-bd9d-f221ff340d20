import React from 'react';
import { usePathname } from 'next/navigation';
import Link from 'next/link';
import { IconSvgObject } from '@hugeicons/react';
import { ArrowRight01Icon } from '@hugeicons-pro/core-stroke-standard';

import { HugeiconsIcon } from '@/components/HugeiconsIcon';
import styles from './Breadcrumbs.module.scss';

interface BreadcrumbItem {
  value: string;
  url?: string;
}

interface BreadcrumbsProps {
  path: BreadcrumbItem[];
}

export const Breadcrumbs: React.FC<BreadcrumbsProps> = ({ path }) => {
  const pathname = usePathname();

  return (
    <div className={styles.breadcrumbs}>
      {path.map((item, index) => (
        <React.Fragment key={item.value}>
          {index > 0 && (
            <HugeiconsIcon
              icon={ArrowRight01Icon as unknown as IconSvgObject}
              size={14}
              className={styles.arrowIcon}
            />
          )}
          {item.url ? (
            <Link href={item.url} className={pathname === item.url ? styles.active : ''}>
              {item.value}
            </Link>
          ) : (
            <span className={styles.active}>{item.value}</span>
          )}
        </React.Fragment>
      ))}
    </div>
  );
};
