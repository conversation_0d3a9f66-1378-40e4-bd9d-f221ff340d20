'use client';

import React, { MouseEvent, useEffect, useRef, useState } from 'react';
import Link from 'next/link';
import classNames from 'classnames';
import { IconSvgObject } from '@hugeicons/react';
import {
  Home11Icon,
  CheckListIcon,
  House04Icon,
  Cancel01Icon,
  CustomerService01Icon,
} from '@hugeicons-pro/core-stroke-standard';
import { SignInButton, SignUpButton, useAuth } from '@clerk/nextjs';
import { SidebarProps } from './Sidebar.types';
import styles from './Sidebar.module.scss';
import { Button } from '../Button';
import { ButtonColor, ButtonSize, ButtonState, ButtonType } from '../Button/Button.types';
import { Logo } from './components/Logo/Logo';
import { HugeiconsIcon } from '../HugeiconsIcon';
import { useChats } from '@/hooks/useChats';
import { usePathname, useRouter } from 'next/navigation';
import { NavigationItem } from './components/NavigationItem/NavigationItem';
import { useSidebar } from '@/hooks/useSidebar';
import { useBreakpoint } from '@/utils/breakpointUtils';
import { useWidgets } from '@/hooks/useWidgets';
import useChatParams from '@/hooks/useChatParams';
import { Add01Icon } from '@hugeicons/react';
import { useUniversalAuth } from '@/hooks/useUniversalAuth';

export const Sidebar: React.FC<SidebarProps> = (
  {
    // onRename,
    // onPin,
    // onShare,
    // onDelete,
  }
) => {
  const { isSignedIn: isAuthenticated, getToken } = useAuth();
  const { getToken: getAuthToken } = useUniversalAuth();
  const { chats, fetchChats, hasMore, isLoading, loadMoreChats } = useChats();
  const [isSettingsOpen, setIsSettingsOpen] = useState(false);
  const sidebarRef = useRef<HTMLDivElement>(null);
  const recentChatsContainerRef = useRef<HTMLDivElement>(null);
  const sentinelRef = useRef(null);
  const { chatId: activeChatId } = useChatParams();
  const pathname = usePathname();
  const { fetchProperties } = useWidgets();

  const router = useRouter();

  const { closeSidebar, toggleSidebar } = useSidebar();
  const { isMobile } = useBreakpoint();

  useEffect(() => {
    const loadProperties = async () => {
      if (isAuthenticated) {
        const token = await getToken();
        if (token) {
          fetchProperties(token);
        }
      }
    };

    loadProperties();
  }, [getToken, fetchProperties, isAuthenticated]);

  useEffect(() => {
    const sentinel = sentinelRef.current;
    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach(async (entry) => {
          if (entry.isIntersecting && hasMore && !isLoading) {
            const token = await getAuthToken();
            if (token) {
              loadMoreChats(token);
            }
          }
        });
      },
      {
        root: recentChatsContainerRef.current,
        threshold: 0.1,
      }
    );

    if (sentinel) {
      observer.observe(sentinel);
    }

    return () => {
      if (sentinel) {
        observer.unobserve(sentinel);
      }
    };
  }, [hasMore, isLoading, loadMoreChats, getAuthToken]);

  useEffect(() => {
    const loadChats = async () => {
      const token = await getAuthToken();
      if (token) {
        fetchChats(token);
      }
    };

    loadChats();
  }, [fetchChats, getAuthToken]);

  useEffect(() => {
    const refreshChats = async () => {
      if (pathname?.startsWith('/chats/') && chats.length > 0) {
        const token = await getAuthToken();
        if (token) {
          if (chats.length < 5) {
            fetchChats(token);
          }
        }
      }
    };

    refreshChats();
  }, [pathname, getAuthToken, fetchChats, chats.length]);

  const chatsWithId = Object.values(chats).filter((chat) => 'id' in chat);

  // const chatOptions = [
  //   {
  //     label: "Rename",
  //     icon: (
  //       <HugeiconsIcon
  //         icon={StrokeStandard.PencilEdit01Icon as unknown as IconSvgObject}
  //         size={14}
  //       />
  //     ),
  //     onClick: onRename,
  //   },
  //   {
  //     label: "Pin",
  //     icon: (
  //       <HugeiconsIcon
  //         icon={StrokeStandard.PinIcon as unknown as IconSvgObject}
  //         size={14}
  //       />
  //     ),
  //     onClick: onPin,
  //   },
  //   {
  //     label: "Share",
  //     icon: (
  //       <HugeiconsIcon
  //         icon={StrokeStandard.Share05Icon as unknown as IconSvgObject}
  //         size={14}
  //       />
  //     ),
  //     onClick: onShare,
  //   },
  //   {
  //     label: "Delete",
  //     icon: (
  //       <HugeiconsIcon
  //         icon={StrokeStandard.Delete02Icon as unknown as IconSvgObject}
  //         size={14}
  //       />
  //     ),
  //     variant: "danger" as const,
  //     onClick: onDelete,
  //   },
  // ];

  const handleCreateNewChat = async (e: MouseEvent<HTMLAnchorElement, MouseEvent>) => {
    e.preventDefault();
    if (isMobile) {
      closeSidebar();
    }
    router?.push(`/`);
  };

  const navigationItems = [
    {
      icon: <HugeiconsIcon icon={Home11Icon as unknown as IconSvgObject} size={20} />,
      label: 'Home',
      url: '/',
      isComingSoon: false,
      guestAccessible: true,
      onClick: handleCreateNewChat,
    },
    {
      icon: <HugeiconsIcon icon={CheckListIcon as unknown as IconSvgObject} size={20} />,
      label: 'To-do list',
      url: '/todo-list',
      guestAccessible: false,
      isComingSoon: true,
    },
    {
      icon: <HugeiconsIcon icon={House04Icon as unknown as IconSvgObject} size={20} />,
      label: 'Property Profile',
      url: '/property-profile',
      guestAccessible: true,
    },
  ];

  return (
    <aside className={styles.sidebar} ref={sidebarRef}>
      <div className={styles.container}>
        <div
          className={classNames(styles.header, {
            [styles.mobile]: isMobile,
          })}
        >
          <div className={styles.logoContainer}>
            {isMobile && (
              <div className={styles.burger} onClick={toggleSidebar}>
                <HugeiconsIcon
                  icon={Cancel01Icon as unknown as IconSvgObject}
                  size={24}
                  color="#6b7280"
                />
              </div>
            )}
            <div
              className={classNames(styles.logo, {
                [styles.mobile]: isMobile,
              })}
            >
              <Logo />
            </div>
          </div>
          <Button
            type={ButtonType.PRIMARY}
            color={ButtonColor.GREEN_PRIMARY}
            state={ButtonState.DEFAULT}
            size={ButtonSize.L}
            onClick={() => {
              if (isMobile) {
                closeSidebar();
              }
              router?.push(`/`);
            }}
            showLeftIcon
            leftIcon={Add01Icon}
          >
            Create new
          </Button>
        </div>

        <div className={styles.content}>
          <nav>
            <ul className={classNames('space-y-2', styles.navContainer)}>
              {navigationItems.map((item) => (
                <li
                  key={item.url}
                  className={classNames({
                    [styles.comingSoon]: item.isComingSoon,
                  })}
                >
                  <NavigationItem
                    icon={item.icon}
                    label={item.label}
                    isAuthenticated={isAuthenticated ?? false}
                    isComingSoon={item.isComingSoon}
                    onClick={item.onClick}
                    url={item.url}
                    guestAccessible={item.guestAccessible}
                    className={classNames({
                      [styles.navItem]: true,
                      [styles.active]:
                        !item.isComingSoon &&
                        pathname &&
                        (item.url === '/' ? pathname === '/' : pathname.startsWith(item.url)),
                    })}
                  />
                </li>
              ))}
              <li
                className={classNames({
                  [styles.comingSoon]: true,
                })}
              >
                <NavigationItem
                  icon={
                    <HugeiconsIcon
                      icon={CustomerService01Icon as unknown as IconSvgObject}
                      size={20}
                    />
                  }
                  label="Help"
                  isAuthenticated={isAuthenticated ?? false}
                  isComingSoon={true}
                  isButton
                  onClick={() => isAuthenticated && setIsSettingsOpen(!isSettingsOpen)}
                  className={styles.settingsItem}
                />
                {isSettingsOpen && isAuthenticated && (
                  <ul className={styles.settingsSubmenu}>
                    <li className={styles.navItem}>
                      <button>Profile</button>
                    </li>
                    <li className={styles.navItem}>
                      <button>Notification</button>
                    </li>
                    <li className={styles.navItem}>
                      <button>Privacy & data</button>
                    </li>
                    <li className={styles.navItem}>
                      <button>Help</button>
                    </li>
                  </ul>
                )}
              </li>
            </ul>
          </nav>

          {/* Show recent chats for both authenticated and guest users */}
          <div className={styles.recentChats}>
            <h2 className={styles.recentChatsTitle}>RECENT CHATS</h2>
            <div
              className={classNames(styles.recentChatsWrapper, {
                [styles.settingsOpen]: isSettingsOpen,
              })}
            >
              <div className={styles.recentChatsContainer} ref={recentChatsContainerRef}>
                <ul className="space-y-1">
                  {chatsWithId.map((chat) => (
                    <li key={chat.id}>
                      <Link
                        href={`/chats/${chat.id}`}
                        onClick={(e) => {
                          e.preventDefault();

                          if (isMobile) {
                            closeSidebar();
                          }

                          router.push(`/chats/${chat.id}`);
                        }}
                        className={classNames(styles.chatItem, {
                          [styles.active]: chat.id === activeChatId,
                        })}
                      >
                        <span className={styles.chatTitle}>{chat.title}</span>
                      </Link>
                    </li>
                  ))}
                </ul>
                {hasMore && <div ref={sentinelRef} style={{ height: 20 }} />}
              </div>
            </div>
          </div>

          {!isAuthenticated && (
            <div className={styles.authContainer}>
              <p className={styles.authText}>Sign up or log in to access all the features</p>
              <div className="flex gap-4">
                <SignUpButton mode="modal">
                  <Button
                    type={ButtonType.PRIMARY}
                    color={ButtonColor.GREEN_PRIMARY}
                    size={ButtonSize.BASE}
                    className="flex-1"
                  >
                    Sign up
                  </Button>
                </SignUpButton>
                <SignInButton mode="modal">
                  <Button
                    type={ButtonType.SECONDARY}
                    color={ButtonColor.GREEN_PRIMARY}
                    size={ButtonSize.BASE}
                    className="flex-1"
                  >
                    Log in
                  </Button>
                </SignInButton>
              </div>
            </div>
          )}
        </div>
      </div>
    </aside>
  );
};
