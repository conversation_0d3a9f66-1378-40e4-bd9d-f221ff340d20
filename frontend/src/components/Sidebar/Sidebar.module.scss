.sidebar {
  width: 260px;
  height: 100dvh;
  background-color: white;
  border-right: 1px solid #e5e7eb;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
}

.container {
  padding: 12px;
  height: 100%;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.header {
  margin-bottom: 1.5rem;
  display: flex;
  flex-direction: column;

  &:not(.mobile) {
    gap: 1rem;
  }

  > svg:first-child {
    width: 140px;
    height: 24px;
    margin-top: 12px;
    margin-left: 8px;
  }

  > button {
    margin-top: 12px;
  }
}

.burger {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-2);
  cursor: pointer;
  border-radius: 4px;
  transition: background-color 0.2s ease;
}

.navItem {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.5rem;
  border-radius: 0.25rem;
  position: relative;

  svg {
    color: #111928;
  }

  &:not(.disabled):hover {
    background-color: #f3f4f6;
    cursor: pointer;
  }

  &.disabled {
    opacity: 0.5;
    cursor: not-allowed;

    svg,
    .navText {
      color: #6b7280;
    }
  }

  &.active {
    background-color: #f3f4f6;
  }
}

.comingSoon {
  cursor: not-allowed;
}

.settingsItem {
  padding-left: 0;

  &.disabled {
    .settingsSubmenu {
      display: none;
    }
  }
}

.settingsButton {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0.5rem 0.625rem 0.5rem 0.5rem;
  border-radius: 0.25rem;
  border: none;
  background: none;
  position: relative;

  &:hover:not(:disabled) {
    background-color: #f3f4f6;
    cursor: pointer;
  }

  .lockIcon {
    position: static;
    color: #1b6e5a;
    background-color: red;
  }
}

.settingsIcon {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.settingsArrow {
  transform: rotate(0deg);
  transition: transform 0.2s;
  color: #111928;

  &.open {
    transform: rotate(180deg);
  }
}

.settingsSubmenu {
  margin-left: 2rem;
  margin-top: 0.4rem;

  .navItem {
    button {
      width: 100%;
      font-family: 'Quasimoda';
      font-size: 16px;
      font-weight: 400;
      line-height: 24px;
      text-align: left;
      text-underline-position: from-font;
      text-decoration-skip-ink: none;
      color: #111928;
      background: none;
      border: none;
      padding: 0.1rem;
      border-radius: 0.25rem;
      cursor: pointer;

      &:hover {
        background-color: #f3f4f6;
      }
    }
  }
}

.authContainer {
  margin-top: auto;
  text-align: center;
  padding: 2rem 1rem;
  border-top: 1px solid #e5e7eb;
}

.authText {
  font-family: 'Quasimoda';
  font-size: 16px;
  font-weight: 400;
  line-height: 24px;
  text-align: center;
  text-underline-position: from-font;
  text-decoration-skip-ink: none;
  color: #111928;
  margin-bottom: 1rem;
}

.content {
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.recentChatsWrapper {
  display: flex;
  flex-direction: column;
  height: calc(100vh - 380px);
  overflow: hidden;

  &.settingsOpen {
    height: calc(100vh - 560px);
  }
}

.recentChats {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  height: 100%;
  overflow: hidden;
}

.recentChatsContainer {
  overflow-y: auto;
  margin-bottom: 0.1rem;

  &::-webkit-scrollbar {
    width: 1px;
    margin: 16px 0;
  }

  &::-webkit-scrollbar-track {
    background: transparent;
    margin: 16px 0;
  }

  &::-webkit-scrollbar-thumb {
    background-color: rgb(231, 230, 230);
    border-radius: 0.5px;
    min-height: 40px;
    max-height: 40px;
    margin: 16px 0;
    cursor: pointer;
  }

  scrollbar-width: thin;
  scrollbar-color: rgb(231, 230, 230) transparent;
}

.logoContainer {
  display: flex;
  align-items: center;
}

.logo {
  padding: 12px 0 0 8px;

  svg {
    width: 140px;
    height: 24px;
  }

  &.mobile {
    padding: 0 0 0 var(--spacing-2);

    svg {
      width: 105px;
      height: 18px;
      margin-top: 2px;
    }
  }
}

.chatItem {
  display: flex;
  align-items: center;
  justify-content: space-between;
  overflow: hidden;
  padding: 0.5rem;
  border-radius: 0.25rem;
  font-family: 'Quasimoda';
  font-size: 16px;
  font-weight: 400;
  line-height: 24px;
  color: #111928;
  height: 2.25rem;

  &:hover {
    background-color: #f3f4f6;

    .moreIcon {
      opacity: 1;
    }
  }

  &.active {
    background-color: #f3f4f6;
  }
}

.chatTitle {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  max-width: calc(100% - 0.75rem);
  cursor: pointer;
}

.chatActions {
  position: relative;
  margin-left: 0.5rem;
  display: flex;
  align-items: center;
  height: 100%;
}

.moreIcon {
  opacity: 0;
  transition: opacity 0.2s;
  color: #6b7280;
  cursor: pointer;
  padding: 0.25rem;

  &:hover {
    color: #111928;
  }
}

.navContainer {
  margin-top: -0.2rem;
  margin-bottom: 1rem;
}

.recentChatsTitle {
  font-family: 'Quasimoda';
  font-size: 14px;
  font-weight: 400;
  color: #6b7280;
  margin-bottom: 0.18rem;
  margin-left: 0.5rem;
  margin-top: 0.1rem;
  padding-top: 1.5rem;
  position: relative;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 0.04rem;
    background-color: #e5e7eb;
  }
}
