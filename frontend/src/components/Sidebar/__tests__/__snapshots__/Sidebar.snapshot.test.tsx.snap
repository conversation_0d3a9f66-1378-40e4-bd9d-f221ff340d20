// Vitest Snapshot v1, https://vitest.dev/guide/snapshot.html

exports[`Sidebar Snapshots > matches authenticated snapshot 1`] = `
<div>
  <aside
    class="_sidebar_51efae"
  >
    <div
      class="_container_51efae"
    >
      <div
        class="_header_51efae"
      >
        <div
          class="_logoContainer_51efae"
        >
          <div
            class="_logo_51efae"
          >
            <svg
              fill="none"
              height="24"
              viewBox="0 0 140 24"
              width="140"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                clip-rule="evenodd"
                d="M100.097 0.006C98.226 0.006 96.7381 0.252 95.6269 0.75C94.5157 1.248 93.7058 2.028 93.191 3.102C92.6762 4.176 92.4251 5.616 92.4251 7.434C92.4251 11.946 92.3686 15.642 92.2493 18.516H99.6888C99.5381 17.64 99.4377 16.944 99.3937 16.434C99.3498 15.924 99.3309 15.222 99.3309 14.328C99.3309 13.08 100.825 12.72 103.813 13.254V7.968C102.112 8.178 100.926 8.184 100.248 7.98C99.5695 7.776 99.2305 7.41 99.2305 6.87C99.2305 6.48 99.356 6.186 99.6134 5.994C99.8708 5.802 100.304 5.7 100.913 5.7C101.56 5.7 102.2 5.724 102.834 5.76L103.763 0.168C102.796 0.06 101.572 0 100.097 0V0.006ZM10.8798 13.446L10.8547 14.958C10.8422 16.326 10.7982 17.508 10.7291 18.516H18.3444C18.2942 17.574 18.2502 16.314 18.2188 14.742C18.1874 13.164 18.1686 11.418 18.1686 9.50399C18.1686 5.39399 18.2251 2.39399 18.3444 0.491994H10.7291C10.7982 1.70999 10.8359 3.10799 10.8547 4.67999C10.8735 5.23199 10.7354 5.65199 10.4466 5.94599C10.1578 6.23999 9.71838 6.38399 9.1722 6.38399C8.62601 6.38399 8.20538 6.23999 7.91031 5.94599C7.61525 5.65199 7.47085 5.23199 7.48969 4.67999C7.50224 3.00599 7.54619 1.61399 7.61525 0.491994H0C0.119283 2.58599 0.175785 5.59199 0.175785 9.50399C0.175785 11.502 0.156951 13.29 0.125561 14.874C0.0941704 16.458 0.0502242 17.67 0 18.516H7.61525C7.60859 18.3485 7.60193 18.1742 7.595 17.9927L7.59499 17.9924L7.59498 17.9921L7.59494 17.9912C7.58227 17.6595 7.56868 17.3035 7.55247 16.92C7.52735 16.326 7.50852 15.69 7.48969 15.006L7.46457 13.446C7.44574 12.894 7.59013 12.474 7.8852 12.18C8.18027 11.886 8.61345 11.742 9.1722 11.742C9.73094 11.742 10.1641 11.886 10.4592 12.18C10.7543 12.474 10.8987 12.894 10.8798 13.446ZM73.4152 14.772C73.296 14.55 73.1139 14.4 72.8691 14.322C72.6242 14.238 72.2538 14.202 71.7579 14.202C71.1301 14.202 70.7031 14.31 70.4834 14.532C70.2637 14.754 70.1193 15.18 70.0502 15.81L69.7426 18.51H62.7363C62.8054 17.862 62.9247 16.758 63.0942 15.198C63.3641 12.78 63.6843 10.35 64.0484 7.90799C64.4126 5.46599 64.8269 2.99399 65.2852 0.491994H78.9399C79.3982 2.99399 79.8126 5.46599 80.1767 7.90799C80.5408 10.35 80.861 12.78 81.1309 15.198C81.3004 16.758 81.4197 17.862 81.4888 18.51H73.974L73.6664 15.81C73.6161 15.342 73.5283 14.994 73.409 14.778L73.4152 14.772ZM73.0323 9.50399C73.2332 9.17999 73.2771 8.55599 73.1578 7.63199C73.0386 6.50999 72.8691 5.78399 72.6493 5.44199C72.4296 5.09999 72.1659 4.93199 71.8583 4.93199C71.5507 4.93199 71.287 5.10599 71.0673 5.45399C70.8475 5.80199 70.678 6.52799 70.5587 7.63199C70.4583 8.55599 70.4897 9.17999 70.6592 9.50399C70.8287 9.82799 71.1928 9.98999 71.7579 9.98999C72.4045 9.98999 72.8251 9.82799 73.0323 9.50399ZM90.209 18.51H83.1022C83.1525 17.652 83.1964 16.41 83.2278 14.796C83.2592 13.182 83.278 11.358 83.278 9.32999C83.278 5.56199 83.2153 2.61599 83.1022 0.491994H90.0081L90.0332 9.32999C90.0332 11.358 90.052 13.182 90.0834 14.796C90.1148 16.41 90.1588 17.652 90.209 18.51ZM33.1731 13.482C32.3067 13.704 31.3462 13.812 30.2915 13.812C29.4439 13.812 28.7408 13.704 28.1883 13.482C27.6359 13.26 27.3596 12.972 27.3596 12.618C27.3596 12.39 27.435 12.258 27.5919 12.216C27.7426 12.174 28.069 12.18 28.5587 12.228C29.1677 12.294 29.7641 12.324 30.3417 12.324C31.6664 12.324 32.9345 12.066 34.1525 11.556C35.3641 11.046 35.9731 9.936 35.9731 8.232C35.9731 6.882 35.4018 5.808 34.2529 4.992C33.104 4.182 31.2143 3.774 28.5838 3.774C26.5811 3.774 24.9803 4.026 23.7937 4.53C22.6072 5.034 21.7408 5.832 21.1946 6.93C20.6484 8.028 20.3785 9.498 20.3785 11.352C20.3785 13.206 20.6735 14.646 21.2574 15.738C21.8412 16.824 22.7829 17.622 24.0699 18.126C25.3632 18.63 27.0834 18.882 29.243 18.882C30.8753 18.882 32.2439 18.774 33.3551 18.564C33.8323 18.474 34.2529 18.372 34.617 18.27L35.565 12.576C34.7928 12.972 33.9955 13.278 33.1605 13.488L33.1731 13.482ZM27.5794 7.938C27.9435 7.434 28.4143 7.182 28.9856 7.182C29.4879 7.182 29.9022 7.32 30.2224 7.59C30.5426 7.866 30.7058 8.208 30.7058 8.622C30.7058 9.276 30.3794 9.732 29.7264 9.984C29.0735 10.236 28.2197 10.296 27.1713 10.164C27.0771 9.18 27.2152 8.436 27.5856 7.932L27.5794 7.938ZM47.1605 10.332C47.1605 7.524 47.104 5.424 46.9847 4.026H53.9408V17.31C53.9408 18.48 53.5704 19.554 52.8421 20.526C52.1076 21.504 51.0027 22.284 49.5273 22.866C48.052 23.448 46.2439 23.742 44.1031 23.742C42.1381 23.742 39.3255 23.358 37.5928 22.848L38.591 16.836C39.087 17.298 39.6583 17.658 40.2924 17.904C40.9829 18.174 41.8242 18.306 42.8287 18.306C43.896 18.306 44.8314 18.18 45.6412 17.928C46.4448 17.676 47.0664 17.364 47.4995 16.98C47.9264 16.596 48.1462 16.206 48.1462 15.798C48.1462 15.474 47.9955 15.312 47.6879 15.312C47.5686 15.312 47.4242 15.342 47.2547 15.396L47.2546 15.396C47.0851 15.45 46.8968 15.51 46.6959 15.576C46.087 15.786 45.4843 15.96 44.9004 16.098C44.3166 16.236 43.6072 16.302 42.7722 16.302C41.1273 16.302 39.8968 15.93 39.0807 15.192C38.2646 14.454 37.8565 13.194 37.8565 11.406C37.8565 8.124 37.8 5.664 37.6807 4.026H44.6116V9.654C44.6116 10.452 44.7058 11.004 44.8942 11.31C45.0762 11.616 45.409 11.772 45.8861 11.772C46.3255 11.772 46.652 11.658 46.8529 11.43C47.0601 11.202 47.1605 10.836 47.1605 10.332ZM106.387 18.51H113.237C113.187 17.826 113.143 16.848 113.111 15.576C113.08 14.304 113.061 12.864 113.061 11.256V4.026H106.387C106.5 5.694 106.563 8.106 106.563 11.256C106.563 12.858 106.544 14.304 106.513 15.576C106.482 16.848 106.438 17.826 106.387 18.51ZM133.433 13.104C135.291 11.766 138.305 12.648 139.184 14.688C141.318 18.786 137.665 24.672 132.736 23.904C132.592 23.88 132.479 23.784 132.435 23.652C132.385 23.49 132.454 23.31 132.611 23.226C134.406 22.242 134.77 20.04 133.082 18.45L133.063 18.432C131.682 16.956 131.763 14.298 133.433 13.098V13.104ZM124.782 13.752C125.837 13.752 126.797 13.644 127.664 13.422L127.651 13.428C128.486 13.218 129.283 12.912 130.056 12.516L129.108 18.21C128.743 18.312 128.323 18.414 127.846 18.504C126.735 18.714 125.366 18.822 123.734 18.822C121.574 18.822 119.854 18.57 118.561 18.066C117.274 17.562 116.332 16.764 115.748 15.678C115.164 14.586 114.869 13.146 114.869 11.292C114.869 9.438 115.139 7.968 115.685 6.87C116.231 5.772 117.098 4.974 118.284 4.47C119.471 3.966 121.072 3.714 123.074 3.714C125.705 3.714 127.595 4.122 128.743 4.932C129.892 5.748 130.464 6.822 130.464 8.172C130.464 9.876 129.855 10.986 128.643 11.496C127.425 12.006 126.157 12.264 124.832 12.264C124.255 12.264 123.658 12.234 123.049 12.168C122.56 12.12 122.233 12.114 122.083 12.156C121.926 12.198 121.85 12.33 121.85 12.558C121.85 12.912 122.126 13.2 122.679 13.422C123.231 13.644 123.935 13.752 124.782 13.752ZM123.476 7.122C122.905 7.122 122.434 7.374 122.07 7.878L122.076 7.872C121.706 8.376 121.568 9.12 121.662 10.104C122.71 10.236 123.564 10.176 124.217 9.924C124.87 9.672 125.196 9.216 125.196 8.562C125.196 8.148 125.033 7.806 124.713 7.53C124.393 7.26 123.978 7.122 123.476 7.122Z"
                fill="#342B25"
                fill-rule="evenodd"
              />
            </svg>
          </div>
        </div>
        <button
          class="_button_ee73f7 _primary_ee73f7 _l_ee73f7 _green-primary_ee73f7 _withText_ee73f7"
        >
          <svg
            class="_icon_ee73f7 _icon-l_ee73f7"
            color="currentColor"
            fill="none"
            height="24"
            viewBox="0 0 24 24"
            width="24"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M12 4V20"
              stroke="currentColor"
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="1.5"
            />
            <path
              d="M4 12H20"
              stroke="currentColor"
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="1.5"
            />
          </svg>
          <span
            class="_text_ee73f7 _text-l_ee73f7"
          >
            Create new
          </span>
        </button>
      </div>
      <div
        class="_content_51efae"
      >
        <nav>
          <ul
            class="space-y-2 _navContainer_51efae"
          >
            <li
              class=""
            >
              <a
                class="_navItem_4aca72 _navItem_51efae _active_51efae"
                href="/"
              >
                <div
                  class="_icon_053e64"
                >
                  <svg
                    color="currentColor"
                    fill="none"
                    height="20"
                    stroke-width="0"
                    viewBox="0 0 24 24"
                    width="20"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      d="M9.00239 16.999L9.00048 21.0005C9.00021 21.5526 8.55258 22 8.00048 22L5 22C3.89543 22 3 21.1046 3 20V10.065C3 9.45242 3.28074 8.87362 3.76182 8.49437L10.7618 2.97609C11.488 2.40361 12.512 2.40361 13.2382 2.97609L20.2382 8.49437C20.7193 8.87362 21 9.45243 21 10.065V20C21 21.1046 20.1046 22 19 22L16 22C15.4477 22 15 21.5523 15 21V17C15 15.8954 14.1046 15 13 15H11.0024C9.8982 15 9.00292 15.8948 9.00239 16.999Z"
                      stroke="currentColor"
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      stroke-width="1.5"
                    />
                  </svg>
                </div>
                <span
                  class="_navText_4aca72"
                >
                  Home
                </span>
              </a>
            </li>
            <li
              class="_comingSoon_51efae"
            >
              <div
                class="_navItem_4aca72 _disabled_4aca72 _navItem_51efae"
              >
                <div
                  class="_icon_053e64"
                >
                  <svg
                    color="currentColor"
                    fill="none"
                    height="20"
                    stroke-width="0"
                    viewBox="0 0 24 24"
                    width="20"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      d="M11 6L21 6"
                      stroke="currentColor"
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      stroke-width="1.5"
                    />
                    <path
                      d="M11 12L21 12"
                      stroke="currentColor"
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      stroke-width="1.5"
                    />
                    <path
                      d="M11 18L21 18"
                      stroke="currentColor"
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      stroke-width="1.5"
                    />
                    <path
                      d="M3 7.5L4.5 9L8.5 4"
                      stroke="currentColor"
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      stroke-width="1.5"
                    />
                    <path
                      d="M3 18.5L4.5 20L8.5 15"
                      stroke="currentColor"
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      stroke-width="1.5"
                    />
                  </svg>
                </div>
                <span
                  class="_navText_4aca72"
                >
                  To-do list
                </span>
                <div
                  class="_icon_053e64 _lockIcon_4aca72"
                >
                  <svg
                    color="currentColor"
                    fill="none"
                    height="20"
                    stroke-width="0"
                    viewBox="0 0 24 24"
                    width="20"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      d="M12 16.5V14.5"
                      stroke="currentColor"
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      stroke-width="1.5"
                    />
                    <path
                      d="M16.5 9V6.5C16.5 4.01472 14.4853 2 12 2C9.51472 2 7.5 4.01472 7.5 6.5V9"
                      stroke="currentColor"
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      stroke-width="1.5"
                    />
                    <path
                      d="M18 9H6.00019C4.89554 9 4.00008 9.89554 4.00018 11.0002L4.00102 20.0002C4.00112 21.1047 4.89652 22 6.00102 22H18C19.1046 22 20 21.1046 20 20V11C20 9.89543 19.1046 9 18 9Z"
                      stroke="currentColor"
                      stroke-linecap="round"
                      stroke-width="1.5"
                    />
                  </svg>
                </div>
              </div>
            </li>
            <li
              class=""
            >
              <a
                class="_navItem_4aca72 _navItem_51efae"
                href="/property-profile"
              >
                <div
                  class="_icon_053e64"
                >
                  <svg
                    color="currentColor"
                    fill="none"
                    height="20"
                    stroke-width="0"
                    viewBox="0 0 24 24"
                    width="20"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      d="M7 8L14.5 2L22 8"
                      stroke="currentColor"
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      stroke-width="1.5"
                    />
                    <path
                      d="M8.5 7V20M20.5 20V7"
                      stroke="currentColor"
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      stroke-width="1.5"
                    />
                    <path
                      d="M3.5 14V20"
                      stroke="currentColor"
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      stroke-width="1.5"
                    />
                    <path
                      d="M2 20H22"
                      stroke="currentColor"
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      stroke-width="1.5"
                    />
                    <path
                      d="M12.5 20V15C12.5 14.4477 12.9477 14 13.5 14H15.5C16.0523 14 16.5 14.4477 16.5 15V20"
                      stroke="currentColor"
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      stroke-width="1.5"
                    />
                    <path
                      d="M13.5 9H15.5"
                      stroke="currentColor"
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      stroke-width="1.5"
                    />
                  </svg>
                </div>
                <span
                  class="_navText_4aca72"
                >
                  Property Profile
                </span>
              </a>
            </li>
            <li
              class="_comingSoon_51efae"
            >
              <button
                class="_navItem_4aca72 _disabled_4aca72 _settingsItem_51efae"
                disabled=""
                type="button"
              >
                <div
                  class="_icon_053e64"
                >
                  <svg
                    color="currentColor"
                    fill="none"
                    height="20"
                    stroke-width="0"
                    viewBox="0 0 24 24"
                    width="20"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      d="M5.51906 10.2595L6.44721 10.7236C6.786 10.893 7 11.2393 7 11.618V16.382C7 16.7607 6.786 17.107 6.44721 17.2764L5.51906 17.7405C5.19808 17.901 4.81541 17.8772 4.53212 17.6569C3.68867 17.001 2 15.5005 2 14C2 12.4995 3.68867 10.999 4.53212 10.3431C4.81541 10.1228 5.19808 10.099 5.51906 10.2595Z"
                      stroke="currentColor"
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      stroke-width="1.5"
                    />
                    <path
                      d="M4 10.5V10C4 5.58172 7.58172 2 12 2V2C16.4183 2 20 5.58172 20 10V10.5"
                      stroke="currentColor"
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      stroke-width="1.5"
                    />
                    <path
                      d="M20 17.5V18C20 20.2091 18.2091 22 16 22H12"
                      stroke="currentColor"
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      stroke-width="1.5"
                    />
                    <path
                      d="M19.4679 10.3431C19.1846 10.1228 18.8019 10.099 18.4809 10.2595L17.5528 10.7236C17.214 10.893 17 11.2393 17 11.618V16.382C17 16.7607 17.214 17.107 17.5528 17.2764L18.4809 17.7405C18.8019 17.901 19.1846 17.8772 19.4679 17.6569C20.3113 17.001 22 15.5005 22 14C22 12.4995 20.3113 10.999 19.4679 10.3431Z"
                      stroke="currentColor"
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      stroke-width="1.5"
                    />
                  </svg>
                </div>
                <span
                  class="_navText_4aca72"
                >
                  Help
                </span>
                <div
                  class="_icon_053e64 _lockIcon_4aca72"
                >
                  <svg
                    color="currentColor"
                    fill="none"
                    height="20"
                    stroke-width="0"
                    viewBox="0 0 24 24"
                    width="20"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      d="M12 16.5V14.5"
                      stroke="currentColor"
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      stroke-width="1.5"
                    />
                    <path
                      d="M16.5 9V6.5C16.5 4.01472 14.4853 2 12 2C9.51472 2 7.5 4.01472 7.5 6.5V9"
                      stroke="currentColor"
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      stroke-width="1.5"
                    />
                    <path
                      d="M18 9H6.00019C4.89554 9 4.00008 9.89554 4.00018 11.0002L4.00102 20.0002C4.00112 21.1047 4.89652 22 6.00102 22H18C19.1046 22 20 21.1046 20 20V11C20 9.89543 19.1046 9 18 9Z"
                      stroke="currentColor"
                      stroke-linecap="round"
                      stroke-width="1.5"
                    />
                  </svg>
                </div>
              </button>
            </li>
          </ul>
        </nav>
        <div
          class="_recentChats_51efae"
        >
          <h2
            class="_recentChatsTitle_51efae"
          >
            RECENT CHATS
          </h2>
          <div
            class="_recentChatsWrapper_51efae"
          >
            <div
              class="_recentChatsContainer_51efae"
            >
              <ul
                class="space-y-1"
              >
                <li>
                  <a
                    class="_chatItem_51efae"
                    href="/chats/1"
                  >
                    <span
                      class="_chatTitle_51efae"
                    >
                      Test chat 1
                    </span>
                  </a>
                </li>
                <li>
                  <a
                    class="_chatItem_51efae"
                    href="/chats/2"
                  >
                    <span
                      class="_chatTitle_51efae"
                    >
                      Test chat 2
                    </span>
                  </a>
                </li>
              </ul>
              <div
                style="height: 20px;"
              />
            </div>
          </div>
        </div>
        <div
          class="_authContainer_51efae"
        >
          <p
            class="_authText_51efae"
          >
            Sign up or log in to access all the features
          </p>
          <div
            class="flex gap-4"
          >
            <div>
              <button
                class="_button_ee73f7 _primary_ee73f7 _base_ee73f7 _green-primary_ee73f7 _withText_ee73f7 flex-1"
              >
                <span
                  class="_text_ee73f7 _text-base_ee73f7"
                >
                  Sign up
                </span>
              </button>
            </div>
            <div>
              <button
                class="_button_ee73f7 _secondary_ee73f7 _base_ee73f7 _green-primary_ee73f7 _withText_ee73f7 flex-1"
              >
                <span
                  class="_text_ee73f7 _text-base_ee73f7"
                >
                  Log in
                </span>
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </aside>
</div>
`;

exports[`Sidebar Snapshots > matches snapshot with settings open 1`] = `
<div>
  <aside
    class="_sidebar_51efae"
  >
    <div
      class="_container_51efae"
    >
      <div
        class="_header_51efae"
      >
        <div
          class="_logoContainer_51efae"
        >
          <div
            class="_logo_51efae"
          >
            <svg
              fill="none"
              height="24"
              viewBox="0 0 140 24"
              width="140"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                clip-rule="evenodd"
                d="M100.097 0.006C98.226 0.006 96.7381 0.252 95.6269 0.75C94.5157 1.248 93.7058 2.028 93.191 3.102C92.6762 4.176 92.4251 5.616 92.4251 7.434C92.4251 11.946 92.3686 15.642 92.2493 18.516H99.6888C99.5381 17.64 99.4377 16.944 99.3937 16.434C99.3498 15.924 99.3309 15.222 99.3309 14.328C99.3309 13.08 100.825 12.72 103.813 13.254V7.968C102.112 8.178 100.926 8.184 100.248 7.98C99.5695 7.776 99.2305 7.41 99.2305 6.87C99.2305 6.48 99.356 6.186 99.6134 5.994C99.8708 5.802 100.304 5.7 100.913 5.7C101.56 5.7 102.2 5.724 102.834 5.76L103.763 0.168C102.796 0.06 101.572 0 100.097 0V0.006ZM10.8798 13.446L10.8547 14.958C10.8422 16.326 10.7982 17.508 10.7291 18.516H18.3444C18.2942 17.574 18.2502 16.314 18.2188 14.742C18.1874 13.164 18.1686 11.418 18.1686 9.50399C18.1686 5.39399 18.2251 2.39399 18.3444 0.491994H10.7291C10.7982 1.70999 10.8359 3.10799 10.8547 4.67999C10.8735 5.23199 10.7354 5.65199 10.4466 5.94599C10.1578 6.23999 9.71838 6.38399 9.1722 6.38399C8.62601 6.38399 8.20538 6.23999 7.91031 5.94599C7.61525 5.65199 7.47085 5.23199 7.48969 4.67999C7.50224 3.00599 7.54619 1.61399 7.61525 0.491994H0C0.119283 2.58599 0.175785 5.59199 0.175785 9.50399C0.175785 11.502 0.156951 13.29 0.125561 14.874C0.0941704 16.458 0.0502242 17.67 0 18.516H7.61525C7.60859 18.3485 7.60193 18.1742 7.595 17.9927L7.59499 17.9924L7.59498 17.9921L7.59494 17.9912C7.58227 17.6595 7.56868 17.3035 7.55247 16.92C7.52735 16.326 7.50852 15.69 7.48969 15.006L7.46457 13.446C7.44574 12.894 7.59013 12.474 7.8852 12.18C8.18027 11.886 8.61345 11.742 9.1722 11.742C9.73094 11.742 10.1641 11.886 10.4592 12.18C10.7543 12.474 10.8987 12.894 10.8798 13.446ZM73.4152 14.772C73.296 14.55 73.1139 14.4 72.8691 14.322C72.6242 14.238 72.2538 14.202 71.7579 14.202C71.1301 14.202 70.7031 14.31 70.4834 14.532C70.2637 14.754 70.1193 15.18 70.0502 15.81L69.7426 18.51H62.7363C62.8054 17.862 62.9247 16.758 63.0942 15.198C63.3641 12.78 63.6843 10.35 64.0484 7.90799C64.4126 5.46599 64.8269 2.99399 65.2852 0.491994H78.9399C79.3982 2.99399 79.8126 5.46599 80.1767 7.90799C80.5408 10.35 80.861 12.78 81.1309 15.198C81.3004 16.758 81.4197 17.862 81.4888 18.51H73.974L73.6664 15.81C73.6161 15.342 73.5283 14.994 73.409 14.778L73.4152 14.772ZM73.0323 9.50399C73.2332 9.17999 73.2771 8.55599 73.1578 7.63199C73.0386 6.50999 72.8691 5.78399 72.6493 5.44199C72.4296 5.09999 72.1659 4.93199 71.8583 4.93199C71.5507 4.93199 71.287 5.10599 71.0673 5.45399C70.8475 5.80199 70.678 6.52799 70.5587 7.63199C70.4583 8.55599 70.4897 9.17999 70.6592 9.50399C70.8287 9.82799 71.1928 9.98999 71.7579 9.98999C72.4045 9.98999 72.8251 9.82799 73.0323 9.50399ZM90.209 18.51H83.1022C83.1525 17.652 83.1964 16.41 83.2278 14.796C83.2592 13.182 83.278 11.358 83.278 9.32999C83.278 5.56199 83.2153 2.61599 83.1022 0.491994H90.0081L90.0332 9.32999C90.0332 11.358 90.052 13.182 90.0834 14.796C90.1148 16.41 90.1588 17.652 90.209 18.51ZM33.1731 13.482C32.3067 13.704 31.3462 13.812 30.2915 13.812C29.4439 13.812 28.7408 13.704 28.1883 13.482C27.6359 13.26 27.3596 12.972 27.3596 12.618C27.3596 12.39 27.435 12.258 27.5919 12.216C27.7426 12.174 28.069 12.18 28.5587 12.228C29.1677 12.294 29.7641 12.324 30.3417 12.324C31.6664 12.324 32.9345 12.066 34.1525 11.556C35.3641 11.046 35.9731 9.936 35.9731 8.232C35.9731 6.882 35.4018 5.808 34.2529 4.992C33.104 4.182 31.2143 3.774 28.5838 3.774C26.5811 3.774 24.9803 4.026 23.7937 4.53C22.6072 5.034 21.7408 5.832 21.1946 6.93C20.6484 8.028 20.3785 9.498 20.3785 11.352C20.3785 13.206 20.6735 14.646 21.2574 15.738C21.8412 16.824 22.7829 17.622 24.0699 18.126C25.3632 18.63 27.0834 18.882 29.243 18.882C30.8753 18.882 32.2439 18.774 33.3551 18.564C33.8323 18.474 34.2529 18.372 34.617 18.27L35.565 12.576C34.7928 12.972 33.9955 13.278 33.1605 13.488L33.1731 13.482ZM27.5794 7.938C27.9435 7.434 28.4143 7.182 28.9856 7.182C29.4879 7.182 29.9022 7.32 30.2224 7.59C30.5426 7.866 30.7058 8.208 30.7058 8.622C30.7058 9.276 30.3794 9.732 29.7264 9.984C29.0735 10.236 28.2197 10.296 27.1713 10.164C27.0771 9.18 27.2152 8.436 27.5856 7.932L27.5794 7.938ZM47.1605 10.332C47.1605 7.524 47.104 5.424 46.9847 4.026H53.9408V17.31C53.9408 18.48 53.5704 19.554 52.8421 20.526C52.1076 21.504 51.0027 22.284 49.5273 22.866C48.052 23.448 46.2439 23.742 44.1031 23.742C42.1381 23.742 39.3255 23.358 37.5928 22.848L38.591 16.836C39.087 17.298 39.6583 17.658 40.2924 17.904C40.9829 18.174 41.8242 18.306 42.8287 18.306C43.896 18.306 44.8314 18.18 45.6412 17.928C46.4448 17.676 47.0664 17.364 47.4995 16.98C47.9264 16.596 48.1462 16.206 48.1462 15.798C48.1462 15.474 47.9955 15.312 47.6879 15.312C47.5686 15.312 47.4242 15.342 47.2547 15.396L47.2546 15.396C47.0851 15.45 46.8968 15.51 46.6959 15.576C46.087 15.786 45.4843 15.96 44.9004 16.098C44.3166 16.236 43.6072 16.302 42.7722 16.302C41.1273 16.302 39.8968 15.93 39.0807 15.192C38.2646 14.454 37.8565 13.194 37.8565 11.406C37.8565 8.124 37.8 5.664 37.6807 4.026H44.6116V9.654C44.6116 10.452 44.7058 11.004 44.8942 11.31C45.0762 11.616 45.409 11.772 45.8861 11.772C46.3255 11.772 46.652 11.658 46.8529 11.43C47.0601 11.202 47.1605 10.836 47.1605 10.332ZM106.387 18.51H113.237C113.187 17.826 113.143 16.848 113.111 15.576C113.08 14.304 113.061 12.864 113.061 11.256V4.026H106.387C106.5 5.694 106.563 8.106 106.563 11.256C106.563 12.858 106.544 14.304 106.513 15.576C106.482 16.848 106.438 17.826 106.387 18.51ZM133.433 13.104C135.291 11.766 138.305 12.648 139.184 14.688C141.318 18.786 137.665 24.672 132.736 23.904C132.592 23.88 132.479 23.784 132.435 23.652C132.385 23.49 132.454 23.31 132.611 23.226C134.406 22.242 134.77 20.04 133.082 18.45L133.063 18.432C131.682 16.956 131.763 14.298 133.433 13.098V13.104ZM124.782 13.752C125.837 13.752 126.797 13.644 127.664 13.422L127.651 13.428C128.486 13.218 129.283 12.912 130.056 12.516L129.108 18.21C128.743 18.312 128.323 18.414 127.846 18.504C126.735 18.714 125.366 18.822 123.734 18.822C121.574 18.822 119.854 18.57 118.561 18.066C117.274 17.562 116.332 16.764 115.748 15.678C115.164 14.586 114.869 13.146 114.869 11.292C114.869 9.438 115.139 7.968 115.685 6.87C116.231 5.772 117.098 4.974 118.284 4.47C119.471 3.966 121.072 3.714 123.074 3.714C125.705 3.714 127.595 4.122 128.743 4.932C129.892 5.748 130.464 6.822 130.464 8.172C130.464 9.876 129.855 10.986 128.643 11.496C127.425 12.006 126.157 12.264 124.832 12.264C124.255 12.264 123.658 12.234 123.049 12.168C122.56 12.12 122.233 12.114 122.083 12.156C121.926 12.198 121.85 12.33 121.85 12.558C121.85 12.912 122.126 13.2 122.679 13.422C123.231 13.644 123.935 13.752 124.782 13.752ZM123.476 7.122C122.905 7.122 122.434 7.374 122.07 7.878L122.076 7.872C121.706 8.376 121.568 9.12 121.662 10.104C122.71 10.236 123.564 10.176 124.217 9.924C124.87 9.672 125.196 9.216 125.196 8.562C125.196 8.148 125.033 7.806 124.713 7.53C124.393 7.26 123.978 7.122 123.476 7.122Z"
                fill="#342B25"
                fill-rule="evenodd"
              />
            </svg>
          </div>
        </div>
        <button
          class="_button_ee73f7 _primary_ee73f7 _l_ee73f7 _green-primary_ee73f7 _withText_ee73f7"
        >
          <svg
            class="_icon_ee73f7 _icon-l_ee73f7"
            color="currentColor"
            fill="none"
            height="24"
            viewBox="0 0 24 24"
            width="24"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M12 4V20"
              stroke="currentColor"
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="1.5"
            />
            <path
              d="M4 12H20"
              stroke="currentColor"
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="1.5"
            />
          </svg>
          <span
            class="_text_ee73f7 _text-l_ee73f7"
          >
            Create new
          </span>
        </button>
      </div>
      <div
        class="_content_51efae"
      >
        <nav>
          <ul
            class="space-y-2 _navContainer_51efae"
          >
            <li
              class=""
            >
              <a
                class="_navItem_4aca72 _navItem_51efae _active_51efae"
                href="/"
              >
                <div
                  class="_icon_053e64"
                >
                  <svg
                    color="currentColor"
                    fill="none"
                    height="20"
                    stroke-width="0"
                    viewBox="0 0 24 24"
                    width="20"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      d="M9.00239 16.999L9.00048 21.0005C9.00021 21.5526 8.55258 22 8.00048 22L5 22C3.89543 22 3 21.1046 3 20V10.065C3 9.45242 3.28074 8.87362 3.76182 8.49437L10.7618 2.97609C11.488 2.40361 12.512 2.40361 13.2382 2.97609L20.2382 8.49437C20.7193 8.87362 21 9.45243 21 10.065V20C21 21.1046 20.1046 22 19 22L16 22C15.4477 22 15 21.5523 15 21V17C15 15.8954 14.1046 15 13 15H11.0024C9.8982 15 9.00292 15.8948 9.00239 16.999Z"
                      stroke="currentColor"
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      stroke-width="1.5"
                    />
                  </svg>
                </div>
                <span
                  class="_navText_4aca72"
                >
                  Home
                </span>
              </a>
            </li>
            <li
              class="_comingSoon_51efae"
            >
              <div
                class="_navItem_4aca72 _disabled_4aca72 _navItem_51efae"
              >
                <div
                  class="_icon_053e64"
                >
                  <svg
                    color="currentColor"
                    fill="none"
                    height="20"
                    stroke-width="0"
                    viewBox="0 0 24 24"
                    width="20"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      d="M11 6L21 6"
                      stroke="currentColor"
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      stroke-width="1.5"
                    />
                    <path
                      d="M11 12L21 12"
                      stroke="currentColor"
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      stroke-width="1.5"
                    />
                    <path
                      d="M11 18L21 18"
                      stroke="currentColor"
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      stroke-width="1.5"
                    />
                    <path
                      d="M3 7.5L4.5 9L8.5 4"
                      stroke="currentColor"
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      stroke-width="1.5"
                    />
                    <path
                      d="M3 18.5L4.5 20L8.5 15"
                      stroke="currentColor"
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      stroke-width="1.5"
                    />
                  </svg>
                </div>
                <span
                  class="_navText_4aca72"
                >
                  To-do list
                </span>
                <div
                  class="_icon_053e64 _lockIcon_4aca72"
                >
                  <svg
                    color="currentColor"
                    fill="none"
                    height="20"
                    stroke-width="0"
                    viewBox="0 0 24 24"
                    width="20"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      d="M12 16.5V14.5"
                      stroke="currentColor"
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      stroke-width="1.5"
                    />
                    <path
                      d="M16.5 9V6.5C16.5 4.01472 14.4853 2 12 2C9.51472 2 7.5 4.01472 7.5 6.5V9"
                      stroke="currentColor"
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      stroke-width="1.5"
                    />
                    <path
                      d="M18 9H6.00019C4.89554 9 4.00008 9.89554 4.00018 11.0002L4.00102 20.0002C4.00112 21.1047 4.89652 22 6.00102 22H18C19.1046 22 20 21.1046 20 20V11C20 9.89543 19.1046 9 18 9Z"
                      stroke="currentColor"
                      stroke-linecap="round"
                      stroke-width="1.5"
                    />
                  </svg>
                </div>
              </div>
            </li>
            <li
              class=""
            >
              <a
                class="_navItem_4aca72 _navItem_51efae"
                href="/property-profile"
              >
                <div
                  class="_icon_053e64"
                >
                  <svg
                    color="currentColor"
                    fill="none"
                    height="20"
                    stroke-width="0"
                    viewBox="0 0 24 24"
                    width="20"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      d="M7 8L14.5 2L22 8"
                      stroke="currentColor"
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      stroke-width="1.5"
                    />
                    <path
                      d="M8.5 7V20M20.5 20V7"
                      stroke="currentColor"
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      stroke-width="1.5"
                    />
                    <path
                      d="M3.5 14V20"
                      stroke="currentColor"
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      stroke-width="1.5"
                    />
                    <path
                      d="M2 20H22"
                      stroke="currentColor"
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      stroke-width="1.5"
                    />
                    <path
                      d="M12.5 20V15C12.5 14.4477 12.9477 14 13.5 14H15.5C16.0523 14 16.5 14.4477 16.5 15V20"
                      stroke="currentColor"
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      stroke-width="1.5"
                    />
                    <path
                      d="M13.5 9H15.5"
                      stroke="currentColor"
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      stroke-width="1.5"
                    />
                  </svg>
                </div>
                <span
                  class="_navText_4aca72"
                >
                  Property Profile
                </span>
              </a>
            </li>
            <li
              class="_comingSoon_51efae"
            >
              <button
                class="_navItem_4aca72 _disabled_4aca72 _settingsItem_51efae"
                disabled=""
                type="button"
              >
                <div
                  class="_icon_053e64"
                >
                  <svg
                    color="currentColor"
                    fill="none"
                    height="20"
                    stroke-width="0"
                    viewBox="0 0 24 24"
                    width="20"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      d="M5.51906 10.2595L6.44721 10.7236C6.786 10.893 7 11.2393 7 11.618V16.382C7 16.7607 6.786 17.107 6.44721 17.2764L5.51906 17.7405C5.19808 17.901 4.81541 17.8772 4.53212 17.6569C3.68867 17.001 2 15.5005 2 14C2 12.4995 3.68867 10.999 4.53212 10.3431C4.81541 10.1228 5.19808 10.099 5.51906 10.2595Z"
                      stroke="currentColor"
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      stroke-width="1.5"
                    />
                    <path
                      d="M4 10.5V10C4 5.58172 7.58172 2 12 2V2C16.4183 2 20 5.58172 20 10V10.5"
                      stroke="currentColor"
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      stroke-width="1.5"
                    />
                    <path
                      d="M20 17.5V18C20 20.2091 18.2091 22 16 22H12"
                      stroke="currentColor"
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      stroke-width="1.5"
                    />
                    <path
                      d="M19.4679 10.3431C19.1846 10.1228 18.8019 10.099 18.4809 10.2595L17.5528 10.7236C17.214 10.893 17 11.2393 17 11.618V16.382C17 16.7607 17.214 17.107 17.5528 17.2764L18.4809 17.7405C18.8019 17.901 19.1846 17.8772 19.4679 17.6569C20.3113 17.001 22 15.5005 22 14C22 12.4995 20.3113 10.999 19.4679 10.3431Z"
                      stroke="currentColor"
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      stroke-width="1.5"
                    />
                  </svg>
                </div>
                <span
                  class="_navText_4aca72"
                >
                  Help
                </span>
                <div
                  class="_icon_053e64 _lockIcon_4aca72"
                >
                  <svg
                    color="currentColor"
                    fill="none"
                    height="20"
                    stroke-width="0"
                    viewBox="0 0 24 24"
                    width="20"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      d="M12 16.5V14.5"
                      stroke="currentColor"
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      stroke-width="1.5"
                    />
                    <path
                      d="M16.5 9V6.5C16.5 4.01472 14.4853 2 12 2C9.51472 2 7.5 4.01472 7.5 6.5V9"
                      stroke="currentColor"
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      stroke-width="1.5"
                    />
                    <path
                      d="M18 9H6.00019C4.89554 9 4.00008 9.89554 4.00018 11.0002L4.00102 20.0002C4.00112 21.1047 4.89652 22 6.00102 22H18C19.1046 22 20 21.1046 20 20V11C20 9.89543 19.1046 9 18 9Z"
                      stroke="currentColor"
                      stroke-linecap="round"
                      stroke-width="1.5"
                    />
                  </svg>
                </div>
              </button>
            </li>
          </ul>
        </nav>
        <div
          class="_recentChats_51efae"
        >
          <h2
            class="_recentChatsTitle_51efae"
          >
            RECENT CHATS
          </h2>
          <div
            class="_recentChatsWrapper_51efae"
          >
            <div
              class="_recentChatsContainer_51efae"
            >
              <ul
                class="space-y-1"
              >
                <li>
                  <a
                    class="_chatItem_51efae"
                    href="/chats/1"
                  >
                    <span
                      class="_chatTitle_51efae"
                    >
                      Test chat 1
                    </span>
                  </a>
                </li>
                <li>
                  <a
                    class="_chatItem_51efae"
                    href="/chats/2"
                  >
                    <span
                      class="_chatTitle_51efae"
                    >
                      Test chat 2
                    </span>
                  </a>
                </li>
              </ul>
              <div
                style="height: 20px;"
              />
            </div>
          </div>
        </div>
        <div
          class="_authContainer_51efae"
        >
          <p
            class="_authText_51efae"
          >
            Sign up or log in to access all the features
          </p>
          <div
            class="flex gap-4"
          >
            <div>
              <button
                class="_button_ee73f7 _primary_ee73f7 _base_ee73f7 _green-primary_ee73f7 _withText_ee73f7 flex-1"
              >
                <span
                  class="_text_ee73f7 _text-base_ee73f7"
                >
                  Sign up
                </span>
              </button>
            </div>
            <div>
              <button
                class="_button_ee73f7 _secondary_ee73f7 _base_ee73f7 _green-primary_ee73f7 _withText_ee73f7 flex-1"
              >
                <span
                  class="_text_ee73f7 _text-base_ee73f7"
                >
                  Log in
                </span>
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </aside>
</div>
`;

exports[`Sidebar Snapshots > matches unauthenticated snapshot 1`] = `
<div>
  <aside
    class="_sidebar_51efae"
  >
    <div
      class="_container_51efae"
    >
      <div
        class="_header_51efae"
      >
        <div
          class="_logoContainer_51efae"
        >
          <div
            class="_logo_51efae"
          >
            <svg
              fill="none"
              height="24"
              viewBox="0 0 140 24"
              width="140"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                clip-rule="evenodd"
                d="M100.097 0.006C98.226 0.006 96.7381 0.252 95.6269 0.75C94.5157 1.248 93.7058 2.028 93.191 3.102C92.6762 4.176 92.4251 5.616 92.4251 7.434C92.4251 11.946 92.3686 15.642 92.2493 18.516H99.6888C99.5381 17.64 99.4377 16.944 99.3937 16.434C99.3498 15.924 99.3309 15.222 99.3309 14.328C99.3309 13.08 100.825 12.72 103.813 13.254V7.968C102.112 8.178 100.926 8.184 100.248 7.98C99.5695 7.776 99.2305 7.41 99.2305 6.87C99.2305 6.48 99.356 6.186 99.6134 5.994C99.8708 5.802 100.304 5.7 100.913 5.7C101.56 5.7 102.2 5.724 102.834 5.76L103.763 0.168C102.796 0.06 101.572 0 100.097 0V0.006ZM10.8798 13.446L10.8547 14.958C10.8422 16.326 10.7982 17.508 10.7291 18.516H18.3444C18.2942 17.574 18.2502 16.314 18.2188 14.742C18.1874 13.164 18.1686 11.418 18.1686 9.50399C18.1686 5.39399 18.2251 2.39399 18.3444 0.491994H10.7291C10.7982 1.70999 10.8359 3.10799 10.8547 4.67999C10.8735 5.23199 10.7354 5.65199 10.4466 5.94599C10.1578 6.23999 9.71838 6.38399 9.1722 6.38399C8.62601 6.38399 8.20538 6.23999 7.91031 5.94599C7.61525 5.65199 7.47085 5.23199 7.48969 4.67999C7.50224 3.00599 7.54619 1.61399 7.61525 0.491994H0C0.119283 2.58599 0.175785 5.59199 0.175785 9.50399C0.175785 11.502 0.156951 13.29 0.125561 14.874C0.0941704 16.458 0.0502242 17.67 0 18.516H7.61525C7.60859 18.3485 7.60193 18.1742 7.595 17.9927L7.59499 17.9924L7.59498 17.9921L7.59494 17.9912C7.58227 17.6595 7.56868 17.3035 7.55247 16.92C7.52735 16.326 7.50852 15.69 7.48969 15.006L7.46457 13.446C7.44574 12.894 7.59013 12.474 7.8852 12.18C8.18027 11.886 8.61345 11.742 9.1722 11.742C9.73094 11.742 10.1641 11.886 10.4592 12.18C10.7543 12.474 10.8987 12.894 10.8798 13.446ZM73.4152 14.772C73.296 14.55 73.1139 14.4 72.8691 14.322C72.6242 14.238 72.2538 14.202 71.7579 14.202C71.1301 14.202 70.7031 14.31 70.4834 14.532C70.2637 14.754 70.1193 15.18 70.0502 15.81L69.7426 18.51H62.7363C62.8054 17.862 62.9247 16.758 63.0942 15.198C63.3641 12.78 63.6843 10.35 64.0484 7.90799C64.4126 5.46599 64.8269 2.99399 65.2852 0.491994H78.9399C79.3982 2.99399 79.8126 5.46599 80.1767 7.90799C80.5408 10.35 80.861 12.78 81.1309 15.198C81.3004 16.758 81.4197 17.862 81.4888 18.51H73.974L73.6664 15.81C73.6161 15.342 73.5283 14.994 73.409 14.778L73.4152 14.772ZM73.0323 9.50399C73.2332 9.17999 73.2771 8.55599 73.1578 7.63199C73.0386 6.50999 72.8691 5.78399 72.6493 5.44199C72.4296 5.09999 72.1659 4.93199 71.8583 4.93199C71.5507 4.93199 71.287 5.10599 71.0673 5.45399C70.8475 5.80199 70.678 6.52799 70.5587 7.63199C70.4583 8.55599 70.4897 9.17999 70.6592 9.50399C70.8287 9.82799 71.1928 9.98999 71.7579 9.98999C72.4045 9.98999 72.8251 9.82799 73.0323 9.50399ZM90.209 18.51H83.1022C83.1525 17.652 83.1964 16.41 83.2278 14.796C83.2592 13.182 83.278 11.358 83.278 9.32999C83.278 5.56199 83.2153 2.61599 83.1022 0.491994H90.0081L90.0332 9.32999C90.0332 11.358 90.052 13.182 90.0834 14.796C90.1148 16.41 90.1588 17.652 90.209 18.51ZM33.1731 13.482C32.3067 13.704 31.3462 13.812 30.2915 13.812C29.4439 13.812 28.7408 13.704 28.1883 13.482C27.6359 13.26 27.3596 12.972 27.3596 12.618C27.3596 12.39 27.435 12.258 27.5919 12.216C27.7426 12.174 28.069 12.18 28.5587 12.228C29.1677 12.294 29.7641 12.324 30.3417 12.324C31.6664 12.324 32.9345 12.066 34.1525 11.556C35.3641 11.046 35.9731 9.936 35.9731 8.232C35.9731 6.882 35.4018 5.808 34.2529 4.992C33.104 4.182 31.2143 3.774 28.5838 3.774C26.5811 3.774 24.9803 4.026 23.7937 4.53C22.6072 5.034 21.7408 5.832 21.1946 6.93C20.6484 8.028 20.3785 9.498 20.3785 11.352C20.3785 13.206 20.6735 14.646 21.2574 15.738C21.8412 16.824 22.7829 17.622 24.0699 18.126C25.3632 18.63 27.0834 18.882 29.243 18.882C30.8753 18.882 32.2439 18.774 33.3551 18.564C33.8323 18.474 34.2529 18.372 34.617 18.27L35.565 12.576C34.7928 12.972 33.9955 13.278 33.1605 13.488L33.1731 13.482ZM27.5794 7.938C27.9435 7.434 28.4143 7.182 28.9856 7.182C29.4879 7.182 29.9022 7.32 30.2224 7.59C30.5426 7.866 30.7058 8.208 30.7058 8.622C30.7058 9.276 30.3794 9.732 29.7264 9.984C29.0735 10.236 28.2197 10.296 27.1713 10.164C27.0771 9.18 27.2152 8.436 27.5856 7.932L27.5794 7.938ZM47.1605 10.332C47.1605 7.524 47.104 5.424 46.9847 4.026H53.9408V17.31C53.9408 18.48 53.5704 19.554 52.8421 20.526C52.1076 21.504 51.0027 22.284 49.5273 22.866C48.052 23.448 46.2439 23.742 44.1031 23.742C42.1381 23.742 39.3255 23.358 37.5928 22.848L38.591 16.836C39.087 17.298 39.6583 17.658 40.2924 17.904C40.9829 18.174 41.8242 18.306 42.8287 18.306C43.896 18.306 44.8314 18.18 45.6412 17.928C46.4448 17.676 47.0664 17.364 47.4995 16.98C47.9264 16.596 48.1462 16.206 48.1462 15.798C48.1462 15.474 47.9955 15.312 47.6879 15.312C47.5686 15.312 47.4242 15.342 47.2547 15.396L47.2546 15.396C47.0851 15.45 46.8968 15.51 46.6959 15.576C46.087 15.786 45.4843 15.96 44.9004 16.098C44.3166 16.236 43.6072 16.302 42.7722 16.302C41.1273 16.302 39.8968 15.93 39.0807 15.192C38.2646 14.454 37.8565 13.194 37.8565 11.406C37.8565 8.124 37.8 5.664 37.6807 4.026H44.6116V9.654C44.6116 10.452 44.7058 11.004 44.8942 11.31C45.0762 11.616 45.409 11.772 45.8861 11.772C46.3255 11.772 46.652 11.658 46.8529 11.43C47.0601 11.202 47.1605 10.836 47.1605 10.332ZM106.387 18.51H113.237C113.187 17.826 113.143 16.848 113.111 15.576C113.08 14.304 113.061 12.864 113.061 11.256V4.026H106.387C106.5 5.694 106.563 8.106 106.563 11.256C106.563 12.858 106.544 14.304 106.513 15.576C106.482 16.848 106.438 17.826 106.387 18.51ZM133.433 13.104C135.291 11.766 138.305 12.648 139.184 14.688C141.318 18.786 137.665 24.672 132.736 23.904C132.592 23.88 132.479 23.784 132.435 23.652C132.385 23.49 132.454 23.31 132.611 23.226C134.406 22.242 134.77 20.04 133.082 18.45L133.063 18.432C131.682 16.956 131.763 14.298 133.433 13.098V13.104ZM124.782 13.752C125.837 13.752 126.797 13.644 127.664 13.422L127.651 13.428C128.486 13.218 129.283 12.912 130.056 12.516L129.108 18.21C128.743 18.312 128.323 18.414 127.846 18.504C126.735 18.714 125.366 18.822 123.734 18.822C121.574 18.822 119.854 18.57 118.561 18.066C117.274 17.562 116.332 16.764 115.748 15.678C115.164 14.586 114.869 13.146 114.869 11.292C114.869 9.438 115.139 7.968 115.685 6.87C116.231 5.772 117.098 4.974 118.284 4.47C119.471 3.966 121.072 3.714 123.074 3.714C125.705 3.714 127.595 4.122 128.743 4.932C129.892 5.748 130.464 6.822 130.464 8.172C130.464 9.876 129.855 10.986 128.643 11.496C127.425 12.006 126.157 12.264 124.832 12.264C124.255 12.264 123.658 12.234 123.049 12.168C122.56 12.12 122.233 12.114 122.083 12.156C121.926 12.198 121.85 12.33 121.85 12.558C121.85 12.912 122.126 13.2 122.679 13.422C123.231 13.644 123.935 13.752 124.782 13.752ZM123.476 7.122C122.905 7.122 122.434 7.374 122.07 7.878L122.076 7.872C121.706 8.376 121.568 9.12 121.662 10.104C122.71 10.236 123.564 10.176 124.217 9.924C124.87 9.672 125.196 9.216 125.196 8.562C125.196 8.148 125.033 7.806 124.713 7.53C124.393 7.26 123.978 7.122 123.476 7.122Z"
                fill="#342B25"
                fill-rule="evenodd"
              />
            </svg>
          </div>
        </div>
        <button
          class="_button_ee73f7 _primary_ee73f7 _l_ee73f7 _green-primary_ee73f7 _withText_ee73f7"
        >
          <svg
            class="_icon_ee73f7 _icon-l_ee73f7"
            color="currentColor"
            fill="none"
            height="24"
            viewBox="0 0 24 24"
            width="24"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M12 4V20"
              stroke="currentColor"
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="1.5"
            />
            <path
              d="M4 12H20"
              stroke="currentColor"
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="1.5"
            />
          </svg>
          <span
            class="_text_ee73f7 _text-l_ee73f7"
          >
            Create new
          </span>
        </button>
      </div>
      <div
        class="_content_51efae"
      >
        <nav>
          <ul
            class="space-y-2 _navContainer_51efae"
          >
            <li
              class=""
            >
              <a
                class="_navItem_4aca72 _navItem_51efae _active_51efae"
                href="/"
              >
                <div
                  class="_icon_053e64"
                >
                  <svg
                    color="currentColor"
                    fill="none"
                    height="20"
                    stroke-width="0"
                    viewBox="0 0 24 24"
                    width="20"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      d="M9.00239 16.999L9.00048 21.0005C9.00021 21.5526 8.55258 22 8.00048 22L5 22C3.89543 22 3 21.1046 3 20V10.065C3 9.45242 3.28074 8.87362 3.76182 8.49437L10.7618 2.97609C11.488 2.40361 12.512 2.40361 13.2382 2.97609L20.2382 8.49437C20.7193 8.87362 21 9.45243 21 10.065V20C21 21.1046 20.1046 22 19 22L16 22C15.4477 22 15 21.5523 15 21V17C15 15.8954 14.1046 15 13 15H11.0024C9.8982 15 9.00292 15.8948 9.00239 16.999Z"
                      stroke="currentColor"
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      stroke-width="1.5"
                    />
                  </svg>
                </div>
                <span
                  class="_navText_4aca72"
                >
                  Home
                </span>
              </a>
            </li>
            <li
              class="_comingSoon_51efae"
            >
              <div
                class="_navItem_4aca72 _disabled_4aca72 _navItem_51efae"
              >
                <div
                  class="_icon_053e64"
                >
                  <svg
                    color="currentColor"
                    fill="none"
                    height="20"
                    stroke-width="0"
                    viewBox="0 0 24 24"
                    width="20"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      d="M11 6L21 6"
                      stroke="currentColor"
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      stroke-width="1.5"
                    />
                    <path
                      d="M11 12L21 12"
                      stroke="currentColor"
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      stroke-width="1.5"
                    />
                    <path
                      d="M11 18L21 18"
                      stroke="currentColor"
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      stroke-width="1.5"
                    />
                    <path
                      d="M3 7.5L4.5 9L8.5 4"
                      stroke="currentColor"
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      stroke-width="1.5"
                    />
                    <path
                      d="M3 18.5L4.5 20L8.5 15"
                      stroke="currentColor"
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      stroke-width="1.5"
                    />
                  </svg>
                </div>
                <span
                  class="_navText_4aca72"
                >
                  To-do list
                </span>
                <div
                  class="_icon_053e64 _lockIcon_4aca72"
                >
                  <svg
                    color="currentColor"
                    fill="none"
                    height="20"
                    stroke-width="0"
                    viewBox="0 0 24 24"
                    width="20"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      d="M12 16.5V14.5"
                      stroke="currentColor"
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      stroke-width="1.5"
                    />
                    <path
                      d="M16.5 9V6.5C16.5 4.01472 14.4853 2 12 2C9.51472 2 7.5 4.01472 7.5 6.5V9"
                      stroke="currentColor"
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      stroke-width="1.5"
                    />
                    <path
                      d="M18 9H6.00019C4.89554 9 4.00008 9.89554 4.00018 11.0002L4.00102 20.0002C4.00112 21.1047 4.89652 22 6.00102 22H18C19.1046 22 20 21.1046 20 20V11C20 9.89543 19.1046 9 18 9Z"
                      stroke="currentColor"
                      stroke-linecap="round"
                      stroke-width="1.5"
                    />
                  </svg>
                </div>
              </div>
            </li>
            <li
              class=""
            >
              <a
                class="_navItem_4aca72 _navItem_51efae"
                href="/property-profile"
              >
                <div
                  class="_icon_053e64"
                >
                  <svg
                    color="currentColor"
                    fill="none"
                    height="20"
                    stroke-width="0"
                    viewBox="0 0 24 24"
                    width="20"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      d="M7 8L14.5 2L22 8"
                      stroke="currentColor"
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      stroke-width="1.5"
                    />
                    <path
                      d="M8.5 7V20M20.5 20V7"
                      stroke="currentColor"
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      stroke-width="1.5"
                    />
                    <path
                      d="M3.5 14V20"
                      stroke="currentColor"
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      stroke-width="1.5"
                    />
                    <path
                      d="M2 20H22"
                      stroke="currentColor"
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      stroke-width="1.5"
                    />
                    <path
                      d="M12.5 20V15C12.5 14.4477 12.9477 14 13.5 14H15.5C16.0523 14 16.5 14.4477 16.5 15V20"
                      stroke="currentColor"
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      stroke-width="1.5"
                    />
                    <path
                      d="M13.5 9H15.5"
                      stroke="currentColor"
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      stroke-width="1.5"
                    />
                  </svg>
                </div>
                <span
                  class="_navText_4aca72"
                >
                  Property Profile
                </span>
              </a>
            </li>
            <li
              class="_comingSoon_51efae"
            >
              <button
                class="_navItem_4aca72 _disabled_4aca72 _settingsItem_51efae"
                disabled=""
                type="button"
              >
                <div
                  class="_icon_053e64"
                >
                  <svg
                    color="currentColor"
                    fill="none"
                    height="20"
                    stroke-width="0"
                    viewBox="0 0 24 24"
                    width="20"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      d="M5.51906 10.2595L6.44721 10.7236C6.786 10.893 7 11.2393 7 11.618V16.382C7 16.7607 6.786 17.107 6.44721 17.2764L5.51906 17.7405C5.19808 17.901 4.81541 17.8772 4.53212 17.6569C3.68867 17.001 2 15.5005 2 14C2 12.4995 3.68867 10.999 4.53212 10.3431C4.81541 10.1228 5.19808 10.099 5.51906 10.2595Z"
                      stroke="currentColor"
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      stroke-width="1.5"
                    />
                    <path
                      d="M4 10.5V10C4 5.58172 7.58172 2 12 2V2C16.4183 2 20 5.58172 20 10V10.5"
                      stroke="currentColor"
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      stroke-width="1.5"
                    />
                    <path
                      d="M20 17.5V18C20 20.2091 18.2091 22 16 22H12"
                      stroke="currentColor"
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      stroke-width="1.5"
                    />
                    <path
                      d="M19.4679 10.3431C19.1846 10.1228 18.8019 10.099 18.4809 10.2595L17.5528 10.7236C17.214 10.893 17 11.2393 17 11.618V16.382C17 16.7607 17.214 17.107 17.5528 17.2764L18.4809 17.7405C18.8019 17.901 19.1846 17.8772 19.4679 17.6569C20.3113 17.001 22 15.5005 22 14C22 12.4995 20.3113 10.999 19.4679 10.3431Z"
                      stroke="currentColor"
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      stroke-width="1.5"
                    />
                  </svg>
                </div>
                <span
                  class="_navText_4aca72"
                >
                  Help
                </span>
                <div
                  class="_icon_053e64 _lockIcon_4aca72"
                >
                  <svg
                    color="currentColor"
                    fill="none"
                    height="20"
                    stroke-width="0"
                    viewBox="0 0 24 24"
                    width="20"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      d="M12 16.5V14.5"
                      stroke="currentColor"
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      stroke-width="1.5"
                    />
                    <path
                      d="M16.5 9V6.5C16.5 4.01472 14.4853 2 12 2C9.51472 2 7.5 4.01472 7.5 6.5V9"
                      stroke="currentColor"
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      stroke-width="1.5"
                    />
                    <path
                      d="M18 9H6.00019C4.89554 9 4.00008 9.89554 4.00018 11.0002L4.00102 20.0002C4.00112 21.1047 4.89652 22 6.00102 22H18C19.1046 22 20 21.1046 20 20V11C20 9.89543 19.1046 9 18 9Z"
                      stroke="currentColor"
                      stroke-linecap="round"
                      stroke-width="1.5"
                    />
                  </svg>
                </div>
              </button>
            </li>
          </ul>
        </nav>
        <div
          class="_recentChats_51efae"
        >
          <h2
            class="_recentChatsTitle_51efae"
          >
            RECENT CHATS
          </h2>
          <div
            class="_recentChatsWrapper_51efae"
          >
            <div
              class="_recentChatsContainer_51efae"
            >
              <ul
                class="space-y-1"
              >
                <li>
                  <a
                    class="_chatItem_51efae"
                    href="/chats/1"
                  >
                    <span
                      class="_chatTitle_51efae"
                    >
                      Test chat 1
                    </span>
                  </a>
                </li>
                <li>
                  <a
                    class="_chatItem_51efae"
                    href="/chats/2"
                  >
                    <span
                      class="_chatTitle_51efae"
                    >
                      Test chat 2
                    </span>
                  </a>
                </li>
              </ul>
              <div
                style="height: 20px;"
              />
            </div>
          </div>
        </div>
        <div
          class="_authContainer_51efae"
        >
          <p
            class="_authText_51efae"
          >
            Sign up or log in to access all the features
          </p>
          <div
            class="flex gap-4"
          >
            <div>
              <button
                class="_button_ee73f7 _primary_ee73f7 _base_ee73f7 _green-primary_ee73f7 _withText_ee73f7 flex-1"
              >
                <span
                  class="_text_ee73f7 _text-base_ee73f7"
                >
                  Sign up
                </span>
              </button>
            </div>
            <div>
              <button
                class="_button_ee73f7 _secondary_ee73f7 _base_ee73f7 _green-primary_ee73f7 _withText_ee73f7 flex-1"
              >
                <span
                  class="_text_ee73f7 _text-base_ee73f7"
                >
                  Log in
                </span>
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </aside>
</div>
`;
