import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import { vi } from 'vitest';
import { Sidebar } from '../Sidebar';

const mockPush = vi.fn();
const mockRouter = {
  push: mockPush,
  back: vi.fn(),
  forward: vi.fn(),
  refresh: vi.fn(),
  replace: vi.fn(),
  prefetch: vi.fn(),
};

vi.mock('next/navigation', () => ({
  useParams: vi.fn(() => ({ chatId: 'test-chat-id' })),
  useRouter: vi.fn(() => mockRouter),
  useSearchParams: vi.fn(() => ({
    get: vi.fn(() => null),
    has: vi.fn(() => false),
    getAll: vi.fn(() => []),
    keys: vi.fn(() => []),
    values: vi.fn(() => []),
    entries: vi.fn(() => []),
    forEach: vi.fn(),
    append: vi.fn(),
    delete: vi.fn(),
    set: vi.fn(),
    sort: vi.fn(),
    toString: vi.fn(() => ''),
  })),
  usePathname: vi.fn(() => '/'),
  redirect: vi.fn(),
  notFound: vi.fn(),
}));

const IntersectionObserverMock = vi.fn(() => ({
  disconnect: vi.fn(),
  observe: vi.fn(),
  takeRecords: vi.fn(),
  unobserve: vi.fn(),
}));

vi.stubGlobal('IntersectionObserver', IntersectionObserverMock);

vi.mock('@clerk/nextjs', () => ({
  SignInButton: ({ children }: { children: React.ReactNode }) => (
    <div>{children}</div>
  ),
  SignUpButton: ({ children }: { children: React.ReactNode }) => (
    <div>{children}</div>
  ),
  useAuth: () => ({
    getToken: () => Promise.resolve('mock-token'),
    isSignedIn: true,
  }),
}));

vi.mock('@/hooks/useChats', () => ({
  useChats: () => ({
    chats: [
      { id: 1, title: 'Test chat 1', status: 'active' },
      { id: 2, title: 'Test chat 2', status: 'active' },
    ],
    isLoading: false,
    hasMore: false,
    fetchChats: vi.fn(),
    loadMoreChats: vi.fn(),
  }),
}));

describe('Sidebar', () => {
  const mockOnCreateNew = vi.fn();

  beforeEach(() => {
    vi.clearAllMocks();
  });

  beforeAll(() => {
    vi.spyOn(console, 'error').mockImplementation(() => { });
  });

  afterAll(() => {
    vi.restoreAllMocks();
  });

  it('renders authenticated state correctly', () => {
    render(<Sidebar onCreateNew={mockOnCreateNew} />);

    expect(screen.getByText('RECENT CHATS')).toBeInTheDocument();
    expect(
      screen.getByRole('button', { name: /create new/i })
    ).toBeInTheDocument();
  });

  it('calls create new callback when button is clicked', () => {
    render(<Sidebar onCreateNew={mockOnCreateNew} />);

    const button = screen.getByRole('button', { name: /create new/i });
    fireEvent.click(button);

    expect(mockPush).toHaveBeenCalledWith('/');
  });
});
