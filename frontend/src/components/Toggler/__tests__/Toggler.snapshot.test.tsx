import React from "react";
import { render } from "@testing-library/react";
import { Toggler } from "../Toggler";
import { CheckboxState } from "../../Checkbox/Checkbox.types";
import { TogglerSize } from "../Toggler.types";

export default describe("Toggler Snapshots", () => {
  const defaultProps = {
    labelText: "Test Label",
    helperText: "Test Helper Text",
  };

  it("matches default toggler snapshot", () => {
    const { container } = render(<Toggler {...defaultProps} />);
    expect(container).toMatchSnapshot();
  });

  it.each(Object.values(TogglerSize))("matches %s size snapshot", (size) => {
    const { container } = render(<Toggler {...defaultProps} size={size} />);
    expect(container).toMatchSnapshot();
  });

  it.each(Object.values(CheckboxState))(
    "matches %s state snapshot",
    (state) => {
      const { container } = render(<Toggler {...defaultProps} state={state} />);
      expect(container).toMatchSnapshot();
    }
  );

  it("matches toggler with error text snapshot", () => {
    const { container } = render(
      <Toggler
        {...defaultProps}
        state={CheckboxState.ERROR}
        errorText="Test Error Text"
      />
    );
    expect(container).toMatchSnapshot();
  });

  it("matches checked toggler snapshot", () => {
    const { container } = render(<Toggler {...defaultProps} checked />);
    expect(container).toMatchSnapshot();
  });

  it("matches toggler without helper text snapshot", () => {
    const { container } = render(
      <Toggler labelText={defaultProps.labelText} />
    );
    expect(container).toMatchSnapshot();
  });
});
