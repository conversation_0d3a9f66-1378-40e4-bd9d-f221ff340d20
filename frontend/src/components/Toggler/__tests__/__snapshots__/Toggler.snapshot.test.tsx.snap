// Vitest Snapshot v1, https://vitest.dev/guide/snapshot.html

exports[`Toggler Snapshots > matches LG size snapshot 1`] = `
<div>
  <label
    class="_checkbox-field_cd53e0 _normal_cd53e0 _toggler_714d6c _lg_714d6c"
  >
    <div
      class="_content_cd53e0"
    >
      <div
        class="_toggle-control_714d6c"
      >
        <input
          class="_input_714d6c"
          type="checkbox"
        />
        <div
          class="_track_714d6c"
        />
        <div
          class="_thumb_714d6c"
        />
      </div>
      <div
        class="_label-helper_cd53e0"
      >
        <div
          class="_label_cd53e0 _label_714d6c"
        >
          Test Label
        </div>
        <div
          class="_caption_cd53e0 _helper_714d6c"
        >
          Test Helper Text
        </div>
      </div>
    </div>
  </label>
</div>
`;

exports[`Toggler Snapshots > matches SM size snapshot 1`] = `
<div>
  <label
    class="_checkbox-field_cd53e0 _normal_cd53e0 _toggler_714d6c _sm_714d6c"
  >
    <div
      class="_content_cd53e0"
    >
      <div
        class="_toggle-control_714d6c"
      >
        <input
          class="_input_714d6c"
          type="checkbox"
        />
        <div
          class="_track_714d6c"
        />
        <div
          class="_thumb_714d6c"
        />
      </div>
      <div
        class="_label-helper_cd53e0"
      >
        <div
          class="_label_cd53e0 _label_714d6c _labelSm_714d6c"
        >
          Test Label
        </div>
        <div
          class="_caption_cd53e0 _helper_714d6c _helperSm_714d6c"
        >
          Test Helper Text
        </div>
      </div>
    </div>
  </label>
</div>
`;

exports[`Toggler Snapshots > matches checked state snapshot 1`] = `
<div>
  <label
    class="_checkbox-field_cd53e0 _checked_cd53e0 _toggler_714d6c _default_714d6c"
  >
    <div
      class="_content_cd53e0"
    >
      <div
        class="_toggle-control_714d6c"
      >
        <input
          class="_input_714d6c"
          type="checkbox"
        />
        <div
          class="_track_714d6c"
        />
        <div
          class="_thumb_714d6c"
        />
      </div>
      <div
        class="_label-helper_cd53e0"
      >
        <div
          class="_label_cd53e0 _label_714d6c"
        >
          Test Label
        </div>
        <div
          class="_caption_cd53e0 _checked_cd53e0 _helper_714d6c"
        >
          Test Helper Text
        </div>
      </div>
    </div>
  </label>
</div>
`;

exports[`Toggler Snapshots > matches checked toggler snapshot 1`] = `
<div>
  <label
    class="_checkbox-field_cd53e0 _normal_cd53e0 _toggler_714d6c _default_714d6c _checked_714d6c"
  >
    <div
      class="_content_cd53e0"
    >
      <div
        class="_toggle-control_714d6c"
      >
        <input
          checked=""
          class="_input_714d6c"
          type="checkbox"
        />
        <div
          class="_track_714d6c"
        />
        <div
          class="_thumb_714d6c"
        />
      </div>
      <div
        class="_label-helper_cd53e0"
      >
        <div
          class="_label_cd53e0 _label_714d6c"
        >
          Test Label
        </div>
        <div
          class="_caption_cd53e0 _helper_714d6c"
        >
          Test Helper Text
        </div>
      </div>
    </div>
  </label>
</div>
`;

exports[`Toggler Snapshots > matches default size snapshot 1`] = `
<div>
  <label
    class="_checkbox-field_cd53e0 _normal_cd53e0 _toggler_714d6c _default_714d6c"
  >
    <div
      class="_content_cd53e0"
    >
      <div
        class="_toggle-control_714d6c"
      >
        <input
          class="_input_714d6c"
          type="checkbox"
        />
        <div
          class="_track_714d6c"
        />
        <div
          class="_thumb_714d6c"
        />
      </div>
      <div
        class="_label-helper_cd53e0"
      >
        <div
          class="_label_cd53e0 _label_714d6c"
        >
          Test Label
        </div>
        <div
          class="_caption_cd53e0 _helper_714d6c"
        >
          Test Helper Text
        </div>
      </div>
    </div>
  </label>
</div>
`;

exports[`Toggler Snapshots > matches default toggler snapshot 1`] = `
<div>
  <label
    class="_checkbox-field_cd53e0 _normal_cd53e0 _toggler_714d6c _default_714d6c"
  >
    <div
      class="_content_cd53e0"
    >
      <div
        class="_toggle-control_714d6c"
      >
        <input
          class="_input_714d6c"
          type="checkbox"
        />
        <div
          class="_track_714d6c"
        />
        <div
          class="_thumb_714d6c"
        />
      </div>
      <div
        class="_label-helper_cd53e0"
      >
        <div
          class="_label_cd53e0 _label_714d6c"
        >
          Test Label
        </div>
        <div
          class="_caption_cd53e0 _helper_714d6c"
        >
          Test Helper Text
        </div>
      </div>
    </div>
  </label>
</div>
`;

exports[`Toggler Snapshots > matches disabled state snapshot 1`] = `
<div>
  <label
    class="_checkbox-field_cd53e0 _disabled_cd53e0 _toggler_714d6c _default_714d6c _disabled_714d6c"
  >
    <div
      class="_content_cd53e0"
    >
      <div
        class="_toggle-control_714d6c"
      >
        <input
          class="_input_714d6c"
          disabled=""
          type="checkbox"
        />
        <div
          class="_track_714d6c"
        />
        <div
          class="_thumb_714d6c"
        />
      </div>
      <div
        class="_label-helper_cd53e0"
      >
        <div
          class="_label_cd53e0 _label_714d6c _labelDisabled_714d6c"
        >
          Test Label
        </div>
        <div
          class="_caption_cd53e0 _disabled_cd53e0 _helper_714d6c _helperDisabled_714d6c"
        >
          Test Helper Text
        </div>
      </div>
    </div>
  </label>
</div>
`;

exports[`Toggler Snapshots > matches error state snapshot 1`] = `
<div>
  <label
    class="_checkbox-field_cd53e0 _error_cd53e0 _toggler_714d6c _default_714d6c"
  >
    <div
      class="_content_cd53e0"
    >
      <div
        class="_toggle-control_714d6c"
      >
        <input
          class="_input_714d6c"
          type="checkbox"
        />
        <div
          class="_track_714d6c"
        />
        <div
          class="_thumb_714d6c"
        />
      </div>
      <div
        class="_label-helper_cd53e0"
      >
        <div
          class="_label_cd53e0 _label_714d6c"
        >
          Test Label
        </div>
        <div
          class="_caption_cd53e0 _error_cd53e0 _helper_714d6c"
        >
          Test Helper Text
        </div>
      </div>
    </div>
  </label>
</div>
`;

exports[`Toggler Snapshots > matches normal state snapshot 1`] = `
<div>
  <label
    class="_checkbox-field_cd53e0 _normal_cd53e0 _toggler_714d6c _default_714d6c"
  >
    <div
      class="_content_cd53e0"
    >
      <div
        class="_toggle-control_714d6c"
      >
        <input
          class="_input_714d6c"
          type="checkbox"
        />
        <div
          class="_track_714d6c"
        />
        <div
          class="_thumb_714d6c"
        />
      </div>
      <div
        class="_label-helper_cd53e0"
      >
        <div
          class="_label_cd53e0 _label_714d6c"
        >
          Test Label
        </div>
        <div
          class="_caption_cd53e0 _helper_714d6c"
        >
          Test Helper Text
        </div>
      </div>
    </div>
  </label>
</div>
`;

exports[`Toggler Snapshots > matches toggler with error text snapshot 1`] = `
<div>
  <label
    class="_checkbox-field_cd53e0 _error_cd53e0 _toggler_714d6c _default_714d6c"
  >
    <div
      class="_content_cd53e0"
    >
      <div
        class="_toggle-control_714d6c"
      >
        <input
          class="_input_714d6c"
          type="checkbox"
        />
        <div
          class="_track_714d6c"
        />
        <div
          class="_thumb_714d6c"
        />
      </div>
      <div
        class="_label-helper_cd53e0"
      >
        <div
          class="_label_cd53e0 _label_714d6c"
        >
          Test Label
        </div>
        <div
          class="_caption_cd53e0 _error_cd53e0 _helper_714d6c"
        >
          Test Helper Text
        </div>
      </div>
    </div>
    <div
      class="_error-container_cd53e0"
    >
      <svg
        class="_error-icon_cd53e0"
        color="currentColor"
        fill="none"
        height="16"
        viewBox="0 0 24 24"
        width="16"
        xmlns="http://www.w3.org/2000/svg"
      >
        <circle
          cx="12"
          cy="12"
          r="10"
          stroke="currentColor"
          stroke-width="1.5"
        />
        <path
          d="M11.992 15H12.001"
          stroke="currentColor"
          stroke-linecap="round"
          stroke-linejoin="round"
          stroke-width="2"
        />
        <path
          d="M12 12L12 8"
          stroke="currentColor"
          stroke-linecap="round"
          stroke-linejoin="round"
          stroke-width="1.5"
        />
      </svg>
      <span
        class="_error-text_cd53e0"
      >
        Test Error Text
      </span>
    </div>
  </label>
</div>
`;

exports[`Toggler Snapshots > matches toggler without helper text snapshot 1`] = `
<div>
  <label
    class="_checkbox-field_cd53e0 _normal_cd53e0 _toggler_714d6c _default_714d6c"
  >
    <div
      class="_content_cd53e0"
    >
      <div
        class="_toggle-control_714d6c"
      >
        <input
          class="_input_714d6c"
          type="checkbox"
        />
        <div
          class="_track_714d6c"
        />
        <div
          class="_thumb_714d6c"
        />
      </div>
      <div
        class="_label-helper_cd53e0"
      >
        <div
          class="_label_cd53e0 _label_714d6c"
        >
          Test Label
        </div>
      </div>
    </div>
  </label>
</div>
`;
