import React from 'react';
import { render, fireEvent } from '@testing-library/react';
import { vi } from 'vitest';
import { Toggler } from '../Toggler';
import { CheckboxState } from '../../Checkbox/Checkbox.types';
import { TogglerSize } from '../Toggler.types';
import styles from '../Toggler.module.scss';

export default describe('Toggler Component', () => {
  const defaultProps = {
    labelText: 'Test Label',
    helperText: 'Test Helper Text',
  };

  // Basic Rendering Tests
  it('renders without crashing', () => {
    const { container } = render(<Toggler {...defaultProps} />);
    expect(container).toBeInTheDocument();
  });

  it('renders label text correctly', () => {
    const { getByText } = render(<Toggler {...defaultProps} />);
    expect(getByText('Test Label')).toBeInTheDocument();
  });

  it('renders helper text when provided', () => {
    const { getByText } = render(<Toggler {...defaultProps} />);
    expect(getByText('Test Helper Text')).toBeInTheDocument();
  });

  // Size Variants Tests
  describe('Size Variants', () => {
    it.each(Object.values(TogglerSize))('renders %s size correctly', (size) => {
      const { container } = render(<Toggler {...defaultProps} size={size} />);
      expect(container.firstChild).toHaveClass(styles[size.toLowerCase()]);
    });
  });

  // State Variants Tests
  describe.skip('State Variants', () => {
    it.each(Object.values(CheckboxState))('renders %s state correctly', (state) => {
      const { container } = render(
        <Toggler {...defaultProps} state={state} checked={state === CheckboxState.CHECKED} />
      );
      const toggler = container.querySelector("input[type='checkbox']");
      expect(toggler).toBeInTheDocument();
      if (state !== CheckboxState.NORMAL) {
        const checkboxField = container.querySelector('label');
        expect(checkboxField).toHaveClass(`_${state.toLowerCase()}_531f07`);
      }
    });
  });

  // Interaction Tests
  describe('Interactions', () => {
    it('calls onChange when clicked', () => {
      const handleChange = vi.fn();
      const { container } = render(<Toggler {...defaultProps} onChange={handleChange} />);
      const toggler = container.querySelector("input[type='checkbox']");
      fireEvent.click(toggler!);
      expect(handleChange).toHaveBeenCalledWith(true);
    });

    it("doesn't call onChange when disabled", () => {
      const handleChange = vi.fn();
      const { container } = render(
        <Toggler {...defaultProps} state={CheckboxState.DISABLED} onChange={handleChange} />
      );
      const toggler = container.querySelector("input[type='checkbox']");
      fireEvent.click(toggler!);
      expect(handleChange).not.toHaveBeenCalled();
    });
  });

  // Error State Tests
  describe('Error State', () => {
    it('renders error message when in error state', () => {
      const errorText = 'Error Message';
      const { getByText } = render(
        <Toggler {...defaultProps} state={CheckboxState.ERROR} errorText={errorText} />
      );
      expect(getByText(errorText)).toBeInTheDocument();
    });

    it("doesn't render error message in normal state", () => {
      const errorText = 'Error Message';
      const { queryByText } = render(<Toggler {...defaultProps} errorText={errorText} />);
      expect(queryByText(errorText)).not.toBeInTheDocument();
    });
  });

  // Text Style Tests
  describe('Text Styles', () => {
    it('applies correct text styles for default size', () => {
      const { getByText } = render(<Toggler {...defaultProps} />);
      const label = getByText('Test Label');
      expect(label).toHaveClass(styles.label);
    });

    it('applies correct text styles for small size', () => {
      const { getByText } = render(<Toggler {...defaultProps} size={TogglerSize.SM} />);
      const label = getByText('Test Label');
      expect(label).toHaveClass(styles.labelSm);
    });
  });
});
