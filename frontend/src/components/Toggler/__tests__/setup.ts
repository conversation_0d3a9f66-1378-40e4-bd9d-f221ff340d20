import '@testing-library/jest-dom';
import React from 'react';

// Mock CSS modules
jest.mock('../Toggler.module.scss', () => ({
  'toggler': 'toggler',
  'toggle-control': 'toggle-control',
  'input': 'input',
  'track': 'track',
  'thumb': 'thumb',
  'label': 'label',
  'labelSm': 'labelSm',
  'labelDisabled': 'labelDisabled',
  'helper': 'helper',
  'helperSm': 'helperSm',
  'helperDisabled': 'helperDisabled',
  'checked': 'checked',
  'normal': 'normal',
  'error': 'error',
  'disabled': 'disabled',
  'sm': 'sm',
  'default': 'default',
  'lg': 'lg',
}));

// Mock HugeIcons
jest.mock('@hugeicons/react', () => new Proxy({}, {
  get: () => {
    return React.createElement.bind(null, 'div', {
      'data-testid': 'mock-icon',
      className: 'mock-icon'
    });
  }
}));

// Global test setup
beforeEach(() => {
  jest.clearAllMocks();
}); 