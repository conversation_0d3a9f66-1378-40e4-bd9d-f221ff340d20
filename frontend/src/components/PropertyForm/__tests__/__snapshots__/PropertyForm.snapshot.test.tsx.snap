// Vitest Snapshot v1, https://vitest.dev/guide/snapshot.html

exports[`PropertyForm Snapshots > renders PropertyForm for office property correctly 1`] = `
<div
  class="_container_d415ee"
>
  <form
    class="_form_d415ee"
  >
    <div
      class="_section_d415ee"
    >
      <div
        class="_subtitle_d415ee"
      >
        Relationship
      </div>
      <div
        class="_container_604974"
      >
        <button
          class="_trigger_604974"
          type="button"
        >
          <span>
            Choose an option
          </span>
          <div
            class="_arrow_604974"
            data-testid="hugicons-icon"
          >
            Mock Icon 
            20
          </div>
        </button>
      </div>
    </div>
    <div
      class="_section_d415ee"
    >
      <div
        class="_subtitle_d415ee"
      >
        Ownership type
      </div>
      <div
        class="_container_604974"
      >
        <button
          class="_trigger_604974"
          type="button"
        >
          <span>
            Freehold
          </span>
          <div
            class="_arrow_604974"
            data-testid="hugicons-icon"
          >
            Mock Icon 
            20
          </div>
        </button>
      </div>
    </div>
    <div
      class="_section_d415ee"
    >
      <div
        class="_subtitle_d415ee"
      >
        Property type
      </div>
      <div
        class="_container_604974"
      >
        <button
          class="_trigger_604974"
          type="button"
        >
          <span>
            Office
          </span>
          <div
            class="_arrow_604974"
            data-testid="hugicons-icon"
          >
            Mock Icon 
            20
          </div>
        </button>
      </div>
    </div>
    <div
      class="_section_d415ee"
    >
      <div
        class="_subtitle_d415ee"
      >
        Property sub type
      </div>
      <div
        class="_container_604974"
      >
        <button
          class="_trigger_604974"
          type="button"
        >
          <span>
            Detached
          </span>
          <div
            class="_arrow_604974"
            data-testid="hugicons-icon"
          >
            Mock Icon 
            20
          </div>
        </button>
      </div>
    </div>
    <div
      class="_section_d415ee"
    >
      <div
        class="_subtitle_d415ee"
      >
        Number of bedrooms
      </div>
      <div
        class="_input-field_9af53b _normal_9af53b _fullWidth_9af53b"
      >
        <div
          class="_input_9af53b _normal_9af53b _regular_9af53b _greenBorder_9af53b"
        >
          <div
            class="_content_9af53b"
          >
            <input
              class="_input-text_9af53b"
              placeholder="e.g. 2"
              type="number"
              value="0"
            />
          </div>
        </div>
      </div>
    </div>
    <div
      class="_section_d415ee"
    >
      <div
        class="_subtitle_d415ee"
      >
        Number of bathrooms
      </div>
      <div
        class="_input-field_9af53b _normal_9af53b _fullWidth_9af53b"
      >
        <div
          class="_input_9af53b _normal_9af53b _regular_9af53b _greenBorder_9af53b"
        >
          <div
            class="_content_9af53b"
          >
            <input
              class="_input-text_9af53b"
              placeholder="e.g. 2"
              type="number"
              value="2"
            />
          </div>
        </div>
      </div>
    </div>
    <div
      class="_section_d415ee"
    >
      <div
        class="_subtitle_d415ee"
      >
        Number of floors
      </div>
      <div
        class="_input-field_9af53b _normal_9af53b _fullWidth_9af53b"
      >
        <div
          class="_input_9af53b _normal_9af53b _regular_9af53b _greenBorder_9af53b"
        >
          <div
            class="_content_9af53b"
          >
            <input
              class="_input-text_9af53b"
              placeholder="e.g. 2"
              type="number"
              value="1"
            />
          </div>
        </div>
      </div>
    </div>
    <div
      class="_section_d415ee"
    >
      <div
        class="_subtitle_d415ee"
      >
        Floor property is on
      </div>
      <div
        class="_input-field_9af53b _normal_9af53b _fullWidth_9af53b"
      >
        <div
          class="_input_9af53b _normal_9af53b _regular_9af53b _greenBorder_9af53b"
        >
          <div
            class="_content_9af53b"
          >
            <input
              class="_input-text_9af53b"
              placeholder="e.g. 2"
              type="number"
              value=""
            />
          </div>
        </div>
      </div>
    </div>
    <div
      class="_section_d415ee"
    >
      <div
        class="_subtitle_d415ee"
      >
        Gross internal area (ft²)
      </div>
      <div
        class="_input-field_9af53b _normal_9af53b _fullWidth_9af53b"
      >
        <div
          class="_input_9af53b _normal_9af53b _regular_9af53b _greenBorder_9af53b"
        >
          <div
            class="_content_9af53b"
          >
            <input
              class="_input-text_9af53b"
              placeholder="e.g. 800"
              type="number"
              value="200"
            />
          </div>
        </div>
      </div>
    </div>
    <div
      class="_section_d415ee"
    >
      <div
        class="_subtitle_d415ee"
      >
        Balcony/Terrace
      </div>
      <div
        class="_yesNoContainer_d415ee"
      >
        <div
          class="_card_e7a3d1"
        >
          <label
            class="_radio-field_31042b _normal_31042b"
          >
            <div
              class="_content_31042b"
            >
              <input
                class="_radio_31042b"
                name="balconyTerrace"
                type="radio"
                value="Yes"
              />
              <div
                class="_label-helper_31042b"
              >
                <div
                  class="_label_31042b"
                >
                  Yes
                </div>
              </div>
            </div>
          </label>
        </div>
        <div
          class="_card_e7a3d1 _selected_e7a3d1"
        >
          <label
            class="_radio-field_31042b _normal_31042b"
          >
            <div
              class="_content_31042b"
            >
              <input
                checked=""
                class="_radio_31042b _checked_31042b"
                name="balconyTerrace"
                type="radio"
                value="No"
              />
              <div
                class="_label-helper_31042b"
              >
                <div
                  class="_label_31042b"
                >
                  No
                </div>
              </div>
            </div>
          </label>
        </div>
      </div>
    </div>
    <div
      class="_section_d415ee"
    >
      <div
        class="_subtitle_d415ee"
      >
        Garden
      </div>
      <div
        class="_yesNoContainer_d415ee"
      >
        <div
          class="_card_e7a3d1"
        >
          <label
            class="_radio-field_31042b _normal_31042b"
          >
            <div
              class="_content_31042b"
            >
              <input
                class="_radio_31042b"
                name="garden"
                type="radio"
                value="Yes"
              />
              <div
                class="_label-helper_31042b"
              >
                <div
                  class="_label_31042b"
                >
                  Yes
                </div>
              </div>
            </div>
          </label>
        </div>
        <div
          class="_card_e7a3d1 _selected_e7a3d1"
        >
          <label
            class="_radio-field_31042b _normal_31042b"
          >
            <div
              class="_content_31042b"
            >
              <input
                checked=""
                class="_radio_31042b _checked_31042b"
                name="garden"
                type="radio"
                value="No"
              />
              <div
                class="_label-helper_31042b"
              >
                <div
                  class="_label_31042b"
                >
                  No
                </div>
              </div>
            </div>
          </label>
        </div>
      </div>
    </div>
    <div
      class="_section_d415ee"
    >
      <div
        class="_subtitle_d415ee"
      >
        Swimming Pool
      </div>
      <div
        class="_yesNoContainer_d415ee"
      >
        <div
          class="_card_e7a3d1"
        >
          <label
            class="_radio-field_31042b _normal_31042b"
          >
            <div
              class="_content_31042b"
            >
              <input
                class="_radio_31042b"
                name="swimmingPool"
                type="radio"
                value="Yes"
              />
              <div
                class="_label-helper_31042b"
              >
                <div
                  class="_label_31042b"
                >
                  Yes
                </div>
              </div>
            </div>
          </label>
        </div>
        <div
          class="_card_e7a3d1 _selected_e7a3d1"
        >
          <label
            class="_radio-field_31042b _normal_31042b"
          >
            <div
              class="_content_31042b"
            >
              <input
                checked=""
                class="_radio_31042b _checked_31042b"
                name="swimmingPool"
                type="radio"
                value="No"
              />
              <div
                class="_label-helper_31042b"
              >
                <div
                  class="_label_31042b"
                >
                  No
                </div>
              </div>
            </div>
          </label>
        </div>
      </div>
    </div>
    <div
      class="_uploadContainer_d415ee"
    >
      <button
        class="_button_ee73f7 _primary_ee73f7 _l_ee73f7 _green-primary_ee73f7 _withText_ee73f7 _disabled_ee73f7 _uploadButton_d415ee"
        disabled=""
      >
        <span
          class="_text_ee73f7 _text-l_ee73f7"
        >
          Upload documents
        </span>
      </button>
    </div>
  </form>
</div>
`;

exports[`PropertyForm Snapshots > renders PropertyForm with custom className correctly 1`] = `
<div
  class="_container_d415ee custom-form-class"
>
  <form
    class="_form_d415ee"
  >
    <div
      class="_section_d415ee"
    >
      <div
        class="_subtitle_d415ee"
      >
        Relationship
      </div>
      <div
        class="_container_604974"
      >
        <button
          class="_trigger_604974"
          type="button"
        >
          <span>
            Choose an option
          </span>
          <div
            class="_arrow_604974"
            data-testid="hugicons-icon"
          >
            Mock Icon 
            20
          </div>
        </button>
      </div>
    </div>
    <div
      class="_section_d415ee"
    >
      <div
        class="_subtitle_d415ee"
      >
        Ownership type
      </div>
      <div
        class="_container_604974"
      >
        <button
          class="_trigger_604974"
          type="button"
        >
          <span>
            Choose an option
          </span>
          <div
            class="_arrow_604974"
            data-testid="hugicons-icon"
          >
            Mock Icon 
            20
          </div>
        </button>
      </div>
    </div>
    <div
      class="_section_d415ee"
    >
      <div
        class="_subtitle_d415ee"
      >
        Property type
      </div>
      <div
        class="_container_604974"
      >
        <button
          class="_trigger_604974"
          type="button"
        >
          <span>
            Choose an option
          </span>
          <div
            class="_arrow_604974"
            data-testid="hugicons-icon"
          >
            Mock Icon 
            20
          </div>
        </button>
      </div>
    </div>
    <div
      class="_section_d415ee"
    >
      <div
        class="_subtitle_d415ee"
      >
        Property sub type
      </div>
      <div
        class="_container_604974"
      >
        <button
          class="_trigger_604974"
          type="button"
        >
          <span>
            Choose an option
          </span>
          <div
            class="_arrow_604974"
            data-testid="hugicons-icon"
          >
            Mock Icon 
            20
          </div>
        </button>
      </div>
    </div>
    <div
      class="_section_d415ee"
    >
      <div
        class="_subtitle_d415ee"
      >
        Number of bedrooms
      </div>
      <div
        class="_input-field_9af53b _normal_9af53b _fullWidth_9af53b"
      >
        <div
          class="_input_9af53b _normal_9af53b _regular_9af53b _greenBorder_9af53b"
        >
          <div
            class="_content_9af53b"
          >
            <input
              class="_input-text_9af53b"
              placeholder="e.g. 2"
              type="number"
              value=""
            />
          </div>
        </div>
      </div>
    </div>
    <div
      class="_section_d415ee"
    >
      <div
        class="_subtitle_d415ee"
      >
        Number of bathrooms
      </div>
      <div
        class="_input-field_9af53b _normal_9af53b _fullWidth_9af53b"
      >
        <div
          class="_input_9af53b _normal_9af53b _regular_9af53b _greenBorder_9af53b"
        >
          <div
            class="_content_9af53b"
          >
            <input
              class="_input-text_9af53b"
              placeholder="e.g. 2"
              type="number"
              value=""
            />
          </div>
        </div>
      </div>
    </div>
    <div
      class="_section_d415ee"
    >
      <div
        class="_subtitle_d415ee"
      >
        Number of floors
      </div>
      <div
        class="_input-field_9af53b _normal_9af53b _fullWidth_9af53b"
      >
        <div
          class="_input_9af53b _normal_9af53b _regular_9af53b _greenBorder_9af53b"
        >
          <div
            class="_content_9af53b"
          >
            <input
              class="_input-text_9af53b"
              placeholder="e.g. 2"
              type="number"
              value=""
            />
          </div>
        </div>
      </div>
    </div>
    <div
      class="_section_d415ee"
    >
      <div
        class="_subtitle_d415ee"
      >
        Floor property is on
      </div>
      <div
        class="_input-field_9af53b _normal_9af53b _fullWidth_9af53b"
      >
        <div
          class="_input_9af53b _normal_9af53b _regular_9af53b _greenBorder_9af53b"
        >
          <div
            class="_content_9af53b"
          >
            <input
              class="_input-text_9af53b"
              placeholder="e.g. 2"
              type="number"
              value=""
            />
          </div>
        </div>
      </div>
    </div>
    <div
      class="_section_d415ee"
    >
      <div
        class="_subtitle_d415ee"
      >
        Gross internal area (ft²)
      </div>
      <div
        class="_input-field_9af53b _normal_9af53b _fullWidth_9af53b"
      >
        <div
          class="_input_9af53b _normal_9af53b _regular_9af53b _greenBorder_9af53b"
        >
          <div
            class="_content_9af53b"
          >
            <input
              class="_input-text_9af53b"
              placeholder="e.g. 800"
              type="number"
              value=""
            />
          </div>
        </div>
      </div>
    </div>
    <div
      class="_section_d415ee"
    >
      <div
        class="_subtitle_d415ee"
      >
        Balcony/Terrace
      </div>
      <div
        class="_yesNoContainer_d415ee"
      >
        <div
          class="_card_e7a3d1"
        >
          <label
            class="_radio-field_31042b _normal_31042b"
          >
            <div
              class="_content_31042b"
            >
              <input
                class="_radio_31042b"
                name="balconyTerrace"
                type="radio"
                value="Yes"
              />
              <div
                class="_label-helper_31042b"
              >
                <div
                  class="_label_31042b"
                >
                  Yes
                </div>
              </div>
            </div>
          </label>
        </div>
        <div
          class="_card_e7a3d1"
        >
          <label
            class="_radio-field_31042b _normal_31042b"
          >
            <div
              class="_content_31042b"
            >
              <input
                class="_radio_31042b"
                name="balconyTerrace"
                type="radio"
                value="No"
              />
              <div
                class="_label-helper_31042b"
              >
                <div
                  class="_label_31042b"
                >
                  No
                </div>
              </div>
            </div>
          </label>
        </div>
      </div>
    </div>
    <div
      class="_section_d415ee"
    >
      <div
        class="_subtitle_d415ee"
      >
        Garden
      </div>
      <div
        class="_yesNoContainer_d415ee"
      >
        <div
          class="_card_e7a3d1"
        >
          <label
            class="_radio-field_31042b _normal_31042b"
          >
            <div
              class="_content_31042b"
            >
              <input
                class="_radio_31042b"
                name="garden"
                type="radio"
                value="Yes"
              />
              <div
                class="_label-helper_31042b"
              >
                <div
                  class="_label_31042b"
                >
                  Yes
                </div>
              </div>
            </div>
          </label>
        </div>
        <div
          class="_card_e7a3d1"
        >
          <label
            class="_radio-field_31042b _normal_31042b"
          >
            <div
              class="_content_31042b"
            >
              <input
                class="_radio_31042b"
                name="garden"
                type="radio"
                value="No"
              />
              <div
                class="_label-helper_31042b"
              >
                <div
                  class="_label_31042b"
                >
                  No
                </div>
              </div>
            </div>
          </label>
        </div>
      </div>
    </div>
    <div
      class="_section_d415ee"
    >
      <div
        class="_subtitle_d415ee"
      >
        Swimming Pool
      </div>
      <div
        class="_yesNoContainer_d415ee"
      >
        <div
          class="_card_e7a3d1"
        >
          <label
            class="_radio-field_31042b _normal_31042b"
          >
            <div
              class="_content_31042b"
            >
              <input
                class="_radio_31042b"
                name="swimmingPool"
                type="radio"
                value="Yes"
              />
              <div
                class="_label-helper_31042b"
              >
                <div
                  class="_label_31042b"
                >
                  Yes
                </div>
              </div>
            </div>
          </label>
        </div>
        <div
          class="_card_e7a3d1"
        >
          <label
            class="_radio-field_31042b _normal_31042b"
          >
            <div
              class="_content_31042b"
            >
              <input
                class="_radio_31042b"
                name="swimmingPool"
                type="radio"
                value="No"
              />
              <div
                class="_label-helper_31042b"
              >
                <div
                  class="_label_31042b"
                >
                  No
                </div>
              </div>
            </div>
          </label>
        </div>
      </div>
    </div>
    <div
      class="_uploadContainer_d415ee"
    >
      <button
        class="_button_ee73f7 _primary_ee73f7 _l_ee73f7 _green-primary_ee73f7 _withText_ee73f7 _disabled_ee73f7 _uploadButton_d415ee"
        disabled=""
      >
        <span
          class="_text_ee73f7 _text-l_ee73f7"
        >
          Upload documents
        </span>
      </button>
    </div>
  </form>
</div>
`;

exports[`PropertyForm Snapshots > renders PropertyForm with initial data correctly 1`] = `
<div
  class="_container_d415ee"
>
  <form
    class="_form_d415ee"
  >
    <div
      class="_section_d415ee"
    >
      <div
        class="_subtitle_d415ee"
      >
        Relationship
      </div>
      <div
        class="_container_604974"
      >
        <button
          class="_trigger_604974"
          type="button"
        >
          <span>
            Choose an option
          </span>
          <div
            class="_arrow_604974"
            data-testid="hugicons-icon"
          >
            Mock Icon 
            20
          </div>
        </button>
      </div>
    </div>
    <div
      class="_section_d415ee"
    >
      <div
        class="_subtitle_d415ee"
      >
        Ownership type
      </div>
      <div
        class="_container_604974"
      >
        <button
          class="_trigger_604974"
          type="button"
        >
          <span>
            Freehold
          </span>
          <div
            class="_arrow_604974"
            data-testid="hugicons-icon"
          >
            Mock Icon 
            20
          </div>
        </button>
      </div>
    </div>
    <div
      class="_section_d415ee"
    >
      <div
        class="_subtitle_d415ee"
      >
        Property type
      </div>
      <div
        class="_container_604974"
      >
        <button
          class="_trigger_604974"
          type="button"
        >
          <span>
            House
          </span>
          <div
            class="_arrow_604974"
            data-testid="hugicons-icon"
          >
            Mock Icon 
            20
          </div>
        </button>
      </div>
    </div>
    <div
      class="_section_d415ee"
    >
      <div
        class="_subtitle_d415ee"
      >
        Property sub type
      </div>
      <div
        class="_container_604974"
      >
        <button
          class="_trigger_604974"
          type="button"
        >
          <span>
            Detached
          </span>
          <div
            class="_arrow_604974"
            data-testid="hugicons-icon"
          >
            Mock Icon 
            20
          </div>
        </button>
      </div>
    </div>
    <div
      class="_section_d415ee"
    >
      <div
        class="_subtitle_d415ee"
      >
        Number of bedrooms
      </div>
      <div
        class="_input-field_9af53b _normal_9af53b _fullWidth_9af53b"
      >
        <div
          class="_input_9af53b _normal_9af53b _regular_9af53b _greenBorder_9af53b"
        >
          <div
            class="_content_9af53b"
          >
            <input
              class="_input-text_9af53b"
              placeholder="e.g. 2"
              type="number"
              value="3"
            />
          </div>
        </div>
      </div>
    </div>
    <div
      class="_section_d415ee"
    >
      <div
        class="_subtitle_d415ee"
      >
        Number of bathrooms
      </div>
      <div
        class="_input-field_9af53b _normal_9af53b _fullWidth_9af53b"
      >
        <div
          class="_input_9af53b _normal_9af53b _regular_9af53b _greenBorder_9af53b"
        >
          <div
            class="_content_9af53b"
          >
            <input
              class="_input-text_9af53b"
              placeholder="e.g. 2"
              type="number"
              value="2"
            />
          </div>
        </div>
      </div>
    </div>
    <div
      class="_section_d415ee"
    >
      <div
        class="_subtitle_d415ee"
      >
        Number of floors
      </div>
      <div
        class="_input-field_9af53b _normal_9af53b _fullWidth_9af53b"
      >
        <div
          class="_input_9af53b _normal_9af53b _regular_9af53b _greenBorder_9af53b"
        >
          <div
            class="_content_9af53b"
          >
            <input
              class="_input-text_9af53b"
              placeholder="e.g. 2"
              type="number"
              value="2"
            />
          </div>
        </div>
      </div>
    </div>
    <div
      class="_section_d415ee"
    >
      <div
        class="_subtitle_d415ee"
      >
        Floor property is on
      </div>
      <div
        class="_input-field_9af53b _normal_9af53b _fullWidth_9af53b"
      >
        <div
          class="_input_9af53b _normal_9af53b _regular_9af53b _greenBorder_9af53b"
        >
          <div
            class="_content_9af53b"
          >
            <input
              class="_input-text_9af53b"
              placeholder="e.g. 2"
              type="number"
              value=""
            />
          </div>
        </div>
      </div>
    </div>
    <div
      class="_section_d415ee"
    >
      <div
        class="_subtitle_d415ee"
      >
        Gross internal area (ft²)
      </div>
      <div
        class="_input-field_9af53b _normal_9af53b _fullWidth_9af53b"
      >
        <div
          class="_input_9af53b _normal_9af53b _regular_9af53b _greenBorder_9af53b"
        >
          <div
            class="_content_9af53b"
          >
            <input
              class="_input-text_9af53b"
              placeholder="e.g. 800"
              type="number"
              value="120"
            />
          </div>
        </div>
      </div>
    </div>
    <div
      class="_section_d415ee"
    >
      <div
        class="_subtitle_d415ee"
      >
        Balcony/Terrace
      </div>
      <div
        class="_yesNoContainer_d415ee"
      >
        <div
          class="_card_e7a3d1 _selected_e7a3d1"
        >
          <label
            class="_radio-field_31042b _normal_31042b"
          >
            <div
              class="_content_31042b"
            >
              <input
                checked=""
                class="_radio_31042b _checked_31042b"
                name="balconyTerrace"
                type="radio"
                value="Yes"
              />
              <div
                class="_label-helper_31042b"
              >
                <div
                  class="_label_31042b"
                >
                  Yes
                </div>
              </div>
            </div>
          </label>
        </div>
        <div
          class="_card_e7a3d1"
        >
          <label
            class="_radio-field_31042b _normal_31042b"
          >
            <div
              class="_content_31042b"
            >
              <input
                class="_radio_31042b"
                name="balconyTerrace"
                type="radio"
                value="No"
              />
              <div
                class="_label-helper_31042b"
              >
                <div
                  class="_label_31042b"
                >
                  No
                </div>
              </div>
            </div>
          </label>
        </div>
      </div>
    </div>
    <div
      class="_section_d415ee"
    >
      <div
        class="_subtitle_d415ee"
      >
        Garden
      </div>
      <div
        class="_yesNoContainer_d415ee"
      >
        <div
          class="_card_e7a3d1"
        >
          <label
            class="_radio-field_31042b _normal_31042b"
          >
            <div
              class="_content_31042b"
            >
              <input
                class="_radio_31042b"
                name="garden"
                type="radio"
                value="Yes"
              />
              <div
                class="_label-helper_31042b"
              >
                <div
                  class="_label_31042b"
                >
                  Yes
                </div>
              </div>
            </div>
          </label>
        </div>
        <div
          class="_card_e7a3d1 _selected_e7a3d1"
        >
          <label
            class="_radio-field_31042b _normal_31042b"
          >
            <div
              class="_content_31042b"
            >
              <input
                checked=""
                class="_radio_31042b _checked_31042b"
                name="garden"
                type="radio"
                value="No"
              />
              <div
                class="_label-helper_31042b"
              >
                <div
                  class="_label_31042b"
                >
                  No
                </div>
              </div>
            </div>
          </label>
        </div>
      </div>
    </div>
    <div
      class="_section_d415ee"
    >
      <div
        class="_subtitle_d415ee"
      >
        Swimming Pool
      </div>
      <div
        class="_yesNoContainer_d415ee"
      >
        <div
          class="_card_e7a3d1 _selected_e7a3d1"
        >
          <label
            class="_radio-field_31042b _normal_31042b"
          >
            <div
              class="_content_31042b"
            >
              <input
                checked=""
                class="_radio_31042b _checked_31042b"
                name="swimmingPool"
                type="radio"
                value="Yes"
              />
              <div
                class="_label-helper_31042b"
              >
                <div
                  class="_label_31042b"
                >
                  Yes
                </div>
              </div>
            </div>
          </label>
        </div>
        <div
          class="_card_e7a3d1"
        >
          <label
            class="_radio-field_31042b _normal_31042b"
          >
            <div
              class="_content_31042b"
            >
              <input
                class="_radio_31042b"
                name="swimmingPool"
                type="radio"
                value="No"
              />
              <div
                class="_label-helper_31042b"
              >
                <div
                  class="_label_31042b"
                >
                  No
                </div>
              </div>
            </div>
          </label>
        </div>
      </div>
    </div>
    <div
      class="_uploadContainer_d415ee"
    >
      <button
        class="_button_ee73f7 _primary_ee73f7 _l_ee73f7 _green-primary_ee73f7 _withText_ee73f7 _uploadButton_d415ee"
      >
        <span
          class="_text_ee73f7 _text-l_ee73f7"
        >
          Upload documents
        </span>
      </button>
    </div>
  </form>
</div>
`;

exports[`PropertyForm Snapshots > renders PropertyForm with partial data correctly 1`] = `
<div
  class="_container_d415ee"
>
  <form
    class="_form_d415ee"
  >
    <div
      class="_section_d415ee"
    >
      <div
        class="_subtitle_d415ee"
      >
        Relationship
      </div>
      <div
        class="_container_604974"
      >
        <button
          class="_trigger_604974"
          type="button"
        >
          <span>
            Choose an option
          </span>
          <div
            class="_arrow_604974"
            data-testid="hugicons-icon"
          >
            Mock Icon 
            20
          </div>
        </button>
      </div>
    </div>
    <div
      class="_section_d415ee"
    >
      <div
        class="_subtitle_d415ee"
      >
        Ownership type
      </div>
      <div
        class="_container_604974"
      >
        <button
          class="_trigger_604974"
          type="button"
        >
          <span>
            Leasehold
          </span>
          <div
            class="_arrow_604974"
            data-testid="hugicons-icon"
          >
            Mock Icon 
            20
          </div>
        </button>
      </div>
    </div>
    <div
      class="_section_d415ee"
    >
      <div
        class="_subtitle_d415ee"
      >
        Property type
      </div>
      <div
        class="_container_604974"
      >
        <button
          class="_trigger_604974"
          type="button"
        >
          <span>
            Flat
          </span>
          <div
            class="_arrow_604974"
            data-testid="hugicons-icon"
          >
            Mock Icon 
            20
          </div>
        </button>
      </div>
    </div>
    <div
      class="_section_d415ee"
    >
      <div
        class="_subtitle_d415ee"
      >
        Property sub type
      </div>
      <div
        class="_container_604974"
      >
        <button
          class="_trigger_604974"
          type="button"
        >
          <span>
            Choose an option
          </span>
          <div
            class="_arrow_604974"
            data-testid="hugicons-icon"
          >
            Mock Icon 
            20
          </div>
        </button>
      </div>
    </div>
    <div
      class="_section_d415ee"
    >
      <div
        class="_subtitle_d415ee"
      >
        Number of bedrooms
      </div>
      <div
        class="_input-field_9af53b _normal_9af53b _fullWidth_9af53b"
      >
        <div
          class="_input_9af53b _normal_9af53b _regular_9af53b _greenBorder_9af53b"
        >
          <div
            class="_content_9af53b"
          >
            <input
              class="_input-text_9af53b"
              placeholder="e.g. 2"
              type="number"
              value="2"
            />
          </div>
        </div>
      </div>
    </div>
    <div
      class="_section_d415ee"
    >
      <div
        class="_subtitle_d415ee"
      >
        Number of bathrooms
      </div>
      <div
        class="_input-field_9af53b _normal_9af53b _fullWidth_9af53b"
      >
        <div
          class="_input_9af53b _normal_9af53b _regular_9af53b _greenBorder_9af53b"
        >
          <div
            class="_content_9af53b"
          >
            <input
              class="_input-text_9af53b"
              placeholder="e.g. 2"
              type="number"
              value=""
            />
          </div>
        </div>
      </div>
    </div>
    <div
      class="_section_d415ee"
    >
      <div
        class="_subtitle_d415ee"
      >
        Number of floors
      </div>
      <div
        class="_input-field_9af53b _normal_9af53b _fullWidth_9af53b"
      >
        <div
          class="_input_9af53b _normal_9af53b _regular_9af53b _greenBorder_9af53b"
        >
          <div
            class="_content_9af53b"
          >
            <input
              class="_input-text_9af53b"
              placeholder="e.g. 2"
              type="number"
              value=""
            />
          </div>
        </div>
      </div>
    </div>
    <div
      class="_section_d415ee"
    >
      <div
        class="_subtitle_d415ee"
      >
        Floor property is on
      </div>
      <div
        class="_input-field_9af53b _normal_9af53b _fullWidth_9af53b"
      >
        <div
          class="_input_9af53b _normal_9af53b _regular_9af53b _greenBorder_9af53b"
        >
          <div
            class="_content_9af53b"
          >
            <input
              class="_input-text_9af53b"
              placeholder="e.g. 2"
              type="number"
              value=""
            />
          </div>
        </div>
      </div>
    </div>
    <div
      class="_section_d415ee"
    >
      <div
        class="_subtitle_d415ee"
      >
        Gross internal area (ft²)
      </div>
      <div
        class="_input-field_9af53b _normal_9af53b _fullWidth_9af53b"
      >
        <div
          class="_input_9af53b _normal_9af53b _regular_9af53b _greenBorder_9af53b"
        >
          <div
            class="_content_9af53b"
          >
            <input
              class="_input-text_9af53b"
              placeholder="e.g. 800"
              type="number"
              value=""
            />
          </div>
        </div>
      </div>
    </div>
    <div
      class="_section_d415ee"
    >
      <div
        class="_subtitle_d415ee"
      >
        Balcony/Terrace
      </div>
      <div
        class="_yesNoContainer_d415ee"
      >
        <div
          class="_card_e7a3d1 _selected_e7a3d1"
        >
          <label
            class="_radio-field_31042b _normal_31042b"
          >
            <div
              class="_content_31042b"
            >
              <input
                checked=""
                class="_radio_31042b _checked_31042b"
                name="balconyTerrace"
                type="radio"
                value="Yes"
              />
              <div
                class="_label-helper_31042b"
              >
                <div
                  class="_label_31042b"
                >
                  Yes
                </div>
              </div>
            </div>
          </label>
        </div>
        <div
          class="_card_e7a3d1"
        >
          <label
            class="_radio-field_31042b _normal_31042b"
          >
            <div
              class="_content_31042b"
            >
              <input
                class="_radio_31042b"
                name="balconyTerrace"
                type="radio"
                value="No"
              />
              <div
                class="_label-helper_31042b"
              >
                <div
                  class="_label_31042b"
                >
                  No
                </div>
              </div>
            </div>
          </label>
        </div>
      </div>
    </div>
    <div
      class="_section_d415ee"
    >
      <div
        class="_subtitle_d415ee"
      >
        Garden
      </div>
      <div
        class="_yesNoContainer_d415ee"
      >
        <div
          class="_card_e7a3d1"
        >
          <label
            class="_radio-field_31042b _normal_31042b"
          >
            <div
              class="_content_31042b"
            >
              <input
                class="_radio_31042b"
                name="garden"
                type="radio"
                value="Yes"
              />
              <div
                class="_label-helper_31042b"
              >
                <div
                  class="_label_31042b"
                >
                  Yes
                </div>
              </div>
            </div>
          </label>
        </div>
        <div
          class="_card_e7a3d1"
        >
          <label
            class="_radio-field_31042b _normal_31042b"
          >
            <div
              class="_content_31042b"
            >
              <input
                class="_radio_31042b"
                name="garden"
                type="radio"
                value="No"
              />
              <div
                class="_label-helper_31042b"
              >
                <div
                  class="_label_31042b"
                >
                  No
                </div>
              </div>
            </div>
          </label>
        </div>
      </div>
    </div>
    <div
      class="_section_d415ee"
    >
      <div
        class="_subtitle_d415ee"
      >
        Swimming Pool
      </div>
      <div
        class="_yesNoContainer_d415ee"
      >
        <div
          class="_card_e7a3d1"
        >
          <label
            class="_radio-field_31042b _normal_31042b"
          >
            <div
              class="_content_31042b"
            >
              <input
                class="_radio_31042b"
                name="swimmingPool"
                type="radio"
                value="Yes"
              />
              <div
                class="_label-helper_31042b"
              >
                <div
                  class="_label_31042b"
                >
                  Yes
                </div>
              </div>
            </div>
          </label>
        </div>
        <div
          class="_card_e7a3d1"
        >
          <label
            class="_radio-field_31042b _normal_31042b"
          >
            <div
              class="_content_31042b"
            >
              <input
                class="_radio_31042b"
                name="swimmingPool"
                type="radio"
                value="No"
              />
              <div
                class="_label-helper_31042b"
              >
                <div
                  class="_label_31042b"
                >
                  No
                </div>
              </div>
            </div>
          </label>
        </div>
      </div>
    </div>
    <div
      class="_uploadContainer_d415ee"
    >
      <button
        class="_button_ee73f7 _primary_ee73f7 _l_ee73f7 _green-primary_ee73f7 _withText_ee73f7 _disabled_ee73f7 _uploadButton_d415ee"
        disabled=""
      >
        <span
          class="_text_ee73f7 _text-l_ee73f7"
        >
          Upload documents
        </span>
      </button>
    </div>
  </form>
</div>
`;

exports[`PropertyForm Snapshots > renders default PropertyForm correctly 1`] = `
<div
  class="_container_d415ee"
>
  <form
    class="_form_d415ee"
  >
    <div
      class="_section_d415ee"
    >
      <div
        class="_subtitle_d415ee"
      >
        Relationship
      </div>
      <div
        class="_container_604974"
      >
        <button
          class="_trigger_604974"
          type="button"
        >
          <span>
            Choose an option
          </span>
          <div
            class="_arrow_604974"
            data-testid="hugicons-icon"
          >
            Mock Icon 
            20
          </div>
        </button>
      </div>
    </div>
    <div
      class="_section_d415ee"
    >
      <div
        class="_subtitle_d415ee"
      >
        Ownership type
      </div>
      <div
        class="_container_604974"
      >
        <button
          class="_trigger_604974"
          type="button"
        >
          <span>
            Choose an option
          </span>
          <div
            class="_arrow_604974"
            data-testid="hugicons-icon"
          >
            Mock Icon 
            20
          </div>
        </button>
      </div>
    </div>
    <div
      class="_section_d415ee"
    >
      <div
        class="_subtitle_d415ee"
      >
        Property type
      </div>
      <div
        class="_container_604974"
      >
        <button
          class="_trigger_604974"
          type="button"
        >
          <span>
            Choose an option
          </span>
          <div
            class="_arrow_604974"
            data-testid="hugicons-icon"
          >
            Mock Icon 
            20
          </div>
        </button>
      </div>
    </div>
    <div
      class="_section_d415ee"
    >
      <div
        class="_subtitle_d415ee"
      >
        Property sub type
      </div>
      <div
        class="_container_604974"
      >
        <button
          class="_trigger_604974"
          type="button"
        >
          <span>
            Choose an option
          </span>
          <div
            class="_arrow_604974"
            data-testid="hugicons-icon"
          >
            Mock Icon 
            20
          </div>
        </button>
      </div>
    </div>
    <div
      class="_section_d415ee"
    >
      <div
        class="_subtitle_d415ee"
      >
        Number of bedrooms
      </div>
      <div
        class="_input-field_9af53b _normal_9af53b _fullWidth_9af53b"
      >
        <div
          class="_input_9af53b _normal_9af53b _regular_9af53b _greenBorder_9af53b"
        >
          <div
            class="_content_9af53b"
          >
            <input
              class="_input-text_9af53b"
              placeholder="e.g. 2"
              type="number"
              value=""
            />
          </div>
        </div>
      </div>
    </div>
    <div
      class="_section_d415ee"
    >
      <div
        class="_subtitle_d415ee"
      >
        Number of bathrooms
      </div>
      <div
        class="_input-field_9af53b _normal_9af53b _fullWidth_9af53b"
      >
        <div
          class="_input_9af53b _normal_9af53b _regular_9af53b _greenBorder_9af53b"
        >
          <div
            class="_content_9af53b"
          >
            <input
              class="_input-text_9af53b"
              placeholder="e.g. 2"
              type="number"
              value=""
            />
          </div>
        </div>
      </div>
    </div>
    <div
      class="_section_d415ee"
    >
      <div
        class="_subtitle_d415ee"
      >
        Number of floors
      </div>
      <div
        class="_input-field_9af53b _normal_9af53b _fullWidth_9af53b"
      >
        <div
          class="_input_9af53b _normal_9af53b _regular_9af53b _greenBorder_9af53b"
        >
          <div
            class="_content_9af53b"
          >
            <input
              class="_input-text_9af53b"
              placeholder="e.g. 2"
              type="number"
              value=""
            />
          </div>
        </div>
      </div>
    </div>
    <div
      class="_section_d415ee"
    >
      <div
        class="_subtitle_d415ee"
      >
        Floor property is on
      </div>
      <div
        class="_input-field_9af53b _normal_9af53b _fullWidth_9af53b"
      >
        <div
          class="_input_9af53b _normal_9af53b _regular_9af53b _greenBorder_9af53b"
        >
          <div
            class="_content_9af53b"
          >
            <input
              class="_input-text_9af53b"
              placeholder="e.g. 2"
              type="number"
              value=""
            />
          </div>
        </div>
      </div>
    </div>
    <div
      class="_section_d415ee"
    >
      <div
        class="_subtitle_d415ee"
      >
        Gross internal area (ft²)
      </div>
      <div
        class="_input-field_9af53b _normal_9af53b _fullWidth_9af53b"
      >
        <div
          class="_input_9af53b _normal_9af53b _regular_9af53b _greenBorder_9af53b"
        >
          <div
            class="_content_9af53b"
          >
            <input
              class="_input-text_9af53b"
              placeholder="e.g. 800"
              type="number"
              value=""
            />
          </div>
        </div>
      </div>
    </div>
    <div
      class="_section_d415ee"
    >
      <div
        class="_subtitle_d415ee"
      >
        Balcony/Terrace
      </div>
      <div
        class="_yesNoContainer_d415ee"
      >
        <div
          class="_card_e7a3d1"
        >
          <label
            class="_radio-field_31042b _normal_31042b"
          >
            <div
              class="_content_31042b"
            >
              <input
                class="_radio_31042b"
                name="balconyTerrace"
                type="radio"
                value="Yes"
              />
              <div
                class="_label-helper_31042b"
              >
                <div
                  class="_label_31042b"
                >
                  Yes
                </div>
              </div>
            </div>
          </label>
        </div>
        <div
          class="_card_e7a3d1"
        >
          <label
            class="_radio-field_31042b _normal_31042b"
          >
            <div
              class="_content_31042b"
            >
              <input
                class="_radio_31042b"
                name="balconyTerrace"
                type="radio"
                value="No"
              />
              <div
                class="_label-helper_31042b"
              >
                <div
                  class="_label_31042b"
                >
                  No
                </div>
              </div>
            </div>
          </label>
        </div>
      </div>
    </div>
    <div
      class="_section_d415ee"
    >
      <div
        class="_subtitle_d415ee"
      >
        Garden
      </div>
      <div
        class="_yesNoContainer_d415ee"
      >
        <div
          class="_card_e7a3d1"
        >
          <label
            class="_radio-field_31042b _normal_31042b"
          >
            <div
              class="_content_31042b"
            >
              <input
                class="_radio_31042b"
                name="garden"
                type="radio"
                value="Yes"
              />
              <div
                class="_label-helper_31042b"
              >
                <div
                  class="_label_31042b"
                >
                  Yes
                </div>
              </div>
            </div>
          </label>
        </div>
        <div
          class="_card_e7a3d1"
        >
          <label
            class="_radio-field_31042b _normal_31042b"
          >
            <div
              class="_content_31042b"
            >
              <input
                class="_radio_31042b"
                name="garden"
                type="radio"
                value="No"
              />
              <div
                class="_label-helper_31042b"
              >
                <div
                  class="_label_31042b"
                >
                  No
                </div>
              </div>
            </div>
          </label>
        </div>
      </div>
    </div>
    <div
      class="_section_d415ee"
    >
      <div
        class="_subtitle_d415ee"
      >
        Swimming Pool
      </div>
      <div
        class="_yesNoContainer_d415ee"
      >
        <div
          class="_card_e7a3d1"
        >
          <label
            class="_radio-field_31042b _normal_31042b"
          >
            <div
              class="_content_31042b"
            >
              <input
                class="_radio_31042b"
                name="swimmingPool"
                type="radio"
                value="Yes"
              />
              <div
                class="_label-helper_31042b"
              >
                <div
                  class="_label_31042b"
                >
                  Yes
                </div>
              </div>
            </div>
          </label>
        </div>
        <div
          class="_card_e7a3d1"
        >
          <label
            class="_radio-field_31042b _normal_31042b"
          >
            <div
              class="_content_31042b"
            >
              <input
                class="_radio_31042b"
                name="swimmingPool"
                type="radio"
                value="No"
              />
              <div
                class="_label-helper_31042b"
              >
                <div
                  class="_label_31042b"
                >
                  No
                </div>
              </div>
            </div>
          </label>
        </div>
      </div>
    </div>
    <div
      class="_uploadContainer_d415ee"
    >
      <button
        class="_button_ee73f7 _primary_ee73f7 _l_ee73f7 _green-primary_ee73f7 _withText_ee73f7 _disabled_ee73f7 _uploadButton_d415ee"
        disabled=""
      >
        <span
          class="_text_ee73f7 _text-l_ee73f7"
        >
          Upload documents
        </span>
      </button>
    </div>
  </form>
</div>
`;
