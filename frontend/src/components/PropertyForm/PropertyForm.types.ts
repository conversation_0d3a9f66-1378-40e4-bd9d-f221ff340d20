export enum OwnershipType {
  FREEHOLD = 'Freehold',
  LEASEHOLD = 'Leasehold',
  SHARE_OF_FREEHOLD = 'Share of freehold',
  NOT_APPLICABLE = 'Not applicable',
}

export enum UserRole {
  OWNER_AND_OCCUPIER = 'Owner and occupier',
  LANDLORD = 'Landlord',
  TENANT = 'Tenant',
}

export enum PropertyType {
  HOUSE = 'House',
  FLAT = 'Flat',
  OFFICE = 'Office',
  RETAIL = 'Retail',
}

export enum PropertySubType {
  TERRACED = 'Terraced',
  SEMI_DETACHED = 'Semi-Detached',
  DETACHED = 'Detached',
  NOT_APPLICABLE = 'Not applicable',
}

export enum YesNoOption {
  YES = 'Yes',
  NO = 'No',
}

export interface PropertyFormData {
  userRole: UserRole | '';
  ownershipType: OwnershipType | '';
  propertyType: PropertyType | '';
  propertySubType: PropertySubType | '';
  numberOfBedrooms: string;
  numberOfBathrooms: string;
  numberOfFloors: string;
  floorPropertyIsOn: string;
  grossInternalArea: string;
  balconyTerrace: YesNoOption | '';
  garden: YesNoOption | '';
  swimmingPool: YesNoOption | '';
  documents?: File[];
}

export interface PropertyFormProps {
  initialData?: Partial<PropertyFormData>;
  onSubmit?: (data: PropertyFormData) => void;
  value?: PropertyFormData;
  onChange?: (data: PropertyFormData) => void;
  className?: string;
  showUploadButton?: boolean;
}
