import {
  PropertyFormData,
  OwnershipType,
  PropertyType,
  PropertySubType,
  UserRole,
} from './PropertyForm.types';

export interface DropdownConfig {
  title: string;
  field: keyof PropertyFormData;
  options: string[];
  placeholder: string;
  key: 'userRole' | 'ownershipType' | 'propertyType' | 'propertySubType';
}

export interface InputConfig {
  title: string;
  field: keyof PropertyFormData;
  placeholder: string;
  inputType?: 'text' | 'number' | 'email' | 'password' | 'tel' | 'url';
}

export interface YesNoConfig {
  title: string;
  field: keyof PropertyFormData;
}

export const DROPDOWN_CONFIGS: DropdownConfig[] = [
  {
    title: 'Relationship',
    field: 'userRole',
    options: [
      UserRole.OWNER_AND_OCCUPIER,
      UserRole.LANDLORD,
      UserRole.TENANT,
    ],
    placeholder: 'Choose an option',
    key: 'userRole',
  },
  {
    title: 'Ownership type',
    field: 'ownershipType',
    options: [
      OwnershipType.FREEHOLD,
      OwnershipType.LEASEHOLD,
      OwnershipType.SHARE_OF_FREEHOLD,
      OwnershipType.NOT_APPLICABLE,
    ],
    placeholder: 'Choose an option',
    key: 'ownershipType',
  },
  {
    title: 'Property type',
    field: 'propertyType',
    options: Object.values(PropertyType),
    placeholder: 'Choose an option',
    key: 'propertyType',
  },
  {
    title: 'Property sub type',
    field: 'propertySubType',
    options: [
      PropertySubType.TERRACED,
      PropertySubType.SEMI_DETACHED,
      PropertySubType.DETACHED,
      PropertySubType.NOT_APPLICABLE,
    ],
    placeholder: 'Choose an option',
    key: 'propertySubType',
  },
];

export const INPUT_CONFIGS: InputConfig[] = [
  {
    title: 'Number of bedrooms',
    field: 'numberOfBedrooms',
    placeholder: 'e.g. 2',
    inputType: 'number',
  },
  {
    title: 'Number of bathrooms',
    field: 'numberOfBathrooms',
    placeholder: 'e.g. 2',
    inputType: 'number',
  },
  {
    title: 'Number of floors',
    field: 'numberOfFloors',
    placeholder: 'e.g. 2',
    inputType: 'number',
  },
  {
    title: 'Floor property is on',
    field: 'floorPropertyIsOn',
    placeholder: 'e.g. 2',
    inputType: 'number',
  },
  {
    title: 'Gross internal area (ft²)',
    field: 'grossInternalArea',
    placeholder: 'e.g. 800',
    inputType: 'number',
  },
];

export const YES_NO_CONFIGS: YesNoConfig[] = [
  {
    title: 'Balcony/Terrace',
    field: 'balconyTerrace',
  },
  {
    title: 'Garden',
    field: 'garden',
  },
  {
    title: 'Swimming Pool',
    field: 'swimmingPool',
  },
];
