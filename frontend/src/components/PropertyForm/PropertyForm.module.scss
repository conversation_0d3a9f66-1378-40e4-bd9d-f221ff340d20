.container {
  display: flex;
  flex-direction: column;
  width: 100%;
  max-width: 640px;
  margin: 0;
  padding: 0;
}

.form {
  display: flex;
  flex-direction: column;
}

.section {
  margin-bottom: 16px;
}

.subtitle {
  font-family: 'Quasimoda', Helvetica;
  font-size: 16px;
  font-weight: bold;
  line-height: 150%;
  margin-bottom: 4px;
  color: var(--colors-gray-900);
}

.formGroup {
  display: flex;
  flex-direction: column;
}

.yesNoContainer {
  display: flex;
  gap: 12px;
  align-items: center;
}

.uploadContainer {
  display: flex;
  justify-content: flex-start;
  margin-top: 8px;
}

.uploadButton {
  padding: 12px 24px;
  font-weight: 500;
  border-radius: 100px;
}

.container :global([class*='Input-module']) {
  margin-bottom: 0;
}

.container :global([class*='input-field']) {
  margin-bottom: 0;
}

.container :global(input.input) {
  min-height: 48px;
  border: 1px solid var(--colors-gray-200);
  transition: border-color 0.2s;

  &:hover {
    border-color: var(--colors-gray-300);
  }

  &:focus,
  &:active {
    border-color: var(--colors-green-600);
  }
}

@media (max-width: 768px) {
  .container {
    padding: 0;
  }

  .subtitle {
    font-size: 15px;
  }

  .yesNoContainer {
    flex-direction: column;
    gap: 6px;
  }

  .uploadButton {
    width: 100%;
    justify-content: center;
  }
}

@media (max-width: 480px) {
  .yesNoContainer {
    gap: 8px;
  }
}
