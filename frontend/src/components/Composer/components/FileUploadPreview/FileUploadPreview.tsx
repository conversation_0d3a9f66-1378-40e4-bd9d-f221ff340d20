import React, { useEffect, useState } from 'react';
import classNames from 'classnames';
import { Spinner } from '../../../Spinner/Spinner';
import styles from './FileUploadPreview.module.scss';
import { RemoveFileButton } from '@/components/RemoveFileButton';
import { RetryButton } from '@/components/RetryButton';
import { Attachment } from '@/types/messages';

interface FileUploadPreviewProps {
  file: Attachment;
  onRemove?: () => void;
  onRetry?: () => void;
  onDelete?: () => void;
}

export const FileUploadPreview: React.FC<FileUploadPreviewProps> = ({
  file,
  onRemove,
  onRetry,
}) => {
  const filename = file.originalFileName || file.file?.name;
  const [objectUrl, setObjectUrl] = useState<string | null>(null);
  const isImage = file.file?.type.startsWith('image/');

  useEffect(() => {
    if (file.file && isImage) {
      const url = URL.createObjectURL(file.file);
      setObjectUrl(url);

      return () => {
        URL.revokeObjectURL(url);
      };
    }
  }, [file.file, isImage]);

  return (
    <div
      className={classNames(styles.preview, {
        [styles.success]: file.status === 'success',
        [styles.error]: file.status === 'error',
      })}
    >
      {file.status !== 'success' && (
        <div className={styles.loaderContainer}>
          {file.status === 'uploading' && <Spinner color="#D28E28" />}
          {file.status === 'error' && (
            <RetryButton onRetry={onRetry} className={styles.retryButton} />
          )}
        </div>
      )}
      {file.status === 'success' && (
        <img
          src={objectUrl || ''}
          alt={filename || ''}
          className={styles.previewImage}
        />
      )}
      <RemoveFileButton onRemove={onRemove} className={styles.removeButton} />
    </div>
  );
};
