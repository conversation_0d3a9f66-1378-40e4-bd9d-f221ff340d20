import React from 'react';
import styles from './GlobalDropZone.module.scss';

interface GlobalDropZoneProps {
  onFileUpload: (file: File, retryIndex?: number) => Promise<void>;
}

export const GlobalDropZone: React.FC<GlobalDropZoneProps> = ({ onFileUpload }) => {
  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    
    const files = Array.from(e.dataTransfer.files);
    if (files.length > 0) {
      onFileUpload(files[0]);
    }
  };

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
  };

  return (
    <div 
      className={styles.dropZone}
      onDrop={handleDrop}
      onDragOver={handleDragOver}
    />
  );
};