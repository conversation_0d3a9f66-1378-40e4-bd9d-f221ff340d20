.Maincontainer {
  width: 100%;
  position: relative;
}

.container {
  display: flex;
  justify-content: center;
  width: 100%;
  position: relative;
}

.composer {
  --composer-bg: var(--colors-gray-100);
  --composer-text: var(--colors-neutral-900);
  --composer-placeholder: var(--colors-neutral-400);
  --composer-border-radius: 16px;
  --composer-min-height: 86px;
  --composer-max-height: 220px;
  --composer-padding: 16px;
  --composer-controls-height: 52px;

  display: flex;
  flex-direction: column;
  background: var(--colors-gray-100);
  border-radius: 16px;
  min-height: 86px;
  overflow: hidden;
  position: relative;
  z-index: 1;

  &.default {
    width: 960px;
  }

  &.playground {
    width: 343px;
  }

  &.disabled {
    pointer-events: none;

    .input {
      cursor: not-allowed;
      background-color: transparent;
      opacity: 0.4;
    }
  }
}

.input {
  white-space: pre-wrap;
  width: calc(100% - 10px);
  min-height: 34px;
  max-height: 180px;
  overflow-y: auto;
  min-width: 24px;
  padding: 12px 7px 0 16px;
  margin-right: 10px;
  border: none;
  background: transparent;
  font-family: 'Quasimoda', Helvetica;
  font-weight: 400;
  font-size: 16px;
  line-height: 150%;
  color: #111928;
  resize: none;
  outline: none;
  word-break: break-word;
  text-align: left;
  text-underline-position: from-font;
  text-decoration-skip-ink: none;
  cursor: text;

  br {
    display: none;
  }

  &.hasAttachments {
    padding-top: 9px;
  }

  &.empty[contenteditable=true]:after {
    content: attr(data-placeholder);
    color: var(--colors-gray-600);
    cursor: text;
    pointer-events: none;
    position: relative;
  }

  &.empty[contenteditable=false]:after {
    content: attr(data-placeholder);
    color: var(--colors-neutral-400);
    cursor: text;
    pointer-events: none;
    position: relative;
  }

  &::-webkit-scrollbar {
    width: 3px;
    margin: 16px 0;
  }

  &::-webkit-scrollbar-track {
    background: transparent;
    margin: 16px 0;
  }

  &::-webkit-scrollbar-thumb {
    background-color: var(--colors-neutral-200);
    border-radius: 1.5px;
    min-height: 40px;
    max-height: 40px;
    margin: 16px 0;
    cursor: pointer;
  }

  &.disabled {
    cursor: not-allowed;
    pointer-events: none;
    opacity: 0.4;
    background-color: transparent;
  }
}

.controls {
  display: flex;
  justify-content: flex-start;
  align-items: center;
  padding: 7px 7px 7px 0;
  margin-top: auto;
  height: 52px;
  box-sizing: border-box;

  .leftControls button,
  .rightControls button:not(.sendButton) {
    color: #374151;
  }

  &.disabled {
    pointer-events: none;

    .leftControls button,
    .rightControls button:not(.sendButton) {
      opacity: 0.4;
    }
  }
}

.leftControls {
  display: flex;
  gap: 10px;
  padding-left: 12px;
}

.hiddenInput {
  display: none;

  &:disabled {
    pointer-events: none;
  }
}

.rightControls {
  display: flex;
  gap: 4px;
  align-items: center;
  margin-left: auto;
  padding-left: 8px;
  padding-right: 2px;

  button {
    width: 34px !important;
    height: 34px !important;
    padding: 0 !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
  }

  button.recording {
    background-color: transparent;
    border: 1px solid #7D2339;
    position: relative;
    width: 34px;
    height: 34px;

    svg,
    .loading-spinner {
      display: none;
    }

    &::after {
      content: '';
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      width: 12px;
      height: 12px;
      background-color: #7D2339;
      border-radius: 2px;
      animation: pulse 1.5s infinite;
    }
  }
}

@keyframes pulse {
  0% {
    transform: translate(-50%, -50%) scale(1);
  }

  50% {
    transform: translate(-50%, -50%) scale(0.8);
  }

  100% {
    transform: translate(-50%, -50%) scale(1);
  }
}

button.sendButton {
  color: var(--colors-white);
  opacity: 1;
  background-color: #1B6E5A;

  &:hover:not(:disabled) {
    background-color: #155847;
  }

  &[disabled] {
    opacity: 0.4;
    cursor: not-allowed;
    pointer-events: none;
  }

  &.loading {
    background-color: #4B5563;
    position: relative;

    svg,
    .loading-spinner {
      display: none;
    }

    &::after {
      content: '';
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      width: 12px;
      height: 12px;
      background-color: var(--colors-white);
      border-radius: 2px;
      animation: pulse 1.5s infinite;
    }
  }

  &:focus:not(:disabled) {
    outline: 3px solid var(--colors-green-200);
    outline-offset: 0;
  }
}

.attachments {
  display: flex;
  gap: 8px;
  flex-wrap: nowrap;
  align-items: center;
  padding: 8px 16px 0 6px;
  overflow-x: auto;
  width: 100%;
  order: -1;

  &::-webkit-scrollbar {
    display: none;
  }

  -ms-overflow-style: none;
  scrollbar-width: none;
}

.lockedOverlay {
  position: absolute;
  bottom: 41px;
  left: 0;
  right: 0;
  height: 83px;
  border-radius: var(--border-radius-rounded-2xl);
  font-size: 14px;
  font-weight: 500;
  text-align: center;
  background-color: #FFDAC7;
  font-family: 'Quasimoda';
  color: #A34E22;
  display: flex;
  padding: var(--spacing-2);
  gap: var(--spacing-1);
  justify-content: center;
  margin: 0 4px;

  div {
    margin-top: var(--spacing-px);
  }
}