export type AllowedFileTypes =
  | 'pdf'
  | 'doc'
  | 'docx'
  | 'xls'
  | 'xlsx'
  | 'txt'
  | 'csv'
  | 'jpg'
  | 'jpeg'
  | 'png'
  | 'gif'
  | 'bmp'
  | 'svg'
  | 'mp4'
  | 'mov'
  | 'avi'
  | 'mkv'
  | 'wmv'
  | 'mp3'
  | 'wav'
  | 'aac'
  | 'ogg'
  | 'flac';

export interface FileUploadResponse {
  fileId: string;
  cdnUrl: string;
}

export enum ComposerSize {
  PLAYGROUND = 'playground',
  DEFAULT = 'default',
}

export enum ControlState {
  ACTIVE = 'active',
  INACTIVE = 'inactive',
}

export interface ComposerProps {
  className?: string;
  placeholder?: string;
  size?: ComposerSize;
  defaultValue?: string;
  isRecording?: boolean;
  loading?: boolean;
  controlState?: ControlState;
  attachButtonActive?: boolean;
  linkButtonActive?: boolean;
  micButtonActive?: boolean;
  sendButtonActive?: boolean;
  disabled?: boolean;
  anchorRef?: React.RefObject<HTMLDivElement>;
  isLocked?: boolean;
}

export interface FileUploadState {
  id: string;
  file: File;
  status: 'uploading' | 'success' | 'error';
  progress: number;
  fileId?: string;
  cdnUrl?: string;
  metadata?: {
    fileExtension?: string;
    sizeInKiloBytes?: number;
    createdAt?: string;
    [key: string]: any; // eslint-disable-line @typescript-eslint/no-explicit-any
  };
}
