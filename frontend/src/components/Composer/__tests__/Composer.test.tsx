import React from 'react';
import { fireEvent, render, screen } from '@testing-library/react';
import { vi } from 'vitest';
import { Composer } from '../Composer';
import { useChats } from '@/hooks/useChats';
import { useMessages } from '@/hooks/useMessages';

// Mock useAuth and useUser from @clerk/nextjs
vi.mock('@/app/store', () => ({
  useAppSelector: vi.fn().mockImplementation((selector) =>
    selector({
      prompts: {
        currentId: null,
        prompts: {},
      },
      user: {
        hasCompletedOnboarding: true,
      },
    })
  ),
  useAppDispatch: vi.fn().mockReturnValue(vi.fn()),
}));

vi.mock('@clerk/nextjs', () => ({
  useAuth: vi.fn().mockReturnValue({
    getToken: vi.fn().mockResolvedValue('mock-token'),
  }),
  useUser: () => ({
    user: {
      firstName: 'Test User',
    },
  }),
}));

vi.mock('@hugeicons/react', () => ({
  Album02Icon: () => <div data-testid="album-icon">Mock Album Icon</div>,
  Attachment01Icon: () => <div data-testid="attachment-icon">Mock Attachment Icon</div>,
  Mic02Icon: () => <div data-testid="mic-icon">Mock Mic Icon</div>,
  TelegramIcon: () => <div data-testid="telegram-icon">Mock Send Icon</div>,
}));

vi.mock('next/navigation', () => ({
  useParams: vi.fn(() => ({ chatId: 'test-chat-id' })),
  useRouter: vi.fn(() => ({
    push: vi.fn(),
    replace: vi.fn(),
    refresh: vi.fn(),
    back: vi.fn(),
    forward: vi.fn(),
  })),
  useSearchParams: vi.fn(() => ({
    get: vi.fn(() => null),
    has: vi.fn(() => false),
    getAll: vi.fn(() => []),
    keys: vi.fn(() => []),
    values: vi.fn(() => []),
    entries: vi.fn(() => []),
    forEach: vi.fn(),
    append: vi.fn(),
    delete: vi.fn(),
    set: vi.fn(),
    sort: vi.fn(),
    toString: vi.fn(() => ''),
  })),
  usePathname: vi.fn(() => '/'),
  redirect: vi.fn(),
  notFound: vi.fn(),
}));

vi.mock('@/hooks/useChats', () => ({
  useChats: vi.fn(),
}));

vi.mock('@/hooks/useMessages', () => ({
  useMessages: vi.fn(),
}));

vi.mock('@/hooks/useUploads', () => ({
  useUploads: vi.fn(),
}));

describe('Composer Component', () => {
  const mockSendMessage = vi.fn();

  beforeEach(() => {
    vi.mocked(useChats).mockReturnValue({
      sendMessage: mockSendMessage,
      setActiveChatId: vi.fn(),
      addChat: vi.fn(),
      isLoading: false,
      activeChatId: 123,
    });

    vi.mocked(useMessages).mockReturnValue({});
  });

  afterEach(() => {
    vi.clearAllMocks();
  });

  it('renders input with placeholder', () => {
    render(<Composer />);
    const input = screen.getByRole('textbox');
    expect(input).toHaveAttribute('data-placeholder', 'Message Alfie');
  });

  it('handles text input', () => {
    render(<Composer />);
    const input = screen.getByRole('textbox');

    fireEvent.input(input, { target: { textContent: 'Hello world' } });

    expect(input.textContent).toBe('Hello world');
  });

  it("doesn't send empty messages", () => {
    render(<Composer />);
    const sendButton = screen.getByTestId('telegram-icon').parentElement;

    fireEvent.click(sendButton!);

    expect(mockSendMessage).not.toHaveBeenCalled();
  });

  it.skip('disables controls when disabled prop is true', () => {
    render(<Composer disabled />);

    const input = screen.getByRole('textbox');
    const composer = input.parentElement;

    expect(input).toHaveAttribute('contenteditable', 'false');
    expect(composer).toHaveClass('_disabled_cd04e5');
  });
});
