// Vitest Snapshot v1, https://vitest.dev/guide/snapshot.html

exports[`Composer Snapshots > matches composer with content snapshot 1`] = `
<div>
  <div
    class="_Maincontainer_4699b1"
  >
    <div
      class="_container_4699b1"
    >
      <div
        class="_composer_4699b1 _default_4699b1"
      >
        <div
          aria-label="Message Alfie"
          aria-multiline="true"
          class="_input_4699b1"
          contenteditable="true"
          data-placeholder="Message Alfie"
          role="textbox"
        >
          Test content
        </div>
        <div
          class="_controls_4699b1"
        >
          <div
            class="_leftControls_4699b1"
          >
            <button
              class="_button_ee73f7 _tertiary_ee73f7 _xs_ee73f7 _green-primary_ee73f7 _iconOnly_ee73f7"
            >
              <svg
                class=""
                color="currentColor"
                fill="none"
                height="24"
                viewBox="0 0 24 24"
                width="24"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  d="M5.82338 12L4.27922 10.4558C2.57359 8.75022 2.57359 5.98485 4.27922 4.27922C5.98485 2.57359 8.75022 2.57359 10.4558 4.27922L19.7208 13.5442C21.4264 15.2498 21.4264 18.0152 19.7208 19.7208C18.0152 21.4264 15.2498 21.4264 13.5442 19.7208L10.0698 16.2464C9.00379 15.1804 9.00379 13.4521 10.0698 12.386C11.1358 11.32 12.8642 11.32 13.9302 12.386L15.8604 14.3162"
                  stroke="currentColor"
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="1.5"
                />
              </svg>
            </button>
            <input
              accept=".pdf,.docx,.doc,.txt,.jpg,.jpeg,.png,.heic,.heif,.xls,.xlsx,.csv"
              class="_hiddenInput_4699b1"
              data-testid="file-input"
              multiple=""
              type="file"
            />
          </div>
          <div
            class="_rightControls_4699b1"
          >
            <button
              class="_button_ee73f7 _tertiary_ee73f7 _xs_ee73f7 _green-primary_ee73f7 _iconOnly_ee73f7"
            >
              <svg
                class=""
                color="currentColor"
                fill="none"
                height="24"
                viewBox="0 0 24 24"
                width="24"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  d="M17 7V11C17 13.7614 14.7614 16 12 16C9.23858 16 7 13.7614 7 11V7C7 4.23858 9.23858 2 12 2C14.7614 2 17 4.23858 17 7Z"
                  stroke="currentColor"
                  stroke-width="1.5"
                />
                <path
                  d="M20 11C20 15.4183 16.4183 19 12 19M12 19C7.58172 19 4 15.4183 4 11M12 19V22M12 22H15M12 22H9"
                  stroke="currentColor"
                  stroke-linecap="round"
                  stroke-width="1.5"
                />
              </svg>
            </button>
            <button
              class="_button_ee73f7 _primary_ee73f7 _xs_ee73f7 _green-primary_ee73f7 _iconOnly_ee73f7 _sendButton_4699b1"
            >
              <svg
                class=""
                color="currentColor"
                fill="none"
                height="24"
                viewBox="0 0 24 24"
                width="24"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  d="M11.9854 15.4083L15.2268 19.0936C16.4277 20.4589 17.0282 21.1416 17.6567 20.9754C18.2852 20.8092 18.5008 19.9108 18.9318 18.1138L21.3229 8.1459C21.9868 5.37832 22.3187 3.99454 21.5808 3.312C20.843 2.62947 19.564 3.13725 17.0061 4.15282L5.13876 8.86449C3.09293 9.67674 2.07001 10.0829 2.00507 10.7808C1.99842 10.8522 1.99831 10.9241 2.00474 10.9955C2.06754 11.6937 3.08921 12.1033 5.13255 12.9223C6.05838 13.2934 6.5213 13.479 6.8532 13.8344C6.89052 13.8743 6.9264 13.9157 6.96078 13.9584C7.26658 14.3384 7.39709 14.8371 7.65808 15.8344L8.14653 17.701C8.4005 18.6715 8.52749 19.1568 8.86008 19.223C9.19267 19.2891 9.48225 18.8867 10.0614 18.0819L11.9854 15.4083ZM11.9854 15.4083L11.6676 15.0771C11.3059 14.7001 11.1251 14.5117 11.1251 14.2775C11.1251 14.0433 11.3059 13.8548 11.6676 13.4778L15.2406 9.75409"
                  stroke="currentColor"
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="1.5"
                />
              </svg>
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
`;

exports[`Composer Snapshots > matches empty composer snapshot 1`] = `
<div>
  <div
    class="_Maincontainer_4699b1"
  >
    <div
      class="_container_4699b1"
    >
      <div
        class="_composer_4699b1 _default_4699b1"
      >
        <div
          aria-label="Message Alfie"
          aria-multiline="true"
          class="_input_4699b1 _empty_4699b1"
          contenteditable="true"
          data-placeholder="Message Alfie"
          role="textbox"
        />
        <div
          class="_controls_4699b1"
        >
          <div
            class="_leftControls_4699b1"
          >
            <button
              class="_button_ee73f7 _tertiary_ee73f7 _xs_ee73f7 _green-primary_ee73f7 _iconOnly_ee73f7"
            >
              <svg
                class=""
                color="currentColor"
                fill="none"
                height="24"
                viewBox="0 0 24 24"
                width="24"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  d="M5.82338 12L4.27922 10.4558C2.57359 8.75022 2.57359 5.98485 4.27922 4.27922C5.98485 2.57359 8.75022 2.57359 10.4558 4.27922L19.7208 13.5442C21.4264 15.2498 21.4264 18.0152 19.7208 19.7208C18.0152 21.4264 15.2498 21.4264 13.5442 19.7208L10.0698 16.2464C9.00379 15.1804 9.00379 13.4521 10.0698 12.386C11.1358 11.32 12.8642 11.32 13.9302 12.386L15.8604 14.3162"
                  stroke="currentColor"
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="1.5"
                />
              </svg>
            </button>
            <input
              accept=".pdf,.docx,.doc,.txt,.jpg,.jpeg,.png,.heic,.heif,.xls,.xlsx,.csv"
              class="_hiddenInput_4699b1"
              data-testid="file-input"
              multiple=""
              type="file"
            />
          </div>
          <div
            class="_rightControls_4699b1"
          >
            <button
              class="_button_ee73f7 _tertiary_ee73f7 _xs_ee73f7 _green-primary_ee73f7 _iconOnly_ee73f7"
            >
              <svg
                class=""
                color="currentColor"
                fill="none"
                height="24"
                viewBox="0 0 24 24"
                width="24"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  d="M17 7V11C17 13.7614 14.7614 16 12 16C9.23858 16 7 13.7614 7 11V7C7 4.23858 9.23858 2 12 2C14.7614 2 17 4.23858 17 7Z"
                  stroke="currentColor"
                  stroke-width="1.5"
                />
                <path
                  d="M20 11C20 15.4183 16.4183 19 12 19M12 19C7.58172 19 4 15.4183 4 11M12 19V22M12 22H15M12 22H9"
                  stroke="currentColor"
                  stroke-linecap="round"
                  stroke-width="1.5"
                />
              </svg>
            </button>
            <button
              class="_button_ee73f7 _primary_ee73f7 _xs_ee73f7 _green-primary_ee73f7 _iconOnly_ee73f7 _disabled_ee73f7 _sendButton_4699b1"
              disabled=""
            >
              <svg
                class=""
                color="currentColor"
                fill="none"
                height="24"
                viewBox="0 0 24 24"
                width="24"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  d="M11.9854 15.4083L15.2268 19.0936C16.4277 20.4589 17.0282 21.1416 17.6567 20.9754C18.2852 20.8092 18.5008 19.9108 18.9318 18.1138L21.3229 8.1459C21.9868 5.37832 22.3187 3.99454 21.5808 3.312C20.843 2.62947 19.564 3.13725 17.0061 4.15282L5.13876 8.86449C3.09293 9.67674 2.07001 10.0829 2.00507 10.7808C1.99842 10.8522 1.99831 10.9241 2.00474 10.9955C2.06754 11.6937 3.08921 12.1033 5.13255 12.9223C6.05838 13.2934 6.5213 13.479 6.8532 13.8344C6.89052 13.8743 6.9264 13.9157 6.96078 13.9584C7.26658 14.3384 7.39709 14.8371 7.65808 15.8344L8.14653 17.701C8.4005 18.6715 8.52749 19.1568 8.86008 19.223C9.19267 19.2891 9.48225 18.8867 10.0614 18.0819L11.9854 15.4083ZM11.9854 15.4083L11.6676 15.0771C11.3059 14.7001 11.1251 14.5117 11.1251 14.2775C11.1251 14.0433 11.3059 13.8548 11.6676 13.4778L15.2406 9.75409"
                  stroke="currentColor"
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="1.5"
                />
              </svg>
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
`;
