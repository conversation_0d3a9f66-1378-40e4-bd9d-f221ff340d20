import React from 'react';
import { render } from '@testing-library/react';
import { vi } from 'vitest';
import { Composer } from '../Composer';
import { ComposerSize, ControlState } from '../Composer.types';

// Mock useAuth and useUser from @clerk/nextjs
vi.mock('@/app/store', () => ({
  useAppSelector: vi.fn().mockImplementation((selector) =>
    selector({
      prompts: {
        currentId: null,
        prompts: {},
      },
      user: {
        hasCompletedOnboarding: true,
      },
    })
  ),
  useAppDispatch: vi.fn().mockReturnValue(vi.fn()),
}));

vi.mock('@clerk/nextjs', () => ({
  useAuth: vi.fn().mockReturnValue({
    getToken: vi.fn().mockResolvedValue('mock-token'),
  }),
  useUser: () => ({
    user: {
      firstName: 'Test User',
    },
  }),
}));

vi.mock('next/navigation', () => ({
  useParams: vi.fn(() => ({ chatId: 'test-chat-id' })),
  useRouter: vi.fn(() => ({
    push: vi.fn(),
    replace: vi.fn(),
    refresh: vi.fn(),
    back: vi.fn(),
    forward: vi.fn(),
  })),
  useSearchParams: vi.fn(() => ({
    get: vi.fn(() => null),
    has: vi.fn(() => false),
    getAll: vi.fn(() => []),
    keys: vi.fn(() => []),
    values: vi.fn(() => []),
    entries: vi.fn(() => []),
    forEach: vi.fn(),
    append: vi.fn(),
    delete: vi.fn(),
    set: vi.fn(),
    sort: vi.fn(),
    toString: vi.fn(() => ''),
  })),
  usePathname: vi.fn(() => '/'),
  redirect: vi.fn(),
  notFound: vi.fn(),
}));

vi.mock('@/stores/useChat/useChat', () => ({
  useChat: () => ({
    sendMessage: vi.fn(),
    setActiveChatId: vi.fn(),
    isLoading: false,
  }),
}));

const defaultProps = {
  placeholder: 'Message Alfie',
  size: ComposerSize.DEFAULT,
  defaultValue: '',
  isRecording: false,
  loading: false,
  controlState: ControlState.ACTIVE,
  disabled: false,
};

describe('Composer Snapshots', () => {
  it('matches empty composer snapshot', () => {
    const { container } = render(<Composer {...defaultProps} />);
    expect(container).toMatchSnapshot();
  });

  it('matches composer with content snapshot', () => {
    const { container } = render(
      <Composer {...defaultProps} defaultValue="Test content" />
    );
    expect(container).toMatchSnapshot();
  });
});
