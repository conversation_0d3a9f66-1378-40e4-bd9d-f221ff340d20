import React from 'react';
import { render } from '@testing-library/react';
import { FirstPromptPage } from '../FirstPromptPage';
import { vi } from 'vitest';

// Mock useAuth and useUser from @clerk/nextjs
vi.mock('@/app/store', () => ({
  useAppSelector: vi.fn().mockImplementation((selector) =>
    selector({
      prompts: {
        currentId: null,
        prompts: {},
      },
      user: {
        hasCompletedOnboarding: true,
      },
    })
  ),
  useAppDispatch: vi.fn().mockReturnValue(vi.fn()),
}));

vi.mock('@clerk/nextjs', () => ({
  useAuth: vi.fn().mockReturnValue({
    getToken: vi.fn().mockResolvedValue('mock-token'),
  }),
  useUser: () => ({
    user: {
      firstName: 'John',
    },
  }),
}));

vi.mock('ezheaders', () => ({
  default: {
    get: vi.fn(),
    set: vi.fn(),
    delete: vi.fn(),
  },
}));

vi.mock('next/navigation', () => ({
  useParams: vi.fn(() => ({ chatId: '123' })),
  useRouter: vi.fn(() => ({
    push: vi.fn(),
    replace: vi.fn(),
    refresh: vi.fn(),
    back: vi.fn(),
    forward: vi.fn(),
  })),
  useSearchParams: vi.fn(() => ({
    get: vi.fn(() => null),
    has: vi.fn(() => false),
    getAll: vi.fn(() => []),
    keys: vi.fn(() => []),
    values: vi.fn(() => []),
    entries: vi.fn(() => []),
    forEach: vi.fn(),
    append: vi.fn(),
    delete: vi.fn(),
    set: vi.fn(),
    sort: vi.fn(),
    toString: vi.fn(() => ''),
  })),
  usePathname: vi.fn(() => '/'),
  redirect: vi.fn(),
  notFound: vi.fn(),
}));

vi.mock('@/hooks/useChats', () => ({
  useChats: () => ({
    sendMessage: vi.fn(),
    setActiveChatId: vi.fn(),
    addChat: vi.fn(),
    isLoading: false,
  }),
}));

vi.mock('@/hooks/useMessages', () => ({
  useMessages: () => ({
    messages: [],
    addMessage: vi.fn(),
  }),
}));

vi.mock('@/hooks/useUser', () => ({
  useUser: vi.fn(),
}));

describe('FirstPromptPage', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('applies custom className when provided', () => {
    const customClass = 'custom-class';
    const { container } = render(<FirstPromptPage className={customClass} />);
    expect(container.firstChild).toHaveClass(customClass);
  });

  it('renders without crashing when query parameter is present', () => {
    expect(() => render(<FirstPromptPage />)).not.toThrow();
  });
});
