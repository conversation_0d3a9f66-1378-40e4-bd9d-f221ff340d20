.container {
  font-family: 'Quasimoda', sans-serif;
  display: flex;
  flex-direction: column;
  justify-content: center;
  width: 100%;
  overflow: hidden;
  flex: 1;
  min-height: 0;
  max-width: 811px;
  margin: 0 auto;
}

.header {
  display: flex;
  align-items: center;
  padding: 16px 0 8px 0;
  width: 100%;

  .address {
    max-width: 960px;
    width: 100%;
    font-weight: 400;
    display: flex;
    color: var(--colors-gray-500);
    font-size: 16px;
    line-height: 24px;
    gap: 4px;
    margin-bottom: 0;
  }
}

.content {
  padding: var(--spacing-8);
}

.title {
  font-size: 20px;
  line-height: 30px;
  font-weight: 400;
  color: #000000;
}

.subtitle {
  font-size: 24px;
  line-height: 36px;
  font-weight: 700;
  color: #000000;
  letter-spacing: 0%;
  padding: 9px 0 20px 0;
}

.addressIcon {
  min-width: 24px;
}
.quickActions {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
  row-gap: 8px;
  column-gap: 17px;
  max-width: 546px;
  width: 100%;
  padding: 20px 0 0 0;
}

.composerWrapper {
  max-width: 960px;
  width: 100%;
  margin: 0 auto 0 0;
  display: flex;
  justify-content: center;
}
