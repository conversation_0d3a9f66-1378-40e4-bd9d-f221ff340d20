import React, { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { useAuth, useClerk, useUser } from '@clerk/nextjs';
import { Modal } from '../Modal';
import { PersonalizationCard } from '../PersonalizationCard';
import { PersonalizationCardField } from '../PersonalizationCard/PersonalizationCard.types';
import { UserInfoModalProps } from './UserInfoModal.types';
import { useWidgets } from '@/hooks/useWidgets';
import { HugeiconsIcon } from '../HugeiconsIcon';
import { CheckmarkCircle02Icon } from '@hugeicons-pro/core-stroke-standard';
import { IconSvgObject } from '@hugeicons/react';
import { Button } from '../Button';
import { ButtonColor, ButtonSize, ButtonState, ButtonType } from '../Button/Button.types';
import { ConfirmDialog } from '../ConfirmDialog';
import {
  AccessInstructionField,
  AccessInstructionFieldRef,
  AddressField,
  AddressFieldRef,
  EmailField,
  EmailFieldRef,
  FirstNameField,
  FirstNameFieldRef,
  LastNameField,
  LastNameFieldRef,
  PhoneField,
  PhoneFieldRef,
} from './components';
import styles from './UserInfoModal.module.scss';
import useJobSubmissionValidation from '@/hooks/useJobSubmissionValidation';

export const UserInfoModal: React.FC<UserInfoModalProps> = ({
  open,
  onClose,
  readOnly = false,
  inline = false,
}) => {
  const { user, isLoaded } = useUser();
  const { getToken, isSignedIn } = useAuth();
  const { addressValue, accessInstruction, fetchProperties } = useWidgets();

  const [isEditing, setIsEditing] = useState(false);
  const [editingFields, setEditingFields] = useState<Set<string>>(new Set());
  const [showCloseConfirmDialog, setShowCloseConfirmDialog] = useState(false);

  // Store guest user data for conversion
  const [guestFirstName, setGuestFirstName] = useState('');
  const [guestLastName, setGuestLastName] = useState('');

  // Track if guest was converted (for development until Clerk integration is complete)
  const [isGuestConverted, setIsGuestConverted] = useState(false);

  const clerk = useClerk();
  const firstNameRef = useRef<FirstNameFieldRef>(null);
  const lastNameRef = useRef<LastNameFieldRef>(null);
  const emailRef = useRef<EmailFieldRef>(null);
  const phoneRef = useRef<PhoneFieldRef>(null);
  const addressRef = useRef<AddressFieldRef>(null);
  const accessInstructionRef = useRef<AccessInstructionFieldRef>(null);

  const handleEditStart = useCallback((fieldId: string) => {
    setEditingFields((prev) => new Set(prev).add(fieldId));
  }, []);

  const handleEditEnd = useCallback((fieldId: string) => {
    setEditingFields((prev) => {
      const newSet = new Set(prev);
      newSet.delete(fieldId);
      return newSet;
    });
  }, []);

  const handlePhoneVerificationSuccess = useCallback(async () => {
    // Reload user data to get updated verification status
    if (user) {
      await user.reload();
      // Force re-render by updating a state that triggers useEffect
      setEditingFields((prev) => new Set(prev));
    }
  }, [user]);

  const handleEmailVerificationSuccess = useCallback(async () => {
    // For authenticated users, reload user data to get updated verification status
    if (isSignedIn && user) {
      await user.reload();
      // Force re-render by updating a state that triggers useEffect
      setEditingFields((prev) => new Set(prev));
    } else {
      // For guests who just got converted, unlock all other fields
      setIsGuestConverted(true);
      setEditingFields(new Set());
      setGuestFirstName('');
      setGuestLastName('');
    }
  }, [isSignedIn, user]);

  const { areRequiredFieldsFilled } = useJobSubmissionValidation();

  // Handlers for saving guest data
  const handleFirstNameSave = useCallback(
    (value: string) => {
      if (!isSignedIn) {
        setGuestFirstName(value);
      }
    },
    [isSignedIn]
  );

  const handleLastNameSave = useCallback(
    (value: string) => {
      if (!isSignedIn) {
        setGuestLastName(value);
      }
    },
    [isSignedIn]
  );

  const handleModalClose = useCallback(() => {
    if (!areRequiredFieldsFilled) {
      setShowCloseConfirmDialog(true);
      return;
    }

    // Reset all editing states when modal closes
    setEditingFields(new Set());
    setIsEditing(false);
    onClose();
  }, [onClose, areRequiredFieldsFilled]);

  const handleConfirmClose = useCallback(() => {
    setShowCloseConfirmDialog(false);
    setEditingFields(new Set());
    setIsEditing(false);
    onClose();
  }, [onClose]);

  const handleCancelClose = useCallback(() => {
    setShowCloseConfirmDialog(false);
  }, []);

  // Load property data when modal opens
  useEffect(() => {
    const loadProperties = async () => {
      const token = await getToken();
      if (token) {
        fetchProperties(token);
      }
    };

    if (open) {
      loadProperties();
    }
  }, [open, getToken, fetchProperties]);

  useEffect(() => {
    if (open && isLoaded) {
      if (editingFields.size === 0) {
        const newEditingFields = new Set<string>();

        // For guests (not signed in), put all fields in editing mode
        if (!isSignedIn) {
          newEditingFields.add('firstName');
          newEditingFields.add('lastName');
          newEditingFields.add('email');
          newEditingFields.add('phone');
          newEditingFields.add('address');
          newEditingFields.add('additionalNotes');
        } else if (user) {
          // For authenticated users, only add fields that need to be filled/verified
          if (
            !user.primaryEmailAddress?.emailAddress ||
            user.primaryEmailAddress?.verification?.status !== 'verified'
          ) {
            newEditingFields.add('email');
          }
          if (!user.primaryPhoneNumber?.phoneNumber) newEditingFields.add('phone');
          if (!addressValue) newEditingFields.add('address');
          if (!accessInstruction || accessInstruction === 'Skipped')
            newEditingFields.add('additionalNotes');
        }

        setEditingFields(newEditingFields);
        setTimeout(() => {
          if (!isSignedIn) {
            // For guests, start editing all fields
            firstNameRef.current?.startEditing();
            lastNameRef.current?.startEditing();
            emailRef.current?.startEditing();
            phoneRef.current?.startEditing();
            addressRef.current?.startEditing();
            accessInstructionRef.current?.startEditing();
          } else if (user) {
            // For authenticated users, only start editing fields that need to be filled/verified
            if (
              !user.primaryEmailAddress?.emailAddress ||
              user.primaryEmailAddress?.verification?.status !== 'verified'
            ) {
              emailRef.current?.startEditing();
            }
            if (!user.primaryPhoneNumber?.phoneNumber) phoneRef.current?.startEditing();
            if (!addressValue) addressRef.current?.startEditing();
            if (!accessInstruction || accessInstruction === 'Skipped')
              accessInstructionRef.current?.startEditing();
          }
        }, 0);
      }
    }
  }, [open, isLoaded, isSignedIn, user, addressValue, accessInstruction]);

  const handleEdit = () => {
    setIsEditing(true);
  };

  const handleCancel = () => {
    setIsEditing(false);
  };

  const handleSave = () => {
    setIsEditing(false);
  };

  const handleContinueToChat = () => {
    onClose();
  };

  const isFieldDisabled = useCallback(
    (fieldId: string) => {
      if (isSignedIn || isGuestConverted) return false;

      const firstNameValue = guestFirstName || user?.firstName || '';
      const lastNameValue = guestLastName || user?.lastName || '';
      const isEmailVerified = user?.primaryEmailAddress?.verification?.status === 'verified';
      const isPhoneVerified = user?.primaryPhoneNumber?.verification?.status === 'verified';

      switch (fieldId) {
        case 'lastName':
          return !firstNameValue.trim();
        case 'email':
          return !firstNameValue.trim() || !lastNameValue.trim();
        case 'phone':
          return !firstNameValue.trim() || !lastNameValue.trim() || !isEmailVerified;
        case 'address':
          return (
            !firstNameValue.trim() || !lastNameValue.trim() || !isEmailVerified || !isPhoneVerified
          );
        case 'additionalNotes':
          return (
            !firstNameValue.trim() ||
            !lastNameValue.trim() ||
            !isEmailVerified ||
            !isPhoneVerified ||
            !addressValue?.trim()
          );
        default:
          return false;
      }
    },
    [
      isSignedIn,
      isGuestConverted,
      guestFirstName,
      guestLastName,
      user?.firstName,
      user?.lastName,
      user?.primaryEmailAddress?.verification?.status,
      user?.primaryPhoneNumber?.verification?.status,
      addressValue,
    ]
  );

  const fields: PersonalizationCardField[] = useMemo(
    () => [
      {
        id: 'firstName',
        label: 'First name',
        value: user?.firstName || '',
        editable: false,
        type: 'component',
        component: (
          <FirstNameField
            ref={firstNameRef}
            initialValue={user?.firstName || ''}
            onEditStart={() => handleEditStart('firstName')}
            onEditEnd={() => handleEditEnd('firstName')}
            onSave={handleFirstNameSave}
          />
        ),
        showEditButton: isSignedIn && !editingFields.has('firstName'),
        onEditClick: () => firstNameRef.current?.startEditing(),
      },
      {
        id: 'lastName',
        label: 'Last name',
        value: user?.lastName || '',
        editable: false,
        type: 'component',
        component: (
          <div className={isFieldDisabled('lastName') ? styles.guestDisabledField : ''}>
            <LastNameField
              ref={lastNameRef}
              initialValue={user?.lastName || ''}
              onEditStart={() => handleEditStart('lastName')}
              onEditEnd={() => handleEditEnd('lastName')}
              onSave={handleLastNameSave}
            />
          </div>
        ),
        showEditButton: isSignedIn && !editingFields.has('lastName'),
        onEditClick: () => lastNameRef.current?.startEditing(),
      },
      {
        id: 'email',
        label: 'Email address',
        value: user?.primaryEmailAddress?.emailAddress || '',
        editable: false,
        type: 'component',
        component: (
          <div
            className={isFieldDisabled('email') ? styles.guestDisabledField : ''}
            style={{ display: 'flex', alignItems: 'center', gap: '4px' }}
          >
            {(() => {
              const firstNameValue = guestFirstName || user?.firstName || '';
              const lastNameValue = guestLastName || user?.lastName || '';
              return (
                <EmailField
                  ref={emailRef}
                  initialValue={
                    user?.primaryEmailAddress?.emailAddress ||
                    user?.emailAddresses?.[0].emailAddress ||
                    ''
                  }
                  onEditStart={() => handleEditStart('email')}
                  onEditEnd={() => handleEditEnd('email')}
                  onVerificationSuccess={handleEmailVerificationSuccess}
                  forceEditing={editingFields.has('email')}
                  firstName={firstNameValue}
                  lastName={lastNameValue}
                  isGuestConverted={isGuestConverted}
                />
              );
            })()}
            {user?.primaryEmailAddress?.verification?.status === 'verified' && (
              <HugeiconsIcon
                icon={CheckmarkCircle02Icon as unknown as IconSvgObject}
                size={16}
                color="#1B6E5A"
              />
            )}
          </div>
        ),
        showEditButton: isSignedIn,
        onEditClick: () => clerk.openUserProfile(),
      },
      {
        id: 'phone',
        label: 'Phone number',
        value: user?.phoneNumbers[0]?.phoneNumber || '',
        editable: false,
        type: 'component',
        component:
          !editingFields.has('phone') &&
          user?.primaryPhoneNumber?.verification?.status === 'verified' ? (
            <div style={{ display: 'flex', alignItems: 'center', gap: '4px' }}>
              <span>{user.primaryPhoneNumber?.phoneNumber}</span>
              {user.primaryPhoneNumber?.verification?.status === 'verified' && (
                <HugeiconsIcon
                  icon={CheckmarkCircle02Icon as unknown as IconSvgObject}
                  size={16}
                  color="#1B6E5A"
                />
              )}
            </div>
          ) : (
            <div className={isFieldDisabled('phone') ? styles.guestDisabledField : ''}>
              <PhoneField
                ref={phoneRef}
                initialValue={user?.phoneNumbers[0]?.phoneNumber || ''}
                onEditStart={() => handleEditStart('phone')}
                onEditEnd={() => handleEditEnd('phone')}
                onVerificationSuccess={handlePhoneVerificationSuccess}
                forceEditing={
                  editingFields.has('phone') ||
                  user?.primaryPhoneNumber?.verification?.status !== 'verified'
                }
              />
            </div>
          ),
        showEditButton: isSignedIn && !editingFields.has('phone'),
        onEditClick: () => {
          handleEditStart('phone');
          phoneRef.current?.startEditing();
        },
      },
      {
        id: 'address',
        label: 'Address',
        value: addressValue || '',
        editable: false,
        type: 'component',
        component: (
          <div className={isFieldDisabled('address') ? styles.guestDisabledField : ''}>
            <AddressField
              ref={addressRef}
              initialValue={addressValue || ''}
              onEditStart={() => handleEditStart('address')}
              onEditEnd={() => handleEditEnd('address')}
            />
          </div>
        ),
        showEditButton: isSignedIn && !editingFields.has('address') && !addressValue,
        onEditClick: () => addressRef.current?.startEditing(),
      },
      {
        id: 'additionalNotes',
        label: 'Additional notes (optional)',
        value: accessInstruction || '',
        editable: false,
        type: 'component',
        component: (
          <div className={isFieldDisabled('additionalNotes') ? styles.guestDisabledField : ''}>
            <AccessInstructionField
              ref={accessInstructionRef}
              initialValue={accessInstruction || ''}
              onEditStart={() => handleEditStart('additionalNotes')}
              onEditEnd={() => handleEditEnd('additionalNotes')}
              isAddressFilled={Boolean(addressValue?.trim()) && !editingFields.has('address')}
              isAddressEditing={editingFields.has('address')}
            />
          </div>
        ),
        showEditButton: isSignedIn && !editingFields.has('additionalNotes'),
        onEditClick: () => accessInstructionRef.current?.startEditing(),
      },
    ],
    [
      user,
      addressValue,
      accessInstruction,
      editingFields,
      isSignedIn,
      isGuestConverted,
      guestFirstName,
      guestLastName,
      handleEditStart,
      handleEditEnd,
      handleFirstNameSave,
      handleLastNameSave,
      handlePhoneVerificationSuccess,
      handleEmailVerificationSuccess,
      isFieldDisabled,
    ]
  );

  if (!isLoaded) {
    if (readOnly && inline) {
      return <div>Loading...</div>;
    }
    return (
      <Modal open={open} onClose={handleModalClose} title="Your details">
        <div>Loading...</div>
      </Modal>
    );
  }

  // If readOnly and inline, render PersonalizationCard directly without modal
  if (readOnly && inline) {
    return (
      <div className={styles.PersonalizationCard}>
        <PersonalizationCard
          title="Your details"
          titleEditable={false}
          fields={fields}
          files={[]}
          isEditing={false}
          onEdit={() => {}}
          onCancel={() => {}}
          onSave={() => {}}
          inline={true}
          readOnly={true}
          alignLabelsTop={true}
        />
      </div>
    );
  }

  return (
    <Modal open={open} onClose={handleModalClose} title="Your details">
      <div className={styles.PersonalizationCard}>
        <PersonalizationCard
          title=""
          titleEditable={false}
          fields={fields}
          files={[]}
          isEditing={isEditing}
          onEdit={handleEdit}
          onCancel={handleCancel}
          onSave={handleSave}
          inline={true}
          alignLabelsTop={true}
        />
      </div>
      <div className={styles.continueButton}>
        <Button
          type={ButtonType.PRIMARY}
          color={ButtonColor.GREEN_PRIMARY}
          size={ButtonSize.L}
          state={areRequiredFieldsFilled ? ButtonState.DEFAULT : ButtonState.DISABLED}
          onClick={handleContinueToChat}
        >
          Continue to chat
        </Button>
      </div>
      <ConfirmDialog
        title="Required details missing"
        description="We need a verified email, verified phone number, and your address to proceed with this booking"
        isOpen={showCloseConfirmDialog}
        onCancel={handleConfirmClose}
        onOk={handleCancelClose}
        cancelText="Cancel anyway"
        okText="Continue"
        cancelType="danger"
      />
    </Modal>
  );
};
