import React, { useState, useCallback, useImperativeHandle, forwardRef } from 'react';
import { useAuth } from '@clerk/nextjs';
import { Button } from '../../Button';
import { MultiFieldInput } from '../../MultiFieldInput/MultiFieldInput';
import { MultiFieldInputType, AddressFields } from '../../MultiFieldInput/MultiFieldInput.types';
import { ButtonType, ButtonSize, ButtonState } from '../../Button/Button.types';
import { useWidgets } from '@/hooks/useWidgets';
import styles from './FieldComponents.module.scss';

interface AccessInstructionFieldProps {
  initialValue: string;
  onSave?: (value: string) => void;
  onEditStart?: () => void;
  onEditEnd?: () => void;
  isAddressFilled?: boolean;
  isAddressEditing?: boolean;
}

export interface AccessInstructionFieldRef {
  startEditing: () => void;
}

export const AccessInstructionField = forwardRef<
  AccessInstructionFieldRef,
  AccessInstructionFieldProps
>(
  (
    {
      initialValue,
      onSave,
      onEditStart,
      onEditEnd,
      isAddressFilled = false,
      isAddressEditing = false,
    },
    ref
  ) => {
    const { getToken } = useAuth();
    const { saveAccessInstruction } = useWidgets();
    const normalizedInitialValue = initialValue === 'Skipped' ? '' : initialValue;
    const [isEditing, setIsEditing] = useState(false);
    const [value, setValue] = useState(normalizedInitialValue);
    const [loading, setLoading] = useState(false);
    const [error, setError] = useState('');

    useImperativeHandle(ref, () => ({
      startEditing: () => {
        setValue(normalizedInitialValue);
        setIsEditing(true);
        setError('');
        onEditStart?.();
      },
    }));

    const handleSave = useCallback(
      async (inputValue: string | AddressFields) => {
        let valueToSave = typeof inputValue === 'string' ? inputValue : value;
        if (!valueToSave.trim()) {
          valueToSave = 'Skipped';
        }
        setLoading(true);
        setError('');

        try {
          const token = await getToken();
          if (token) {
            await saveAccessInstruction(valueToSave, token);
            setIsEditing(false);
            onEditEnd?.();
            onSave?.(typeof inputValue === 'string' && !inputValue.trim() ? '' : valueToSave);
          } else {
            throw new Error('Authentication token not available');
          }
        } catch (err) {
          const errorMessage =
            err instanceof Error ? err.message : 'Failed to save access instructions';
          setError(errorMessage);
        } finally {
          setLoading(false);
        }
      },
      [value, getToken, saveAccessInstruction, onEditEnd, onSave]
    );

    const handleChange = useCallback((newValue: string | AddressFields) => {
      if (typeof newValue === 'string') {
        setValue(newValue);
      }
    }, []);

    if (isEditing) {
      return (
        <div className={styles.editingContainer}>
          <MultiFieldInput
            type={MultiFieldInputType.ACCESS_INSTRUCTION}
            value={value}
            onChange={handleChange}
            onSave={handleSave}
            loading={loading}
            hideSaveButton={true}
            hideTitle={true}
            hideBorder={true}
            placeholder="For example: Instructions about access, parking, or pets."
            containerClassName={styles.noPaddingContainer}
          />
          {error && <div className={styles.error}>{error}</div>}
          <div className={styles.actions + (!error ? ' ' + styles.buttonWithMargin : '')}>
            <Button
              type={ButtonType.SECONDARY}
              size={ButtonSize.BASE}
              onClick={() => handleSave(value)}
              state={
                loading || !isAddressFilled || isAddressEditing
                  ? ButtonState.DISABLED
                  : ButtonState.DEFAULT
              }
              disabled={loading || !isAddressFilled || isAddressEditing}
            >
              Save additional notes
            </Button>
          </div>
        </div>
      );
    }

    return (
      <div className={styles.displayContainer}>
        <span className={styles.value}>{normalizedInitialValue || '—'}</span>
      </div>
    );
  }
);

AccessInstructionField.displayName = 'AccessInstructionField';
