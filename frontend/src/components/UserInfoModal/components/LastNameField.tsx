import React, { useState, useCallback, useImperativeHandle, forwardRef, useEffect } from 'react';
import { useUser, useAuth } from '@clerk/nextjs';
import { Button } from '../../Button';
import { Input } from '../../Input';
import { ButtonType, ButtonSize } from '../../Button/Button.types';
import { InputState } from '../../Input/Input.types';
import styles from './FieldComponents.module.scss';

interface LastNameFieldProps {
  initialValue: string;
  onSave?: (value: string) => void;
  onEditStart?: () => void;
  onEditEnd?: () => void;
}

export interface LastNameFieldRef {
  startEditing: () => void;
}

export const LastNameField = forwardRef<LastNameFieldRef, LastNameFieldProps>(
  ({ initialValue, onSave, onEditStart, onEditEnd }, ref) => {
    const { user } = useUser();
    const { isSignedIn } = useAuth();
    const [isEditing, setIsEditing] = useState(false);
    const [value, setValue] = useState(initialValue);
    const [loading, setLoading] = useState(false);
    const [error, setError] = useState('');

    // Update value when initialValue changes (for authenticated users)
    useEffect(() => {
      if (isSignedIn && initialValue !== value) {
        setValue(initialValue);
      }
    }, [initialValue, isSignedIn, value]);

    useImperativeHandle(ref, () => ({
      startEditing: () => {
        setValue(initialValue);
        setIsEditing(true);
        setError('');
        onEditStart?.();
      },
    }));

    const handleSave = useCallback(async () => {
      setLoading(true);
      setError('');

      try {
        // For authenticated users, update via Clerk
        if (isSignedIn && user) {
          await user.update({
            lastName: value,
          });
          await user.reload();
        }

        // For both guests and authenticated users, finish editing
        setIsEditing(false);
        onEditEnd?.();
        onSave?.(value);
      } catch (err) {
        const errorMessage = err instanceof Error ? err.message : 'Failed to update last name';
        setError(errorMessage);
      } finally {
        setLoading(false);
      }
    }, [isSignedIn, user, value, onSave, onEditEnd]);

    if (isEditing) {
      return (
        <div className={styles.editingContainer}>
          <Input
            value={value}
            onChange={setValue}
            placeholderText="Last name"
            showLabel={false}
            showHelperText={false}
            showLeftIcon={false}
            state={InputState.NORMAL}
            disableClear={true}
            className={styles.input}
          />
          {error && <div className={styles.error}>{error}</div>}
          <div className={styles.actions + (!error ? ' ' + styles.buttonWithMargin : '')}>
            <Button
              type={ButtonType.PRIMARY}
              size={ButtonSize.BASE}
              onClick={handleSave}
              disabled={loading}
            >
              Save last name
            </Button>
          </div>
        </div>
      );
    }

    return (
      <div className={styles.displayContainer}>
        <span className={styles.value}>{value || '—'}</span>
      </div>
    );
  }
);

LastNameField.displayName = 'LastNameField';
