import React, { useState, useCallback, useImperativeHandle, forwardRef, useEffect } from 'react';
import { useUser } from '@clerk/nextjs';
import { MultiFieldInput } from '../../MultiFieldInput/MultiFieldInput';
import { MultiFieldInputType, AddressFields } from '../../MultiFieldInput/MultiFieldInput.types';
import styles from './FieldComponents.module.scss';

interface PhoneFieldProps {
  initialValue: string;
  onSave?: (value: string) => void;
  onEditStart?: () => void;
  onEditEnd?: () => void;
  onVerificationSuccess?: () => void;
  forceEditing?: boolean;
}

export interface PhoneFieldRef {
  startEditing: () => void;
}

export const PhoneField = forwardRef<PhoneFieldRef, PhoneFieldProps>(
  ({ initialValue, onSave, onEditStart, onEditEnd, onVerificationSuccess, forceEditing }, ref) => {
    const { user } = useUser();
    const [isEditing, setIsEditing] = useState(!!forceEditing);
    const [value, setValue] = useState(initialValue);
    const [loading, setLoading] = useState(false);
    const [error, setError] = useState('');

    useEffect(() => {
      if (forceEditing) setIsEditing(true);
    }, [forceEditing]);

    useImperativeHandle(ref, () => ({
      startEditing: () => {
        setValue(initialValue);
        setIsEditing(true);
        setError('');
        onEditStart?.();
      },
    }));

    const handleSendVerificationCode = useCallback(
      async (phoneNumber: string) => {
        if (!user) return false;

        try {
          // Remove all existing phone numbers before creating a new one
          for (const existingPhone of user.phoneNumbers) {
            await existingPhone.destroy();
          }

          // Reload user to ensure phone numbers are removed
          await user.reload();

          // Create new phone number
          const newPhoneObj = await user.createPhoneNumber({ phoneNumber });

          // Reload user again to ensure the new phone number is available
          await user.reload();

          // Find the newly created phone number
          const phoneObj = user.phoneNumbers.find((a) => a.id === newPhoneObj.id);
          if (!phoneObj) throw new Error('Phone number not found after creation');

          await phoneObj.prepareVerification();
          return true;
        } catch (err) {
          console.error('Error sending verification code:', err);
          setError(err instanceof Error ? err.message : 'Failed to send verification code');
          return false;
        }
      },
      [user]
    );

    const handleVerifyCode = useCallback(
      async (code: string) => {
        if (!user) return false;

        try {
          const phoneObj = user.primaryPhoneNumber || user.phoneNumbers[0];
          if (!phoneObj) throw new Error('Phone number not found');

          await phoneObj.attemptVerification({ code });
          await user.reload();

          const isVerified = phoneObj?.verification?.status === 'verified';

          if (isVerified) {
            setIsEditing(false);
            onEditEnd?.();
            onSave?.(phoneObj.phoneNumber);
            onVerificationSuccess?.();
          }

          return isVerified;
        } catch (err) {
          console.error('Error verifying code:', err);
          setError(err instanceof Error ? err.message : 'Failed to verify code');
          return false;
        }
      },
      [user, onEditEnd, onSave, onVerificationSuccess]
    );

    const handleSave = useCallback(
      async (inputValue: string | AddressFields) => {
        const phoneNumber = typeof inputValue === 'string' ? inputValue : value;

        if (!phoneNumber.trim()) {
          setError('Please enter a phone number');
          return;
        }

        setLoading(true);
        setError('');

        try {
          setIsEditing(false);
          onEditEnd?.();
          onSave?.(phoneNumber);
        } catch (err) {
          const errorMessage = err instanceof Error ? err.message : 'Failed to save phone number';
          setError(errorMessage);
        } finally {
          setLoading(false);
        }
      },
      [value, onEditEnd, onSave]
    );

    const handleChange = useCallback((newValue: string | AddressFields) => {
      if (typeof newValue === 'string') {
        setValue(newValue);
      }
    }, []);

    if (isEditing) {
      return (
        <div className={styles.editingContainer}>
          <MultiFieldInput
            type={MultiFieldInputType.PHONE}
            value={value}
            onChange={handleChange}
            onSendVerificationCode={handleSendVerificationCode}
            onVerifyCode={handleVerifyCode}
            onSave={handleSave}
            loading={loading}
            hideSaveButton={true}
            hideTitle={true}
            hideBorder={true}
            placeholder="+44XXXXXXXXXX"
            disabled={false}
            containerClassName={styles.noPaddingContainer}
            error={error}
          />
        </div>
      );
    }

    return (
      <div className={styles.displayContainer}>
        <span className={styles.value}>{initialValue || '—'}</span>
      </div>
    );
  }
);

PhoneField.displayName = 'PhoneField';
