import React, { useState, useCallback, useImperativeHandle, forwardRef, useEffect } from 'react';
import { useUser, useAuth } from '@clerk/nextjs';
import { MultiFieldInput } from '../../MultiFieldInput/MultiFieldInput';
import { MultiFieldInputType, AddressFields } from '../../MultiFieldInput/MultiFieldInput.types';
import { useGuestConversion } from '@/hooks/useGuestConversion';
import styles from './FieldComponents.module.scss';

interface EmailFieldProps {
  initialValue: string;
  onSave?: (value: string) => void;
  onEditStart?: () => void;
  onEditEnd?: () => void;
  onVerificationSuccess?: () => void;
  forceEditing?: boolean;
  firstName?: string;
  lastName?: string;
  isGuestConverted?: boolean;
}

export interface EmailFieldRef {
  startEditing: () => void;
}

export const EmailField = forwardRef<EmailFieldRef, EmailFieldProps>(
  (
    {
      initialValue,
      onSave,
      onEditStart,
      onEditEnd,
      onVerificationSuccess,
      forceEditing,
      firstName,
      lastName,
      isGuestConverted = false,
    },
    ref
  ) => {
    const { user } = useUser();
    const { isSignedIn } = useAuth();
    const { convertGuest, isConverting, currentStep } = useGuestConversion();
    const [isEditing, setIsEditing] = useState(!!forceEditing);
    const [value, setValue] = useState(initialValue);
    const [loading, setLoading] = useState(false);
    const [error, setError] = useState('');

    useEffect(() => {
      if (forceEditing) setIsEditing(true);
    }, [forceEditing]);

    // Update value when initialValue changes (for authenticated users)
    useEffect(() => {
      if (isSignedIn && initialValue !== value) {
        setValue(initialValue);
      }
    }, [initialValue, isSignedIn, value]);

    useImperativeHandle(ref, () => ({
      startEditing: () => {
        setValue(initialValue);
        setIsEditing(true);
        setError('');
        onEditStart?.();
      },
    }));

    const handleSendVerificationCode = useCallback(
      async (email: string): Promise<boolean> => {
        if (!isSignedIn) {
          return true;
        }

        if (!user) return false;

        try {
          await user.reload();

          let emailObj = user.emailAddresses.find((e) => e.emailAddress === email);

          if (!emailObj) {
            emailObj = await user.createEmailAddress({ email });
          }

          await emailObj.prepareVerification({ strategy: 'email_code' });
          return true;
        } catch (err) {
          console.error('Error sending verification code:', err);
          setError(err instanceof Error ? err.message : 'Failed to send verification code');
          return false;
        }
      },
      [isSignedIn, user]
    );

    const handleVerifyCode = useCallback(
      async (code: string, email: string): Promise<boolean> => {
        // For guests, just simulate success and finish editing
        if (!isSignedIn) {
          setIsEditing(false);
          onEditEnd?.();
          onSave?.(email);
          onVerificationSuccess?.();
          return true;
        }

        if (!user) return false;

        try {
          const emailObj = user.emailAddresses.find((e) => e.emailAddress === email);
          if (!emailObj) throw new Error('Email address not found');

          await emailObj.attemptVerification({ code });

          if (!user.primaryEmailAddressId || user.primaryEmailAddressId !== emailObj.id) {
            await user.update({ primaryEmailAddressId: emailObj.id });
          }

          await user.reload();

          const isVerified = emailObj?.verification?.status === 'verified';

          if (isVerified) {
            setIsEditing(false);
            onEditEnd?.();
            onSave?.(emailObj.emailAddress);
            onVerificationSuccess?.();
          }

          return isVerified;
        } catch (err) {
          console.error('Error verifying code:', err);
          setError(err instanceof Error ? err.message : 'Failed to verify code');
          return false;
        }
      },
      [isSignedIn, user, value, onEditEnd, onSave, onVerificationSuccess]
    );

    const handleSave = useCallback(
      async (inputValue: string | AddressFields) => {
        const email = typeof inputValue === 'string' ? inputValue : value;

        if (!email.trim()) {
          setError('Please enter an email address');
          return;
        }

        setLoading(true);
        setError('');

        try {
          if (!isSignedIn && firstName && lastName) {
            const conversionSuccess = await convertGuest({
              email,
              firstName,
              lastName,
            });

            if (conversionSuccess) {
              setIsEditing(false);
              onEditEnd?.();
              onSave?.(email);
            } else {
              setError('Failed to convert guest user');
              return;
            }
          } else {
            // For authenticated users or when missing data, just save
            setIsEditing(false);
            onEditEnd?.();
            onSave?.(email);
          }
        } catch (err) {
          const errorMessage = err instanceof Error ? err.message : 'Failed to save email address';
          setError(errorMessage);
        } finally {
          setLoading(false);
        }
      },
      [
        value,
        isSignedIn,
        firstName,
        lastName,
        convertGuest,
        onEditEnd,
        onSave,
        onVerificationSuccess,
      ]
    );

    const handleChange = useCallback((newValue: string | AddressFields) => {
      if (typeof newValue === 'string') {
        setValue(newValue);
      }
    }, []);

    // Helper function to get step message
    const getStepMessage = () => {
      if (!isConverting) return '';
      switch (currentStep) {
        case 'converting':
          return 'Converting guest account...';
        case 'complete':
          return 'Conversion complete!';
        default:
          return '';
      }
    };

    if (isEditing) {
      return (
        <div className={styles.editingContainer}>
          <MultiFieldInput
            type={MultiFieldInputType.EMAIL}
            value={value}
            onChange={handleChange}
            onSendEmailVerificationCode={handleSendVerificationCode}
            onVerifyEmailCode={handleVerifyCode}
            onSave={handleSave}
            loading={loading || isConverting}
            hideSaveButton={true}
            hideTitle={true}
            hideBorder={true}
            placeholder="Enter your email address"
            disabled={false}
            containerClassName={styles.noPaddingContainer}
            error={error || getStepMessage()}
            isGuestConverted={isGuestConverted}
          />
        </div>
      );
    }

    return (
      <div className={styles.displayContainer} style={{ width: 'inherit' }}>
        <span className={styles.value}>{value || '—'}</span>
      </div>
    );
  }
);

EmailField.displayName = 'EmailField';
