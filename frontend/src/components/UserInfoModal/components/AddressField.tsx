import React, { useState, useCallback, useImperativeHandle, forwardRef } from 'react';
import { useAuth } from '@clerk/nextjs';
import { Button } from '../../Button';
import { MultiFieldInput } from '../../MultiFieldInput/MultiFieldInput';
import { MultiFieldInputType, AddressFields } from '../../MultiFieldInput/MultiFieldInput.types';
import { ButtonType, ButtonSize } from '../../Button/Button.types';
import { useWidgets, AddressFields as WidgetAddressFields } from '@/hooks/useWidgets';
import { debounce } from 'lodash';
import styles from './FieldComponents.module.scss';

interface AddressFieldProps {
  initialValue: string;
  onSave?: (value: string) => void;
  onEditStart?: () => void;
  onEditEnd?: () => void;
}

export interface AddressFieldRef {
  startEditing: () => void;
}

export const AddressField = forwardRef<AddressFieldRef, AddressFieldProps>(
  ({ initialValue, onSave, onEditStart, onEditEnd }, ref) => {
    const { getToken } = useAuth();
    const { findAddresses, saveAddress: saveAddressToStore } = useWidgets();
    const [isEditing, setIsEditing] = useState(false);
    const [value, setValue] = useState<string | AddressFields>(initialValue);
    const [selectedAddressId, setSelectedAddressId] = useState<string | null>(null);
    const [loading, setLoading] = useState(false);
    const [error, setError] = useState('');

    useImperativeHandle(ref, () => ({
      startEditing: () => {
        setValue(initialValue);
        setIsEditing(true);
        setError('');
        onEditStart?.();
      },
    }));

    // eslint-disable-next-line react-hooks/exhaustive-deps
    const debouncedAutocomplete = useCallback(
      debounce(async (query: string) => {
        const token = await getToken();
        if (token) {
          return findAddresses(query, token);
        }
        return [] as object;
      }, 500),
      [getToken, findAddresses]
    );

    const handleAutocomplete = useCallback(
      async (query: string): Promise<object> => {
        const result = await debouncedAutocomplete(query);
        return result || ([] as object);
      },
      [debouncedAutocomplete]
    );

    const handleSave = useCallback(
      async (inputValue?: string | WidgetAddressFields) => {
        let valueToSave: string | WidgetAddressFields;

        if (inputValue !== undefined) {
          valueToSave = inputValue;
        } else {
          if (selectedAddressId) {
            valueToSave = selectedAddressId;
          } else {
            valueToSave = value;
          }
        }

        setLoading(true);
        setError('');

        try {
          const token = await getToken();
          if (token) {
            await saveAddressToStore(valueToSave, token);
            setIsEditing(false);
            onEditEnd?.();
            onSave?.(typeof valueToSave === 'string' ? valueToSave : JSON.stringify(valueToSave));
          } else {
            throw new Error('Authentication token not available');
          }
        } catch (err) {
          const errorMessage = err instanceof Error ? err.message : 'Failed to save address';
          setError(errorMessage);
        } finally {
          setLoading(false);
        }
      },
      [value, selectedAddressId, getToken, saveAddressToStore, onEditEnd, onSave]
    );

    const handleSelectOption = useCallback((option: { value: string; label: string }) => {
      setSelectedAddressId(option.value);
      setValue(option.label);
    }, []);

    const handleChange = useCallback((newValue: string | AddressFields) => {
      setValue(newValue);
      if (typeof newValue === 'object') {
        setSelectedAddressId(null);
      }
    }, []);

    if (isEditing) {
      return (
        <div className={styles.editingContainer}>
          <MultiFieldInput
            type={MultiFieldInputType.ADDRESS}
            value={value}
            onChange={handleChange}
            onAutocomplete={handleAutocomplete}
            onSave={handleSave}
            onSelectOption={handleSelectOption}
            loading={loading}
            hideSaveButton={true}
            hideTitle={true}
            hideBorder={true}
            placeholder="Find address"
            manualEntryText="Enter address manually"
            containerClassName={styles.noPaddingContainer}
          />
          {error && <div className={styles.error}>{error}</div>}
          <div className={styles.actions + (!error ? ' ' + styles.buttonWithMargin : '')}>
            <Button
              type={ButtonType.PRIMARY}
              size={ButtonSize.BASE}
              onClick={() => handleSave()}
              disabled={loading}
            >
              Save address
            </Button>
          </div>
        </div>
      );
    }

    return (
      <div className={styles.displayContainer}>
        <span className={styles.value}>{initialValue || '—'}</span>
      </div>
    );
  }
);

AddressField.displayName = 'AddressField';
