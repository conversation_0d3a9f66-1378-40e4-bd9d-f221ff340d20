import React from 'react';
import classNames from 'classnames';
import { FileUploadPreview } from '@/components/Composer/components/FileUploadPreview/FileUploadPreview';
import {
  MessageDocumentPreview,
} from '@/components/Message/components/MessageAttachments/components/MessageDocumentPreview';
import { FileUploadGridProps } from './FileUploadManager.types';
import { getFileId, isImageFile, isUploadedFile } from '@/utils/fileUtils';
import { Attachment } from '@/types/messages';
import styles from './FileUploadGrid.module.scss';
import { isCategorizedFile } from '@/types/file';

export const FileUploadGrid: React.FC<FileUploadGridProps> = ({
  files,
  onFileRemove,
  onFileRetry,
  className,
  showRemoveButton = true,
}) => {
  if (files.length === 0) return null;

  return (
    <div className={classNames(styles.grid, className)}>
      {files.map((file, index) => {
        const fileId = getFileId(file);
        const isImage = isImageFile(file);

        const convertedFile: Attachment = isUploadedFile(file) || isCategorizedFile(file)
          ? {
              documentId: file.documentId,
              name: file.name,
              type: file.type,
              size: file.size,
              file: file.file,
              status: file.status,
              progress: file.progress,
              createdAt: file.createdAt,
              fileId: file.fileId,
              originalFileName: file.originalFileName,
              cdnUrl: file.cdnUrl,
              fileExtension: file.fileExtension,
              sizeInKiloBytes: file.sizeInKiloBytes,
            }
          : {
              documentId: parseInt(String(fileId)) || Date.now(),
              name: file.name,
              type: file.type || 'document',
              size: file.size,
              file: file.file,
              status: file.status,
              progress: file.progress,
              createdAt: file.createdAt,
              fileId: String(fileId),
              cdnUrl: file.url,
            };

        const handleRemove =
          showRemoveButton && onFileRemove ? () => onFileRemove(fileId) : undefined;
        const handleRetry = onFileRetry ? () => onFileRetry(file) : undefined;

        return isImage ? (
          <FileUploadPreview
            key={`${fileId}-${file.name}`}
            file={convertedFile}
            onRemove={handleRemove}
            onRetry={handleRetry}
          />
        ) : (
          <MessageDocumentPreview
            key={`${fileId}-${file.name}`}
            fileUpload={convertedFile}
            index={index}
            onRemove={handleRemove}
          />
        );
      })}
    </div>
  );
};
