import React from "react";
import type { <PERSON><PERSON>, StoryObj } from "@storybook/react";
import { StarIcon } from '@hugeicons/react';
import { Toast } from "./Toast";
import { ToastColor } from "./Toast.types";

const meta = {
  title: "Components/Toast",
  component: Toast,
  parameters: {
    layout: "centered",
    docs: {
      description: {
        component: `
## Toast Component

A toast notification component that supports multiple variants. Use it for showing temporary notifications or feedback to users.

### Key Features
- Multiple color variants (success, warning, error, info)
- Customizable heading text and icons
- Close button
- Consistent styling with design system

### Usage Guidelines

1. **Choose the right color:**
   - \`success\`: For success messages
   - \`warning\`: For warning messages
   - \`error\`: For error messages
   - \`info\`: For informational messages

2. **Custom Icons:**
   You can provide custom icons using the \`leftIcon\` prop:
   
   \`\`\`jsx
   import { StarIcon } from "@hugeicons/react";
   
   <Toast 
     color="success"
     headingText="Custom Icon"
     leftIcon={StarIcon}
   />
   \`\`\`

### Examples

\`\`\`jsx
// Basic toast
<Toast 
  color="success"
  headingText="Operation successful"
/>

// Warning toast with custom icon
<Toast 
  color="warning"
  headingText="Please review your input"
  leftIcon={AlertCircleIcon}
/>
\`\`\`
`,
      },
    },
  },
  argTypes: {
    color: {
      control: "select",
      options: Object.values(ToastColor),
      description: "Color variant of the toast",
    },
    headingText: {
      control: "text",
      description: "Heading text of the toast",
    },
    leftIcon: {
      control: "object",
      description: "Custom icon component",
    },
  },
} satisfies Meta<typeof Toast>;

export default meta;
type Story = StoryObj<typeof Toast>;

export const Success: Story = {
  args: {
    color: ToastColor.SUCCESS,
    headingText: "Toast heading",
  },
};

export const Warning: Story = {
  args: {
    ...Success.args,
    color: ToastColor.WARNING,
  },
};

export const Error: Story = {
  args: {
    ...Success.args,
    color: ToastColor.ERROR,
  },
};

export const Info: Story = {
  args: {
    ...Success.args,
    color: ToastColor.INFO,
  },
};

export const WithCustomIcon: Story = {
  args: {
    ...Success.args,
    headingText: "Custom Icon Toast",
    leftIcon: StarIcon,
  },
};

export const AllVariants: Story = {
  render: () => (
    <div className="flex flex-col gap-4 p-4">
      <Toast color={ToastColor.SUCCESS} headingText="Success Toast" />
      <Toast color={ToastColor.WARNING} headingText="Warning Toast" />
      <Toast color={ToastColor.ERROR} headingText="Error Toast" />
      <Toast color={ToastColor.INFO} headingText="Info Toast" />
      <Toast
        color={ToastColor.SUCCESS}
        headingText="Custom Icon Toast"
        leftIcon={StarIcon}
      />
    </div>
  ),
};
