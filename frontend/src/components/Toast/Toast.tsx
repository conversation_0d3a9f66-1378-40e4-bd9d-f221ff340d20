import React from 'react';
import classNames from 'classnames';
import { InformationCircleIcon, CheckmarkCircle02Icon, AlertCircleIcon, Cancel01Icon } from '@hugeicons/react';
import { Button } from '../Button';
import { ButtonColor, ButtonSize, ButtonType } from '../Button/Button.types';
import styles from './Toast.module.scss';
import { ToastColor, ToastProps } from './Toast.types';

export const Toast = ({
  headingText = 'Toast heading',
  color = ToastColor.SUCCESS,
  className,
  onClose,
  leftIcon,
}: ToastProps): JSX.Element => {
  const getCloseButtonColor = () => {
    switch (color) {
      case ToastColor.ERROR:
        return ButtonColor.RED;
      case ToastColor.INFO:
        return ButtonColor.BLUE_SECONDARY;
      case ToastColor.SUCCESS:
        return ButtonColor.GREEN_PRIMARY;
      case ToastColor.WARNING:
        return ButtonColor.YELLOW;
      default:
        return ButtonColor.GREEN_PRIMARY;
    }
  };

  const getDefaultIcon = () => {
    switch (color) {
      case ToastColor.ERROR:
        return InformationCircleIcon;
      case ToastColor.INFO:
        return InformationCircleIcon;
      case ToastColor.SUCCESS:
        return CheckmarkCircle02Icon;
      case ToastColor.WARNING:
        return AlertCircleIcon;
      default:
        return CheckmarkCircle02Icon;
    }
  };

  const IconComponent = leftIcon || getDefaultIcon();

  return (
    <div className={classNames(styles.toast, styles[color], className)}>
      <div className={styles.content}>
        <div className={styles.leftBorder} />
        <div className={styles.header}>
          <div className={classNames(styles.iconWrapper, styles[color])}>
            <IconComponent className={classNames(styles.icon, styles[color])} />
          </div>

          <div className={classNames(styles.heading, styles[color])}>{headingText}</div>

          <Button
            color={getCloseButtonColor()}
            iconOnly
            size={ButtonSize.XS}
            type={ButtonType.TERTIARY}
            leftIcon={Cancel01Icon}
            className={styles.closeButton}
            onClick={onClose}
          />
        </div>
      </div>
    </div>
  );
};
