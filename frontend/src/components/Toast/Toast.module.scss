.toast {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  gap: var(--spacing-3);
  padding: var(--spacing-4);
  border-radius: var(--border-radius-rounded-md);
  position: relative;
  width: 100%;
  min-width: 300px;
  flex: 1;
}

.content {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  gap: var(--spacing-1-5);
  align-self: stretch;
  width: 100%;
  flex: 0 0 auto;
}

.leftBorder {
  position: absolute;
  width: 4px;
  height: 100%;
  top: 0;
  left: 0;
  border-radius: 6px 0 0 6px;
}

.header {
  display: flex;
  align-items: start;
  gap: 6px;
  align-self: stretch;
  width: 100%;
  flex: 0 0 auto;
  padding-right: 24px;
}

.iconWrapper {
  position: relative;
}

.icon {
  width: 24px;
  height: 24px;
}

.heading {
  margin-top: -1px;
  font-family: 'Quasimoda-Bold', Helvetica;
  font-weight: 700;
  font-size: 16px;
  line-height: 24px;
  flex: 1;
}

.closeButton {
  position: absolute;
  top: 18px;
  right: 12px;
}

// Color variants
.success {
  background-color: var(--colors-green-50);

  .leftBorder {
    background-color: var(--colors-green-700);
  }

  .iconWrapper {
    border-color: var(--colors-green-700);
  }

  .icon {
    color: var(--colors-green-700);
  }

  .heading {
    color: var(--colors-green-800);
  }
}

.warning {
  background-color: var(--colors-yellow-50);

  .leftBorder {
    background-color: var(--colors-yellow-700);
  }

  .iconWrapper {
    border-color: var(--colors-yellow-700);
  }

  .icon {
    color: var(--colors-yellow-700);
  }

  .heading {
    color: var(--colors-yellow-800);
  }
}

.error {
  background-color: var(--colors-red-50);

  .leftBorder {
    background-color: var(--colors-red-700);
  }

  .iconWrapper {
    border-color: var(--colors-red-700);
  }

  .icon {
    color: var(--colors-red-700);
  }

  .heading {
    color: var(--colors-red-800);
  }
}

.info {
  background-color: var(--colors-blue-50);

  .leftBorder {
    background-color: var(--colors-blue-700);
  }

  .iconWrapper {
    border-color: var(--colors-blue-700);
  }

  .icon {
    color: var(--colors-blue-700);
  }

  .heading {
    color: var(--colors-blue-800);
  }
}
