import '@testing-library/jest-dom';
import React from 'react';

// Mock CSS modules
jest.mock('../Toast.module.scss', () => ({
  toast: 'toast',
  success: 'success',
  warning: 'warning',
  error: 'error',
  info: 'info',
  content: 'content',
  header: 'header',
  leftBorder: 'leftBorder',
  iconWrapper: 'iconWrapper',
  icon: 'icon',
  heading: 'heading',
  closeButton: 'closeButton',
}));

// Mock the entire @hugeicons/react module with a more efficient approach
jest.mock('@hugeicons/react', () => new Proxy({}, {
  get: () => {
    return React.createElement.bind(null, 'div', {
      'data-testid': 'mock-icon',
      className: 'mock-icon'
    });
  }
}));

// Global test setup
beforeEach(() => {
  jest.clearAllMocks();
}); 