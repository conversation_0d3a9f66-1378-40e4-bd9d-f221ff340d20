// Vitest Snapshot v1, https://vitest.dev/guide/snapshot.html

exports[`Toast Snapshots > matches error toast snapshot 1`] = `
<div>
  <div
    class="_toast_e81ce1 _error_e81ce1"
  >
    <div
      class="_content_e81ce1"
    >
      <div
        class="_leftBorder_e81ce1"
      />
      <div
        class="_header_e81ce1"
      >
        <div
          class="_iconWrapper_e81ce1 _error_e81ce1"
        >
          <svg
            class="_icon_e81ce1 _error_e81ce1"
            color="currentColor"
            fill="none"
            height="24"
            viewBox="0 0 24 24"
            width="24"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M22 12C22 6.47715 17.5228 2 12 2C6.47715 2 2 6.47715 2 12C2 17.5228 6.47715 22 12 22C17.5228 22 22 17.5228 22 12Z"
              stroke="currentColor"
              stroke-width="1.5"
            />
            <path
              d="M12.2422 17V12C12.2422 11.5286 12.2422 11.2929 12.0957 11.1464C11.9493 11 11.7136 11 11.2422 11"
              stroke="currentColor"
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="1.5"
            />
            <path
              d="M11.992 8H12.001"
              stroke="currentColor"
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
            />
          </svg>
        </div>
        <div
          class="_heading_e81ce1 _error_e81ce1"
        >
          Test Toast
        </div>
        <button
          class="_button_ee73f7 _tertiary_ee73f7 _xs_ee73f7 _red_ee73f7 _iconOnly_ee73f7 _closeButton_e81ce1"
        >
          <svg
            class="_icon_ee73f7 _icon-xs_ee73f7"
            color="currentColor"
            fill="none"
            height="24"
            viewBox="0 0 24 24"
            width="24"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M19 5L5 19M5 5L19 19"
              stroke="currentColor"
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="1.5"
            />
          </svg>
        </button>
      </div>
    </div>
  </div>
</div>
`;

exports[`Toast Snapshots > matches info toast snapshot 1`] = `
<div>
  <div
    class="_toast_e81ce1 _info_e81ce1"
  >
    <div
      class="_content_e81ce1"
    >
      <div
        class="_leftBorder_e81ce1"
      />
      <div
        class="_header_e81ce1"
      >
        <div
          class="_iconWrapper_e81ce1 _info_e81ce1"
        >
          <svg
            class="_icon_e81ce1 _info_e81ce1"
            color="currentColor"
            fill="none"
            height="24"
            viewBox="0 0 24 24"
            width="24"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M22 12C22 6.47715 17.5228 2 12 2C6.47715 2 2 6.47715 2 12C2 17.5228 6.47715 22 12 22C17.5228 22 22 17.5228 22 12Z"
              stroke="currentColor"
              stroke-width="1.5"
            />
            <path
              d="M12.2422 17V12C12.2422 11.5286 12.2422 11.2929 12.0957 11.1464C11.9493 11 11.7136 11 11.2422 11"
              stroke="currentColor"
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="1.5"
            />
            <path
              d="M11.992 8H12.001"
              stroke="currentColor"
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
            />
          </svg>
        </div>
        <div
          class="_heading_e81ce1 _info_e81ce1"
        >
          Test Toast
        </div>
        <button
          class="_button_ee73f7 _tertiary_ee73f7 _xs_ee73f7 _blue-secondary_ee73f7 _iconOnly_ee73f7 _closeButton_e81ce1"
        >
          <svg
            class="_icon_ee73f7 _icon-xs_ee73f7"
            color="currentColor"
            fill="none"
            height="24"
            viewBox="0 0 24 24"
            width="24"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M19 5L5 19M5 5L19 19"
              stroke="currentColor"
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="1.5"
            />
          </svg>
        </button>
      </div>
    </div>
  </div>
</div>
`;

exports[`Toast Snapshots > matches success toast snapshot 1`] = `
<div>
  <div
    class="_toast_e81ce1 _success_e81ce1"
  >
    <div
      class="_content_e81ce1"
    >
      <div
        class="_leftBorder_e81ce1"
      />
      <div
        class="_header_e81ce1"
      >
        <div
          class="_iconWrapper_e81ce1 _success_e81ce1"
        >
          <svg
            class="_icon_e81ce1 _success_e81ce1"
            color="currentColor"
            fill="none"
            height="24"
            viewBox="0 0 24 24"
            width="24"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M22 12C22 6.47715 17.5228 2 12 2C6.47715 2 2 6.47715 2 12C2 17.5228 6.47715 22 12 22C17.5228 22 22 17.5228 22 12Z"
              stroke="currentColor"
              stroke-width="1.5"
            />
            <path
              d="M8 12.5L10.5 15L16 9"
              stroke="currentColor"
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="1.5"
            />
          </svg>
        </div>
        <div
          class="_heading_e81ce1 _success_e81ce1"
        >
          Test Toast
        </div>
        <button
          class="_button_ee73f7 _tertiary_ee73f7 _xs_ee73f7 _green-primary_ee73f7 _iconOnly_ee73f7 _closeButton_e81ce1"
        >
          <svg
            class="_icon_ee73f7 _icon-xs_ee73f7"
            color="currentColor"
            fill="none"
            height="24"
            viewBox="0 0 24 24"
            width="24"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M19 5L5 19M5 5L19 19"
              stroke="currentColor"
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="1.5"
            />
          </svg>
        </button>
      </div>
    </div>
  </div>
</div>
`;

exports[`Toast Snapshots > matches toast with custom icon snapshot 1`] = `
<div>
  <div
    class="_toast_e81ce1 _success_e81ce1"
  >
    <div
      class="_content_e81ce1"
    >
      <div
        class="_leftBorder_e81ce1"
      />
      <div
        class="_header_e81ce1"
      >
        <div
          class="_iconWrapper_e81ce1 _success_e81ce1"
        >
          <svg
            class="_icon_e81ce1 _success_e81ce1"
            color="currentColor"
            fill="none"
            height="24"
            viewBox="0 0 24 24"
            width="24"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M13.7276 3.44418L15.4874 6.99288C15.7274 7.48687 16.3673 7.9607 16.9073 8.05143L20.0969 8.58575C22.1367 8.92853 22.6167 10.4206 21.1468 11.8925L18.6671 14.3927C18.2471 14.8161 18.0172 15.6327 18.1471 16.2175L18.8571 19.3125C19.417 21.7623 18.1271 22.71 15.9774 21.4296L12.9877 19.6452C12.4478 19.3226 11.5579 19.3226 11.0079 19.6452L8.01827 21.4296C5.8785 22.71 4.57865 21.7522 5.13859 19.3125L5.84851 16.2175C5.97849 15.6327 5.74852 14.8161 5.32856 14.3927L2.84884 11.8925C1.389 10.4206 1.85895 8.92853 3.89872 8.58575L7.08837 8.05143C7.61831 7.9607 8.25824 7.48687 8.49821 6.99288L10.258 3.44418C11.2179 1.51861 12.7777 1.51861 13.7276 3.44418Z"
              stroke="currentColor"
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="1.5"
            />
          </svg>
        </div>
        <div
          class="_heading_e81ce1 _success_e81ce1"
        >
          Test Toast
        </div>
        <button
          class="_button_ee73f7 _tertiary_ee73f7 _xs_ee73f7 _green-primary_ee73f7 _iconOnly_ee73f7 _closeButton_e81ce1"
        >
          <svg
            class="_icon_ee73f7 _icon-xs_ee73f7"
            color="currentColor"
            fill="none"
            height="24"
            viewBox="0 0 24 24"
            width="24"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M19 5L5 19M5 5L19 19"
              stroke="currentColor"
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="1.5"
            />
          </svg>
        </button>
      </div>
    </div>
  </div>
</div>
`;

exports[`Toast Snapshots > matches toast with onClose handler snapshot 1`] = `
<div>
  <div
    class="_toast_e81ce1 _success_e81ce1"
  >
    <div
      class="_content_e81ce1"
    >
      <div
        class="_leftBorder_e81ce1"
      />
      <div
        class="_header_e81ce1"
      >
        <div
          class="_iconWrapper_e81ce1 _success_e81ce1"
        >
          <svg
            class="_icon_e81ce1 _success_e81ce1"
            color="currentColor"
            fill="none"
            height="24"
            viewBox="0 0 24 24"
            width="24"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M22 12C22 6.47715 17.5228 2 12 2C6.47715 2 2 6.47715 2 12C2 17.5228 6.47715 22 12 22C17.5228 22 22 17.5228 22 12Z"
              stroke="currentColor"
              stroke-width="1.5"
            />
            <path
              d="M8 12.5L10.5 15L16 9"
              stroke="currentColor"
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="1.5"
            />
          </svg>
        </div>
        <div
          class="_heading_e81ce1 _success_e81ce1"
        >
          Test Toast
        </div>
        <button
          class="_button_ee73f7 _tertiary_ee73f7 _xs_ee73f7 _green-primary_ee73f7 _iconOnly_ee73f7 _closeButton_e81ce1"
        >
          <svg
            class="_icon_ee73f7 _icon-xs_ee73f7"
            color="currentColor"
            fill="none"
            height="24"
            viewBox="0 0 24 24"
            width="24"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M19 5L5 19M5 5L19 19"
              stroke="currentColor"
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="1.5"
            />
          </svg>
        </button>
      </div>
    </div>
  </div>
</div>
`;

exports[`Toast Snapshots > matches warning toast snapshot 1`] = `
<div>
  <div
    class="_toast_e81ce1 _warning_e81ce1"
  >
    <div
      class="_content_e81ce1"
    >
      <div
        class="_leftBorder_e81ce1"
      />
      <div
        class="_header_e81ce1"
      >
        <div
          class="_iconWrapper_e81ce1 _warning_e81ce1"
        >
          <svg
            class="_icon_e81ce1 _warning_e81ce1"
            color="currentColor"
            fill="none"
            height="24"
            viewBox="0 0 24 24"
            width="24"
            xmlns="http://www.w3.org/2000/svg"
          >
            <circle
              cx="12"
              cy="12"
              r="10"
              stroke="currentColor"
              stroke-width="1.5"
            />
            <path
              d="M11.992 15H12.001"
              stroke="currentColor"
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
            />
            <path
              d="M12 12L12 8"
              stroke="currentColor"
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="1.5"
            />
          </svg>
        </div>
        <div
          class="_heading_e81ce1 _warning_e81ce1"
        >
          Test Toast
        </div>
        <button
          class="_button_ee73f7 _tertiary_ee73f7 _xs_ee73f7 _yellow_ee73f7 _iconOnly_ee73f7 _closeButton_e81ce1"
        >
          <svg
            class="_icon_ee73f7 _icon-xs_ee73f7"
            color="currentColor"
            fill="none"
            height="24"
            viewBox="0 0 24 24"
            width="24"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M19 5L5 19M5 5L19 19"
              stroke="currentColor"
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="1.5"
            />
          </svg>
        </button>
      </div>
    </div>
  </div>
</div>
`;
