import React from "react";
import { render } from "@testing-library/react";
import { Toast } from "../Toast";
import { ToastColor } from "../Toast.types";
import { StarIcon } from '@hugeicons/react';

describe("Toast Snapshots", () => {
  const defaultProps = {
    headingText: "Test Toast",
    color: ToastColor.SUCCESS,
  };

  it("matches success toast snapshot", () => {
    const { container } = render(<Toast {...defaultProps} />);
    expect(container).toMatchSnapshot();
  });

  it("matches warning toast snapshot", () => {
    const { container } = render(
      <Toast {...defaultProps} color={ToastColor.WARNING} />
    );
    expect(container).toMatchSnapshot();
  });

  it("matches error toast snapshot", () => {
    const { container } = render(
      <Toast {...defaultProps} color={ToastColor.ERROR} />
    );
    expect(container).toMatchSnapshot();
  });

  it("matches info toast snapshot", () => {
    const { container } = render(
      <Toast {...defaultProps} color={ToastColor.INFO} />
    );
    expect(container).toMatchSnapshot();
  });

  it("matches toast with custom icon snapshot", () => {
    const { container } = render(
      <Toast {...defaultProps} leftIcon={StarIcon} />
    );
    expect(container).toMatchSnapshot();
  });

  it("matches toast with onClose handler snapshot", () => {
    const { container } = render(
      <Toast {...defaultProps} onClose={() => {}} />
    );
    expect(container).toMatchSnapshot();
  });
});
