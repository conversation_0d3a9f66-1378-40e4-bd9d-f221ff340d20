import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import { vi } from 'vitest';
import { Toast } from '../Toast';
import { ToastColor } from '../Toast.types';
import { StarIcon } from '@hugeicons/react';

// Mock HugeIcons
vi.mock('@hugeicons/react', () => ({
  CheckmarkCircle02Icon: ({ className }: { className: string }) => (
    <div data-testid="toast-icon" className={className}>
      Mock Success Icon
    </div>
  ),
  InformationCircleIcon: ({ className }: { className: string }) => (
    <div data-testid="toast-icon" className={className}>
      Mock Info Icon
    </div>
  ),
  AlertCircleIcon: ({ className }: { className: string }) => (
    <div data-testid="toast-icon" className={className}>
      Mock Warning Icon
    </div>
  ),
  AlertTriangleIcon: ({ className }: { className: string }) => (
    <div data-testid="toast-icon" className={className}>
      Mock Error Icon
    </div>
  ),
  StarIcon: ({ className }: { className: string }) => (
    <div data-testid="toast-icon" className={className}>
      Mock Star Icon
    </div>
  ),
  Cancel01Icon: ({ className }: { className: string }) => (
    <div data-testid="close-button-icon" className={className}>
      Mock Cancel Icon
    </div>
  ),
}));

describe('Toast Component', () => {
  const defaultProps = {
    headingText: 'Test Toast',
    color: ToastColor.SUCCESS,
  };

  // Basic Rendering Tests
  describe('Rendering', () => {
    it('renders toast with heading text correctly', () => {
      render(<Toast {...defaultProps} />);
      expect(screen.getByText('Test Toast')).toBeInTheDocument();
    });

    it('renders with default props when none provided', () => {
      render(<Toast />);
      expect(screen.getByText('Toast heading')).toBeInTheDocument();
    });
  });

  // Color Variants Tests
  describe.skip('Color Variants', () => {
    it('renders success color variant correctly', () => {
      const { container } = render(<Toast {...defaultProps} color={ToastColor.SUCCESS} />);
      const toast = container.querySelector('._toast_d9f17e');
      expect(toast).toHaveClass('_success_d9f17e');
    });

    it('renders warning color variant correctly', () => {
      const { container } = render(<Toast {...defaultProps} color={ToastColor.WARNING} />);
      const toast = container.querySelector('._toast_d9f17e');
      expect(toast).toHaveClass('_warning_d9f17e');
    });

    it('renders error color variant correctly', () => {
      const { container } = render(<Toast {...defaultProps} color={ToastColor.ERROR} />);
      const toast = container.querySelector('._toast_d9f17e');
      expect(toast).toHaveClass('_error_d9f17e');
    });

    it('renders info color variant correctly', () => {
      const { container } = render(<Toast {...defaultProps} color={ToastColor.INFO} />);
      const toast = container.querySelector('._toast_d9f17e');
      expect(toast).toHaveClass('_info_d9f17e');
    });
  });

  // Icon Tests
  describe('Icons', () => {
    it('renders default icon based on color variant', () => {
      render(<Toast {...defaultProps} />);
      const icon = screen.getByTestId('toast-icon');
      expect(icon).toBeInTheDocument();
      expect(icon).toHaveTextContent('Mock Success Icon');
    });

    it('renders custom icon when provided', () => {
      render(<Toast {...defaultProps} leftIcon={StarIcon} />);
      const icon = screen.getByTestId('toast-icon');
      expect(icon).toBeInTheDocument();
      expect(icon).toHaveTextContent('Mock Star Icon');
    });
  });

  // Close Button Tests
  describe('Close Button', () => {
    it('calls onClose when close button is clicked', () => {
      const handleClose = vi.fn();
      render(<Toast {...defaultProps} onClose={handleClose} />);
      const closeButton = screen.getByRole('button');
      fireEvent.click(closeButton);
      expect(handleClose).toHaveBeenCalledTimes(1);
    });
  });

  // Class Composition Tests
  describe.skip('Class Composition', () => {
    it('applies custom className correctly', () => {
      const customClass = 'custom-class';
      const { container } = render(<Toast {...defaultProps} className={customClass} />);
      const toast = container.querySelector('._toast_d9f17e');
      expect(toast).toHaveClass(customClass);
    });

    it('combines multiple variant classes correctly', () => {
      const { container } = render(
        <Toast {...defaultProps} color={ToastColor.SUCCESS} className="custom-class" />
      );
      const toast = container.querySelector('._toast_d9f17e');
      expect(toast).toHaveClass('_success_d9f17e', 'custom-class');
    });
  });

  // Edge Cases
  describe.skip('Edge Cases', () => {
    it('handles empty heading text gracefully', () => {
      const { container } = render(<Toast {...defaultProps} headingText="" />);
      const toast = container.querySelector('._toast_d9f17e');
      expect(toast).toBeInTheDocument();
    });

    it('handles missing optional props', () => {
      const { container } = render(<Toast />);
      const toast = container.querySelector('._toast_d9f17e');
      expect(toast).toBeInTheDocument();
    });
  });
});
