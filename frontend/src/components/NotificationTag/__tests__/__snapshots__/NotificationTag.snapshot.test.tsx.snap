// Vitest Snapshot v1, https://vitest.dev/guide/snapshot.html

exports[`NotificationTag Snapshots > matches basic notification tag snapshot 1`] = `
<div>
  <div
    class="_notificationTag_fd78ce _flex_fd78ce"
  >
    Test notification
  </div>
</div>
`;

exports[`NotificationTag Snapshots > matches empty notification tag snapshot 1`] = `
<div>
  <div
    class="_notificationTag_fd78ce _flex_fd78ce"
  />
</div>
`;

exports[`NotificationTag Snapshots > matches notification tag with complex children snapshot 1`] = `
<div>
  <div
    class="_notificationTag_fd78ce _flex_fd78ce"
  >
    <span>
      Added to Property Profile
    </span>
    <div>
      Icon
    </div>
  </div>
</div>
`;

exports[`NotificationTag Snapshots > matches notification tag with custom className snapshot 1`] = `
<div>
  <div
    class="_notificationTag_fd78ce custom-class _flex_fd78ce"
  >
    Test notification
  </div>
</div>
`;
