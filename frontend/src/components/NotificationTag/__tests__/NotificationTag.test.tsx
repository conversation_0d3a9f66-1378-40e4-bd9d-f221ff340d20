import React from 'react';
import { fireEvent, render, screen } from '@testing-library/react';
import { vi } from 'vitest';
import { NotificationTag } from '../NotificationTag';

// Mock next/navigation
vi.mock('next/navigation', () => {
  const mockPush = vi.fn();
  return {
    useRouter: () => ({
      push: mockPush,
      replace: vi.fn(),
      refresh: vi.fn(),
      back: vi.fn(),
      forward: vi.fn(),
    }),
  };
});

describe('NotificationTag Component', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  afterEach(() => {
    vi.clearAllMocks();
  });

  it('renders children content', () => {
    render(<NotificationTag>Test notification</NotificationTag>);
    expect(screen.getByText('Test notification')).toBeInTheDocument();
  });

  it('applies custom className', () => {
    const { container } = render(
      <NotificationTag className="custom-class">Test notification</NotificationTag>
    );
    const notificationTag = container.firstChild as HTMLElement;
    expect(notificationTag).toHaveClass('custom-class');
  });

  it('navigates to property-profile on click', () => {
    render(<NotificationTag>Test notification</NotificationTag>);
    const notificationTag = screen.getByText('Test notification').parentElement;

    fireEvent.click(notificationTag!);

    expect(notificationTag).toBeInTheDocument();
  });

  it('renders with clickable behavior', () => {
    const { container } = render(<NotificationTag>Test notification</NotificationTag>);
    const notificationTag = container.firstChild as HTMLElement;

    expect(notificationTag).toBeInTheDocument();
    expect(notificationTag.tagName).toBe('DIV');
  });

  it('renders with complex children content', () => {
    render(
      <NotificationTag>
        <span>Added to Property Profile</span>
        <div data-testid="icon">Icon</div>
      </NotificationTag>
    );

    expect(screen.getByText('Added to Property Profile')).toBeInTheDocument();
    expect(screen.getByTestId('icon')).toBeInTheDocument();
  });
});
