import React from "react";
import { render, fireEvent } from "@testing-library/react";
import { vi } from "vitest";
import { Radio } from "../Radio";
import { RadioState } from "../Radio.types";
import styles from "../Radio.module.scss";

export default describe("Radio Component", () => {
  const defaultProps = {
    labelText: "Test Label",
    helperText: "Test Helper Text",
  };

  // Basic Rendering Tests
  it("renders without crashing", () => {
    const { container } = render(<Radio {...defaultProps} />);
    expect(container).toBeInTheDocument();
  });

  it("renders label text correctly", () => {
    const { getByText } = render(<Radio {...defaultProps} />);
    expect(getByText("Test Label")).toBeInTheDocument();
  });

  it("renders helper text when provided", () => {
    const { getByText } = render(<Radio {...defaultProps} />);
    expect(getByText("Test Helper Text")).toBeInTheDocument();
  });

  // State Variants Tests
  describe("State Variants", () => {
    it.each(Object.values(RadioState))(
      "renders %s state correctly",
      (state) => {
        const { container } = render(
          <Radio
            {...defaultProps}
            state={state}
            checked={state === RadioState.CHECKED}
          />
        );
        const radio = container.querySelector("input[type='radio']");
        expect(radio).toHaveClass(styles.radio);
        if (state !== RadioState.NORMAL) {
          expect(radio).toHaveClass(styles[state.toLowerCase()]);
        }
      }
    );
  });

  // Interaction Tests
  describe("Interactions", () => {
    it("calls onChange when clicked", () => {
      const handleChange = vi.fn();
      const { container } = render(
        <Radio {...defaultProps} onChange={handleChange} />
      );
      const radio = container.querySelector("input[type='radio']");
      fireEvent.click(radio!);
      expect(handleChange).toHaveBeenCalledWith(true);
    });

    it("doesn't call onChange when disabled", () => {
      const handleChange = vi.fn();
      const { container } = render(
        <Radio
          {...defaultProps}
          state={RadioState.DISABLED}
          onChange={handleChange}
        />
      );
      const radio = container.querySelector("input[type='radio']");
      fireEvent.click(radio!);
      expect(handleChange).not.toHaveBeenCalled();
    });
  });

  // Error State Tests
  describe("Error State", () => {
    it("renders error message when in error state", () => {
      const errorText = "Error Message";
      const { getByText } = render(
        <Radio
          {...defaultProps}
          state={RadioState.ERROR}
          errorText={errorText}
        />
      );
      expect(getByText(errorText)).toBeInTheDocument();
    });

    it("doesn't render error message in normal state", () => {
      const errorText = "Error Message";
      const { queryByText } = render(
        <Radio {...defaultProps} errorText={errorText} />
      );
      expect(queryByText(errorText)).not.toBeInTheDocument();
    });
  });

  // Radio Group Tests
  describe("Radio Group", () => {
    it("works correctly in a group", () => {
      const handleChange1 = vi.fn();
      const handleChange2 = vi.fn();
      const { container } = render(
        <>
          <Radio
            {...defaultProps}
            name="group"
            value="1"
            onChange={handleChange1}
          />
          <Radio
            {...defaultProps}
            name="group"
            value="2"
            onChange={handleChange2}
          />
        </>
      );
      const radios = container.querySelectorAll("input[type='radio']");
      fireEvent.click(radios[0]);
      expect(handleChange1).toHaveBeenCalledWith(true);
      fireEvent.click(radios[1]);
      expect(handleChange2).toHaveBeenCalledWith(true);
    });
  });
});
