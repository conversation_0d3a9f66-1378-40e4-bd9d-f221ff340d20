import React from "react";
import { render } from "@testing-library/react";
import { Radio } from "../Radio";
import { RadioState } from "../Radio.types";

export default describe("Radio Snapshots", () => {
  const defaultProps = {
    labelText: "Test Label",
    helperText: "Test Helper Text",
  };

  it("matches default radio snapshot", () => {
    const { container } = render(<Radio {...defaultProps} />);
    expect(container).toMatchSnapshot();
  });

  it.each(Object.values(RadioState))("matches %s state snapshot", (state) => {
    const { container } = render(<Radio {...defaultProps} state={state} />);
    expect(container).toMatchSnapshot();
  });

  it("matches radio with error text snapshot", () => {
    const { container } = render(
      <Radio
        {...defaultProps}
        state={RadioState.ERROR}
        errorText="Test Error Text"
      />
    );
    expect(container).toMatchSnapshot();
  });

  it("matches checked radio snapshot", () => {
    const { container } = render(<Radio {...defaultProps} checked />);
    expect(container).toMatchSnapshot();
  });

  it("matches radio without helper text snapshot", () => {
    const { container } = render(<Radio labelText={defaultProps.labelText} />);
    expect(container).toMatchSnapshot();
  });
});
