import '@testing-library/jest-dom';
import React from 'react';

// Mock CSS modules
jest.mock('../Radio.module.scss', () => ({
  'radio-field': 'radio-field',
  'radio': '_radio_d50e6e',
  'content': 'content',
  'label-helper': 'label-helper',
  'label': 'label',
  'caption': 'caption',
  'error-container': 'error-container',
  'error-icon': 'error-icon',
  'error-text': 'error-text',
  'checked': '_checked_d50e6e',
  'error': '_error_d50e6e',
  'disabled': '_disabled_d50e6e',
}));

// Mock HugeIcons
jest.mock('@hugeicons/react', () => new Proxy({}, {
  get: () => {
    return React.createElement.bind(null, 'div', {
      'data-testid': 'mock-icon',
      className: 'mock-icon'
    });
  }
}));

// Global test setup
beforeEach(() => {
  jest.clearAllMocks();
}); 