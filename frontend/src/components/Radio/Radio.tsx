import React from "react";
import classNames from "classnames";
import { AlertCircleIcon } from '@hugeicons/react';
import { RadioProps, RadioState } from "./Radio.types";
import styles from "./Radio.module.scss";

export const Radio = ({
  labelText,
  helperText,
  errorText,
  state = RadioState.NORMAL,
  className,
  checked = false,
  onChange,
  name,
  value,
}: RadioProps): JSX.Element => {
  const radioFieldClasses = classNames(
    styles["radio-field"],
    styles[state.toLowerCase()],
    className
  );

  const radioClasses = classNames(styles.radio, {
    [styles.checked]: checked,
    [styles.disabled]: state === RadioState.DISABLED,
    [styles.error]: state === RadioState.ERROR,
  });

  const helperTextClasses = classNames(
    styles.caption,
    state !== RadioState.NORMAL && styles[state.toLowerCase()]
  );

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (state !== RadioState.DISABLED && onChange) {
      onChange(e.target.checked);
    }
  };

  return (
    <label className={radioFieldClasses}>
      <div className={styles.content}>
        <input
          type="radio"
          className={radioClasses}
          checked={checked}
          onChange={handleChange}
          disabled={state === RadioState.DISABLED}
          name={name}
          value={value}
        />
        <div className={styles["label-helper"]}>
          {labelText && <div className={styles.label}>{labelText}</div>}
          {helperText && <div className={helperTextClasses}>{helperText}</div>}
        </div>
      </div>
      {state === RadioState.ERROR && errorText && (
        <div className={styles["error-container"]}>
          <AlertCircleIcon
            className={styles["error-icon"]}
            size={16}
          />
          <span className={styles["error-text"]}>{errorText}</span>
        </div>
      )}
    </label>
  );
};
