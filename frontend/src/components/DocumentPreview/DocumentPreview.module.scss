.documentContainer {
  border: 1px solid var(--color-gray-300);
  border-radius: 0.5rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.documentContainerDark {
  background: #DFD8CB;
  border: 1px solid #DFD8CB;
}

.documentContainerShort {
  position: relative;
  font-family: Quasimoda, sans-serif;
  display: flex;
  gap: 10px;
  align-items: center;
  width: calc(50% - 16px);
  height: 68px;
  flex-shrink: 0;

  @media (max-width: 768px) {
    width: 100%;
    max-width: unset;
  }
}

.documentPreviewIconContainer {
  display: flex;
  align-items: center;
  padding: 1rem;
  width: calc(100% - 100px);
  gap: 1rem;
}

.documentPreviewIconContainerShort {
  display: flex;
  align-items: center;
  padding: 1rem;
  width: 100%;
  gap: 1rem;
}

.documentPreviewIcon {
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 44px;
  height: 44px;
  border-radius: 8px;
  background-color: var(--color-yellow-30);
}

.documentThumbnail {
  width: 44px;
  height: 44px;
  border-radius: 8px;
}

.documentContent {
  font-size: 14px;
  display: flex;
  gap: 2px;
  flex-direction: column;
  overflow: hidden;
}

.documentName {
  max-width: 100%;
  color: var(--color-base-black);
}

.documentDescription {
  color: var(--color-gray-600);
}

.ellipsis {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

:global(.body-xss) {
  &.uploaded {
    font-size: 8px;
    line-height: 10px;

    @media screen and (min-width: 768px) {
      font-size: 8px;
      line-height: 10px;
    }
  }
}

.uploadedOnContainer {
  color: var(--color-gray-600);
  padding-right: 1rem;
  display: none;
  flex-direction: column;
  gap: 0;
  justify-content: center;
  text-align: center;

  @media screen and (min-width: 360px) {
    display: flex;
  }
}
