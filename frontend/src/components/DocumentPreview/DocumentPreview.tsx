import { Files01Icon } from '@hugeicons-pro/core-stroke-rounded';

import { HugeiconsIcon } from '@/components/HugeiconsIcon';
import { IconSvgObject } from '@hugeicons/react';
import React, { useEffect, useState } from 'react';
import { Typography } from '../Typography/Typography';
import classNames from 'classnames';
import styles from './DocumentPreview.module.scss';
import { fetchDocument } from '@/api/documents';
import { useInView } from 'react-intersection-observer';
import { useAuth } from '@clerk/nextjs';

interface IDocumentPreviewProps {
  filename: string;
  isImage: boolean;
  description?: string | null;
  uploadedOn?: string;
  thumbnailDocumentId: number;
  dark?: boolean;
  short?: boolean;
}

const imageCache = new Map<number, string>();

export async function getCachedImageObjectUrl(token: string, documentId: number): Promise<string> {
  if (imageCache.has(documentId)) {
    return imageCache.get(documentId)!;
  }

  const file = await fetchDocument(token, documentId);
  const objectUrl = URL.createObjectURL(file);

  imageCache.set(documentId, objectUrl);

  return objectUrl;
}

export const DocumentPreview: React.FC<IDocumentPreviewProps> = ({
  filename,
  isImage,
  description,
  uploadedOn,
  thumbnailDocumentId,
  dark,
  short,
}) => {
  const { getToken } = useAuth();
  const { ref, inView } = useInView({
    threshold: 0,
  });
  const [thumbnail, setThumbnail] = useState<string>('');

  useEffect(() => {
    if (inView) {
      getToken()
        .then((token) => token && getCachedImageObjectUrl(token, thumbnailDocumentId))
        .then((thumbnail) => thumbnail && setThumbnail(thumbnail));
    }
  }, [getToken, inView, thumbnailDocumentId]);

  return (
    <div
      ref={ref}
      className={`${styles.documentContainer} ${dark ? styles.documentContainerDark : ''} ${short ? styles.documentContainerShort : ''}`}
    >
      <div className={short ? styles.documentPreviewIconContainerShort : styles.documentPreviewIconContainer}>
        {thumbnail && isImage ? (
          <img src={thumbnail} alt={filename} className={styles.documentThumbnail} />
        ) : (
          <div className={styles.documentPreviewIcon}>
            <HugeiconsIcon
              icon={Files01Icon as unknown as IconSvgObject}
              size={20}
              color="#fff"
            />
          </div>
        )}
        <div className={styles.documentContent}>
          <Typography
            variant="body-s"
            font="quasimoda"
            className={classNames(styles.ellipsis, styles.documentName)}
          >
            {filename}
          </Typography>
          {description && (
            <Typography
              variant="body-xss"
              font="quasimoda"
              className={classNames(styles.ellipsis, styles.documentDescription)}
            >
              {description}
            </Typography>
          )}
        </div>
      </div>
      {!short && uploadedOn && (
        <div className={styles.uploadedOnContainer}>
          <Typography
            variant="body-xss"
            font="quasimoda"
            className={classNames(styles.uploaded, styles.ellipsis)}
          >
            Uploaded on
          </Typography>
          <Typography variant="body-xss" font="quasimoda">
            {uploadedOn}
          </Typography>
        </div>
      )}
    </div>
  );
};
