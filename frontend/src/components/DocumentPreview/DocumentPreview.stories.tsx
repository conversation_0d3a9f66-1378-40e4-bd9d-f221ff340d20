import type { Meta, StoryObj } from '@storybook/react';
import { DocumentPreview } from './DocumentPreview';

const meta = {
  title: 'Components/DocumentPreview',
  component: DocumentPreview,
  parameters: {
    layout: 'centered',
  },
  tags: ['autodocs'],
} satisfies Meta<typeof DocumentPreview>;

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
  args: {
    filename: 'insurance-policy.pdf',
    description: 'Insurance Policy Document',
    uploadedOn: '01/10/2023',
    thumbnailDocumentId: 1,
    isImage: false,
  },
  render: (args) => (
    <>
      <style>
        {`
        .custom-width {
          width: 260px;
        }

        @media (min-width: 360px) {
          .custom-width {
            width: 340px;
          }
        }

        @media (min-width: 768px) {
          .custom-width {
            width: 700px;
          }
        }
      `}
      </style>
      <div className="custom-width">
        <DocumentPreview {...args} />
        <DocumentPreview {...args} />
      </div>
    </>
  ),
};
