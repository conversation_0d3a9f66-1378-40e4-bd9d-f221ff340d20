import React from 'react';
import { ConfirmToastProps } from './ConfirmToast.types';
import styles from './ConfirmToast.module.scss';
import { Button } from '../Button';
import { ButtonColor, ButtonSize, ButtonState, ButtonType } from '../Button/Button.types';
import { InformationCircleIcon } from '@hugeicons/react';
import { useAuth } from '@clerk/nextjs';
import { useMessages } from '@/hooks/useMessages';
import { acceptJob } from '@/api/jobs';
import useMessageSender from '@/hooks/useMessageSender';

export const ConfirmToast: React.FC<ConfirmToastProps> = ({
  text,
  isJobSummary = false,
  isLastMessage = false,
  scrollToBottom,
  message,
}) => {
  const { sendMessage } = useMessageSender();
  const { jobSummaryConfirmed, setJobSummaryConfirmed } = useMessages();
  const { getToken } = useAuth();

  const handleClick = async (value: string) => {
    const isSent = await sendMessage(value);
    if (isSent) {
      scrollToBottom?.();
    }
  };

  const handleConfirm = async () => {
    const token = await getToken();

    if (token) {
      try {
        await acceptJob({
          jobId: parseInt(message?.additionalData?.jobSummary?.jobId || ''),
          token,
        });
        setJobSummaryConfirmed(true);
        scrollToBottom?.();
      } catch (error) {
        console.error('Failed to accept job:', error);
      }
    }
  };

  if (!isJobSummary || !isLastMessage) {
    return null;
  }

  return (
    <div className={styles.container}>
      <div className={styles.textContainer}>
        <InformationCircleIcon className={styles.icon} size={16} />
        <p className={styles.text}>{text}</p>
      </div>
      <div className={styles.buttons}>
        <Button
          onClick={handleConfirm}
          size={ButtonSize.BASE}
          color={ButtonColor.GREEN_PRIMARY}
          type={ButtonType.PRIMARY}
          state={jobSummaryConfirmed ? ButtonState.DISABLED : ButtonState.DEFAULT}
        >
          Yes, that looks good
        </Button>
        <Button
          onClick={() => handleClick('Edit job details')}
          size={ButtonSize.BASE}
          color={ButtonColor.GREEN_PRIMARY}
          type={ButtonType.SECONDARY}
          state={jobSummaryConfirmed ? ButtonState.DISABLED : ButtonState.DEFAULT}
          outline
        >
          Edit job details
        </Button>
      </div>
    </div>
  );
};
