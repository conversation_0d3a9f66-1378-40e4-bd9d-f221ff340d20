import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { ConfirmToast } from '../ConfirmToast';
import { useChats } from '@/hooks/useChats';
import { useMessages } from '@/hooks/useMessages';
import { useAuth } from '@clerk/nextjs';
import { acceptJob } from '@/api/jobs';
import { vi } from 'vitest';
import { Message } from '@/types/messages';

vi.mock('@/hooks/useChats');
vi.mock('@/hooks/useMessages');
vi.mock('@clerk/nextjs');
vi.mock('@/api/jobs');

vi.mock('next/navigation', () => ({
  useParams: vi.fn(() => ({ chatId: 'test-chat-id' })),
  useRouter: vi.fn(() => ({
    push: vi.fn(),
    replace: vi.fn(),
    refresh: vi.fn(),
    back: vi.fn(),
    forward: vi.fn(),
  })),
  useSearchParams: vi.fn(() => ({
    get: vi.fn(() => null),
    has: vi.fn(() => false),
    getAll: vi.fn(() => []),
    keys: vi.fn(() => []),
    values: vi.fn(() => []),
    entries: vi.fn(() => []),
    forEach: vi.fn(),
    append: vi.fn(),
    delete: vi.fn(),
    set: vi.fn(),
    sort: vi.fn(),
    toString: vi.fn(() => ''),
  })),
  usePathname: vi.fn(() => '/'),
  redirect: vi.fn(),
  notFound: vi.fn(),
}));

vi.mock('next/headers.js', () => ({
  headers: () => new Map(),
  cookies: () => new Map(),
}));

vi.mock('ezheaders', () => ({
  default: {
    headers: () => new Map(),
    cookies: () => new Map(),
  },
}));

describe('ConfirmToast', () => {
  const mockSendMessage = vi.fn();
  const mockAppendMessages = vi.fn();
  const mockAddMessage = vi.fn();
  const mockGetToken = vi.fn();
  const mockSetJobSummaryConfirmed = vi.fn();
  const mockMessages = [
    {
      additionalData: {
        jobSummary: {
          jobId: '123',
        },
      },
    },
  ];

  beforeEach(() => {
    (useChats as unknown as ReturnType<typeof vi.fn>).mockReturnValue({
      sendMessage: mockSendMessage,
      appendMessages: mockAppendMessages,
    });

    (useMessages as unknown as ReturnType<typeof vi.fn>).mockReturnValue({
      addMessage: mockAddMessage,
      messages: mockMessages,
      jobSummaryConfirmed: false,
      setJobSummaryConfirmed: mockSetJobSummaryConfirmed,
    });

    (useAuth as ReturnType<typeof vi.fn>).mockReturnValue({
      getToken: mockGetToken,
    });

    mockGetToken.mockResolvedValue('mock-token');
  });

  afterEach(() => {
    vi.clearAllMocks();
  });

  describe('Conditional rendering', () => {
    it('should not be displayed if isJobSummary is false', () => {
      render(<ConfirmToast text="Once you confirm..." isJobSummary={false} isLastMessage={true} />);
      expect(screen.queryByText('Once you confirm...')).not.toBeInTheDocument();
    });

    it('should not be displayed if isLastMessage is false', () => {
      render(<ConfirmToast text="Once you confirm..." isJobSummary={true} isLastMessage={false} />);
      expect(screen.queryByText('Once you confirm...')).not.toBeInTheDocument();
    });

    it('should be displayed if both flags are true', () => {
      render(<ConfirmToast text="Once you confirm..." isJobSummary={true} isLastMessage={true} />);
      expect(screen.getByText('Once you confirm...')).toBeInTheDocument();
    });
  });

  describe('Content display', () => {
    it('should display all necessary elements', () => {
      render(<ConfirmToast text="Once you confirm..." isJobSummary={true} isLastMessage={true} />);

      expect(screen.getByText('Once you confirm...')).toBeInTheDocument();
      expect(screen.getByText('Yes, that looks good')).toBeInTheDocument();
      expect(screen.getByText('Edit job details')).toBeInTheDocument();
    });
  });

  describe('Button interactions', () => {
    it('should call acceptJob when confirmation button is clicked', async () => {
      render(
        <ConfirmToast
          text="Once you confirm..."
          isJobSummary={true}
          isLastMessage={true}
          message={mockMessages[0] as Message}
        />
      );

      const confirmButton = screen.getByText('Yes, that looks good');
      fireEvent.click(confirmButton);

      await waitFor(() => {
        expect(acceptJob).toHaveBeenCalledWith({
          jobId: 123,
          token: 'mock-token',
        });
      });

      expect(mockSetJobSummaryConfirmed).toHaveBeenCalledWith(true);
    });

    it('should call sendMessage when edit button is clicked', async () => {
      render(<ConfirmToast text="Once you confirm..." isJobSummary={true} isLastMessage={true} />);

      const editButton = screen.getByText('Edit job details');
      fireEvent.click(editButton);
    });
  });

  describe('Error handling', () => {
    it('should properly handle error when calling acceptJob', async () => {
      const consoleErrorSpy = vi.spyOn(console, 'error').mockImplementation(() => { });
      (acceptJob as ReturnType<typeof vi.fn>).mockRejectedValueOnce(new Error('API Error'));

      render(<ConfirmToast text="Once you confirm..." isJobSummary={true} isLastMessage={true} />);

      const confirmButton = screen.getByText('Yes, that looks good');
      fireEvent.click(confirmButton);

      await waitFor(() => {
        expect(consoleErrorSpy).toHaveBeenCalledWith('Failed to accept job:', expect.any(Error));
      });

      consoleErrorSpy.mockRestore();
    });
    it('should properly handle error when calling sendMessage', async () => {
      const consoleErrorSpy = vi.spyOn(console, 'error').mockImplementation(() => { });
      mockSendMessage.mockRejectedValueOnce(new Error('API Error'));

      render(<ConfirmToast text="Once you confirm..." isJobSummary={true} isLastMessage={true} />);

      const editButton = screen.getByText('Edit job details');
      fireEvent.click(editButton);

      await waitFor(() => {
        expect(consoleErrorSpy).toHaveBeenCalledWith('Failed to send message:', expect.any(Error));
      });

      consoleErrorSpy.mockRestore();
    });
  });
});
