.container {
    padding-top: 15px;
    width: 100%;
    max-width: 671px;
}

.textContainer {
    background: #FFDAC7;
    border-radius: var(--border-radius-rounded-lg);
    display: flex;
    align-items: flex-start;
    gap: var(--spacing-1);
    margin-bottom: 15px;
    padding: var(--spacing-2) var(--spacing-4);
}

.text {
    margin: 0;
    font-size: 16px;
    line-height: 150%;
    color: #A34E22;
    flex: 1;
}

.icon {
    color: #A34E22;
    margin-top: var(--spacing-1);
}

.buttons {
    display: flex;
    width: 100%;
    gap: var(--spacing-2);
    flex-wrap: wrap;
}