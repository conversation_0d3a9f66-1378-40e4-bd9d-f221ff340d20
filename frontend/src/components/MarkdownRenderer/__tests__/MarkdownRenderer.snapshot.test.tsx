import React from "react";
import { render } from "@testing-library/react";
import { MarkdownRenderer } from "../MarkdownRenderer";

export default describe("MarkdownRenderer Snapshots", () => {
    const defaultProps = {
        content: "# Test Content",
        className: "test-class",
    };

    it("matches default markdown snapshot", () => {
        const { container } = render(<MarkdownRenderer {...defaultProps} />);
        expect(container).toMatchSnapshot();
    });

    it("matches snapshot with table content", () => {
        const tableContent = `
      | Header 1 | Header 2 |
      |----------|----------|
      | Cell 1   | Cell 2   |
    `;
        const { container } = render(<MarkdownRenderer content={tableContent} />);
        expect(container).toMatchSnapshot();
    });

    it("matches snapshot with formatted text", () => {
        const formattedContent = `
      # Heading 1
      ## Heading 2
      
      **Bold text**
      *Italic text*
      
      - List item 1
      - List item 2
    `;
        const { container } = render(<MarkdownRenderer content={formattedContent} />);
        expect(container).toMatchSnapshot();
    });

    it("matches snapshot with empty content", () => {
        const { container } = render(<MarkdownRenderer content="" />);
        expect(container).toMatchSnapshot();
    });

    it("matches snapshot without className", () => {
        const { container } = render(<MarkdownRenderer content={defaultProps.content} />);
        expect(container).toMatchSnapshot();
    });
});
