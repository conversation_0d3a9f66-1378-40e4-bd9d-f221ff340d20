import React from "react";
import { render, screen } from "@testing-library/react";
import { MarkdownRenderer } from "../MarkdownRenderer";
import styles from "../MarkdownRenderer.module.scss";

export default describe("MarkdownRenderer Component", () => {
    const defaultProps = {
        content: "# Test Content",
        className: "test-class",
    };

    describe("Rendering", () => {
        it("renders with default props", () => {
            const { container } = render(<MarkdownRenderer {...defaultProps} />);
            const wrapper = container.querySelector(`.${styles.markdownWrapper}`);
            expect(wrapper).toHaveClass('test-class');
            expect(wrapper).toHaveTextContent('Test Content');
        });

        it("renders text content with formatting", () => {
            const markdownContent = `# Heading

**Bold text**`;

            const { container } = render(<MarkdownRenderer content={markdownContent} />);
            const wrapper = container.querySelector(`.${styles.markdownWrapper}`);
            expect(wrapper?.textContent?.trim()).toBe('Heading\nBold text');
        });

        it("renders tables with formatting", () => {
            const tableContent = `| Column 1 | Column 2 |
|----------|----------|
| Cell 1   | Cell 2   |`;

            render(<MarkdownRenderer content={tableContent} />);

            expect(screen.getByRole('table')).toBeInTheDocument();
            expect(screen.getByRole('columnheader', { name: 'Column 1' })).toBeInTheDocument();
            expect(screen.getByRole('columnheader', { name: 'Column 2' })).toBeInTheDocument();
            expect(screen.getByRole('cell', { name: 'Cell 1' })).toBeInTheDocument();
            expect(screen.getByRole('cell', { name: 'Cell 2' })).toBeInTheDocument();
        });
    });

    describe("Edge Cases", () => {
        it("handles empty content", () => {
            const { container } = render(<MarkdownRenderer content="" />);
            const wrapper = container.querySelector(`.${styles.markdownWrapper}`);
            expect(wrapper).toBeInTheDocument();
            expect(wrapper?.textContent).toBe('');
        });

        it("handles content with special characters", () => {
            const specialContent = "# Test & < > \" '";
            const { container } = render(<MarkdownRenderer content={specialContent} />);
            const wrapper = container.querySelector(`.${styles.markdownWrapper}`);
            expect(wrapper?.textContent).toBe('Test & < > " \'');
        });
    });

    describe("Table Rendering", () => {
        it("renders tables with correct styling", () => {
            const tableContent = `| Header 1 | Header 2 |
|----------|----------|
| Data 1   | Data 2   |`;

            render(<MarkdownRenderer content={tableContent} />);

            const table = screen.getByRole('table');
            expect(table).toHaveClass(styles.table);

            const headers = screen.getAllByRole('columnheader');
            headers.forEach(header => {
                expect(header).toHaveClass(styles.th);
            });

            const cells = screen.getAllByRole('cell');
            cells.forEach(cell => {
                expect(cell).toHaveClass(styles.td);
            });
        });

        it("renders plain text when no table is present", () => {
            const textContent = '# Just a heading\nSome text';
            const { container } = render(<MarkdownRenderer content={textContent} />);
            const wrapper = container.querySelector(`.${styles.markdownWrapper}`);
            expect(wrapper?.textContent?.trim()).toBe('Just a heading\nSome text');
        });
    });
});
