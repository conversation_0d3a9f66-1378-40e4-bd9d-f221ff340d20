import React from 'react';
import ReactMarkdown from 'react-markdown';
import remarkGfm from 'remark-gfm';
import styles from './MarkdownRenderer.module.scss';

interface MarkdownRendererProps {
    content: string;
    className?: string;
}

export const MarkdownRenderer: React.FC<MarkdownRendererProps> = ({ content, className }) => {
    return (
        <div className={`${styles.markdownWrapper} ${className || ''}`}>
            <ReactMarkdown
                remarkPlugins={[remarkGfm]}
                components={{
                    table: ({ ...props }) => (
                        <table className={styles.table} {...props} />
                    ),
                    thead: ({ ...props }) => (
                        <thead {...props} />
                    ),
                    tbody: ({ ...props }) => (
                        <tbody {...props} />
                    ),
                    tr: ({ ...props }) => (
                        <tr {...props} />
                    ),
                    td: ({ ...props }) => (
                        <td className={styles.td} {...props} />
                    ),
                    th: ({ ...props }) => (
                        <th className={styles.th} {...props} />
                    ),

                    ol: ({ children }) => (
                        <div className={`${styles.customList} ${styles.orderedList}`}>
                            {children}
                        </div>
                    ),
                    ul: ({ children }) => (
                        <div className={`${styles.customList} ${styles.unorderedList}`}>
                            {children}
                        </div>
                    ),
                    li: ({ children, index }) => {
                        return (
                            <div className={styles.listItem}>
                                <div className={styles.listMarker} data-index={index + 1}></div>
                                <div className={styles.listContent}>{children}</div>
                            </div>
                        );
                    },

                    p: ({ children, ...props }) => (
                        <p {...props}>{children}</p>
                    ),

                    a: ({ ...props }) => (
                        <a target="_blank" rel="noopener noreferrer" {...props} />
                    ),
                    code: ({ inline, ...props }) => (
                        inline ? <code {...props} /> : <pre><code {...props} /></pre>
                    ),
                }}
            >
                {content}
            </ReactMarkdown>
        </div>
    );
};
