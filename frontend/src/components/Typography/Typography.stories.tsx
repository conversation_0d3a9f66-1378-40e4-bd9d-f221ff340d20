import React from "react";
import type { <PERSON><PERSON>, StoryObj } from "@storybook/react";
import { Typography } from "./Typography";

const meta: Meta<typeof Typography> = {
  title: "Design System/Typography",
  component: Typography,
  parameters: {
    layout: "centered",
  },
};

export default meta;
type Story = StoryObj<typeof Typography>;

export const Headings: Story = {
  render: () => (
    <div style={{ display: "flex", gap: "64px" }}>
      <div className="typography-preview">
        <Typography variant="h2" font="ryker">
          Ryker Font
        </Typography>
        <div className="typography-preview__item">
          <Typography variant="h1" font="ryker">
            Heading 1
          </Typography>
          <div className="typography-preview__meta">
            Desktop: 60px/3.35rem | Mobile: 36px/2.55rem | Line height: 1.1em
          </div>
        </div>

        <div className="typography-preview__item">
          <Typography variant="h2" font="ryker">
            Heading 2
          </Typography>
          <div className="typography-preview__meta">
            Desktop: 48px/2.65rem | Mobile: 30px/2.15rem | Line height: 1.2em
          </div>
        </div>

        <div className="typography-preview__item">
          <Typography variant="h3" font="ryker">
            Heading 3
          </Typography>
          <div className="typography-preview__meta">
            Desktop: 32px/1.75rem | Mobile: 24px/1.3rem | Line height: 1.2em
          </div>
        </div>

        <div className="typography-preview__item">
          <Typography variant="h4" font="ryker">
            Heading 4
          </Typography>
          <div className="typography-preview__meta">
            Desktop: 18px/1rem | Mobile: 14px/1rem | Line height: 1.4em
          </div>
        </div>

        <div className="typography-preview__item">
          <Typography variant="h5" font="ryker">
            Heading 5
          </Typography>
          <div className="typography-preview__meta">
            Desktop: 16px/0.85rem | Mobile: 12px/0.85rem | Line height: 1.4em
          </div>
        </div>

        <div className="typography-preview__item">
          <Typography variant="h6" font="ryker">
            Heading 6
          </Typography>
          <div className="typography-preview__meta">
            Desktop: 14px/0.75rem | Mobile: 12px/0.75rem | Line height: 1.4em
          </div>
        </div>
      </div>

      <div className="typography-preview">
        <Typography variant="h2" font="quasimoda">
          Quasimoda Font
        </Typography>
        <div className="typography-preview__item">
          <Typography variant="h1" font="quasimoda">
            Heading 1
          </Typography>
          <div className="typography-preview__meta">
            Desktop: 60px/3.35rem | Mobile: 36px/2.55rem | Line height: 1.1em
          </div>
        </div>

        <div className="typography-preview__item">
          <Typography variant="h2" font="quasimoda">
            Heading 2
          </Typography>
          <div className="typography-preview__meta">
            Desktop: 48px/2.65rem | Mobile: 30px/2.15rem | Line height: 1.2em
          </div>
        </div>

        <div className="typography-preview__item">
          <Typography variant="h3" font="quasimoda">
            Heading 3
          </Typography>
          <div className="typography-preview__meta">
            Desktop: 32px/1.75rem | Mobile: 24px/1.3rem | Line height: 1.2em
          </div>
        </div>

        <div className="typography-preview__item">
          <Typography variant="h4" font="quasimoda">
            Heading 4
          </Typography>
          <div className="typography-preview__meta">
            Desktop: 18px/1rem | Mobile: 14px/1rem | Line height: 1.4em
          </div>
        </div>

        <div className="typography-preview__item">
          <Typography variant="h5" font="quasimoda">
            Heading 5
          </Typography>
          <div className="typography-preview__meta">
            Desktop: 16px/0.85rem | Mobile: 12px/0.85rem | Line height: 1.4em
          </div>
        </div>

        <div className="typography-preview__item">
          <Typography variant="h6" font="quasimoda">
            Heading 6
          </Typography>
          <div className="typography-preview__meta">
            Desktop: 14px/0.75rem | Mobile: 12px/0.75rem | Line height: 1.4em
          </div>
        </div>
      </div>
    </div>
  ),
};

export const BodyText: Story = {
  render: () => (
    <div style={{ display: "flex", gap: "64px" }}>
      {/* Ryker Column */}
      <div className="typography-preview">
        <Typography variant="h2" font="ryker">
          Ryker Font
        </Typography>
        <div className="typography-preview__item">
          <Typography variant="body-l" font="ryker">
            Body Large
          </Typography>
          <div className="typography-preview__meta">
            Desktop: 20px/1.1rem | Mobile: 15px/1.05rem | Line height: 1.5em
          </div>
        </div>

        <div className="typography-preview__item">
          <Typography variant="body-l-bold" font="ryker">
            Body Large Bold
          </Typography>
          <div className="typography-preview__meta">
            Desktop: 20px/1.1rem | Mobile: 15px/1.05rem | Line height: 1.5em
          </div>
        </div>

        <div className="typography-preview__item">
          <Typography variant="body" font="ryker">
            Body
          </Typography>
          <div className="typography-preview__meta">
            Desktop: 18px/1rem | Mobile: 14px/0.875rem | Line height: 1.5em
          </div>
        </div>

        <div className="typography-preview__item">
          <Typography variant="body-bold" font="ryker">
            Body Bold
          </Typography>
          <div className="typography-preview__meta">
            Desktop: 18px/1rem | Mobile: 14px/0.875rem | Line height: 1.5em
          </div>
        </div>

        <div className="typography-preview__item">
          <Typography variant="body-s" font="ryker">
            Body Small
          </Typography>
          <div className="typography-preview__meta">
            Desktop: 16px/0.85rem | Mobile: 12px/0.75rem | Line height: 1.5em
          </div>
        </div>

        <div className="typography-preview__item">
          <Typography variant="body-s-bold" font="ryker">
            Body Small Bold
          </Typography>
          <div className="typography-preview__meta">
            Desktop: 16px/0.85rem | Mobile: 12px/0.75rem | Line height: 1.5em
          </div>
        </div>
      </div>

      {/* Quasimoda Column */}
      <div className="typography-preview">
        <Typography variant="h2" font="quasimoda">
          Quasimoda Font
        </Typography>
        <div className="typography-preview__item">
          <Typography variant="body-l" font="quasimoda">
            Body Large
          </Typography>
          <div className="typography-preview__meta">
            Desktop: 20px/1.1rem | Mobile: 15px/1.05rem | Line height: 1.5em
          </div>
        </div>

        <div className="typography-preview__item">
          <Typography variant="body-l-bold" font="quasimoda">
            Body Large Bold
          </Typography>
          <div className="typography-preview__meta">
            Desktop: 20px/1.1rem | Mobile: 15px/1.05rem | Line height: 1.5em
          </div>
        </div>

        <div className="typography-preview__item">
          <Typography variant="body" font="quasimoda">
            Body
          </Typography>
          <div className="typography-preview__meta">
            Desktop: 18px/1rem | Mobile: 14px/0.875rem | Line height: 1.5em
          </div>
        </div>

        <div className="typography-preview__item">
          <Typography variant="body-bold" font="quasimoda">
            Body Bold
          </Typography>
          <div className="typography-preview__meta">
            Desktop: 18px/1rem | Mobile: 14px/0.875rem | Line height: 1.5em
          </div>
        </div>

        <div className="typography-preview__item">
          <Typography variant="body-s" font="quasimoda">
            Body Small
          </Typography>
          <div className="typography-preview__meta">
            Desktop: 16px/0.85rem | Mobile: 12px/0.75rem | Line height: 1.5em
          </div>
        </div>

        <div className="typography-preview__item">
          <Typography variant="body-s-bold" font="quasimoda">
            Body Small Bold
          </Typography>
          <div className="typography-preview__meta">
            Desktop: 16px/0.85rem | Mobile: 12px/0.75rem | Line height: 1.5em
          </div>
        </div>
      </div>
    </div>
  ),
};

export const SampleText: Story = {
  render: () => (
    <div style={{ display: "flex", gap: "64px" }}>
      {/* Ryker Column */}
      <div className="typography-preview" style={{ maxWidth: "400px" }}>
        <Typography variant="h2" font="ryker">
          Ryker Font
        </Typography>
        <Typography variant="h1" font="ryker">
          The Quick Brown Fox
        </Typography>
        <Typography variant="body-l" font="ryker">
          Lorem ipsum dolor sit amet, consectetur adipiscing elit.
        </Typography>
      </div>

      {/* Quasimoda Column */}
      <div className="typography-preview" style={{ maxWidth: "400px" }}>
        <Typography variant="h2" font="quasimoda">
          Quasimoda Font
        </Typography>
        <Typography variant="h1" font="quasimoda">
          The Quick Brown Fox
        </Typography>
        <Typography variant="body-l" font="quasimoda">
          Lorem ipsum dolor sit amet, consectetur adipiscing elit.
        </Typography>
      </div>
    </div>
  ),
};
