import { useState, useEffect, RefObject, useCallback } from 'react';

interface UsePlacementProps {
  triggerRef: RefObject<HTMLDivElement>;
  menuRef: RefObject<HTMLDivElement>;
  defaultPlacement: 'left' | 'right';
  isOpen: boolean;
}

export const usePlacement = ({
  triggerRef,
  menuRef,
  defaultPlacement,
  isOpen
}: UsePlacementProps) => {
  const [placement, setPlacement] = useState(defaultPlacement);

  const calculateOptimalPlacement = useCallback(() => {
    if (!triggerRef.current || !menuRef.current) return;

    const triggerRect = triggerRef.current.getBoundingClientRect();
    const menuRect = menuRef.current.getBoundingClientRect();
    const windowWidth = window.innerWidth;

    const spaceLeft = triggerRect.left;
    const spaceRight = windowWidth - triggerRect.right;

    if (defaultPlacement === 'left' && spaceRight < menuRect.width && spaceLeft > spaceRight) {
      setPlacement('right');
    } else if (defaultPlacement === 'right' && spaceLeft < menuRect.width && spaceRight > spaceLeft) {
      setPlacement('left');
    } else {
      setPlacement(defaultPlacement);
    }
  }, [triggerRef, menuRef, defaultPlacement]);

  useEffect(() => {
    if (isOpen) {
      calculateOptimalPlacement();
      window.addEventListener('resize', calculateOptimalPlacement);
    }
    return () => window.removeEventListener('resize', calculateOptimalPlacement);
  }, [isOpen, calculateOptimalPlacement]);

  return placement;
}; 