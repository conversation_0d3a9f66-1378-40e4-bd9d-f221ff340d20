.dropdown {
  position: relative;
  display: inline-block;
}

.trigger {
  cursor: pointer;
  padding: 4px;
  margin-right: 10px;
}

.menu {
  position: fixed;
  background: white;
  border-radius: 12px;
  box-shadow: 0px 2px 4px -2px rgba(0, 0, 0, 0.05),
              0px 4px 6px -1px rgba(0, 0, 0, 0.1);
  min-width: 200px;
  z-index: 1000;
  padding: 8px 0;
}

.item {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
  padding: var(--spacing-2) var(--spacing-3);
  width: 100%;
  height: 37px;
  color: var(--colors-gray-700);
  text-decoration: none;
  cursor: pointer;
  transition: all 0.2s ease-in-out;
  font-family: 'Quasimoda', sans-serif;
  font-size: 16px;
  line-height: 21px;
  font-weight: 400;
  background: none;
  border: none;

  &:hover {
    background-color: var(--colors-gray-50);
  }

  svg {
    width: 16px;
    height: 16px;
    color: var(--colors-gray-500);
  }

  &.danger {
    color: var(--colors-red-600);
    
    svg {
      color: var(--colors-red-500);
    }

    &:hover {
      background-color: var(--colors-red-50);
    }
  }

  &.disabled {
    opacity: 0.5;
    cursor: not-allowed;
    pointer-events: none;
  }

  &.loading {
    position: relative;
    cursor: wait;
    
    .loader {
      position: absolute;
      right: var(--spacing-3);
      width: 14px;
      height: 14px;
      border: 2px solid var(--colors-gray-200);
      border-top-color: var(--colors-gray-500);
      border-radius: 50%;
      animation: spin 1s linear infinite;
    }
  }

  .externalIcon {
    margin-left: auto;
    width: 12px;
    height: 12px;
    color: var(--colors-gray-400);
  }
}

.divider {
  width: 100%;
  height: 1px;
  background-color: var(--colors-gray-200);
  margin: var(--spacing-1) 0;
}

.menuButton {
  height: 40px;
}

.icon {
  display: flex;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}