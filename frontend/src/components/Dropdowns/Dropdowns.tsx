import React, { useState, useRef, useEffect } from 'react';
import classNames from 'classnames';
import styles from './Dropdowns.module.scss';
import { DropdownsProps } from './Dropdowns.types';
import { useDropdownPosition } from './hooks/useDropdownPosition';
import { usePlacement } from './hooks/usePlacement';
import { ArrowUpRight01Icon } from '@hugeicons/react';

export const Dropdowns: React.FC<DropdownsProps> = ({
  items,
  trigger,
  className,
  menuClassName,
  itemClassName,
  placement: defaultPlacement = 'left',
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const dropdownRef = useRef<HTMLDivElement>(null);
  const menuRef = useRef<HTMLDivElement>(null);
  const triggerRef = useRef<HTMLDivElement>(null);

  const placement = usePlacement({
    triggerRef,
    menuRef,
    defaultPlacement,
    isOpen
  });

  const { menuStyle } = useDropdownPosition({
    triggerRef,
    menuRef,
    isOpen,
    placement
  });

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  return (
    <div ref={dropdownRef} className={classNames(styles.dropdown, className)}>
      <div ref={triggerRef} className={styles.trigger} onClick={() => setIsOpen(!isOpen)}>
        {trigger}
      </div>
      
      {isOpen && (
        <div 
          ref={menuRef}
          className={classNames(styles.menu, menuClassName)}
          style={menuStyle}
        >
          {items.map((item, index) => {
            const ItemTag = item.href ? 'a' : 'button';
            return (
              <React.Fragment key={index}>
                {item.divider && <div className={styles.divider} />}
                <ItemTag
                  className={classNames(styles.item, {
                    [styles.danger]: item.variant === 'danger',
                    [styles.disabled]: item.disabled,
                    [styles.loading]: item.loading
                  }, itemClassName)}
                  onClick={(e) => {
                    if (item.disabled) {
                      e.preventDefault();
                      return;
                    }
                    item.onClick?.();
                    if (!item.loading) {
                      setIsOpen(false);
                    }
                  }}
                  href={item.href}
                  target={item.href ? "_blank" : undefined}
                  rel={item.href ? "noopener noreferrer" : undefined}
                  disabled={item.disabled}
                >
                  {item.icon && <span className={styles.icon}>{item.icon}</span>}
                  <span className={styles.label}>{item.label}</span>
                  {item.loading && <span className={styles.loader} />}
                  {item.href && <ArrowUpRight01Icon className={styles.externalIcon} />}
                </ItemTag>
              </React.Fragment>
            );
          })}
        </div>
      )}
    </div>
  );
};
