import React from 'react';
import type { Meta, StoryObj } from '@storybook/react';
import { Dropdowns } from './Dropdowns';
import { Button } from '../Button';
import { ButtonType, ButtonSize } from '../Button/Button.types';
import { Activity01Icon, Link01Icon, Settings01Icon, AlertCircleIcon, Logout02Icon, Menu01Icon } from '@hugeicons/react';
import styles from './Dropdowns.module.scss';
import classNames from 'classnames';
import { DropdownItem } from './Dropdowns.types';

const meta = {
  title: 'Components/Dropdowns',
  component: Dropdowns,
  parameters: {
    layout: 'centered',
  },
  argTypes: {
    placement: {
      control: 'radio',
      options: ['left', 'right'],
      description: 'Menu placement',
    },
    showIcon: {
      control: 'boolean',
      defaultValue: false,
      description: 'Show menu icon'
    },
    menuIcon: {
      control: 'select',
      options: Object.keys(["a"]).filter(key => key.endsWith('Icon')),
      defaultValue: 'Menu01Icon',
      description: 'Menu icon variant'
    },
    items: {
      table: {
        disable: true
      }
    },
    trigger: {
      table: {
        disable: true
      }
    },
    className: {
      table: {
        disable: true
      }
    },
    menuClassName: {
      table: {
        disable: true
      }
    },
    itemClassName: {
      table: {
        disable: true
      }
    }
  }
} satisfies Meta<typeof Dropdowns>;

export default meta;
type Story = StoryObj<typeof Dropdowns>;

const defaultItems: DropdownItem[] = [
  {
    icon: <Activity01Icon />,
    label: 'Regular Button',
    onClick: () => console.log('Regular button clicked'),
  },
  {
    icon: <Link01Icon />,
    label: 'External Link',
    href: 'https://example.com',
    onClick: () => console.log('Link clicked'),
  },
  {
    icon: <Settings01Icon />,
    label: 'With Loading',
    onClick: () => new Promise(resolve => setTimeout(resolve, 1000)),
    loading: true,
  },
  {
    icon: <AlertCircleIcon />,
    label: 'Disabled Button',
    onClick: () => console.log('Should not be called'),
    disabled: true,
  },
  {
    icon: <Logout02Icon />,
    label: 'Sign Out',
    variant: 'danger' as const,
    divider: true,
    onClick: () => console.log('Sign out clicked'),
  },
];

export const Default: Story = {
  args: {
    items: defaultItems,
    placement: 'left',
    showIcon: false,
    menuIcon: 'Menu01Icon'
  },
  render: (args) => {
    const { showIcon, ...rest } = args;
    return (
      <Dropdowns
        {...rest}
        trigger={
          <Button
            className={classNames(styles.iconButton, showIcon && styles.withIcon)}
            rightIcon={showIcon ? Menu01Icon : undefined}
            showRightIcon={showIcon}
          >
            Menu
          </Button>
        }
      />
    );
  }
};

export const WithIcon: Story = {
  args: {
    showIcon: true,
    menuIcon: 'Menu01Icon',
    items: defaultItems,
  },
  render: (args) => {
    const { showIcon, ...rest } = args;
    return (
      <Dropdowns
        {...rest}
        trigger={
          <Button
            iconOnly
            leftIcon={Menu01Icon}
            showLeftIcon={showIcon}
            type={ButtonType.TERTIARY}
            size={ButtonSize.XS}
          />
        }
      />
    );
  }
};

export const AllVariants: Story = {
  render: () => (
    <div style={{ display: 'flex', flexDirection: 'column', gap: '32px' }}>
      {/* Icon Only Variants */}
      <div>
        <h3>Icon Only Variants</h3>
        <div style={{ display: 'flex', gap: '16px', marginTop: '16px' }}>
          {[ButtonType.PRIMARY, ButtonType.SECONDARY, ButtonType.TERTIARY].map((type) => (
            <Dropdowns
              key={type}
              trigger={
                <Button
                  type={type}
                  iconOnly
                  leftIcon={Menu01Icon}
                  showLeftIcon
                  size={ButtonSize.XS}
                />
              }
              items={defaultItems}
            />
          ))}
        </div>
      </div>

      {/* Placements */}
      <div>
        <h3>Placements</h3>
        <div style={{ display: 'flex', gap: '16px', marginTop: '16px' }}>
          {['left', 'right'].map((placement) => (
            <Dropdowns
              key={placement}
              placement={placement as 'left' | 'right'}
              trigger={
                <Button
                  className={styles.iconButton}
                  type={ButtonType.PRIMARY}
                  rightIcon={Menu01Icon}
                  showRightIcon
                >
                  {placement}
                </Button>
              }
              items={defaultItems}
            />
          ))}
        </div>
      </div>
    </div>
  ),
};
