import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { vi } from 'vitest';
import { Dropdowns } from '../Dropdowns';
import { Button } from '../../Button';
import styles from '../Dropdowns.module.scss';

describe('Dropdowns Component', () => {
  const mockOnClick = vi.fn();
  const defaultItems = [
    {
      icon: <div data-testid="mock-icon">Icon1</div>,
      label: 'First Action',
      onClick: mockOnClick,
    },
    {
      icon: <div data-testid="mock-icon">Icon2</div>,
      label: 'Second Action',
      onClick: mockOnClick,
    },
  ];

  const defaultProps = {
    items: defaultItems,
    trigger: <Button>Menu</Button>,
    placement: 'left' as const,
  };

  beforeEach(() => {
    mockOnClick.mockClear();
  });

  describe('Rendering', () => {
    it('renders trigger button correctly', () => {
      render(<Dropdowns {...defaultProps} />);
      expect(screen.getByText('Menu')).toBeInTheDocument();
    });

    it('does not show menu initially', () => {
      render(<Dropdowns {...defaultProps} />);
      expect(screen.queryByText('First Action')).not.toBeInTheDocument();
    });

    it('shows menu when trigger is clicked', () => {
      render(<Dropdowns {...defaultProps} />);
      fireEvent.click(screen.getByText('Menu'));
      expect(screen.getByText('First Action')).toBeInTheDocument();
    });
  });

  describe('Interactions', () => {
    it('calls item onClick handler when clicked', () => {
      render(<Dropdowns {...defaultProps} />);
      fireEvent.click(screen.getByText('Menu'));
      fireEvent.click(screen.getByText('First Action'));
      expect(mockOnClick).toHaveBeenCalledTimes(1);
    });

    it('closes menu after item click', async () => {
      render(<Dropdowns {...defaultProps} />);
      fireEvent.click(screen.getByText('Menu'));
      fireEvent.click(screen.getByText('First Action'));
      await waitFor(() => {
        expect(screen.queryByText('First Action')).not.toBeInTheDocument();
      });
    });

    it('closes menu when clicking outside', () => {
      render(
        <div>
          <div data-testid="outside">Outside</div>
          <Dropdowns {...defaultProps} />
        </div>
      );
      
      fireEvent.click(screen.getByText('Menu'));
      expect(screen.getByText('First Action')).toBeInTheDocument();
      
      fireEvent.mouseDown(screen.getByTestId('outside'));
      expect(screen.queryByText('First Action')).not.toBeInTheDocument();
    });
  });

  describe('Placement', () => {
    it.each(['left', 'right'] as const)(
      'applies correct placement for %s',
      (placement) => {
        render(<Dropdowns {...defaultProps} placement={placement} />);
        fireEvent.click(screen.getByText('Menu'));
        const menu = screen.getByText('First Action').parentElement;
        expect(menu).toBeInTheDocument();
      }
    );
  });

  describe('Styling', () => {
    it('applies custom className to wrapper', () => {
      render(<Dropdowns {...defaultProps} className="custom-class" />);
      const wrapper = screen.getByText('Menu').closest(`.${styles.dropdown}`);
      expect(wrapper).toHaveClass('custom-class');
    });

    it('applies custom menuClassName to menu', () => {
      render(<Dropdowns {...defaultProps} menuClassName="custom-menu" />);
      fireEvent.click(screen.getByText('Menu'));
      const menu = screen.getByText('First Action').closest(`.${styles.menu}`);
      expect(menu).toHaveClass('custom-menu');
    });

    it('applies custom itemClassName to items', () => {
      render(<Dropdowns {...defaultProps} itemClassName="custom-item" />);
      fireEvent.click(screen.getByText('Menu'));
      const button = screen.getByText('First Action').closest('button');
      expect(button).toHaveClass('custom-item');
    });
  });

  describe('Special Items', () => {
    it('renders divider correctly', () => {
      const itemsWithDivider = [
        ...defaultItems,
        {
          icon: <div data-testid="mock-icon">Icon3</div>,
          label: 'Divided Action',
          divider: true,
          onClick: mockOnClick,
        },
      ];
      render(<Dropdowns {...defaultProps} items={itemsWithDivider} />);
      fireEvent.click(screen.getByText('Menu'));
      expect(document.querySelector(`.${styles.divider}`)).toBeInTheDocument();
    });

    it('applies danger variant correctly', () => {
      const itemsWithDanger = [
        ...defaultItems,
        {
          icon: <div data-testid="mock-icon">Icon3</div>,
          label: 'Danger Action',
          variant: 'danger' as const,
          onClick: mockOnClick,
        },
      ];
      render(<Dropdowns {...defaultProps} items={itemsWithDanger} />);
      fireEvent.click(screen.getByText('Menu'));
      
      const dangerButton = screen.getByText('Danger Action').closest('button');
      expect(dangerButton).toHaveClass(styles.danger);
    });
  });
});
