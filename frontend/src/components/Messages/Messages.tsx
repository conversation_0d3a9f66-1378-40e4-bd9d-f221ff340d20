'use client';

import React, { memo, useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { Message } from '@/components/Message';
import styles from './Messages.module.scss';
import { MessagesProps } from './Messages.types';
import { MessageTypeValue } from '@/types/messages';

import { useAuth, useUser } from '@clerk/nextjs';
import { useUniversalAuth } from '@/hooks/useUniversalAuth';
import { useWidgets } from '@/hooks/useWidgets';
import { useMessages } from '@/hooks/useMessages';
import { fetchJob } from '@/api/jobs';
import { getCurrentUser } from '@/api/user';
import { useBreakpoint } from '@/utils/breakpointUtils';
import { Alert } from '../Alert/Alert';
import { AlertColor } from '../Alert/Alert.types';
import { Button } from '../Button';
import { ButtonColor, ButtonSize, ButtonState, ButtonType } from '../Button/Button.types';
import { useChats } from '@/hooks/useChats';
import useChatParams from '@/hooks/useChatParams';
import useMessageSender from '@/hooks/useMessageSender';
import { mapDocumentsToAttachments } from '@/utils/messageUtils';
import { fetchDocuments } from '@/api/documents';
import { useStreamingState } from '@/hooks/useStreamingState';
import { UserInfoModal } from '../UserInfoModal';
import useJobSubmissionValidation from '@/hooks/useJobSubmissionValidation';

interface MessageDocument {
  id: number;
  category?: string;
}

interface MessageWithDocuments {
  id: number;
  senderType: 'user' | 'system' | 'customer_support';
  documents?: MessageDocument[];
}

// Memoized component for handling notification tags per message
const NotificationTagManager: React.FC<{
  messageId: number;
  messageNotificationTags: Record<number, boolean>;
  messages: MessageWithDocuments[];
  children: (showNotificationTag: boolean) => React.ReactNode;
}> = memo(({ messageId, messageNotificationTags, messages, children }) => {
  const showNotificationTag = useMemo(() => {
    // Find the message and its index
    const messageIndex = messages.findIndex((msg) => msg.id === messageId);
    const message = messages[messageIndex];

    if (!message || message.senderType !== 'system' || messageIndex >= messages.length - 1) {
      return false;
    }

    // Check the previous user message (next in array = previous chronologically)
    const prevMsg = messages[messageIndex + 1];
    if (!prevMsg || prevMsg.senderType !== 'user') {
      return false;
    }

    // Check both the updated message documents and the notification tag state
    const hasExistingRelevantDocs =
      Array.isArray(prevMsg.documents) &&
      prevMsg.documents.some((doc: MessageDocument) =>
        ['appliance', 'files', 'property details'].includes(doc.category?.toLowerCase?.() || '')
      );

    const hasNotificationTag = messageNotificationTags[prevMsg.id] || false;

    return hasExistingRelevantDocs || hasNotificationTag;
  }, [messageId, messageNotificationTags, messages]);

  return <>{children(showNotificationTag)}</>;
});

NotificationTagManager.displayName = 'NotificationTagManager';

export const Messages: React.FC<MessagesProps> = ({
  messages,
  isLoading,
  hasMore,
  isSubmitting,
  onLoadMore,
  anchorRef,
  optimisticMessage,
}): JSX.Element => {
  const { jobSummaryConfirmed, setJobSummaryConfirmed, messageHeight } = useMessages();
  const sentinelRef = useRef(null);
  const containerRef = useRef<HTMLDivElement>(null);
  const prevMessagesLengthRef = useRef(messages.length);
  const { getToken } = useUniversalAuth();
  const { chatId } = useChatParams();
  const { user, isLoaded } = useUser();
  const { isDesktop } = useBreakpoint();
  const {
    isRetryButtonShown,
    isMessagesSpacingActive,
    setIsMessagesSpacingActive,
    updateMessageDocuments,
  } = useChats();
  const { sendMessage } = useMessageSender();
  const { isStreamingMessage } = useStreamingState();
  const isWaitingForMessage = isSubmitting || messages.length === 0;

  // Document polling state
  const pollingIntervalRef = useRef<NodeJS.Timeout | null>(null);
  const POLLING_INTERVAL = 5000; // 5 seconds

  // Notification tag state for each message - locked during streaming to prevent flickering
  const [messageNotificationTags, setMessageNotificationTags] = useState<Record<number, boolean>>(
    {}
  );

  // Lock updates during streaming to prevent flickering
  const [lockedNotificationTags, setLockedNotificationTags] = useState<Record<number, boolean>>({});
  const isStreamingRef = useRef(false);

  // Stable messages during streaming to prevent re-renders
  const [streamingMessages, setStreamingMessages] = useState<typeof messages>([]);
  const messagesRef = useRef(messages);

  // UserInfoModal state
  const [isUserInfoModalOpen, setIsUserInfoModalOpen] = useState(false);

  const [showItLooksGoodMessage, setShowItLooksGoodMessage] = useState(false);
  const [userDetailsValidated, setUserDetailsValidated] = useState(false);
  const [isValidationErrorShown, setIsValidationErrorShown] = useState(false);
  const [isValidatingDetails, setIsValidatingDetails] = useState(false);

  // Keep messages ref updated
  useEffect(() => {
    messagesRef.current = messages;
  }, [messages]);

  // Track streaming state - capture messages only when streaming starts
  useEffect(() => {
    if (isStreamingMessage && !isStreamingRef.current) {
      // Started streaming - lock current notification tags and capture current messages
      isStreamingRef.current = true;
      setLockedNotificationTags(messageNotificationTags);

      // Create stable user message for streaming - use optimistic message
      let stableUserMessage = null;

      if (optimisticMessage) {
        // Use optimistic message as stable user message
        stableUserMessage = {
          id: -1, // Temporary ID for optimistic message
          content: optimisticMessage.content,
          attachments: optimisticMessage.attachments || [],
          type: MessageTypeValue.Text,
          timestamp: new Date().toISOString(),
          senderType: 'user' as const,
          isOptimistic: true,
        };
      } else if (messagesRef.current.length > 1) {
        // Fallback to second message if no optimistic message
        stableUserMessage = messagesRef.current[1];
      }

      // Store only the stable user message
      setStreamingMessages(stableUserMessage ? [stableUserMessage] : []);
    } else if (!isStreamingMessage && isStreamingRef.current) {
      // Finished streaming - unlock and apply any pending updates
      isStreamingRef.current = false;
      setLockedNotificationTags({});
      setStreamingMessages([]);
    }
  }, [isStreamingMessage, messageNotificationTags, optimisticMessage]);

  // Use locked tags during streaming, regular tags otherwise
  const activeNotificationTags = isStreamingMessage
    ? lockedNotificationTags
    : messageNotificationTags;

  // During streaming, use stable messages to prevent re-renders
  const activeMessages = useMemo(() => {
    if (isStreamingMessage && streamingMessages.length > 0) {
      // During streaming: use live AI response + stable user message
      const result = [];

      // Add live AI response (first message in current messages array)
      if (messages.length > 0) {
        result.push(messages[0]); // Live streaming AI message
      }

      // Add stable user message (stored in streamingMessages[0])
      result.push(streamingMessages[0]); // Stable user message from optimistic

      return result;
    }
    // Normal operation: render all messages
    return messages;
  }, [isStreamingMessage, streamingMessages, messages]);

  // Reset notification tags when chat changes
  useEffect(() => {
    if (!isStreamingMessage) {
      setMessageNotificationTags({});
      setLockedNotificationTags({});
      setShowItLooksGoodMessage(false);
      setUserDetailsValidated(false);
      setIsValidationErrorShown(false);
    }
  }, [chatId, isStreamingMessage]);

  // Index of user messages with their document IDs for quick lookup
  const userMessagesIndex = useMemo(() => {
    const index: Record<number, number[]> = {};
    messages.forEach((message) => {
      if (message.senderType === 'user' && Array.isArray(message.documents)) {
        index[message.id] = message.documents.map((doc) => doc.id);
      }
    });
    return index;
  }, [messages]);

  const { fetchProperties, addressValue } = useWidgets();

  const { areRequiredFieldsFilled } = useJobSubmissionValidation();

  // Document polling logic - pause during streaming to prevent flickering
  const pollDocuments = useCallback(async () => {
    // Skip polling if currently streaming to prevent flickering
    if (isStreamingMessage) return;

    try {
      const token = await getToken();
      if (!token) return;

      const documents = await fetchDocuments(token, 'chat', 'processingCompleted');

      // Update notification tags for all user messages
      const updatedTags: Record<number, boolean> = {};

      Object.entries(userMessagesIndex).forEach(([messageIdStr, documentIds]) => {
        const messageId = parseInt(messageIdStr, 10);

        // Get the current message to compare documents
        const currentMessage = messages.find((m) => m.id === messageId);

        // Check if any of this message's documents have been updated with categories
        const relevantPolledDocs = documents.filter((doc) => documentIds.includes(doc.id));
        const hasRelevantDocs = relevantPolledDocs.some((doc) =>
          ['appliance', 'files', 'property details'].includes(doc.category?.toLowerCase?.() || '')
        );

        // Update message documents if they have been processed since last time
        if (relevantPolledDocs.length > 0 && currentMessage && chatId) {
          const currentDocuments = currentMessage.documents || [];
          const needsUpdate = relevantPolledDocs.some((polledDoc) => {
            const currentDoc = currentDocuments.find((cd) => cd.id === polledDoc.id);
            return (
              !currentDoc ||
              currentDoc.category !== polledDoc.category ||
              currentDoc.status !== polledDoc.status
            );
          });

          if (needsUpdate) {
            // Update the message with fresh document data
            // Map DocumentDto to MessageDocument, handling null values
            const processedDocs = relevantPolledDocs.map((doc) => ({
              id: doc.id,
              fileName: doc.fileName,
              sizeInKiloBytes: doc.sizeInKiloBytes,
              browserMimeType: doc.browserMimeType,
              createdAt: doc.createdAt,
              uploadContext: doc.uploadContext || 'chat',
              status: doc.status,
              category: doc.category || undefined,
              label: doc.label || undefined,
              hasStatusBeenDisplayed: false,
            }));
            updateMessageDocuments(chatId, messageId, processedDocs);
          }
        }

        updatedTags[messageId] = hasRelevantDocs;
      });

      // Only update if not streaming
      if (!isStreamingMessage) {
        setMessageNotificationTags(updatedTags);
      }
    } catch (error) {
      console.error('Error polling documents:', error);
    }
  }, [getToken, userMessagesIndex, messages, chatId, updateMessageDocuments, isStreamingMessage]);

  // Set up document polling with streaming-aware interval
  useEffect(() => {
    if (pollingIntervalRef.current) {
      clearInterval(pollingIntervalRef.current);
    }

    pollingIntervalRef.current = setInterval(pollDocuments, POLLING_INTERVAL);
    void pollDocuments();

    return () => {
      if (pollingIntervalRef.current) {
        clearInterval(pollingIntervalRef.current);
        pollingIntervalRef.current = null;
      }
    };
  }, [pollDocuments]);

  useEffect(() => {
    const sentinel = sentinelRef.current;

    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting && hasMore && !isLoading) {
            onLoadMore?.();
          }
        });
      },
      {
        root: containerRef.current,
        threshold: 0.1,
      }
    );

    if (sentinel) {
      observer.observe(sentinel);
    }

    return () => {
      if (sentinel) {
        observer.unobserve(sentinel);
      }
    };
  }, [hasMore, isLoading, onLoadMore]);

  useEffect(() => {
    setTimeout(() => {
      if (
        anchorRef?.current &&
        (activeMessages.length > prevMessagesLengthRef.current || isWaitingForMessage)
      ) {
        anchorRef.current.scrollIntoView();
        prevMessagesLengthRef.current = activeMessages.length;
      }
    }, 0);
  }, [activeMessages, isWaitingForMessage, anchorRef, optimisticMessage]);

  useEffect(() => {
    const loadProperties = async () => {
      const token = await getToken();
      if (token) {
        fetchProperties(token);
      }
    };

    loadProperties();
  }, [getToken, fetchProperties]);

  const checkJobSummaryStatus = useCallback(async () => {
    if (activeMessages[0]?.additionalData?.jobSummary?.jobId) {
      try {
        const token = await getToken();
        if (!token) return;
        const jobId = activeMessages[0].additionalData.jobSummary.jobId;
        const response = await fetchJob({
          jobId: Number(jobId),
          token,
        });
        const jobStatus = response.status;
        const isConfirmed = jobStatus === 'user_accepted';
        setJobSummaryConfirmed(isConfirmed);
      } catch (error) {
        console.error('Error checking job status:', error);
        setJobSummaryConfirmed(false);
      }
    } else {
      setJobSummaryConfirmed(false);
    }
  }, [activeMessages, getToken, setJobSummaryConfirmed]);

  useEffect(() => {
    checkJobSummaryStatus();
  }, [checkJobSummaryStatus]);

  useEffect(() => {
    setIsMessagesSpacingActive(false);
  }, [chatId, setIsMessagesSpacingActive]);

  // eslint-disable-next-line react-hooks/exhaustive-deps

  const handleClickRetry = async () => {
    const { value, attachmentsList } = isRetryButtonShown
      ? {
          value: optimisticMessage?.content,
          attachmentsList: optimisticMessage?.attachments,
        }
      : {
          value: activeMessages[0]?.content,
          attachmentsList: activeMessages[0]?.attachments,
        };

    if (value) {
      await sendMessage(value, attachmentsList);
    }
  };

  const handleDetailsLookGood = async () => {
    setIsValidatingDetails(true);
    setIsValidationErrorShown(false);

    const maxRetries = 3;
    const retryDelay = 1000;

    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      if (attempt > 1) {
        await new Promise((resolve) => setTimeout(resolve, retryDelay));
      }

      try {
        const userDetails = await getCurrentUser({ token: await getToken() });

        if (!!userDetails.canUserAcceptJobs) {
          setUserDetailsValidated(true);
          setShowItLooksGoodMessage(true);
          setIsValidatingDetails(false);
          setTimeout(() => {
            scrollToBottom();
          }, 100);
          return;
        }
      } catch (retryError) {
        console.error(`Retry attempt ${attempt} failed:`, retryError);
      }
    }

    setIsValidationErrorShown(true);
    setIsValidatingDetails(false);
  };

  const scrollToBottom = useCallback(() => {
    if (anchorRef?.current) {
      anchorRef.current.scrollIntoView();
    }
  }, [anchorRef]);

  const wrapperHeight = useMemo(() => {
    const { optimistic = 0, lastSystem = 0 } = messageHeight || {};
    const height = optimistic + lastSystem;
    const heightWithAttachments = optimisticMessage?.attachments?.length ? height + 112 : height;
    const totalHeight = isDesktop ? heightWithAttachments : heightWithAttachments - 16;

    return totalHeight && !isRetryButtonShown && isMessagesSpacingActive
      ? `calc(-240px + 100dvh - ${totalHeight}px)`
      : 0;
  }, [
    messageHeight,
    optimisticMessage?.attachments?.length,
    isDesktop,
    isRetryButtonShown,
    isMessagesSpacingActive,
  ]);

  return (
    <div ref={containerRef} className={styles.messagesContainer}>
      <div className={styles.messagesWrapper}>
        <div ref={anchorRef}></div>
        <div
          style={{
            height: wrapperHeight,
          }}
        ></div>
        {isRetryButtonShown && (
          <Alert
            inline
            color={AlertColor.WARNING}
            headingText="It looks like the service is temporarily unavailable, please try again in a few minutes"
            button={
              <Button
                type={ButtonType.PRIMARY}
                color={ButtonColor.ORANGE}
                size={ButtonSize.XS}
                onClick={handleClickRetry}
              >
                Retry
              </Button>
            }
          />
        )}
        {isWaitingForMessage && <Message isAIThinking={isSubmitting} />}
        {isWaitingForMessage && optimisticMessage && (
          <Message
            key={'__OptimisticMessage__'}
            message={{
              id: -1,
              content: optimisticMessage.content,
              attachments: mapDocumentsToAttachments(optimisticMessage?.attachments),
              type: MessageTypeValue.Text,
              timestamp: new Date().toISOString(),
              senderType: 'user',
              isOptimistic: true,
            }}
          />
        )}
        {!isWaitingForMessage &&
          activeMessages.map((message, index) => {
            const jobSummary = message.additionalData?.jobSummary;
            const isMostRecentJobSummary =
              jobSummary &&
              !activeMessages.slice(0, index).some((m) => m.additionalData?.jobSummary);
            const isLastMessage = index === 0;
            const shouldDisableButtons = jobSummaryConfirmed || !isLastMessage;
            const shouldDisableItLooksGood =
              shouldDisableButtons ||
              !areRequiredFieldsFilled ||
              showItLooksGoodMessage ||
              isValidatingDetails;
            // Create stable key during streaming to prevent unnecessary re-renders
            const messageKey = `${message.id}${message.timestamp}`;

            if (isMostRecentJobSummary) {
              return (
                <div key={messageKey}>
                  {!areRequiredFieldsFilled ? (
                    <Message
                      key="contact-details-missing-message"
                      message={{
                        id: -998, // Stable negative ID to avoid conflicts with real messages
                        content: 'Please review your contact details to proceed.',
                        attachments: [],
                        type: MessageTypeValue.Text,
                        timestamp: '2024-01-01T00:00:00.000Z', // Stable timestamp
                        senderType: 'system',
                      }}
                    >
                      <div className={styles.userInfoSummary}>
                        <div className={styles.userInfoActions}>
                          <div className={styles.actionButtons}>
                            <Button
                              type={ButtonType.PRIMARY}
                              color={ButtonColor.GREEN_PRIMARY}
                              size={ButtonSize.BASE}
                              onClick={() => setIsUserInfoModalOpen(true)}
                              state={ButtonState.DEFAULT}
                            >
                              Enter details securely
                            </Button>
                          </div>
                        </div>
                      </div>
                    </Message>
                  ) : (
                    <>
                      <Message
                        key="user-info-summary-message"
                        message={{
                          id: -999, // Stable negative ID to avoid conflicts with real messages
                          content: 'This is what I already know about you and your place:',
                          attachments: [],
                          type: MessageTypeValue.Text,
                          timestamp: '2024-01-01T00:00:00.000Z', // Stable timestamp
                          senderType: 'system',
                        }}
                      >
                        <div className={styles.userInfoSummary}>
                          <div className={styles.userInfoContainer}>
                            <UserInfoModal
                              open={false}
                              onClose={() => {}}
                              readOnly={true}
                              inline={true}
                            />
                          </div>
                          <div className={styles.userInfoActions}>
                            <p className={styles.confirmationQuestion}>
                              Are your current details correct?
                            </p>
                            {isValidationErrorShown && (
                              <div className={styles.error}>
                                Please make sure you have provided all the required details and
                                verified your email and phone number. If you keep seeing this
                                message, please{' '}
                                <a
                                  href="https://help.heyalfie.com/help-centre/kb-tickets/new"
                                  target="_blank"
                                  rel="noopener noreferrer"
                                  className={styles.link}
                                >
                                  contact support
                                </a>
                              </div>
                            )}
                            <div className={styles.actionButtons}>
                              <Button
                                type={ButtonType.PRIMARY}
                                color={ButtonColor.GREEN_PRIMARY}
                                size={ButtonSize.BASE}
                                onClick={handleDetailsLookGood}
                                loading={isValidatingDetails}
                                state={
                                  shouldDisableItLooksGood
                                    ? ButtonState.DISABLED
                                    : ButtonState.DEFAULT
                                }
                              >
                                It looks good
                              </Button>
                              <Button
                                type={ButtonType.SECONDARY}
                                color={ButtonColor.GREEN_PRIMARY}
                                size={ButtonSize.BASE}
                                onClick={() => setIsUserInfoModalOpen(true)}
                                state={
                                  shouldDisableButtons ? ButtonState.DISABLED : ButtonState.DEFAULT
                                }
                              >
                                Edit securely
                              </Button>
                            </div>
                          </div>
                        </div>
                      </Message>
                      {shouldDisableItLooksGood && (
                        <Message
                          key="it-looks-good-fake-message"
                          message={{
                            id: -1000,
                            content: 'It looks good',
                            attachments: [],
                            type: MessageTypeValue.Text,
                            timestamp: new Date().toISOString(),
                            senderType: 'user',
                          }}
                          className={styles.fakeMessage}
                        />
                      )}
                    </>
                  )}

                  {user?.primaryPhoneNumber?.verification?.status === 'verified' &&
                    !!addressValue &&
                    userDetailsValidated && (
                      <NotificationTagManager
                        messageId={message.id}
                        messageNotificationTags={activeNotificationTags}
                        messages={activeMessages}
                      >
                        {(showNotificationTag) => (
                          <Message
                            message={message}
                            messages={activeMessages}
                            scrollToBottom={scrollToBottom}
                            showNotificationTag={showNotificationTag}
                          />
                        )}
                      </NotificationTagManager>
                    )}
                  {jobSummaryConfirmed && userDetailsValidated && (
                    <>
                      <Message
                        key={Date.now() + Date.now()}
                        message={{
                          id: Date.now() + Date.now(),
                          content: 'Yes, that looks good',
                          attachments: [],
                          type: MessageTypeValue.Text,
                          timestamp: new Date().toISOString(),
                          senderType: 'user',
                        }}
                      />
                      <Message
                        key={Date.now()}
                        message={{
                          id: Date.now(),
                          content: `Your job request is now in progress. I'm reaching out to trusted professionals and you'll receive an email with quotes soon.
                        Next steps:
                        - Keep an eye on your inbox for updates.
                        - If you have questions, just reply to my email.
                        I'll be in touch soon!`,
                          attachments: [],
                          type: MessageTypeValue.Text,
                          timestamp: new Date().toISOString(),
                          senderType: 'system',
                        }}
                      />
                    </>
                  )}
                </div>
              );
            }

            return (
              <div key={messageKey}>
                <NotificationTagManager
                  messageId={message.id}
                  messageNotificationTags={activeNotificationTags}
                  messages={activeMessages}
                >
                  {(showNotificationTag) => (
                    <Message
                      message={message}
                      messages={activeMessages}
                      scrollToBottom={scrollToBottom}
                      showNotificationTag={showNotificationTag}
                    />
                  )}
                </NotificationTagManager>
              </div>
            );
          })}
        {hasMore && <div ref={sentinelRef} style={{ height: 20 }} />}
      </div>
      <UserInfoModal open={isUserInfoModalOpen} onClose={() => setIsUserInfoModalOpen(false)} />
    </div>
  );
};
