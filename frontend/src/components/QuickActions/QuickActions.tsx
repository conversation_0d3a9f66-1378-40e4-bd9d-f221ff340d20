import React from 'react';
import { Button } from '@/components/Button';
import { ButtonSize, ButtonType } from '@/components/Button/Button.types';
import { MessageButton } from '@/utils/messageUtils';
import styles from './QuickActions.module.scss';
import { useBreakpoint } from '@/utils/breakpointUtils';
import useMessageSender from '@/hooks/useMessageSender';

interface QuickActionsProps {
  buttons: MessageButton[];
}

export const QuickActions: React.FC<QuickActionsProps> = ({ buttons }) => {
  const { sendMessage } = useMessageSender();
  const { isMobile } = useBreakpoint();

  return (
    <div className={styles.container}>
      {buttons.map((button) => (
        <Button
          multiLine
          key={button.value}
          type={ButtonType.SECONDARY}
          size={isMobile ? ButtonSize.BASE : ButtonSize.L}
          onClick={() => sendMessage(button.value)}
        >
          {button.text}
        </Button>
      ))}
    </div>
  );
};
