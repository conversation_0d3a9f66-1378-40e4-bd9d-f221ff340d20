import { MultiFieldInputType } from './MultiFieldInput.types';

export const DEFAULT_PLACEHOLDERS = {
  [MultiFieldInputType.PHONE]: '+44XXXXXXXXXX',
  [MultiFieldInputType.ADDRESS]: 'Select your address',
  [MultiFieldInputType.ACCESS_INSTRUCTION]: 'Describe access instruction',
  [MultiFieldInputType.EMAIL]: 'Enter your email address',
};

export const DEFAULT_TITLES = {
  [MultiFieldInputType.PHONE]: 'Enter your phone number',
  [MultiFieldInputType.ACCESS_INSTRUCTION]: 'Access instructions (optional)',
  [MultiFieldInputType.ADDRESS]: 'Select your address',
  [MultiFieldInputType.EMAIL]: 'Enter your email address',
};

export const ADDRESS_MANUAL_TITLE = 'Enter your address';

export const DEFAULT_SAVE_BUTTON_TEXT = {
  [MultiFieldInputType.PHONE]: 'Save phone number',
  [MultiFieldInputType.ADDRESS]: 'Save Address',
  [MultiFieldInputType.ACCESS_INSTRUCTION]: 'Save instructions',
  [MultiFieldInputType.EMAIL]: 'Save email address',
};

export const DEFAULT_MANUAL_ENTRY_TEXT = {
  [MultiFieldInputType.ADDRESS]: 'Enter manually',
};

export const MOCK_ADDRESS_OPTIONS = [
  {
    value: 'flat101',
    label:
      'Flat 1at 101, Bamboo Building 1AB 2Cat 101, Bamboo Building 1AB 2Cat 101, Bamboo Building 1AB 2C01, Bamboo Building 1AB 2CD',
  },
  {
    value: 'flat102',
    label: 'Flat 101, Bamboo Building, Panda Road, London, 1AB 2CD',
  },
  { value: 'flat103', label: 'Flat 103, Bamboo Building... 1AB 2CD' },
];

export const ADDRESS_FIELD_PLACEHOLDERS = {
  line1: 'Line 1',
  line2: 'Line 2',
  city: 'City',
  postcode: 'Postcode',
};

export const ADDRESS_LOOKUP_TEXT = 'Use address lookup';

export const PHONE_VERIFICATION = {
  SEND_CODE_BUTTON: 'Verify phone number',
  VERIFY_BUTTON: 'Verify phone number and save',
  CODE_PLACEHOLDER: 'Enter 6-digit code',
  VERIFICATION_TITLE: 'Verify your phone number',
  RESEND_CODE: `Didn't receive the code? Resend`,
};

export const EMAIL_VERIFICATION = {
  SEND_CODE_BUTTON: 'Verify email',
  VERIFY_BUTTON: 'Verify email and save',
  CODE_PLACEHOLDER: 'Enter 6-digit code',
  VERIFICATION_TITLE: 'Verify your email',
  RESEND_CODE: `Didn't receive the code? Resend`,
};
