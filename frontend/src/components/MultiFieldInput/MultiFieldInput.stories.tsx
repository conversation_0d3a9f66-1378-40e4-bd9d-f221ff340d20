import { <PERSON>a, StoryObj } from '@storybook/react';
import { MultiFieldInput } from './MultiFieldInput';
import { MultiFieldInputMode, MultiFieldInputType } from './MultiFieldInput.types';
import { MOCK_ADDRESS_OPTIONS } from './MultiFieldInput.constants';

const meta = {
  title: 'Components/MultiFieldInput',
  component: MultiFieldInput,
  parameters: {
    layout: 'centered',
  },
  tags: ['autodocs'],
  argTypes: {
    type: {
      control: 'select',
      options: Object.values(MultiFieldInputType),
      description: 'Input field type',
    },
    title: {
      control: 'text',
      description: 'Input field title',
    },
    value: {
      control: 'text',
      description: 'Input field value',
    },
    placeholder: {
      control: 'text',
      description: 'Input field placeholder',
    },
    saveButtonText: {
      control: 'text',
      description: 'Save button text',
    },
    loading: {
      control: 'boolean',
      description: 'Loading state',
    },
    error: {
      control: 'text',
      description: 'Error message',
    },
    disabled: {
      control: 'boolean',
      description: 'Disabled state',
    },
    initialMode: {
      control: 'select',
      options: Object.values(MultiFieldInputMode),
      description: 'Initial input mode',
    },
    onChange: {
      action: 'changed',
      description: 'Change handler',
    },
  },
} satisfies Meta<typeof MultiFieldInput>;

export default meta;
type Story = StoryObj<typeof meta>;

export const Phone: Story = {
  args: {
    type: MultiFieldInputType.PHONE,
    value: '',
    onSave: (value) => console.log('Saved phone:', value),
    onChange: (value) => console.log('Changed value:', value),
  },
};

export const PhoneVerification: Story = {
  args: {
    type: MultiFieldInputType.PHONE,
    value: '',
    onSave: (value) => console.log('Saved phone:', value),
    onChange: (value) => console.log('Changed value:', value),
    onSendVerificationCode: async (phoneNumber) => {
      console.log('Sending verification code to:', phoneNumber);
      return true; // Simulate successful code sending
    },
    onVerifyCode: async (phoneNumber, code) => {
      console.log('Verifying code:', code, 'for phone:', phoneNumber);
      return code === '123456'; // Example validation
    },
  },
};

export const Address: Story = {
  args: {
    type: MultiFieldInputType.ADDRESS,
    value: '',
    initialMode: MultiFieldInputMode.SELECT,
    options: MOCK_ADDRESS_OPTIONS,
    onSelectOption: (option) => console.log('Selected option:', option),
    onSwitchToManual: () => console.log('Switched to manual entry'),
    onSave: (value) => console.log('Saved address:', value),
    onChange: (value) => console.log('Changed value:', value),
  },
};

export const AddressManual: Story = {
  args: {
    type: MultiFieldInputType.ADDRESS,
    value: {
      line1: '',
      line2: '',
      city: '',
      postcode: '',
    },
    initialMode: MultiFieldInputMode.MANUAL,
    onSave: (value) => console.log('Saved address:', value),
    onChange: (value) => console.log('Changed value:', value),
  },
};

export const AccessInstruction: Story = {
  args: {
    type: MultiFieldInputType.ACCESS_INSTRUCTION,
    value: '',
    onSave: (value) => console.log('Saved instructions:', value),
    onChange: (value) => console.log('Changed value:', value),
  },
};
