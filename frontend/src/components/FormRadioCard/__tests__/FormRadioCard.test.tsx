import React from 'react';
import { render, screen } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { vi } from 'vitest';
import { FormRadioCard } from '../FormRadioCard';

export default describe('FormRadioCard', () => {
  const defaultProps = {
    label: 'Test Option',
    checked: false,
    onChange: vi.fn(),
    name: 'test',
    value: 'test-value',
  };

  it('renders with label text', () => {
    render(<FormRadioCard {...defaultProps} />);
    expect(screen.getByText('Test Option')).toBeInTheDocument();
  });

  it('renders radio button with correct attributes', () => {
    render(<FormRadioCard {...defaultProps} />);
    const radio = screen.getByRole('radio');
    expect(radio).toHaveAttribute('name', 'test');
    expect(radio).toHaveAttribute('value', 'test-value');
  });

  it('shows checked state when checked prop is true', () => {
    render(<FormRadioCard {...defaultProps} checked={true} />);
    const radio = screen.getByRole('radio');
    expect(radio).toBeChecked();
  });

  it('shows unchecked state when checked prop is false', () => {
    render(<FormRadioCard {...defaultProps} checked={false} />);
    const radio = screen.getByRole('radio');
    expect(radio).not.toBeChecked();
  });

  it('calls onChange when clicked', async () => {
    const user = userEvent.setup();
    const mockOnChange = vi.fn();
    render(<FormRadioCard {...defaultProps} onChange={mockOnChange} />);

    const card = screen.getByText('Test Option').closest('div');
    await user.click(card!);

    expect(mockOnChange).toHaveBeenCalledTimes(1);
    expect(mockOnChange).toHaveBeenCalledWith(true);
  });

  it('calls onChange when radio button is clicked directly', async () => {
    const user = userEvent.setup();
    const mockOnChange = vi.fn();
    render(<FormRadioCard {...defaultProps} onChange={mockOnChange} />);

    const radio = screen.getByRole('radio');
    await user.click(radio);

    expect(mockOnChange).toHaveBeenCalledTimes(1);
    expect(mockOnChange).toHaveBeenCalledWith(true);
  });

  it('applies selected styling when checked', () => {
    const { container } = render(<FormRadioCard {...defaultProps} checked={true} />);
    // Check that the card has the selected class (CSS modules will hash the class name)
    const selectedCard = container.querySelector('[class*="selected"]');
    expect(selectedCard).toBeInTheDocument();
  });

  it('does not apply selected styling when unchecked', () => {
    const { container } = render(<FormRadioCard {...defaultProps} checked={false} />);
    // Check that the card does not have the selected class
    const selectedCard = container.querySelector('[class*="selected"]');
    expect(selectedCard).not.toBeInTheDocument();
  });

  it('applies disabled styling when disabled', () => {
    const { container } = render(<FormRadioCard {...defaultProps} disabled />);
    // Check that the card has the disabled class (CSS modules will hash the class name)
    const disabledCard = container.querySelector('[class*="disabled"]');
    expect(disabledCard).toBeInTheDocument();
  });

  it('disables radio button when disabled prop is true', () => {
    render(<FormRadioCard {...defaultProps} disabled />);
    const radio = screen.getByRole('radio');
    expect(radio).toBeDisabled();
  });

  it('does not call onChange when disabled and clicked', async () => {
    const user = userEvent.setup();
    const mockOnChange = vi.fn();
    render(<FormRadioCard {...defaultProps} onChange={mockOnChange} disabled />);

    const card = screen.getByText('Test Option').closest('div');
    await user.click(card!);

    expect(mockOnChange).not.toHaveBeenCalled();
  });

  it('applies custom className', () => {
    const { container } = render(<FormRadioCard {...defaultProps} className="custom-class" />);
    expect(container.firstChild).toHaveClass('custom-class');
  });

  it('handles Yes/No options correctly', async () => {
    const user = userEvent.setup();
    const mockOnChange = vi.fn();

    render(
      <div>
        <FormRadioCard
          label="Yes"
          checked={false}
          onChange={mockOnChange}
          name="yesno"
          value="yes"
        />
        <FormRadioCard label="No" checked={false} onChange={mockOnChange} name="yesno" value="no" />
      </div>
    );

    const yesOption = screen.getByLabelText('Yes');
    await user.click(yesOption);

    expect(mockOnChange).toHaveBeenCalledWith(true);
  });

  it('maintains proper radio group behavior', () => {
    render(
      <div>
        <FormRadioCard
          label="Option 1"
          checked={true}
          onChange={vi.fn()}
          name="group"
          value="option1"
        />
        <FormRadioCard
          label="Option 2"
          checked={false}
          onChange={vi.fn()}
          name="group"
          value="option2"
        />
      </div>
    );

    const radios = screen.getAllByRole('radio');
    expect(radios).toHaveLength(2);
    expect(radios[0]).toBeChecked();
    expect(radios[1]).not.toBeChecked();
    expect(radios[0]).toHaveAttribute('name', 'group');
    expect(radios[1]).toHaveAttribute('name', 'group');
  });

  it('has proper accessibility attributes', () => {
    render(<FormRadioCard {...defaultProps} />);
    const radio = screen.getByRole('radio');
    expect(radio).toHaveAccessibleName('Test Option');
  });

  it('supports keyboard navigation', async () => {
    const user = userEvent.setup();
    const mockOnChange = vi.fn();
    render(<FormRadioCard {...defaultProps} onChange={mockOnChange} />);

    const radio = screen.getByRole('radio');
    radio.focus();
    await user.keyboard(' ');

    expect(mockOnChange).toHaveBeenCalledWith(true);
  });
});
