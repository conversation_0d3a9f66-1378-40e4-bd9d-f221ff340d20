// Vitest Snapshot v1, https://vitest.dev/guide/snapshot.html

exports[`Alert Snapshots > matches alert with custom button snapshot 1`] = `
<div>
  <div
    class="_alert_7b834f _default_7b834f"
  >
    <div
      class="_content_7b834f"
    >
      <div
        class="_header_7b834f"
      >
        <svg
          class="_icon_7b834f _default_7b834f"
          color="var(--colors-gray-800)"
          fill="none"
          height="24"
          viewBox="0 0 24 24"
          width="24"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M22 12C22 6.47715 17.5228 2 12 2C6.47715 2 2 6.47715 2 12C2 17.5228 6.47715 22 12 22C17.5228 22 22 17.5228 22 12Z"
            stroke="currentColor"
            stroke-width="1.5"
          />
          <path
            d="M12.2422 17V12C12.2422 11.5286 12.2422 11.2929 12.0957 11.1464C11.9493 11 11.7136 11 11.2422 11"
            stroke="currentColor"
            stroke-linecap="round"
            stroke-linejoin="round"
            stroke-width="1.5"
          />
          <path
            d="M11.992 8H12.001"
            stroke="currentColor"
            stroke-linecap="round"
            stroke-linejoin="round"
            stroke-width="2"
          />
        </svg>
        <div
          class="_heading_7b834f _default_7b834f"
        >
          Test Alert
        </div>
        <button
          class="_button_ee73f7 _tertiary_ee73f7 _sm_ee73f7 _blue-secondary_ee73f7 _iconOnly_ee73f7 _closeButton_7b834f"
        >
          <svg
            class="_icon_ee73f7 _icon-sm_ee73f7"
            color="currentColor"
            fill="none"
            height="24"
            viewBox="0 0 24 24"
            width="24"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M19 5L5 19M5 5L19 19"
              stroke="currentColor"
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="1.5"
            />
          </svg>
        </button>
      </div>
      <p
        class="_body_7b834f _default_7b834f"
      >
        This is a test alert message
      </p>
    </div>
    <button>
      Custom Button
    </button>
  </div>
</div>
`;

exports[`Alert Snapshots > matches alert without icons snapshot 1`] = `
<div>
  <div
    class="_alert_7b834f _default_7b834f"
  >
    <div
      class="_content_7b834f"
    >
      <div
        class="_header_7b834f"
      >
        <div
          class="_heading_7b834f _default_7b834f"
        >
          Test Alert
        </div>
      </div>
      <p
        class="_body_7b834f _default_7b834f"
      >
        This is a test alert message
      </p>
    </div>
    <button
      class="_button_ee73f7 _primary_ee73f7 _xs_ee73f7 _blue-secondary_ee73f7 _withText_ee73f7 !flex-[0_0_auto]"
    >
      <svg
        class="_icon_ee73f7 _icon-xs_ee73f7"
        color="currentColor"
        fill="none"
        height="24"
        viewBox="0 0 24 24"
        width="24"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          d="M21.544 11.045C21.848 11.4713 22 11.6845 22 12C22 12.3155 21.848 12.5287 21.544 12.955C20.1779 14.8706 16.6892 19 12 19C7.31078 19 3.8221 14.8706 2.45604 12.955C2.15201 12.5287 2 12.3155 2 12C2 11.6845 2.15201 11.4713 2.45604 11.045C3.8221 9.12944 7.31078 5 12 5C16.6892 5 20.1779 9.12944 21.544 11.045Z"
          stroke="currentColor"
          stroke-width="1.5"
        />
        <path
          d="M15 12C15 10.3431 13.6569 9 12 9C10.3431 9 9 10.3431 9 12C9 13.6569 10.3431 15 12 15C13.6569 15 15 13.6569 15 12Z"
          stroke="currentColor"
          stroke-width="1.5"
        />
      </svg>
      <span
        class="_text_ee73f7 _text-xs_ee73f7"
      >
        View more
      </span>
    </button>
  </div>
</div>
`;

exports[`Alert Snapshots > matches danger alert snapshot 1`] = `
<div>
  <div
    class="_alert_7b834f _danger_7b834f"
  >
    <div
      class="_content_7b834f"
    >
      <div
        class="_header_7b834f"
      >
        <svg
          class="_icon_7b834f _danger_7b834f"
          color="var(--colors-red-800)"
          fill="none"
          height="24"
          viewBox="0 0 24 24"
          width="24"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M22 12C22 6.47715 17.5228 2 12 2C6.47715 2 2 6.47715 2 12C2 17.5228 6.47715 22 12 22C17.5228 22 22 17.5228 22 12Z"
            stroke="currentColor"
            stroke-width="1.5"
          />
          <path
            d="M12.2422 17V12C12.2422 11.5286 12.2422 11.2929 12.0957 11.1464C11.9493 11 11.7136 11 11.2422 11"
            stroke="currentColor"
            stroke-linecap="round"
            stroke-linejoin="round"
            stroke-width="1.5"
          />
          <path
            d="M11.992 8H12.001"
            stroke="currentColor"
            stroke-linecap="round"
            stroke-linejoin="round"
            stroke-width="2"
          />
        </svg>
        <div
          class="_heading_7b834f _danger_7b834f"
        >
          Test Alert
        </div>
        <button
          class="_button_ee73f7 _tertiary_ee73f7 _sm_ee73f7 _red_ee73f7 _iconOnly_ee73f7 _closeButton_7b834f"
        >
          <svg
            class="_icon_ee73f7 _icon-sm_ee73f7"
            color="currentColor"
            fill="none"
            height="24"
            viewBox="0 0 24 24"
            width="24"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M19 5L5 19M5 5L19 19"
              stroke="currentColor"
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="1.5"
            />
          </svg>
        </button>
      </div>
      <p
        class="_body_7b834f _danger_7b834f"
      >
        This is a test alert message
      </p>
    </div>
    <button
      class="_button_ee73f7 _primary_ee73f7 _xs_ee73f7 _red_ee73f7 _withText_ee73f7 !flex-[0_0_auto]"
    >
      <svg
        class="_icon_ee73f7 _icon-xs_ee73f7"
        color="currentColor"
        fill="none"
        height="24"
        viewBox="0 0 24 24"
        width="24"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          d="M21.544 11.045C21.848 11.4713 22 11.6845 22 12C22 12.3155 21.848 12.5287 21.544 12.955C20.1779 14.8706 16.6892 19 12 19C7.31078 19 3.8221 14.8706 2.45604 12.955C2.15201 12.5287 2 12.3155 2 12C2 11.6845 2.15201 11.4713 2.45604 11.045C3.8221 9.12944 7.31078 5 12 5C16.6892 5 20.1779 9.12944 21.544 11.045Z"
          stroke="currentColor"
          stroke-width="1.5"
        />
        <path
          d="M15 12C15 10.3431 13.6569 9 12 9C10.3431 9 9 10.3431 9 12C9 13.6569 10.3431 15 12 15C13.6569 15 15 13.6569 15 12Z"
          stroke="currentColor"
          stroke-width="1.5"
        />
      </svg>
      <span
        class="_text_ee73f7 _text-xs_ee73f7"
      >
        View more
      </span>
    </button>
  </div>
</div>
`;

exports[`Alert Snapshots > matches default alert snapshot 1`] = `
<div>
  <div
    class="_alert_7b834f _default_7b834f"
  >
    <div
      class="_content_7b834f"
    >
      <div
        class="_header_7b834f"
      >
        <svg
          class="_icon_7b834f _default_7b834f"
          color="var(--colors-gray-800)"
          fill="none"
          height="24"
          viewBox="0 0 24 24"
          width="24"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M22 12C22 6.47715 17.5228 2 12 2C6.47715 2 2 6.47715 2 12C2 17.5228 6.47715 22 12 22C17.5228 22 22 17.5228 22 12Z"
            stroke="currentColor"
            stroke-width="1.5"
          />
          <path
            d="M12.2422 17V12C12.2422 11.5286 12.2422 11.2929 12.0957 11.1464C11.9493 11 11.7136 11 11.2422 11"
            stroke="currentColor"
            stroke-linecap="round"
            stroke-linejoin="round"
            stroke-width="1.5"
          />
          <path
            d="M11.992 8H12.001"
            stroke="currentColor"
            stroke-linecap="round"
            stroke-linejoin="round"
            stroke-width="2"
          />
        </svg>
        <div
          class="_heading_7b834f _default_7b834f"
        >
          Test Alert
        </div>
        <button
          class="_button_ee73f7 _tertiary_ee73f7 _sm_ee73f7 _blue-secondary_ee73f7 _iconOnly_ee73f7 _closeButton_7b834f"
        >
          <svg
            class="_icon_ee73f7 _icon-sm_ee73f7"
            color="currentColor"
            fill="none"
            height="24"
            viewBox="0 0 24 24"
            width="24"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M19 5L5 19M5 5L19 19"
              stroke="currentColor"
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="1.5"
            />
          </svg>
        </button>
      </div>
      <p
        class="_body_7b834f _default_7b834f"
      >
        This is a test alert message
      </p>
    </div>
    <button
      class="_button_ee73f7 _primary_ee73f7 _xs_ee73f7 _blue-secondary_ee73f7 _withText_ee73f7 !flex-[0_0_auto]"
    >
      <svg
        class="_icon_ee73f7 _icon-xs_ee73f7"
        color="currentColor"
        fill="none"
        height="24"
        viewBox="0 0 24 24"
        width="24"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          d="M21.544 11.045C21.848 11.4713 22 11.6845 22 12C22 12.3155 21.848 12.5287 21.544 12.955C20.1779 14.8706 16.6892 19 12 19C7.31078 19 3.8221 14.8706 2.45604 12.955C2.15201 12.5287 2 12.3155 2 12C2 11.6845 2.15201 11.4713 2.45604 11.045C3.8221 9.12944 7.31078 5 12 5C16.6892 5 20.1779 9.12944 21.544 11.045Z"
          stroke="currentColor"
          stroke-width="1.5"
        />
        <path
          d="M15 12C15 10.3431 13.6569 9 12 9C10.3431 9 9 10.3431 9 12C9 13.6569 10.3431 15 12 15C13.6569 15 15 13.6569 15 12Z"
          stroke="currentColor"
          stroke-width="1.5"
        />
      </svg>
      <span
        class="_text_ee73f7 _text-xs_ee73f7"
      >
        View more
      </span>
    </button>
  </div>
</div>
`;

exports[`Alert Snapshots > matches info alert snapshot 1`] = `
<div>
  <div
    class="_alert_7b834f _info_7b834f"
  >
    <div
      class="_content_7b834f"
    >
      <div
        class="_header_7b834f"
      >
        <svg
          class="_icon_7b834f _info_7b834f"
          color="var(--colors-blue-800)"
          fill="none"
          height="24"
          viewBox="0 0 24 24"
          width="24"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M22 12C22 6.47715 17.5228 2 12 2C6.47715 2 2 6.47715 2 12C2 17.5228 6.47715 22 12 22C17.5228 22 22 17.5228 22 12Z"
            stroke="currentColor"
            stroke-width="1.5"
          />
          <path
            d="M12.2422 17V12C12.2422 11.5286 12.2422 11.2929 12.0957 11.1464C11.9493 11 11.7136 11 11.2422 11"
            stroke="currentColor"
            stroke-linecap="round"
            stroke-linejoin="round"
            stroke-width="1.5"
          />
          <path
            d="M11.992 8H12.001"
            stroke="currentColor"
            stroke-linecap="round"
            stroke-linejoin="round"
            stroke-width="2"
          />
        </svg>
        <div
          class="_heading_7b834f _info_7b834f"
        >
          Test Alert
        </div>
        <button
          class="_button_ee73f7 _tertiary_ee73f7 _sm_ee73f7 _blue-secondary_ee73f7 _iconOnly_ee73f7 _closeButton_7b834f"
        >
          <svg
            class="_icon_ee73f7 _icon-sm_ee73f7"
            color="currentColor"
            fill="none"
            height="24"
            viewBox="0 0 24 24"
            width="24"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M19 5L5 19M5 5L19 19"
              stroke="currentColor"
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="1.5"
            />
          </svg>
        </button>
      </div>
      <p
        class="_body_7b834f _info_7b834f"
      >
        This is a test alert message
      </p>
    </div>
    <button
      class="_button_ee73f7 _primary_ee73f7 _xs_ee73f7 _blue-secondary_ee73f7 _withText_ee73f7 !flex-[0_0_auto]"
    >
      <svg
        class="_icon_ee73f7 _icon-xs_ee73f7"
        color="currentColor"
        fill="none"
        height="24"
        viewBox="0 0 24 24"
        width="24"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          d="M21.544 11.045C21.848 11.4713 22 11.6845 22 12C22 12.3155 21.848 12.5287 21.544 12.955C20.1779 14.8706 16.6892 19 12 19C7.31078 19 3.8221 14.8706 2.45604 12.955C2.15201 12.5287 2 12.3155 2 12C2 11.6845 2.15201 11.4713 2.45604 11.045C3.8221 9.12944 7.31078 5 12 5C16.6892 5 20.1779 9.12944 21.544 11.045Z"
          stroke="currentColor"
          stroke-width="1.5"
        />
        <path
          d="M15 12C15 10.3431 13.6569 9 12 9C10.3431 9 9 10.3431 9 12C9 13.6569 10.3431 15 12 15C13.6569 15 15 13.6569 15 12Z"
          stroke="currentColor"
          stroke-width="1.5"
        />
      </svg>
      <span
        class="_text_ee73f7 _text-xs_ee73f7"
      >
        View more
      </span>
    </button>
  </div>
</div>
`;

exports[`Alert Snapshots > matches inline desktop danger alert snapshot 1`] = `
<div>
  <div
    class="_alert_7b834f _danger_7b834f _inline_7b834f"
  >
    <div
      class="_inlineContent_7b834f"
    >
      <svg
        class="_icon_7b834f _danger_7b834f"
        color="var(--colors-red-800)"
        fill="none"
        height="17"
        viewBox="0 0 24 24"
        width="17"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          d="M22 12C22 6.47715 17.5228 2 12 2C6.47715 2 2 6.47715 2 12C2 17.5228 6.47715 22 12 22C17.5228 22 22 17.5228 22 12Z"
          stroke="currentColor"
          stroke-width="1.5"
        />
        <path
          d="M12.2422 17V12C12.2422 11.5286 12.2422 11.2929 12.0957 11.1464C11.9493 11 11.7136 11 11.2422 11"
          stroke="currentColor"
          stroke-linecap="round"
          stroke-linejoin="round"
          stroke-width="1.5"
        />
        <path
          d="M11.992 8H12.001"
          stroke="currentColor"
          stroke-linecap="round"
          stroke-linejoin="round"
          stroke-width="2"
        />
      </svg>
      <div
        class="_heading_7b834f _danger_7b834f"
      >
        Test Alert
      </div>
    </div>
    <button
      class="_button_ee73f7 _primary_ee73f7 _xs_ee73f7 _red_ee73f7 _withText_ee73f7"
    >
      <span
        class="_text_ee73f7 _text-xs_ee73f7"
      >
        Retry
      </span>
    </button>
  </div>
</div>
`;

exports[`Alert Snapshots > matches inline desktop default alert snapshot 1`] = `
<div>
  <div
    class="_alert_7b834f _default_7b834f _inline_7b834f"
  >
    <div
      class="_inlineContent_7b834f"
    >
      <svg
        class="_icon_7b834f _default_7b834f"
        color="var(--colors-gray-800)"
        fill="none"
        height="17"
        viewBox="0 0 24 24"
        width="17"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          d="M22 12C22 6.47715 17.5228 2 12 2C6.47715 2 2 6.47715 2 12C2 17.5228 6.47715 22 12 22C17.5228 22 22 17.5228 22 12Z"
          stroke="currentColor"
          stroke-width="1.5"
        />
        <path
          d="M12.2422 17V12C12.2422 11.5286 12.2422 11.2929 12.0957 11.1464C11.9493 11 11.7136 11 11.2422 11"
          stroke="currentColor"
          stroke-linecap="round"
          stroke-linejoin="round"
          stroke-width="1.5"
        />
        <path
          d="M11.992 8H12.001"
          stroke="currentColor"
          stroke-linecap="round"
          stroke-linejoin="round"
          stroke-width="2"
        />
      </svg>
      <div
        class="_heading_7b834f _default_7b834f"
      >
        Test Alert
      </div>
    </div>
    <button
      class="_button_ee73f7 _primary_ee73f7 _xs_ee73f7 _blue-secondary_ee73f7 _withText_ee73f7"
    >
      <span
        class="_text_ee73f7 _text-xs_ee73f7"
      >
        Retry
      </span>
    </button>
  </div>
</div>
`;

exports[`Alert Snapshots > matches inline desktop info alert snapshot 1`] = `
<div>
  <div
    class="_alert_7b834f _info_7b834f _inline_7b834f"
  >
    <div
      class="_inlineContent_7b834f"
    >
      <svg
        class="_icon_7b834f _info_7b834f"
        color="var(--colors-blue-800)"
        fill="none"
        height="17"
        viewBox="0 0 24 24"
        width="17"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          d="M22 12C22 6.47715 17.5228 2 12 2C6.47715 2 2 6.47715 2 12C2 17.5228 6.47715 22 12 22C17.5228 22 22 17.5228 22 12Z"
          stroke="currentColor"
          stroke-width="1.5"
        />
        <path
          d="M12.2422 17V12C12.2422 11.5286 12.2422 11.2929 12.0957 11.1464C11.9493 11 11.7136 11 11.2422 11"
          stroke="currentColor"
          stroke-linecap="round"
          stroke-linejoin="round"
          stroke-width="1.5"
        />
        <path
          d="M11.992 8H12.001"
          stroke="currentColor"
          stroke-linecap="round"
          stroke-linejoin="round"
          stroke-width="2"
        />
      </svg>
      <div
        class="_heading_7b834f _info_7b834f"
      >
        Test Alert
      </div>
    </div>
    <button
      class="_button_ee73f7 _primary_ee73f7 _xs_ee73f7 _blue-secondary_ee73f7 _withText_ee73f7"
    >
      <span
        class="_text_ee73f7 _text-xs_ee73f7"
      >
        Retry
      </span>
    </button>
  </div>
</div>
`;

exports[`Alert Snapshots > matches inline desktop notice alert snapshot 1`] = `
<div>
  <div
    class="_alert_7b834f _notice_7b834f _inline_7b834f"
  >
    <div
      class="_inlineContent_7b834f"
    >
      <svg
        class="_icon_7b834f _notice_7b834f"
        color="var(--colors-green-900)"
        fill="none"
        height="17"
        viewBox="0 0 24 24"
        width="17"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          d="M22 12C22 6.47715 17.5228 2 12 2C6.47715 2 2 6.47715 2 12C2 17.5228 6.47715 22 12 22C17.5228 22 22 17.5228 22 12Z"
          stroke="currentColor"
          stroke-width="1.5"
        />
        <path
          d="M12.2422 17V12C12.2422 11.5286 12.2422 11.2929 12.0957 11.1464C11.9493 11 11.7136 11 11.2422 11"
          stroke="currentColor"
          stroke-linecap="round"
          stroke-linejoin="round"
          stroke-width="1.5"
        />
        <path
          d="M11.992 8H12.001"
          stroke="currentColor"
          stroke-linecap="round"
          stroke-linejoin="round"
          stroke-width="2"
        />
      </svg>
      <div
        class="_heading_7b834f _notice_7b834f"
      >
        Test Alert
      </div>
    </div>
    <button
      class="_button_ee73f7 _primary_ee73f7 _xs_ee73f7 _green-primary_ee73f7 _withText_ee73f7"
    >
      <span
        class="_text_ee73f7 _text-xs_ee73f7"
      >
        Retry
      </span>
    </button>
  </div>
</div>
`;

exports[`Alert Snapshots > matches inline desktop success alert snapshot 1`] = `
<div>
  <div
    class="_alert_7b834f _success_7b834f _inline_7b834f"
  >
    <div
      class="_inlineContent_7b834f"
    >
      <svg
        class="_icon_7b834f _success_7b834f"
        color="var(--colors-green-800)"
        fill="none"
        height="17"
        viewBox="0 0 24 24"
        width="17"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          d="M22 12C22 6.47715 17.5228 2 12 2C6.47715 2 2 6.47715 2 12C2 17.5228 6.47715 22 12 22C17.5228 22 22 17.5228 22 12Z"
          stroke="currentColor"
          stroke-width="1.5"
        />
        <path
          d="M8 12.75C8 12.75 9.6 13.6625 10.4 15C10.4 15 12.8 9.75 16 8"
          stroke="currentColor"
          stroke-linecap="round"
          stroke-linejoin="round"
          stroke-width="1.5"
        />
      </svg>
      <div
        class="_heading_7b834f _success_7b834f"
      >
        Test Alert
      </div>
    </div>
    <button
      class="_button_ee73f7 _primary_ee73f7 _xs_ee73f7 _green-primary_ee73f7 _withText_ee73f7"
    >
      <span
        class="_text_ee73f7 _text-xs_ee73f7"
      >
        Retry
      </span>
    </button>
  </div>
</div>
`;

exports[`Alert Snapshots > matches inline desktop warning alert snapshot 1`] = `
<div>
  <div
    class="_alert_7b834f _warning_7b834f _inline_7b834f"
  >
    <div
      class="_inlineContent_7b834f"
    >
      <svg
        class="_icon_7b834f _warning_7b834f"
        color="var(--colors-orange-800)"
        fill="none"
        height="17"
        viewBox="0 0 24 24"
        width="17"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          d="M22 12C22 6.47715 17.5228 2 12 2C6.47715 2 2 6.47715 2 12C2 17.5228 6.47715 22 12 22C17.5228 22 22 17.5228 22 12Z"
          stroke="currentColor"
          stroke-width="1.5"
        />
        <path
          d="M12.2422 17V12C12.2422 11.5286 12.2422 11.2929 12.0957 11.1464C11.9493 11 11.7136 11 11.2422 11"
          stroke="currentColor"
          stroke-linecap="round"
          stroke-linejoin="round"
          stroke-width="1.5"
        />
        <path
          d="M11.992 8H12.001"
          stroke="currentColor"
          stroke-linecap="round"
          stroke-linejoin="round"
          stroke-width="2"
        />
      </svg>
      <div
        class="_heading_7b834f _warning_7b834f"
      >
        Test Alert
      </div>
    </div>
    <button
      class="_button_ee73f7 _primary_ee73f7 _xs_ee73f7 _orange_ee73f7 _withText_ee73f7"
    >
      <span
        class="_text_ee73f7 _text-xs_ee73f7"
      >
        Retry
      </span>
    </button>
  </div>
</div>
`;

exports[`Alert Snapshots > matches inline mobile danger alert snapshot 1`] = `
<div>
  <div
    class="_alert_7b834f _danger_7b834f _inline_7b834f"
  >
    <div
      class="_inlineContent_7b834f"
    >
      <svg
        class="_icon_7b834f _danger_7b834f"
        color="var(--colors-red-800)"
        fill="none"
        height="17"
        viewBox="0 0 24 24"
        width="17"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          d="M22 12C22 6.47715 17.5228 2 12 2C6.47715 2 2 6.47715 2 12C2 17.5228 6.47715 22 12 22C17.5228 22 22 17.5228 22 12Z"
          stroke="currentColor"
          stroke-width="1.5"
        />
        <path
          d="M12.2422 17V12C12.2422 11.5286 12.2422 11.2929 12.0957 11.1464C11.9493 11 11.7136 11 11.2422 11"
          stroke="currentColor"
          stroke-linecap="round"
          stroke-linejoin="round"
          stroke-width="1.5"
        />
        <path
          d="M11.992 8H12.001"
          stroke="currentColor"
          stroke-linecap="round"
          stroke-linejoin="round"
          stroke-width="2"
        />
      </svg>
      <div
        class="_heading_7b834f _danger_7b834f"
      >
        Test Alert
      </div>
    </div>
    <button
      class="_button_ee73f7 _primary_ee73f7 _xs_ee73f7 _red_ee73f7 _withText_ee73f7"
    >
      <span
        class="_text_ee73f7 _text-xs_ee73f7"
      >
        Retry
      </span>
    </button>
  </div>
</div>
`;

exports[`Alert Snapshots > matches inline mobile default alert snapshot 1`] = `
<div>
  <div
    class="_alert_7b834f _default_7b834f _inline_7b834f"
  >
    <div
      class="_inlineContent_7b834f"
    >
      <svg
        class="_icon_7b834f _default_7b834f"
        color="var(--colors-gray-800)"
        fill="none"
        height="17"
        viewBox="0 0 24 24"
        width="17"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          d="M22 12C22 6.47715 17.5228 2 12 2C6.47715 2 2 6.47715 2 12C2 17.5228 6.47715 22 12 22C17.5228 22 22 17.5228 22 12Z"
          stroke="currentColor"
          stroke-width="1.5"
        />
        <path
          d="M12.2422 17V12C12.2422 11.5286 12.2422 11.2929 12.0957 11.1464C11.9493 11 11.7136 11 11.2422 11"
          stroke="currentColor"
          stroke-linecap="round"
          stroke-linejoin="round"
          stroke-width="1.5"
        />
        <path
          d="M11.992 8H12.001"
          stroke="currentColor"
          stroke-linecap="round"
          stroke-linejoin="round"
          stroke-width="2"
        />
      </svg>
      <div
        class="_heading_7b834f _default_7b834f"
      >
        Test Alert
      </div>
    </div>
    <button
      class="_button_ee73f7 _primary_ee73f7 _xs_ee73f7 _blue-secondary_ee73f7 _withText_ee73f7"
    >
      <span
        class="_text_ee73f7 _text-xs_ee73f7"
      >
        Retry
      </span>
    </button>
  </div>
</div>
`;

exports[`Alert Snapshots > matches inline mobile info alert snapshot 1`] = `
<div>
  <div
    class="_alert_7b834f _info_7b834f _inline_7b834f"
  >
    <div
      class="_inlineContent_7b834f"
    >
      <svg
        class="_icon_7b834f _info_7b834f"
        color="var(--colors-blue-800)"
        fill="none"
        height="17"
        viewBox="0 0 24 24"
        width="17"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          d="M22 12C22 6.47715 17.5228 2 12 2C6.47715 2 2 6.47715 2 12C2 17.5228 6.47715 22 12 22C17.5228 22 22 17.5228 22 12Z"
          stroke="currentColor"
          stroke-width="1.5"
        />
        <path
          d="M12.2422 17V12C12.2422 11.5286 12.2422 11.2929 12.0957 11.1464C11.9493 11 11.7136 11 11.2422 11"
          stroke="currentColor"
          stroke-linecap="round"
          stroke-linejoin="round"
          stroke-width="1.5"
        />
        <path
          d="M11.992 8H12.001"
          stroke="currentColor"
          stroke-linecap="round"
          stroke-linejoin="round"
          stroke-width="2"
        />
      </svg>
      <div
        class="_heading_7b834f _info_7b834f"
      >
        Test Alert
      </div>
    </div>
    <button
      class="_button_ee73f7 _primary_ee73f7 _xs_ee73f7 _blue-secondary_ee73f7 _withText_ee73f7"
    >
      <span
        class="_text_ee73f7 _text-xs_ee73f7"
      >
        Retry
      </span>
    </button>
  </div>
</div>
`;

exports[`Alert Snapshots > matches inline mobile notice alert snapshot 1`] = `
<div>
  <div
    class="_alert_7b834f _notice_7b834f _inline_7b834f"
  >
    <div
      class="_inlineContent_7b834f"
    >
      <svg
        class="_icon_7b834f _notice_7b834f"
        color="var(--colors-green-900)"
        fill="none"
        height="17"
        viewBox="0 0 24 24"
        width="17"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          d="M22 12C22 6.47715 17.5228 2 12 2C6.47715 2 2 6.47715 2 12C2 17.5228 6.47715 22 12 22C17.5228 22 22 17.5228 22 12Z"
          stroke="currentColor"
          stroke-width="1.5"
        />
        <path
          d="M12.2422 17V12C12.2422 11.5286 12.2422 11.2929 12.0957 11.1464C11.9493 11 11.7136 11 11.2422 11"
          stroke="currentColor"
          stroke-linecap="round"
          stroke-linejoin="round"
          stroke-width="1.5"
        />
        <path
          d="M11.992 8H12.001"
          stroke="currentColor"
          stroke-linecap="round"
          stroke-linejoin="round"
          stroke-width="2"
        />
      </svg>
      <div
        class="_heading_7b834f _notice_7b834f"
      >
        Test Alert
      </div>
    </div>
    <button
      class="_button_ee73f7 _primary_ee73f7 _xs_ee73f7 _green-primary_ee73f7 _withText_ee73f7"
    >
      <span
        class="_text_ee73f7 _text-xs_ee73f7"
      >
        Retry
      </span>
    </button>
  </div>
</div>
`;

exports[`Alert Snapshots > matches inline mobile success alert snapshot 1`] = `
<div>
  <div
    class="_alert_7b834f _success_7b834f _inline_7b834f"
  >
    <div
      class="_inlineContent_7b834f"
    >
      <svg
        class="_icon_7b834f _success_7b834f"
        color="var(--colors-green-800)"
        fill="none"
        height="17"
        viewBox="0 0 24 24"
        width="17"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          d="M22 12C22 6.47715 17.5228 2 12 2C6.47715 2 2 6.47715 2 12C2 17.5228 6.47715 22 12 22C17.5228 22 22 17.5228 22 12Z"
          stroke="currentColor"
          stroke-width="1.5"
        />
        <path
          d="M8 12.75C8 12.75 9.6 13.6625 10.4 15C10.4 15 12.8 9.75 16 8"
          stroke="currentColor"
          stroke-linecap="round"
          stroke-linejoin="round"
          stroke-width="1.5"
        />
      </svg>
      <div
        class="_heading_7b834f _success_7b834f"
      >
        Test Alert
      </div>
    </div>
    <button
      class="_button_ee73f7 _primary_ee73f7 _xs_ee73f7 _green-primary_ee73f7 _withText_ee73f7"
    >
      <span
        class="_text_ee73f7 _text-xs_ee73f7"
      >
        Retry
      </span>
    </button>
  </div>
</div>
`;

exports[`Alert Snapshots > matches inline mobile warning alert snapshot 1`] = `
<div>
  <div
    class="_alert_7b834f _warning_7b834f _inline_7b834f"
  >
    <div
      class="_inlineContent_7b834f"
    >
      <svg
        class="_icon_7b834f _warning_7b834f"
        color="var(--colors-orange-800)"
        fill="none"
        height="17"
        viewBox="0 0 24 24"
        width="17"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          d="M22 12C22 6.47715 17.5228 2 12 2C6.47715 2 2 6.47715 2 12C2 17.5228 6.47715 22 12 22C17.5228 22 22 17.5228 22 12Z"
          stroke="currentColor"
          stroke-width="1.5"
        />
        <path
          d="M12.2422 17V12C12.2422 11.5286 12.2422 11.2929 12.0957 11.1464C11.9493 11 11.7136 11 11.2422 11"
          stroke="currentColor"
          stroke-linecap="round"
          stroke-linejoin="round"
          stroke-width="1.5"
        />
        <path
          d="M11.992 8H12.001"
          stroke="currentColor"
          stroke-linecap="round"
          stroke-linejoin="round"
          stroke-width="2"
        />
      </svg>
      <div
        class="_heading_7b834f _warning_7b834f"
      >
        Test Alert
      </div>
    </div>
    <button
      class="_button_ee73f7 _primary_ee73f7 _xs_ee73f7 _orange_ee73f7 _withText_ee73f7"
    >
      <span
        class="_text_ee73f7 _text-xs_ee73f7"
      >
        Retry
      </span>
    </button>
  </div>
</div>
`;

exports[`Alert Snapshots > matches notice alert snapshot 1`] = `
<div>
  <div
    class="_alert_7b834f _notice_7b834f"
  >
    <div
      class="_content_7b834f"
    >
      <div
        class="_header_7b834f"
      >
        <svg
          class="_icon_7b834f _notice_7b834f"
          color="var(--colors-green-900)"
          fill="none"
          height="24"
          viewBox="0 0 24 24"
          width="24"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M22 12C22 6.47715 17.5228 2 12 2C6.47715 2 2 6.47715 2 12C2 17.5228 6.47715 22 12 22C17.5228 22 22 17.5228 22 12Z"
            stroke="currentColor"
            stroke-width="1.5"
          />
          <path
            d="M12.2422 17V12C12.2422 11.5286 12.2422 11.2929 12.0957 11.1464C11.9493 11 11.7136 11 11.2422 11"
            stroke="currentColor"
            stroke-linecap="round"
            stroke-linejoin="round"
            stroke-width="1.5"
          />
          <path
            d="M11.992 8H12.001"
            stroke="currentColor"
            stroke-linecap="round"
            stroke-linejoin="round"
            stroke-width="2"
          />
        </svg>
        <div
          class="_heading_7b834f _notice_7b834f"
        >
          Test Alert
        </div>
        <button
          class="_button_ee73f7 _tertiary_ee73f7 _sm_ee73f7 _green-primary_ee73f7 _iconOnly_ee73f7 _closeButton_7b834f"
        >
          <svg
            class="_icon_ee73f7 _icon-sm_ee73f7"
            color="currentColor"
            fill="none"
            height="24"
            viewBox="0 0 24 24"
            width="24"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M19 5L5 19M5 5L19 19"
              stroke="currentColor"
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="1.5"
            />
          </svg>
        </button>
      </div>
      <p
        class="_body_7b834f _notice_7b834f"
      >
        This is a test alert message
      </p>
    </div>
    <button
      class="_button_ee73f7 _primary_ee73f7 _xs_ee73f7 _green-primary_ee73f7 _withText_ee73f7 !flex-[0_0_auto]"
    >
      <svg
        class="_icon_ee73f7 _icon-xs_ee73f7"
        color="currentColor"
        fill="none"
        height="24"
        viewBox="0 0 24 24"
        width="24"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          d="M21.544 11.045C21.848 11.4713 22 11.6845 22 12C22 12.3155 21.848 12.5287 21.544 12.955C20.1779 14.8706 16.6892 19 12 19C7.31078 19 3.8221 14.8706 2.45604 12.955C2.15201 12.5287 2 12.3155 2 12C2 11.6845 2.15201 11.4713 2.45604 11.045C3.8221 9.12944 7.31078 5 12 5C16.6892 5 20.1779 9.12944 21.544 11.045Z"
          stroke="currentColor"
          stroke-width="1.5"
        />
        <path
          d="M15 12C15 10.3431 13.6569 9 12 9C10.3431 9 9 10.3431 9 12C9 13.6569 10.3431 15 12 15C13.6569 15 15 13.6569 15 12Z"
          stroke="currentColor"
          stroke-width="1.5"
        />
      </svg>
      <span
        class="_text_ee73f7 _text-xs_ee73f7"
      >
        View more
      </span>
    </button>
  </div>
</div>
`;

exports[`Alert Snapshots > matches success alert snapshot 1`] = `
<div>
  <div
    class="_alert_7b834f _success_7b834f"
  >
    <div
      class="_content_7b834f"
    >
      <div
        class="_header_7b834f"
      >
        <svg
          class="_icon_7b834f _success_7b834f"
          color="var(--colors-green-800)"
          fill="none"
          height="24"
          viewBox="0 0 24 24"
          width="24"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M22 12C22 6.47715 17.5228 2 12 2C6.47715 2 2 6.47715 2 12C2 17.5228 6.47715 22 12 22C17.5228 22 22 17.5228 22 12Z"
            stroke="currentColor"
            stroke-width="1.5"
          />
          <path
            d="M8 12.75C8 12.75 9.6 13.6625 10.4 15C10.4 15 12.8 9.75 16 8"
            stroke="currentColor"
            stroke-linecap="round"
            stroke-linejoin="round"
            stroke-width="1.5"
          />
        </svg>
        <div
          class="_heading_7b834f _success_7b834f"
        >
          Test Alert
        </div>
        <button
          class="_button_ee73f7 _tertiary_ee73f7 _sm_ee73f7 _green-primary_ee73f7 _iconOnly_ee73f7 _closeButton_7b834f"
        >
          <svg
            class="_icon_ee73f7 _icon-sm_ee73f7"
            color="currentColor"
            fill="none"
            height="24"
            viewBox="0 0 24 24"
            width="24"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M19 5L5 19M5 5L19 19"
              stroke="currentColor"
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="1.5"
            />
          </svg>
        </button>
      </div>
      <p
        class="_body_7b834f _success_7b834f"
      >
        This is a test alert message
      </p>
    </div>
    <button
      class="_button_ee73f7 _primary_ee73f7 _xs_ee73f7 _green-primary_ee73f7 _withText_ee73f7 !flex-[0_0_auto]"
    >
      <svg
        class="_icon_ee73f7 _icon-xs_ee73f7"
        color="currentColor"
        fill="none"
        height="24"
        viewBox="0 0 24 24"
        width="24"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          d="M21.544 11.045C21.848 11.4713 22 11.6845 22 12C22 12.3155 21.848 12.5287 21.544 12.955C20.1779 14.8706 16.6892 19 12 19C7.31078 19 3.8221 14.8706 2.45604 12.955C2.15201 12.5287 2 12.3155 2 12C2 11.6845 2.15201 11.4713 2.45604 11.045C3.8221 9.12944 7.31078 5 12 5C16.6892 5 20.1779 9.12944 21.544 11.045Z"
          stroke="currentColor"
          stroke-width="1.5"
        />
        <path
          d="M15 12C15 10.3431 13.6569 9 12 9C10.3431 9 9 10.3431 9 12C9 13.6569 10.3431 15 12 15C13.6569 15 15 13.6569 15 12Z"
          stroke="currentColor"
          stroke-width="1.5"
        />
      </svg>
      <span
        class="_text_ee73f7 _text-xs_ee73f7"
      >
        View more
      </span>
    </button>
  </div>
</div>
`;

exports[`Alert Snapshots > matches warning alert snapshot 1`] = `
<div>
  <div
    class="_alert_7b834f _warning_7b834f"
  >
    <div
      class="_content_7b834f"
    >
      <div
        class="_header_7b834f"
      >
        <svg
          class="_icon_7b834f _warning_7b834f"
          color="var(--colors-orange-800)"
          fill="none"
          height="24"
          viewBox="0 0 24 24"
          width="24"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M22 12C22 6.47715 17.5228 2 12 2C6.47715 2 2 6.47715 2 12C2 17.5228 6.47715 22 12 22C17.5228 22 22 17.5228 22 12Z"
            stroke="currentColor"
            stroke-width="1.5"
          />
          <path
            d="M12.2422 17V12C12.2422 11.5286 12.2422 11.2929 12.0957 11.1464C11.9493 11 11.7136 11 11.2422 11"
            stroke="currentColor"
            stroke-linecap="round"
            stroke-linejoin="round"
            stroke-width="1.5"
          />
          <path
            d="M11.992 8H12.001"
            stroke="currentColor"
            stroke-linecap="round"
            stroke-linejoin="round"
            stroke-width="2"
          />
        </svg>
        <div
          class="_heading_7b834f _warning_7b834f"
        >
          Test Alert
        </div>
        <button
          class="_button_ee73f7 _tertiary_ee73f7 _sm_ee73f7 _orange_ee73f7 _iconOnly_ee73f7 _closeButton_7b834f"
        >
          <svg
            class="_icon_ee73f7 _icon-sm_ee73f7"
            color="currentColor"
            fill="none"
            height="24"
            viewBox="0 0 24 24"
            width="24"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M19 5L5 19M5 5L19 19"
              stroke="currentColor"
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="1.5"
            />
          </svg>
        </button>
      </div>
      <p
        class="_body_7b834f _warning_7b834f"
      >
        This is a test alert message
      </p>
    </div>
    <button
      class="_button_ee73f7 _primary_ee73f7 _xs_ee73f7 _orange_ee73f7 _withText_ee73f7 !flex-[0_0_auto]"
    >
      <svg
        class="_icon_ee73f7 _icon-xs_ee73f7"
        color="currentColor"
        fill="none"
        height="24"
        viewBox="0 0 24 24"
        width="24"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          d="M21.544 11.045C21.848 11.4713 22 11.6845 22 12C22 12.3155 21.848 12.5287 21.544 12.955C20.1779 14.8706 16.6892 19 12 19C7.31078 19 3.8221 14.8706 2.45604 12.955C2.15201 12.5287 2 12.3155 2 12C2 11.6845 2.15201 11.4713 2.45604 11.045C3.8221 9.12944 7.31078 5 12 5C16.6892 5 20.1779 9.12944 21.544 11.045Z"
          stroke="currentColor"
          stroke-width="1.5"
        />
        <path
          d="M15 12C15 10.3431 13.6569 9 12 9C10.3431 9 9 10.3431 9 12C9 13.6569 10.3431 15 12 15C13.6569 15 15 13.6569 15 12Z"
          stroke="currentColor"
          stroke-width="1.5"
        />
      </svg>
      <span
        class="_text_ee73f7 _text-xs_ee73f7"
      >
        View more
      </span>
    </button>
  </div>
</div>
`;
