import '@testing-library/jest-dom';
import React from 'react';

// Mock CSS modules
jest.mock('../Alert.module.scss', () => ({
  alert: 'alert',
  default: 'default',
  info: 'info',
  success: 'success',
  warning: 'warning',
  danger: 'danger',
  content: 'content',
  header: 'header',
  icon: 'icon',
  heading: 'heading',
  body: 'body',
  closeButton: 'closeButton',
}));

// Mock HugeIcons with specific components
vi.mock('@hugeicons/react', () => ({
  CheckmarkCircle01Icon: ({ className }: { className: string }) => (
    <div data-testid="alert-icon" className={className}>
      Mock Success Icon
    </div>
  ),
  InformationCircleIcon: ({ className }: { className: string }) => (
    <div data-testid="alert-icon" className={className}>
      Mock Info Icon
    </div>
  ),
  AlertCircleIcon: ({ className }: { className: string }) => (
    <div data-testid="alert-icon" className={className}>
      Mock Warning Icon
    </div>
  ),
  AlertTriangleIcon: ({ className }: { className: string }) => (
    <div data-testid="alert-icon" className={className}>
      Mock Error Icon
    </div>
  ),
  ViewIcon: ({ className }: { className: string }) => (
    <div data-testid="view-button-icon" className={className}>
      Mock View Icon
    </div>
  ),
  Cancel01Icon: ({ className }: { className: string }) => (
    <div data-testid="close-button-icon" className={className}>
      Mock Cancel Icon
    </div>
  ),
}));

// Global test setup
beforeEach(() => {
  vi.clearAllMocks();
}); 