import React from 'react';
import type { <PERSON><PERSON>, StoryObj } from '@storybook/react';
import { Alert } from './Alert';
import { AlertColor } from './Alert.types';
import { Button } from '../Button';
import { ButtonColor, ButtonSize } from '../Button/Button.types';
import { AddTeamIcon, DiscoverCircleIcon } from '@hugeicons/react';

const meta = {
  title: 'Components/Alert',
  component: Alert,
  parameters: {
    layout: 'centered',
    docs: {
      description: {
        component: `
## Alert Component

A versatile alert component that supports multiple variants and states. Use it for showing important messages, notifications, or feedback to users.

### Key Features
- Multiple color variants (default, info, success, warning, danger)
- Optional heading and body text
- Configurable icons
- Optional action button
- Dark mode support

### Usage Guidelines

1. **Choose the right color:**
   - \`default\`: For neutral messages
   - \`info\`: For informational messages
   - \`success\`: For success messages
   - \`warning\`: For warning messages
   - \`danger\`: For error messages

### Examples

\`\`\`jsx
// Basic alert
<Alert 
  color="info"
  headingText="Information"
  bodyText="This is an informational message."
/>

// Alert with button
<Alert 
  color="success"
  headingText="Success"
  bodyText="Operation completed successfully"
  showButton={true}
/>
\`\`\`
`,
      },
    },
  },
  argTypes: {
    color: {
      control: 'select',
      options: Object.values(AlertColor),
      description: 'Color variant of the alert',
    },
    headingText: {
      control: 'text',
      description: 'Heading text of the alert',
    },
    bodyText: {
      control: 'text',
      description: 'Body text of the alert',
    },
    showButton: {
      control: 'boolean',
      description: 'Show/hide action button',
    },
    showBodyText: {
      control: 'boolean',
      description: 'Show/hide body text',
    },
    showRightIcon: {
      control: 'boolean',
      description: 'Show/hide right icon',
    },
    showLeftIcon: {
      control: 'boolean',
      description: 'Show/hide left icon',
    },
    showAlertHeading: {
      control: 'boolean',
      description: 'Show/hide alert heading',
    },
    leftIcon: {
      control: 'object',
      description: 'Custom left icon component',
    },
  },
} satisfies Meta<typeof Alert>;

export default meta;
type Story = StoryObj<typeof Alert>;

export const Default: Story = {
  args: {
    headingText: 'Alert heading',
    bodyText: 'This is a default alert message.',
  },
};

export const Info: Story = {
  args: {
    headingText: 'Info alert',
    bodyText: 'This is an info alert message.',
    color: AlertColor.INFO,
  },
};

export const Success: Story = {
  args: {
    headingText: 'Success alert',
    bodyText: 'This is a success alert message.',
    color: AlertColor.SUCCESS,
  },
};

export const Warning: Story = {
  args: {
    headingText: 'Warning alert',
    bodyText: 'This is a warning alert message.',
    color: AlertColor.WARNING,
  },
};

export const Danger: Story = {
  args: {
    headingText: 'Danger alert',
    bodyText: 'This is a danger alert message.',
    color: AlertColor.DANGER,
  },
};

export const Notice: Story = {
  args: {
    headingText: 'Notice alert',
    bodyText: 'This is a notice alert message.',
    color: AlertColor.NOTICE,
  },
};

export const InlineInfo: Story = {
  args: {
    headingText: 'Something went wrong',
    color: AlertColor.INFO,
    inline: true,
  },
};

export const InlineSuccess: Story = {
  args: {
    headingText: 'Something went wrong',
    color: AlertColor.SUCCESS,
    inline: true,
  },
};

export const InlineWarning: Story = {
  args: {
    headingText: 'Something went wrong',
    color: AlertColor.WARNING,
    inline: true,
  },
};

export const InlineDanger: Story = {
  args: {
    headingText: 'Something went wrong',
    color: AlertColor.DANGER,
    inline: true,
  },
};

export const InlineNotice: Story = {
  args: {
    headingText: 'Notice message',
    color: AlertColor.NOTICE,
    inline: true,
  },
};

export const WithCustomButton: Story = {
  args: {
    ...Default.args,
    color: AlertColor.SUCCESS,
    headingText: 'Custom Button',
    bodyText: 'This alert has a custom button component.',
    button: (
      <Button
        color={ButtonColor.GREEN_PRIMARY}
        size={ButtonSize.XS}
        leftIcon={DiscoverCircleIcon}
        showLeftIcon={true}
      >
        Custom Action
      </Button>
    ),
  },
  parameters: {
    docs: {
      description: {
        story: `
Example of using a custom button component:

\`\`\`jsx
<Alert
  color={AlertColor.SUCCESS}
  headingText="Custom Button"
  bodyText="This alert has a custom button component."
  button={
    <Button
      color={ButtonColor.GREEN_PRIMARY}
      size={ButtonSize.XS}
      leftIconName="CheckCircle01"
      showLeftIcon={true}
    >
      Custom Action
    </Button>
  }
/>
\`\`\`
        `,
      },
    },
  },
};

export const WithCustomIcon: Story = {
  args: {
    ...Default.args,
    color: AlertColor.INFO,
    headingText: 'Custom Icon',
    bodyText: 'This alert uses a custom icon component.',
    leftIcon: AddTeamIcon,
  },
  parameters: {
    docs: {
      description: {
        story: `
Example of using a custom icon:

\`\`\`jsx
<Alert
  color={AlertColor.INFO}
  headingText="Custom Icon"
  bodyText="This alert uses a custom icon component."
  leftIcon={HugeIcons.Bell01Icon}
/>
\`\`\`
        `,
      },
    },
  },
};

export const AllVariants: Story = {
  render: () => (
    <div className="flex flex-col gap-4 p-4">
      <Alert
        color={AlertColor.SUCCESS}
        headingText="Success Alert"
        bodyText="This is a success alert message."
      />
      <Alert
        color={AlertColor.INFO}
        headingText="Info Alert"
        bodyText="This is an info alert message."
      />
      <Alert
        color={AlertColor.WARNING}
        headingText="Warning Alert"
        bodyText="This is a warning alert message."
      />
      <Alert
        color={AlertColor.DANGER}
        headingText="Error Alert"
        bodyText="This is an error alert message."
      />
      <Alert
        color={AlertColor.NOTICE}
        headingText="Notice Alert"
        bodyText="This is a notice alert message."
      />

      {/* Inline */}
      <Alert color={AlertColor.SUCCESS} headingText="Something went wrong" inline />
      <Alert color={AlertColor.INFO} headingText="Something went wrong" inline />
      <Alert color={AlertColor.WARNING} headingText="Something went wrong" inline />
      <Alert color={AlertColor.DANGER} headingText="Something went wrong" inline />
      <Alert color={AlertColor.NOTICE} headingText="Notice message" inline />
    </div>
  ),
};
