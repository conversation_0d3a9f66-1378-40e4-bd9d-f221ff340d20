import React from "react";
import type { <PERSON><PERSON>, <PERSON><PERSON>bj } from "@storybook/react";
import { <PERSON><PERSON> } from "./Button";
import {
  ButtonColor,
  ButtonSize,
  ButtonState,
  ButtonType,
} from "./Button.types";
import { ArrowLeft01Icon, ArrowRight01Icon, Menu01Icon } from '@hugeicons/react';

const SIZES = [
  ButtonSize.XL,
  ButtonSize.L,
  ButtonSize.BASE,
  ButtonSize.SM,
  ButtonSize.XS,
] as const;

const COLORS = [
  ButtonColor.GREEN_PRIMARY,
  ButtonColor.BLUE_SECONDARY,
  ButtonColor.RED,
  ButtonColor.YELLOW,
  ButtonColor.ORANGE,
] as const;

const TYPES = [
  ButtonType.PRIMARY,
  ButtonType.SECONDARY,
  ButtonType.TERTIARY,
] as const;

const meta = {
  title: "Components/Button",
  component: Button,
  parameters: {
    layout: "centered",
    docs: {
      description: {
        component: `
## Button Component

A versatile button component that supports multiple variants, sizes, and states. Use it for primary actions, secondary options, or tertiary choices in your interface.

### Key Features
- Multiple visual styles (primary, secondary, tertiary)
- Various size options (xs to xl)
- Support for icons (left, right, or both)
- Disabled state handling
- Link functionality with href prop
- Color variants for different contexts

### Usage Guidelines

1. **Choose the right type:**
   - \`primary\`: Use for main actions (e.g., "Submit", "Save")
   - \`secondary\`: For alternative actions (e.g., "Cancel", "Back")
   - \`tertiary\`: For less prominent actions (e.g., "Learn More")

2. **Size selection:**
   - \`xl\`: Hero or CTA buttons
   - \`l\`: Main action buttons
   - \`base\`: Standard buttons
   - \`sm\`: Form buttons
   - \`xs\`: Compact UI areas

3. **Color usage:**
   - \`green-primary\`: Success, confirmation actions
   - \`blue-secondary\`: Information, neutral actions
   - \`red\`: Destructive actions, warnings
   - \`yellow\`: Attention-grabbing actions

### Examples

\`\`\`jsx
// Primary button with text
<Button 
  type="primary"
  color="green-primary"
  size="base"
  buttonText="Submit Form"
/>

// Secondary button with icon
<Button 
  type="secondary"
  color="blue-secondary"
  size="sm"
  buttonText="Back"
  leftIcon={ArrowLeft01Icon}
  showLeftIcon
/>

// Link button
<Button 
  type="tertiary"
  color="blue-secondary"
  size="base"
  buttonText="Learn More"
  href="/docs"
/>
\`\`\`

### Accessibility
- Buttons automatically handle focus states
- Link buttons (\`href\` prop) render as \`<a>\` tags
- Icon-only buttons include aria-label
- Disabled state is properly handled for screen readers
`,
      },
    },
  },
  tags: ["autodocs"],
  argTypes: {
    type: {
      control: "select",
      options: Object.values(ButtonType),
      description: "Visual style variant",
      table: {
        defaultValue: { summary: ButtonType.PRIMARY },
      },
    },
    color: {
      control: "select",
      options: COLORS,
      description: "Color theme of the button",
      table: {
        defaultValue: { summary: ButtonColor.GREEN_PRIMARY },
        type: { summary: Object.values(ButtonColor).join(" | ") },
      },
    },
    size: {
      control: "select",
      options: SIZES,
      description: "Size variant of the button",
      table: {
        defaultValue: { summary: ButtonSize.BASE },
        type: { summary: Object.values(ButtonSize).join(" | ") },
      },
    },
    state: {
      control: "select",
      options: Object.values(ButtonState),
      description: "Current state of the button",
      table: {
        defaultValue: { summary: ButtonState.DEFAULT },
      },
    },
    showLeftIcon: {
      control: "boolean",
      description: "Show/hide left icon",
      table: {
        defaultValue: { summary: "false" },
      },
    },
    showRightIcon: {
      control: "boolean",
      description: "Show/hide right icon",
      table: {
        defaultValue: { summary: "false" },
      },
    },
    iconOnly: {
      control: "boolean",
      description: "Render as icon-only button (square)",
      table: {
        defaultValue: { summary: "false" },
      },
    },
    href: {
      control: "text",
      description: "URL for link buttons (renders as <a> tag)",
      table: {
        type: { summary: "string" },
      },
    },
    leftIcon: {
      control: "object",
      description: "Icon component to show on the left",
      table: {
        type: { summary: "React.ComponentType" },
      },
    },
    rightIcon: {
      control: "object",
      description: "Icon component to show on the right",
      table: {
        type: { summary: "React.ComponentType" },
      },
    },
  },
} satisfies Meta<typeof Button>;

export default meta;
type Story = StoryObj<typeof Button>;

// Base stories with detailed documentation
export const Primary: Story = {
  args: {
    type: ButtonType.PRIMARY,
    color: ButtonColor.GREEN_PRIMARY,
    size: ButtonSize.BASE,
    children: "Primary Button",
    showLeftIcon: false,
    showRightIcon: false,
  },
  parameters: {
    docs: {
      description: {
        story: `
Primary buttons are used for the main action in a section or form. 
They have the strongest visual weight and should be used sparingly - typically once per view.

\`\`\`jsx
<Button
  type="primary"
  color="green-primary"
  size="base"
>
  Save Changes
</Button>
\`\`\`
        `,
      },
    },
  },
};

export const Secondary: Story = {
  args: {
    ...Primary.args,
    type: ButtonType.SECONDARY,
    children: "Secondary Button",
  },
  parameters: {
    docs: {
      description: {
        story: `
Secondary buttons are for alternative actions. They work well for "Cancel" or "Back" actions
that need to be visible but not dominant.

\`\`\`jsx
<Button
  type="secondary"
  color="blue-secondary"
  size="base"
  buttonText="Cancel"
/>
\`\`\`
        `,
      },
    },
  },
};

export const Tertiary: Story = {
  args: {
    ...Primary.args,
    type: ButtonType.TERTIARY,
    children: "Tertiary Button",
  },
  parameters: {
    docs: {
      description: {
        story: `
Tertiary buttons have the lightest visual weight. Use them for supplementary actions
or in areas with many equal-weight actions.

\`\`\`jsx
<Button
  type="tertiary"
  color="blue-secondary"
  size="base"
  buttonText="Learn More"
/>
\`\`\`
        `,
      },
    },
  },
};

// Example of a specific use case
export const DestructiveAction: Story = {
  args: {
    type: ButtonType.PRIMARY,
    color: ButtonColor.RED,
    size: ButtonSize.BASE,
    children: "Delete Account",
    leftIcon: Menu01Icon,
    showLeftIcon: true,
  },
  parameters: {
    docs: {
      description: {
        story: `
Use red buttons for destructive actions that need careful consideration.
Always combine with confirmation dialogs for irreversible actions.

\`\`\`jsx
<Button
  type="primary"
  color="red"
  size="base"
  buttonText="Delete Account"
  leftIcon={Menu01Icon}
  showLeftIcon
/>
\`\`\`
        `,
      },
    },
  },
};

// Showcase all variants
export const AllVariants: Story = {
  render: () => {
    return (
      <div className="p-8 max-w-[1200px]">
        {/* Regular Buttons */}
        <h2 className="text-2xl font-bold mb-8 text-dark-40">
          Button Variants
        </h2>

        {COLORS.map((color) => (
          <div
            key={color}
            className="mb-12 rounded-lg border border-neutral-30 overflow-hidden bg-neutral-10"
          >
            <h3 className="text-xl font-semibold p-4 bg-neutral-20 border-b border-neutral-30 capitalize text-dark-40">
              {color.replace(/_/g, " ").toLowerCase()}
            </h3>
            <div className="p-4">
              <table className="border-collapse w-full">
                <thead>
                  <tr className="bg-neutral-20">
                    <th className="p-4 text-left border-b-2 border-neutral-30 w-32 text-dark-30">
                      Type
                    </th>
                    <th className="p-4 text-left border-b-2 border-neutral-30 text-dark-30">
                      Regular
                    </th>
                    <th className="p-4 text-left border-b-2 border-neutral-30 text-dark-30">
                      Disabled
                    </th>
                  </tr>
                </thead>
                <tbody>
                  {TYPES.map((type) => (
                    <tr
                      key={type}
                      className="border-b border-neutral-30 hover:bg-neutral-20"
                    >
                      <td className="p-4 font-medium capitalize text-dark-40">
                        {type.toLowerCase()}
                      </td>
                      <td className="p-4">
                        <div className="flex flex-wrap items-center gap-4">
                          {SIZES.map((size) => (
                            <Button
                              key={size}
                              color={color}
                              size={size}
                              type={type}
                              showLeftIcon={false}
                              showRightIcon={false}
                            >
                              Button text
                            </Button>
                          ))}
                        </div>
                      </td>
                      <td className="p-4">
                        <div className="flex flex-wrap items-center gap-4">
                          {SIZES.map((size) => (
                            <Button
                              key={size}
                              color={color}
                              size={size}
                              state={ButtonState.DISABLED}
                              type={type}
                              showLeftIcon={false}
                              showRightIcon={false}
                            >
                              Button text
                            </Button>
                          ))}
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
        ))}

        {/* With Icons */}
        <h2 className="text-2xl font-bold mt-12 mb-6 text-dark-40">
          Button with Icons
        </h2>
        <div className="p-6 rounded-lg border border-neutral-30 bg-neutral-10">
          <div className="flex flex-wrap gap-4">
            <Button
              type={ButtonType.PRIMARY}
              color={ButtonColor.YELLOW}
              size={ButtonSize.BASE}
              leftIcon={ArrowLeft01Icon}
              rightIcon={ArrowRight01Icon}
              showLeftIcon={true}
              showRightIcon={true}
            >
              Both Icons
            </Button>
            <Button
              type={ButtonType.PRIMARY}
              color={ButtonColor.YELLOW}
              size={ButtonSize.BASE}
              leftIcon={ArrowLeft01Icon}
              showLeftIcon={true}
              showRightIcon={false}
            >
              Left Icon Only
            </Button>
            <Button
              type={ButtonType.PRIMARY}
              color={ButtonColor.YELLOW}
              size={ButtonSize.BASE}
              rightIcon={ArrowRight01Icon}
              showLeftIcon={false}
            >
              Right Icon Only
            </Button>
          </div>
        </div>

        {/* Icon Only Buttons */}
        <h2 className="text-2xl font-bold mt-12 mb-6 text-dark-40">
          Icon Only Buttons
        </h2>
        {COLORS.map((color) => (
          <div
            key={color}
            className="mb-12 rounded-lg border border-neutral-30 overflow-hidden bg-neutral-10"
          >
            <h3 className="text-xl font-semibold p-4 bg-neutral-20 border-b border-neutral-30 capitalize text-dark-40">
              {color.replace(/_/g, " ").toLowerCase()}
            </h3>
            <div className="p-4">
              <table className="border-collapse w-full">
                <thead>
                  <tr className="bg-neutral-20">
                    <th className="p-4 text-left border-b-2 border-neutral-30 w-32 text-dark-30">
                      Type
                    </th>
                    <th className="p-4 text-left border-b-2 border-neutral-30 text-dark-30">
                      Regular
                    </th>
                    <th className="p-4 text-left border-b-2 border-neutral-30 text-dark-30">
                      Disabled
                    </th>
                  </tr>
                </thead>
                <tbody>
                  {TYPES.map((type) => (
                    <tr
                      key={type}
                      className="border-b border-neutral-30 hover:bg-neutral-20"
                    >
                      <td className="p-4 font-medium capitalize text-dark-40">
                        {type.toLowerCase()}
                      </td>
                      <td className="p-4">
                        <div className="flex flex-wrap items-center gap-4">
                          {SIZES.map((size) => (
                            <Button
                              key={size}
                              color={color}
                              size={size}
                              type={type}
                              showLeftIcon={false}
                              showRightIcon={false}
                            >
                              Button text
                            </Button>
                          ))}
                        </div>
                      </td>
                      <td className="p-4">
                        <div className="flex flex-wrap items-center gap-4">
                          {SIZES.map((size) => (
                            <Button
                              key={size}
                              color={color}
                              size={size}
                              state={ButtonState.DISABLED}
                              type={type}
                              showLeftIcon={false}
                              showRightIcon={false}
                            >
                              Button text
                            </Button>
                          ))}
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
        ))}

        {/* Links */}
        <h2 className="text-2xl font-bold mt-12 mb-6 text-dark-40">
          Link Buttons
        </h2>
        <div className="p-6 rounded-lg border border-neutral-30 bg-neutral-10">
          <div className="flex flex-wrap gap-4">
            <Button
              type={ButtonType.PRIMARY}
              color={ButtonColor.YELLOW}
              size={ButtonSize.BASE}
              href="#"
            >
              Link Button
            </Button>
            <Button
              type={ButtonType.SECONDARY}
              color={ButtonColor.YELLOW}
              size={ButtonSize.BASE}
              href="#"
            >
              Link Button
            </Button>
          </div>
        </div>
      </div>
    );
  },
};
