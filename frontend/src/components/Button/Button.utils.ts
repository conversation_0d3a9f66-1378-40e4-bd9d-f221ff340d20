import React from 'react';

type SvgElementProps = React.SVGProps<SVGPathElement>;

type SvgElementTuple = [string, SvgElementProps];

type IconInput =
  | {
      paths?: SvgElementTuple[];
      path?: string;
      d?: string;
      pathProps?: React.SVGProps<SVGPathElement>;
    }
  | SvgElementTuple[];

/**
 * Converts various icon data formats into a standardized array of SVG element tuples.
 * @param icon The icon data in one of the supported formats.
 * @returns An array of SVG element tuples, e.g., [['path', { d: '...' }], ['circle', { cx: '...' }]].
 */
export const convertIconToPaths = (icon: IconInput): SvgElementTuple[] => {
  if (!icon || typeof icon !== 'object') {
    return [];
  }

  if (
    Array.isArray(icon) &&
    icon.every(
      (item) =>
        Array.isArray(item) &&
        item.length === 2 &&
        typeof item[0] === 'string' &&
        typeof item[1] === 'object'
    )
  ) {
    return icon;
  }

  if (Array.isArray(icon)) {
    return [];
  }

  if (Array.isArray(icon.paths)) {
    return icon.paths;
  }

  if (icon.path) {
    return [['path', { d: icon.path, ...icon.pathProps }]];
  }

  if (icon.d) {
    return [['path', { d: icon.d }]];
  }

  return [];
};
