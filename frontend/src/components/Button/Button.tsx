import React from "react";
import classNames from "classnames";
import styles from "./Button.module.scss";
import {
  ButtonColor,
  ButtonProps,
  ButtonSize,
  ButtonState,
  ButtonType,
} from "./Button.types";
import { Spinner } from "../Spinner";

export const Button = ({
  showRightIcon = false,
  showLeftIcon = false,
  children,
  leftIcon,
  rightIcon,
  type = ButtonType.PRIMARY,
  color = ButtonColor.GREEN_PRIMARY,
  size = ButtonSize.L,
  state = ButtonState.DEFAULT,
  outline = false,
  iconOnly = false,
  className,
  href,
  onClick,
  loading = false,
  multiLine = false,
}: ButtonProps): JSX.Element => {
  const buttonClasses = classNames(
    styles.button,
    styles[type],
    styles[size],
    styles[color],
    {
      [styles.withText]: !iconOnly,
      [styles.iconOnly]: iconOnly,
      [styles.disabled]: state === ButtonState.DISABLED,
      [styles.outline]: outline,
      [styles.loading]: loading,
    },
    className
  );

  const iconClasses = classNames(styles.icon, styles[`icon-${size}`]);
  const textClasses = classNames(styles.text, styles[`text-${size}`], {
    [styles.multiLine]: multiLine,
  });

  const renderIcon = (Icon: typeof leftIcon | typeof rightIcon) => {
    if (!Icon) return null;
    if (React.isValidElement(Icon)) {
      return Icon;
    }
    if (typeof Icon === 'function' || typeof Icon === 'object') {
      const Comp = Icon as React.ElementType;
      return <Comp className={iconClasses} size={24} />;
    }
    return null;
  };

  const buttonContent = (
    <>
      {!iconOnly && showLeftIcon && renderIcon(leftIcon)}
      {!iconOnly && children && <span className={textClasses}>{children}</span>}
      {!iconOnly && showRightIcon && renderIcon(rightIcon)}
      {iconOnly && renderIcon(leftIcon)}
      {loading && (
        <Spinner color="white" />
      )}
    </>
  );

  if (href) {
    return (
      <a href={href} className={buttonClasses} onClick={onClick}>
        {buttonContent}
      </a>
    );
  }

  return (
    <button
      className={buttonClasses}
      disabled={state === ButtonState.DISABLED}
      onClick={onClick}
    >
      {buttonContent}
    </button>
  );
};
