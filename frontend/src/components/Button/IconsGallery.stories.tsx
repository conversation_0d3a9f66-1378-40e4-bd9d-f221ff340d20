import React from "react";
import type { Meta, StoryObj } from "@storybook/react";
import { FixedSizeGrid } from "react-window";
import { HugeiconsIcon } from "../HugeiconsIcon";
import { convertIconToPaths } from "./Button.utils";
import { IconSvgObject } from "@hugeicons/react";
import * as BulkRounded from "@hugeicons-pro/core-bulk-rounded";
import * as DuotoneRounded from "@hugeicons-pro/core-duotone-rounded";
import * as SolidRounded from "@hugeicons-pro/core-solid-rounded";
import * as SolidSharp from "@hugeicons-pro/core-solid-sharp";
import * as SolidStandard from "@hugeicons-pro/core-solid-standard";
import * as StrokeRounded from "@hugeicons-pro/core-stroke-rounded";
import * as StrokeSharp from "@hugeicons-pro/core-stroke-sharp";
import * as StrokeStandard from "@hugeicons-pro/core-stroke-standard";
import * as TwotoneRounded from "@hugeicons-pro/core-twotone-rounded";

const meta = {
  title: "Components/Icons Gallery",
  parameters: {
    layout: "fullscreen",
    docs: {
      description: {
        component: `
## Hugeicons Pro Gallery

This is a showcase of all available icons from Hugeicons Pro packages. 
Click on any icon to copy its name to clipboard.
`,
      },
    },
  },
} satisfies Meta;

export default meta;
type Story = StoryObj;

const COLUMN_WIDTH = 160;
const ROW_HEIGHT = 100;
const PADDING = 32;

interface IconData {
  name: string;
  paths: Array<[string, React.SVGProps<SVGPathElement>]>;
  category: string;
}

const iconPackages = {
  "Bulk Rounded": BulkRounded,
  "Duotone Rounded": DuotoneRounded,
  "Solid Rounded": SolidRounded,
  "Solid Sharp": SolidSharp,
  "Solid Standard": SolidStandard,
  "Stroke Rounded": StrokeRounded,
  "Stroke Sharp": StrokeSharp,
  "Stroke Standard": StrokeStandard,
  "Twotone Rounded": TwotoneRounded,
} as const;

const FILTERS = [
  { id: "all", label: "All" },
  { id: "Stroke Standard", label: "Stroke", sublabel: "(standard)" },
  { id: "Solid Standard", label: "Solid", sublabel: "(standard)" },
  { id: "Stroke Rounded", label: "Stroke" },
  { id: "Duotone Rounded", label: "Duotone" },
  { id: "Twotone Rounded", label: "Twotone" },
  { id: "Solid Rounded", label: "Solid" },
  { id: "Bulk Rounded", label: "Bulk" },
  { id: "Stroke Sharp", label: "Stroke", sublabel: "(sharp)" },
  { id: "Solid Sharp", label: "Solid", sublabel: "(sharp)" },
] as const;

type FilterId = (typeof FILTERS)[number]["id"];

const FilterButton: React.FC<{
  filter: (typeof FILTERS)[number];
  isActive: boolean;
  onClick: () => void;
}> = ({ filter, isActive, onClick }) => (
  <button
    type="button"
    onClick={onClick}
    className={`
      flex relative items-center gap-3 px-4 py-[13px] rounded-lg h-[44px] 
      border border-solid transition-all
      ${
        isActive
          ? "bg-[#C5D1DA] font-semibold border-transparent"
          : "bg-[#EFF0F3] border-[#ECEEF2] font-normal"
      }
    `}
  >
    <span className="text-[#2B363E] text-sm leading-[18px] tracking-[-0.07px]">
      {filter.label}
    </span>
    {"sublabel" in filter && (
      <span className="text-sm opacity-50 -ml-2">{filter.sublabel}</span>
    )}
  </button>
);

interface CellProps {
  columnIndex: number;
  rowIndex: number;
  style: React.CSSProperties;
  data: {
    icons: IconData[];
    columnCount: number;
    copiedIcon: string | null;
    onCopy: (name: string) => void;
  };
}

const Cell: React.FC<CellProps> = React.memo(
  ({ columnIndex, rowIndex, style, data }) => {
    const { icons, columnCount, copiedIcon, onCopy } = data;
    const itemIndex = rowIndex * columnCount + columnIndex;
    if (itemIndex >= icons.length) return null;

    const { name, paths, category } = icons[itemIndex];

    return (
      <div style={style}>
        <div
          className="flex flex-col items-center justify-center mx-2 my-1 p-3 border border-gray-200 rounded hover:border-blue-500 cursor-pointer h-[90px] bg-white"
          onClick={() => onCopy(name)}
        >
          <HugeiconsIcon icon={paths as unknown as IconSvgObject} size={24} />
          <div className="mt-2 text-xs text-center space-y-1 w-full px-1">
            {copiedIcon === name ? (
              <span className="text-green-600">Copied!</span>
            ) : (
              <>
                <div className="text-gray-700 truncate">{name}</div>
                <div className="text-gray-500">{category}</div>
              </>
            )}
          </div>
        </div>
      </div>
    );
  }
);

Cell.displayName = "Cell";

// Update IconGallery component
const IconGallery = () => {
  const [searchTerm, setSearchTerm] = React.useState("");
  const [activeFilter, setActiveFilter] = React.useState<FilterId>("all");
  const [copiedIcon, setCopiedIcon] = React.useState<string | null>(null);

  const processedIcons = React.useMemo(() => {
    const searchLower = searchTerm.toLowerCase();
    const allIcons: IconData[] = [];

    Object.entries(iconPackages).forEach(([packageName, packageModule]) => {
      Object.entries(packageModule)
        .filter(([name]) => name.toLowerCase().includes(searchLower))
        .forEach(([name, icon]) => {
          allIcons.push({
            name,
            paths: convertIconToPaths(icon as unknown as IconSvgObject),
            category: packageName,
          });
        });
    });

    if (activeFilter !== "all") {
      return allIcons.filter((icon) => icon.category === activeFilter);
    }

    return allIcons;
  }, [searchTerm, activeFilter]);

  const copyToClipboard = React.useCallback((iconName: string) => {
    navigator.clipboard.writeText(iconName);
    setCopiedIcon(iconName);
    setTimeout(() => setCopiedIcon(null), 1000);
  }, []);

  const containerRef = React.useRef<HTMLDivElement>(null);
  const [containerWidth, setContainerWidth] = React.useState(0);

  React.useEffect(() => {
    const updateWidth = () => {
      if (containerRef.current) {
        setContainerWidth(containerRef.current.clientWidth);
      }
    };

    updateWidth();
    window.addEventListener("resize", updateWidth);
    return () => window.removeEventListener("resize", updateWidth);
  }, []);

  const columnCount = Math.floor((containerWidth - PADDING * 2) / COLUMN_WIDTH);
  const rowCount = Math.ceil(processedIcons.length / columnCount);

  return (
    <div className="min-h-screen bg-gray-50 w-full">
      <div className="max-w-[1600px] mx-auto px-8">
        <div className="sticky top-0 bg-gray-50 py-6 z-10 space-y-4">
          <input
            type="text"
            placeholder="Search icons..."
            className="w-full p-2 border border-gray-300 rounded bg-white"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
          />
          <div className="flex gap-1 overflow-x-auto pb-2">
            {FILTERS.map((filter) => (
              <FilterButton
                key={filter.id}
                filter={filter}
                isActive={activeFilter === filter.id}
                onClick={() => setActiveFilter(filter.id)}
              />
            ))}
          </div>
        </div>
        <div
          ref={containerRef}
          className="bg-white rounded-lg border border-gray-200 p-4"
        >
          <FixedSizeGrid
            className="scrollbar-thin scrollbar-thumb-gray-300 scrollbar-track-transparent"
            columnCount={columnCount}
            columnWidth={COLUMN_WIDTH}
            height={Math.min(rowCount * ROW_HEIGHT, window.innerHeight - 300)}
            rowCount={rowCount}
            rowHeight={ROW_HEIGHT}
            width={containerWidth - PADDING * 2}
            itemData={{
              icons: processedIcons,
              columnCount,
              copiedIcon,
              onCopy: copyToClipboard,
            }}
          >
            {Cell}
          </FixedSizeGrid>
        </div>
        <div className="py-4 text-sm text-gray-500">
          Total icons: {processedIcons.length}
        </div>
      </div>
    </div>
  );
};

IconGallery.displayName = "IconGallery";

export const Gallery: Story = {
  render: () => <IconGallery />,
  parameters: {
    docs: {
      description: {
        story: `
### Using Icons

To use an icon in your component:

\`\`\`jsx
import { AbacusIcon } from "@hugeicons-pro/core-stroke-rounded";
// ... use the icon component
\`\`\`

Click on any icon in the gallery to copy its name to clipboard.
`,
      },
    },
  },
};
