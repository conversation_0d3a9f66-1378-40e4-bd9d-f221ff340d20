.button {
  display: inline-flex;
  position: relative;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-2);
  border-radius: var(--border-radius-rounded-full);
  transition: all 0.2s ease-in-out;
  max-width: 100%;

  &.withText {
    justify-content: center;
  }

  // Regular button icon sizes
  &:not(.iconOnly) {

    &.xl,
    &.l {
      svg {
        width: 16px;
        height: 16px;
      }
    }

    &.base {
      svg {
        width: 14px;
        height: 14px;
      }
    }

    &.sm,
    &.xs {
      svg {
        width: 12px;
        height: 12px;
      }
    }
  }

  // Icon only styles
  &.iconOnly {

    // Icon sizes
    &.xl,
    &.l {
      svg {
        width: 16px;
        height: 16px;
      }

      padding: var(--spacing-3-5);
    }

    &.l {
      padding: var(--spacing-3);
    }

    &.base {
      svg {
        width: 14px;
        height: 14px;
      }

      padding: var(--spacing-2-5);
    }

    &.sm,
    &.xs,
    &.xxs {
      display: flex;
      align-items: center;
      justify-content: center;

      svg {
        width: 16px;
        height: 16px;
      }

      width: 34px;
      height: 34px;
      padding: 0;
    }

    &.xxs {
      width: 24px;
      height: 24px;

      svg {
        width: 14px;
        height: 14px;
      }
    }

    // Padding for sm and xs
    &.sm {
      padding: var(--spacing-2);
    }

    &.xs {
      padding: var(--spacing-1);
    }
  }

  &.loading {
    position: relative;
  }
}

// Base types
.primary {
  background-color: var(--colors-blue-700);
  color: var(--colors-white);
  border: none;

  &:hover:not(:disabled) {
    background-color: var(--colors-blue-800);
  }

  &:focus:not(:disabled) {
    outline: 3px solid var(--colors-blue-200);
    outline-offset: 0;
  }
}

.secondary {
  background-color: transparent;
  border: 1px solid var(--colors-blue-700);
  color: var(--colors-blue-700);

  &:hover:not(:disabled) {
    color: var(--colors-white);
  }

  &:focus:not(:disabled) {
    outline: 3px solid var(--colors-blue-200);
    outline-offset: 0;
  }
}

.tertiary {
  background-color: transparent;
  border: none;

  &:focus:not(:disabled) {
    outline: 2px solid var(--colors-blue-200);
    outline-offset: 0;
  }
}

// Color variants
.blue-secondary {
  &.primary {
    background-color: var(--colors-blue-700);

    &:hover:not(:disabled) {
      background-color: var(--colors-blue-800);
    }

    &:focus:not(:disabled) {
      outline-color: var(--colors-blue-200);
    }
  }

  &.secondary {
    border-color: var(--colors-blue-700);
    color: var(--colors-blue-700);

    &:hover:not(:disabled) {
      background-color: var(--colors-blue-700);
    }

    &:focus:not(:disabled) {
      outline: 3px solid var(--colors-blue-200);
      outline-offset: 0;
    }
  }

  &.tertiary {
    color: var(--colors-blue-700);

    &:hover:not(:disabled) {
      color: var(--colors-blue-800);
    }

    &:focus:not(:disabled) {
      outline: 3px solid var(--colors-blue-200);
      outline-offset: 0;
    }
  }
}

.green-primary {
  &.primary {
    background-color: var(--colors-green-700);

    &:hover:not(:disabled) {
      background-color: var(--colors-green-800);
    }

    &:focus:not(:disabled) {
      outline-color: var(--colors-green-200);
    }
  }

  &.secondary {
    border-color: var(--colors-green-700);
    color: var(--colors-green-700);

    &:hover:not(:disabled) {
      background-color: var(--colors-green-700);
    }

    &:focus:not(:disabled) {
      outline: 3px solid var(--colors-green-200);
      outline-offset: 0;
    }
  }

  &.tertiary {
    color: var(--colors-green-700);

    &:hover:not(:disabled) {
      color: var(--colors-green-800);
    }

    &:focus:not(:disabled) {
      outline: 3px solid var(--colors-green-200);
      outline-offset: 0;
    }
  }
}

.red {
  &.primary {
    background-color: var(--colors-red-700);

    &:hover:not(:disabled) {
      background-color: var(--colors-red-800);
    }

    &:focus:not(:disabled) {
      outline-color: var(--colors-red-200);
    }
  }

  &.secondary {
    border-color: var(--colors-red-700);
    color: var(--colors-red-700);

    &:hover:not(:disabled) {
      background-color: var(--colors-red-700);
    }

    &:focus:not(:disabled) {
      outline: 3px solid var(--colors-red-200);
      outline-offset: 0;
    }
  }

  &.tertiary {
    color: var(--colors-red-700);

    &:hover:not(:disabled) {
      color: var(--colors-red-800);
    }

    &:focus:not(:disabled) {
      outline: 3px solid var(--colors-red-200);
      outline-offset: 0;
    }
  }
}

.yellow {
  &.primary {
    background-color: var(--colors-yellow-700);

    &:hover:not(:disabled) {
      background-color: var(--colors-yellow-800);
    }

    &:focus:not(:disabled) {
      outline-color: var(--colors-yellow-200);
    }
  }

  &.secondary {
    border-color: var(--colors-yellow-700);
    color: var(--colors-yellow-700);

    &:hover:not(:disabled) {
      background-color: var(--colors-yellow-700);
    }

    &:focus:not(:disabled) {
      outline: 3px solid var(--colors-yellow-200);
      outline-offset: 0;
    }
  }

  &.tertiary {
    color: var(--colors-yellow-700);

    &:hover:not(:disabled) {
      color: var(--colors-yellow-800);
    }

    &:focus:not(:disabled) {
      outline: 3px solid var(--colors-yellow-200);
      outline-offset: 0;
    }
  }
}

.orange {
  &.primary {
    background-color: var(--colors-orange-700);

    &:hover:not(:disabled) {
      background-color: var(--colors-orange-800);
    }

    &:focus:not(:disabled) {
      outline-color: var(--colors-orange-200);
    }
  }

  &.secondary {
    border-color: var(--colors-orange-700);
    color: var(--colors-orange-700);

    &:hover:not(:disabled) {
      background-color: var(--colors-orange-700);
    }

    &:focus:not(:disabled) {
      outline: 3px solid var(--colors-orange-200);
      outline-offset: 0;
    }
  }

  &.tertiary {
    color: var(--colors-orange-700);

    &:hover:not(:disabled) {
      color: var(--colors-orange-800);
    }

    &:focus:not(:disabled) {
      outline: 3px solid var(--colors-orange-200);
      outline-offset: 0;
    }
  }
}

// Sizes
.xl:not(.iconOnly) {
  padding: var(--spacing-3-5) var(--spacing-6);
}

.l:not(.iconOnly) {
  padding: var(--spacing-3) var(--spacing-5);
}

.base:not(.iconOnly) {
  padding: var(--spacing-2-5) var(--spacing-5);
}

.sm:not(.iconOnly) {
  padding: var(--spacing-2) var(--spacing-3);
}

.xs:not(.iconOnly) {
  padding: var(--spacing-2) var(--spacing-3);
}

// States
.disabled {
  cursor: not-allowed;
  pointer-events: none;

  // Primary variant disabled styles
  &.primary {
    &.blue-secondary {
      background-color: #A6BCCB;
    }

    &.green-primary {
      background-color: #A4C5BD;
    }

    &.red {
      background-color: #CBA7B0;
    }

    &.yellow {
      background-color: #DAC3A2;
    }

    &.orange {
      background-color: #DAC3A2;
    }
  }

  // Secondary variant disabled styles
  &.secondary {
    &.blue-secondary {
      border-color: #A6BCCB;
      color: #A6BCCB;
    }

    &.green-primary {
      border-color: #A4C5BD;
      color: #A4C5BD;
    }

    &.red {
      border-color: #CBA7B0;
      color: #CBA7B0;
    }

    &.yellow {
      border-color: #DAC3A2;
      color: #DAC3A2;
    }

    &.orange {
      border-color: #DAC3A2;
      color: #DAC3A2;
    }
  }

  // Tertiary variant disabled styles
  &.tertiary {
    &.blue-secondary {
      color: #A6BCCB;
    }

    &.green-primary {
      color: #A4C5BD;
    }

    &.red {
      color: #CBA7B0;
    }

    &.yellow {
      color: #DAC3A2;
    }

    &.orange {
      color: #DAC3A2;
    }
  }
}

// Text styles
.text {
  font-family: 'Quasimoda', Helvetica;
  font-weight: 400;
  text-align: left;
  text-underline-position: from-font;
  text-decoration-skip-ink: none;
  white-space: nowrap;
  position: relative;
  overflow: hidden;
  text-overflow: ellipsis;
  display: inline-block;
  max-width: 100%;

  &.multiLine {
    text-align: center;
    white-space: normal;
    text-overflow: unset;
    overflow: unset;
    hyphens: auto;
  }
}

.xl,
.l {
  .text {
    font-size: 1rem; // 16px
    line-height: 1.5rem; // 24px
  }
}

.base,
.sm {
  .text {
    font-size: 0.875rem; // 14px
    line-height: 1.3125rem; // 21px
  }
}

.xs {
  .text {
    font-size: 0.75rem; // 12px
    line-height: 1.125rem; // 18px
  }
}

.gray-700 {
  &.tertiary {
    color: #374151;

    &:hover:not(:disabled) {
      color: #1f2937;
    }
  }
}

.white {
  &.tertiary {
    color: var(--colors-white);
  }
}

.button {
  &.iconOnly {
    &.xs {
      width: 24px;
      height: 24px;
      padding: 0;

      svg {
        width: 16px;
        height: 16px;
      }
    }
  }
}