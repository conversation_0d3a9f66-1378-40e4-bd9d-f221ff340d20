export enum ButtonType {
  PRIMARY = "primary",
  SECONDARY = "secondary",
  TERTIARY = "tertiary"
}

export enum ButtonColor {
  BLUE_SECONDARY = "blue-secondary",
  GREEN_PRIMARY = "green-primary",
  RED = "red",
  YELLOW = "yellow",
  ORANGE = "orange",
}

export enum ButtonSize {
  XS = "xs",
  SM = "sm",
  BASE = "base",
  L = "l",
  XL = "xl"
}

export enum ButtonState {
  DEFAULT = "default",
  HOVER = "hover",
  FOCUS = "focus",
  DISABLED = "disabled"
}

export interface ButtonProps {
  showRightIcon?: boolean;
  showLeftIcon?: boolean;
  children?: React.ReactNode;
  leftIcon?: React.ElementType | React.ReactNode;
  rightIcon?: React.ElementType | React.ReactNode;
  type?: ButtonType;
  color?: ButtonColor;
  size?: ButtonSize;
  state?: ButtonState;
  outline?: boolean;
  iconOnly?: boolean;
  className?: string;
  href?: string;
  onClick?: () => void;
  loading?: boolean;
  disabled?: boolean;
  multiLine?: boolean;
}