import '@testing-library/jest-dom';
import React from 'react';

// Mock CSS modules
jest.mock('../Button.module.scss', () => ({
  button: 'button',
  primary: 'primary',
  secondary: 'secondary',
  tertiary: 'tertiary',
  'blue-secondary': 'blue-secondary',
  'green-primary': 'green-primary',
  red: 'red',
  yellow: 'yellow',
  xs: 'xs',
  sm: 'sm',
  base: 'base',
  l: 'l',
  xl: 'xl',
  disabled: 'disabled',
  withText: 'withText',
  iconOnly: 'iconOnly',
  icon: 'icon',
  text: 'text',
}));

// Mock the entire @hugeicons/react module with a more efficient approach
jest.mock('@hugeicons/react', () => new Proxy({}, {
  get: () => {
    return React.createElement.bind(null, 'div', {
      'data-testid': 'mock-icon',
      className: 'mock-icon'
    });
  }
}));

// Global test setup
beforeEach(() => {
  jest.clearAllMocks();
}); 