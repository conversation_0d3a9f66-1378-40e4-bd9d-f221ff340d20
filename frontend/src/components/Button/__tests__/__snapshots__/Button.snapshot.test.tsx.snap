// Vitest Snapshot v1, https://vitest.dev/guide/snapshot.html

exports[`Button Snapshots > matches button with icons snapshot 1`] = `
<div>
  <button
    class="_button_ee73f7 _primary_ee73f7 _base_ee73f7 _blue-secondary_ee73f7 _withText_ee73f7"
  >
    <span
      class="_text_ee73f7 _text-base_ee73f7"
    >
      Test Button
    </span>
  </button>
</div>
`;

exports[`Button Snapshots > matches disabled button snapshot 1`] = `
<div>
  <button
    class="_button_ee73f7 _primary_ee73f7 _base_ee73f7 _blue-secondary_ee73f7 _withText_ee73f7 _disabled_ee73f7"
    disabled=""
  >
    <span
      class="_text_ee73f7 _text-base_ee73f7"
    >
      Test Button
    </span>
  </button>
</div>
`;

exports[`Button Snapshots > matches icon-only button snapshot 1`] = `
<div>
  <button
    class="_button_ee73f7 _primary_ee73f7 _base_ee73f7 _blue-secondary_ee73f7 _iconOnly_ee73f7"
  />
</div>
`;

exports[`Button Snapshots > matches link button snapshot 1`] = `
<div>
  <a
    class="_button_ee73f7 _primary_ee73f7 _base_ee73f7 _blue-secondary_ee73f7 _withText_ee73f7"
    href="/test"
  >
    <span
      class="_text_ee73f7 _text-base_ee73f7"
    >
      Test Button
    </span>
  </a>
</div>
`;

exports[`Button Snapshots > matches primary button snapshot 1`] = `
<div>
  <button
    class="_button_ee73f7 _primary_ee73f7 _base_ee73f7 _blue-secondary_ee73f7 _withText_ee73f7"
  >
    <span
      class="_text_ee73f7 _text-base_ee73f7"
    >
      Test Button
    </span>
  </button>
</div>
`;

exports[`Button Snapshots > matches secondary button snapshot 1`] = `
<div>
  <button
    class="_button_ee73f7 _secondary_ee73f7 _base_ee73f7 _blue-secondary_ee73f7 _withText_ee73f7"
  >
    <span
      class="_text_ee73f7 _text-base_ee73f7"
    >
      Test Button
    </span>
  </button>
</div>
`;
