import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import { vi } from 'vitest';
import { PersonalizationCard } from '../PersonalizationCard';
import { PersonalizationCardField, PersonalizationCardFile } from '../PersonalizationCard.types';
import { ButtonState } from '@/components/Button/Button.types';
import { mockFields, createMockFiles, createDefaultProps } from './test-utils';
import { isImage } from '@/api/documents';

interface MockButtonProps {
  children?: React.ReactNode;
  onClick?: () => void;
  state?: ButtonState;
  disabled?: boolean;
}

vi.mock('ezheaders', () => ({
  headers: vi.fn(() => ({})),
}));

vi.mock('@clerk/nextjs', () => ({
  useAuth: vi.fn().mockReturnValue({
    getToken: vi.fn().mockResolvedValue('mock-token'),
  }),
}));

vi.mock('@/components/Button', () => ({
  Button: ({ children, onClick, state }: MockButtonProps) => (
    <button onClick={onClick} disabled={state === ButtonState.DISABLED} data-testid="mock-button">
      {children}
    </button>
  ),
  convertIconToPaths: vi.fn(() => [['path', { d: 'M10 10L20 20' }]]),
}));

vi.mock('@/components/FileUploadManager', () => ({
  FileUploadGrid: ({ files }: { files: PersonalizationCardFile[] }) => (
    <div data-testid="file-upload-grid">
      {files.map((document) => (
        <div key={document.id} data-testid="file-item">
          <span>{document.fileName}</span>
          {!isImage(document) ? (
            <span>PDF</span>
          ) : (
            <span>IMAGE</span>
          )}
        </div>
      ))}
    </div>
  ),
}));

vi.mock('react-intersection-observer', () => ({
  useInView: vi.fn(() => ({
    ref: vi.fn(),
    inView: true,
  })),
}));

describe('PersonalizationCard Component', () => {
  const mockFiles = createMockFiles();
  const defaultProps = createDefaultProps();

  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('Rendering', () => {
    it('renders component correctly with fields', () => {
      render(<PersonalizationCard {...defaultProps} />);
      expect(screen.getByText('Fridge Freezer')).toBeInTheDocument();
      expect(screen.getByText('Brand')).toBeInTheDocument();
      expect(screen.getByText('Bosch')).toBeInTheDocument();
      expect(screen.getByText('Model')).toBeInTheDocument();
      expect(screen.getByText('12345677')).toBeInTheDocument();
    });

    it('renders files correctly', () => {
      const propsWithFiles = createDefaultProps({ files: mockFiles });
      render(<PersonalizationCard {...propsWithFiles} />);
      expect(screen.getByText('document1.pdf')).toBeInTheDocument();
      expect(screen.getAllByText('PDF')).toHaveLength(2);
    });

    it('renders edit button when not in editing mode', () => {
      render(<PersonalizationCard {...defaultProps} />);
      expect(screen.getByText('Edit details')).toBeInTheDocument();
    });

    it('renders save and cancel buttons when in editing mode', () => {
      const editingProps = createDefaultProps({ isEditing: true });
      render(<PersonalizationCard {...editingProps} />);
      expect(screen.getByText('Save')).toBeInTheDocument();
      expect(screen.getByText('Cancel')).toBeInTheDocument();
    });

    it('renders correctly without files', () => {
      render(<PersonalizationCard {...defaultProps} />);
      expect(screen.getByText('Brand')).toBeInTheDocument();
      expect(screen.getByText('Model')).toBeInTheDocument();
      expect(screen.getByText('Edit details')).toBeInTheDocument();
    });
  });

  describe('Interactions', () => {
    it('calls onEdit when edit button is clicked', () => {
      const props = createDefaultProps();
      render(<PersonalizationCard {...props} />);

      const editButton = screen.getByText('Edit details');
      fireEvent.click(editButton);

      expect(props.onEdit).toHaveBeenCalled();
    });

    it('calls onSave when save button is clicked', () => {
      const props = createDefaultProps({ isEditing: true });
      render(<PersonalizationCard {...props} />);

      const brandInput = screen.getByDisplayValue('Bosch');
      fireEvent.change(brandInput, { target: { value: 'Samsung' } });

      const saveButton = screen.getByText('Save');
      fireEvent.click(saveButton);

      const expectedFields = mockFields.map(field =>
        field.id === '1' ? { ...field, value: 'Samsung' } : field
      );
      expect(props.onSave).toHaveBeenCalledWith(expectedFields, [], 'Fridge Freezer');
    });

    it('calls onCancel when cancel button is clicked', () => {
      const props = createDefaultProps({ isEditing: true });
      render(<PersonalizationCard {...props} />);

      const cancelButton = screen.getByText('Cancel');
      fireEvent.click(cancelButton);

      expect(props.onCancel).toHaveBeenCalled();
    });

    it('renders fields with their values', () => {
      const customFields = mockFields.map((field) => ({
        ...field,
        value: field.id === '1' ? 'LG' : 'LG123',
      }));

      const props = createDefaultProps({ fields: customFields });
      render(<PersonalizationCard {...props} />);

      expect(screen.getByText('LG')).toBeInTheDocument();
      expect(screen.getByText('LG123')).toBeInTheDocument();
    });

    it('shows field values as text when not editing', () => {
      render(<PersonalizationCard {...defaultProps} />);

      expect(screen.getByText('Bosch')).toBeInTheDocument();
      expect(screen.getByText('12345677')).toBeInTheDocument();
    });

    it('renders files when provided', () => {
      const propsWithFiles = createDefaultProps({ files: mockFiles });
      render(<PersonalizationCard {...propsWithFiles} />);

      expect(screen.getByText('document1.pdf')).toBeInTheDocument();
      expect(screen.getByText('document2.pdf')).toBeInTheDocument();
      expect(screen.getAllByText('PDF')).toHaveLength(2);
    });

    it('handles field changes correctly', () => {
      const props = createDefaultProps({ isEditing: true });
      render(<PersonalizationCard {...props} />);

      const brandInput = screen.getByDisplayValue('Bosch');
      fireEvent.change(brandInput, { target: { value: 'Samsung' } });

      expect(brandInput).toHaveValue('Samsung');
    });

    it('shows inputs only for empty fields by default, text for fields with data', () => {
      const fieldsWithData = [
        {
          id: '1',
          label: 'Brand',
          value: 'Samsung',
          editable: true,
          type: 'text' as const,
        },
        {
          id: '2',
          label: 'Model',
          value: 'RF23M8070SR',
          editable: true,
          type: 'text' as const,
        },
        {
          id: '3',
          label: 'Serial number',
          value: '',
          editable: true,
          type: 'text' as const,
        },
      ];

      const props = createDefaultProps({ fields: fieldsWithData });
      render(<PersonalizationCard {...props} />);

      expect(screen.getByText('Samsung')).toBeInTheDocument();
      expect(screen.getByText('RF23M8070SR')).toBeInTheDocument();

      const serialInput = screen.getByDisplayValue('');
      expect(serialInput).toBeInTheDocument();
      expect(serialInput.tagName).toBe('INPUT');

      expect(screen.queryByDisplayValue('Samsung')).not.toBeInTheDocument();
      expect(screen.queryByDisplayValue('RF23M8070SR')).not.toBeInTheDocument();
    });

    it('shows inputs for all editable fields when explicitly editing', () => {
      const fieldsWithData = [
        {
          id: '1',
          label: 'Brand',
          value: 'Samsung',
          editable: true,
          type: 'text' as const,
        },
        {
          id: '2',
          label: 'Model',
          value: 'RF23M8070SR',
          editable: true,
          type: 'text' as const,
        },
        {
          id: '3',
          label: 'Serial number',
          value: '',
          editable: true,
          type: 'text' as const,
        },
      ];

      const props = createDefaultProps({ fields: fieldsWithData, isEditing: true });
      render(<PersonalizationCard {...props} />);

      const brandInput = screen.getByDisplayValue('Samsung');
      const modelInput = screen.getByDisplayValue('RF23M8070SR');
      const serialInput = screen.getByDisplayValue('');

      expect(brandInput).toBeInTheDocument();
      expect(brandInput.tagName).toBe('INPUT');
      expect(modelInput).toBeInTheDocument();
      expect(modelInput.tagName).toBe('INPUT');
      expect(serialInput).toBeInTheDocument();
      expect(serialInput.tagName).toBe('INPUT');
    });
  });

  describe('File handling', () => {
    it('renders files when provided', () => {
      const propsWithFiles = createDefaultProps({ files: mockFiles });
      render(<PersonalizationCard {...propsWithFiles} />);
      expect(screen.getByText('document1.pdf')).toBeInTheDocument();
      expect(screen.getByText('document2.pdf')).toBeInTheDocument();
    });
  });

  describe('Edge cases', () => {
    it('handles case with no fields', () => {
      const propsWithNoFields = createDefaultProps({ fields: [] });
      render(<PersonalizationCard {...propsWithNoFields} />);

      expect(screen.getByText('Fridge Freezer')).toBeInTheDocument();
    });

    it('applies custom className correctly', () => {
      const propsWithClassName = createDefaultProps({ className: 'custom-class' });
      const { container } = render(<PersonalizationCard {...propsWithClassName} />);

      expect(container.firstChild).toHaveClass('custom-class');
    });

    it('handles select fields correctly', () => {
      const selectField: PersonalizationCardField = {
        id: 'select-1',
        label: 'Select Option',
        value: 'option1',
        editable: true,
        type: 'select',
        options: [
          { label: 'Option 1', value: 'option1' },
          { label: 'Option 2', value: 'option2' },
        ],
      };

      const propsWithSelect = createDefaultProps({
        fields: [selectField],
        isEditing: true,
      });
      render(<PersonalizationCard {...propsWithSelect} />);

      // FormDropdown uses a button instead of select element
      const dropdownButton = screen.getByRole('button', { name: 'Option 1' });
      expect(dropdownButton).toBeInTheDocument();
      expect(dropdownButton).toHaveTextContent('Option 1');
    });
  });

  describe('Input persistence', () => {
    it('keeps input visible for initially empty fields even after user starts typing', () => {
      const fieldsWithEmptyField = [
        {
          id: '1',
          label: 'Brand',
          value: 'Samsung',
          editable: true,
          type: 'text' as const,
        },
        {
          id: '2',
          label: 'Serial number',
          value: '',
          editable: true,
          type: 'text' as const,
        },
      ];

      const props = createDefaultProps({ fields: fieldsWithEmptyField });
      render(<PersonalizationCard {...props} />);

      const serialInput = screen.getByDisplayValue('');
      expect(serialInput).toBeInTheDocument();
      expect(serialInput.tagName).toBe('INPUT');

      fireEvent.change(serialInput, { target: { value: 'A' } });

      const updatedInput = screen.getByDisplayValue('A');
      expect(updatedInput).toBeInTheDocument();
      expect(updatedInput.tagName).toBe('INPUT');

      fireEvent.change(updatedInput, { target: { value: 'ABC123' } });

      const finalInput = screen.getByDisplayValue('ABC123');
      expect(finalInput).toBeInTheDocument();
      expect(finalInput.tagName).toBe('INPUT');

      expect(screen.getByText('Samsung')).toBeInTheDocument();
      expect(screen.queryByDisplayValue('Samsung')).not.toBeInTheDocument();
    });
  });

  describe('Save button validation', () => {
    it('enables save button when initially filled field becomes empty (allowing field reset)', () => {
      const fieldsWithData = [
        {
          id: '1',
          label: 'Brand',
          value: 'Samsung',
          editable: true,
          type: 'text' as const,
        },
        {
          id: '2',
          label: 'Model',
          value: 'RF23M8070SR',
          editable: true,
          type: 'text' as const,
        },
      ];

      const props = createDefaultProps({ fields: fieldsWithData, isEditing: true });
      render(<PersonalizationCard {...props} />);

      const saveButton = screen.getByText('Save');
      expect(saveButton).toBeDisabled();

      const brandInput = screen.getByDisplayValue('Samsung');
      fireEvent.change(brandInput, { target: { value: 'LG' } });

      expect(saveButton).not.toBeDisabled();

      fireEvent.change(brandInput, { target: { value: '' } });

      expect(saveButton).not.toBeDisabled();
    });

    it('disables save button when no changes are made during editing', () => {
      const fieldsWithData = [
        {
          id: '1',
          label: 'Brand',
          value: 'Samsung',
          editable: true,
          type: 'text' as const,
        },
      ];

      const props = createDefaultProps({ fields: fieldsWithData, isEditing: true });
      render(<PersonalizationCard {...props} />);

      const saveButton = screen.getByText('Save');
      expect(saveButton).toBeDisabled();

      const brandInput = screen.getByDisplayValue('Samsung');
      fireEvent.change(brandInput, { target: { value: 'Samsung' } });

      expect(saveButton).toBeDisabled();
    });

    it('enables save button when changes are made', () => {
      const fieldsWithData = [
        {
          id: '1',
          label: 'Brand',
          value: 'Samsung',
          editable: true,
          type: 'text' as const,
        },
      ];

      const props = createDefaultProps({ fields: fieldsWithData, isEditing: true });
      render(<PersonalizationCard {...props} />);

      const saveButton = screen.getByText('Save');
      expect(saveButton).toBeDisabled();

      const brandInput = screen.getByDisplayValue('Samsung');
      fireEvent.change(brandInput, { target: { value: 'LG' } });

      expect(saveButton).not.toBeDisabled();
    });

    it('enables save button when title is changed', () => {
      const fieldsWithData = [
        {
          id: '1',
          label: 'Brand',
          value: 'Samsung',
          editable: true,
          type: 'text' as const,
        },
      ];

      const props = createDefaultProps({
        fields: fieldsWithData,
        title: 'Fridge',
        isEditing: true,
        titleEditable: true,
      });
      render(<PersonalizationCard {...props} />);

      const saveButton = screen.getByText('Save');
      expect(saveButton).toBeDisabled();

      const titleInput = screen.getByDisplayValue('Fridge');
      fireEvent.change(titleInput, { target: { value: 'Refrigerator' } });

      expect(saveButton).not.toBeDisabled();
    });
  });

  describe('Input persistence', () => {
    it('keeps input visible for initially empty fields even after user starts typing', () => {
      const fieldsWithEmptyField = [
        {
          id: '1',
          label: 'Brand',
          value: 'Samsung',
          editable: true,
          type: 'text' as const,
        },
        {
          id: '2',
          label: 'Serial number',
          value: '',
          editable: true,
          type: 'text' as const,
        },
      ];

      const props = createDefaultProps({ fields: fieldsWithEmptyField });
      render(<PersonalizationCard {...props} />);

      const serialInput = screen.getByDisplayValue('');
      expect(serialInput).toBeInTheDocument();
      expect(serialInput.tagName).toBe('INPUT');

      fireEvent.change(serialInput, { target: { value: 'A' } });

      const updatedInput = screen.getByDisplayValue('A');
      expect(updatedInput).toBeInTheDocument();
      expect(updatedInput.tagName).toBe('INPUT');

      fireEvent.change(updatedInput, { target: { value: 'ABC123' } });

      const finalInput = screen.getByDisplayValue('ABC123');
      expect(finalInput).toBeInTheDocument();
      expect(finalInput.tagName).toBe('INPUT');

      expect(screen.getByText('Samsung')).toBeInTheDocument();
      expect(screen.queryByDisplayValue('Samsung')).not.toBeInTheDocument();
    });
  });

  describe('Save button validation', () => {
    it('enables save button when initially filled field becomes empty (allowing field reset)', () => {
      const fieldsWithData = [
        {
          id: '1',
          label: 'Brand',
          value: 'Samsung',
          editable: true,
          type: 'text' as const,
        },
        {
          id: '2',
          label: 'Model',
          value: 'RF23M8070SR',
          editable: true,
          type: 'text' as const,
        },
      ];

      const props = createDefaultProps({ fields: fieldsWithData, isEditing: true });
      render(<PersonalizationCard {...props} />);

      const saveButton = screen.getByText('Save');
      expect(saveButton).toBeDisabled();

      const brandInput = screen.getByDisplayValue('Samsung');
      fireEvent.change(brandInput, { target: { value: 'LG' } });

      expect(saveButton).not.toBeDisabled();

      fireEvent.change(brandInput, { target: { value: '' } });

      expect(saveButton).not.toBeDisabled();
    });

    it('disables save button when no changes are made during editing', () => {
      const fieldsWithData = [
        {
          id: '1',
          label: 'Brand',
          value: 'Samsung',
          editable: true,
          type: 'text' as const,
        },
      ];

      const props = createDefaultProps({ fields: fieldsWithData, isEditing: true });
      render(<PersonalizationCard {...props} />);

      const saveButton = screen.getByText('Save');
      expect(saveButton).toBeDisabled();

      const brandInput = screen.getByDisplayValue('Samsung');
      fireEvent.change(brandInput, { target: { value: 'Samsung' } });

      expect(saveButton).toBeDisabled();
    });

    it('enables save button when changes are made', () => {
      const fieldsWithData = [
        {
          id: '1',
          label: 'Brand',
          value: 'Samsung',
          editable: true,
          type: 'text' as const,
        },
      ];

      const props = createDefaultProps({ fields: fieldsWithData, isEditing: true });
      render(<PersonalizationCard {...props} />);

      const saveButton = screen.getByText('Save');
      expect(saveButton).toBeDisabled();

      const brandInput = screen.getByDisplayValue('Samsung');
      fireEvent.change(brandInput, { target: { value: 'LG' } });

      expect(saveButton).not.toBeDisabled();
    });

    it('enables save button when title is changed', () => {
      const fieldsWithData = [
        {
          id: '1',
          label: 'Brand',
          value: 'Samsung',
          editable: true,
          type: 'text' as const,
        },
      ];

      const props = createDefaultProps({
        fields: fieldsWithData,
        title: 'Fridge',
        isEditing: true,
        titleEditable: true,
      });
      render(<PersonalizationCard {...props} />);

      const saveButton = screen.getByText('Save');
      expect(saveButton).toBeDisabled();

      const titleInput = screen.getByDisplayValue('Fridge');
      fireEvent.change(titleInput, { target: { value: 'Refrigerator' } });

      expect(saveButton).not.toBeDisabled();
    });
  });
});
