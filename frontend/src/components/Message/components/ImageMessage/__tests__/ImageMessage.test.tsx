import React from 'react';
import { render, screen } from '@testing-library/react';
import { ImageMessage } from '../ImageMessage';
import { MessageTypeValue } from '@/types/messages';
import styles from '../ImageMessage.module.scss';

const mockMessage = {
  id: 1,
  content: 'Image caption',
  type: MessageTypeValue.Image,
  senderType: 'system' as const,
  timestamp: new Date().toISOString(),
  additionalData: {
    imageUrl: 'https://example.com/image.jpg',
    category: 'test'
  }
};

describe('ImageMessage Component', () => {
  describe('Rendering', () => {
    it('renders image with caption correctly', () => {
      render(<ImageMessage message={mockMessage} />);
      const image = screen.getByRole('img');
      const caption = screen.getByText('Image caption');
      
      expect(image).toBeInTheDocument();
      expect(image).toHaveAttribute('alt', 'Image caption');
      expect(caption).toBeInTheDocument();
    });

    it('applies custom className', () => {
      const { container } = render(
        <ImageMessage message={mockMessage} className="custom-class" />
      );
      const messageContainer = container.querySelector(`.${styles.messageContainer}`);
      expect(messageContainer?.className).toContain('custom-class');
    });

    it('renders system message with logo', () => {
      render(<ImageMessage message={mockMessage} />);
      const logoContainer = screen.getByTestId('logo-container');
      expect(logoContainer).toBeInTheDocument();
    });

    it('renders user message without logo', () => {
      const userMessage = { ...mockMessage, senderType: 'user' as const };
      const { container } = render(<ImageMessage message={userMessage} />);
      const logoContainer = container.querySelector('.logoContainer');
      expect(logoContainer).not.toBeInTheDocument();
    });
  });

  describe('Image Handling', () => {
    it('does not render without imageUrl', () => {
      const invalidMessage = {
        ...mockMessage,
        additionalData: { 
          imageUrl: '',
          category: 'test'
        }
      };
      const { container } = render(<ImageMessage message={invalidMessage} />);
      expect(container.firstChild).toBeNull();
    });

    it('handles missing additionalData', () => {
      const invalidMessage = {
        ...mockMessage,
        additionalData: undefined
      };
      const { container } = render(<ImageMessage message={invalidMessage} />);
      expect(container.firstChild).toBeNull();
    });

    it('renders image without caption', () => {
      const messageWithoutCaption = { ...mockMessage, content: '' };
      const { container } = render(<ImageMessage message={messageWithoutCaption} />);
      const caption = container.querySelector('.caption');
      expect(caption).not.toBeInTheDocument();
    });
  });

  describe('Styling', () => {
    it('applies correct container styles for user messages', () => {
      const userMessage = { ...mockMessage, senderType: 'user' as const };
      const { container } = render(<ImageMessage message={userMessage} />);
      const messageContainer = container.querySelector(`.${styles.messageContainer}`);
      expect(messageContainer?.className).toContain(styles.userContainer);
    });

    it('applies correct container styles for system messages', () => {
      const { container } = render(<ImageMessage message={mockMessage} />);
      const messageContainer = container.querySelector(`.${styles.messageContainer}`);
      expect(messageContainer?.className).toContain(styles.systemContainer);
    });

    it('applies correct bubble styles for user messages', () => {
      const userMessage = { ...mockMessage, senderType: 'user' as const };
      const { container } = render(<ImageMessage message={userMessage} />);
      const bubble = container.querySelector(`.${styles.bubble}`);
      expect(bubble?.className).toContain(styles.userBubble);
    });

    it('applies correct bubble styles for system messages', () => {
      const { container } = render(<ImageMessage message={mockMessage} />);
      const bubble = container.querySelector(`.${styles.bubble}`);
      expect(bubble?.className).toContain(styles.systemBubble);
    });
  });
});