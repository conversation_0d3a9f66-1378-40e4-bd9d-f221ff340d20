import React from 'react';
import { render } from '@testing-library/react';
import { ImageMessage } from '../ImageMessage';
import { MessageTypeValue } from '@/types/messages';

const mockMessage = {
  id: 1,
  content: 'Image caption',
  type: MessageTypeValue.Image,
  senderType: 'system' as const,
  timestamp: '2024-01-01T12:00:00.000Z',
  additionalData: {
    imageUrl: 'https://example.com/image.jpg',
    category: 'test'
  }
};

describe('ImageMessage Snapshots', () => {
  it('matches system message snapshot', () => {
    const { container } = render(<ImageMessage message={mockMessage} />);
    expect(container).toMatchSnapshot();
  });

  it('matches user message snapshot', () => {
    const userMessage = { ...mockMessage, senderType: 'user' as const };
    const { container } = render(<ImageMessage message={userMessage} />);
    expect(container).toMatchSnapshot();
  });

  it('matches message without caption snapshot', () => {
    const messageWithoutCaption = { ...mockMessage, content: '' };
    const { container } = render(<ImageMessage message={messageWithoutCaption} />);
    expect(container).toMatchSnapshot();
  });

  it('matches message with custom className snapshot', () => {
    const { container } = render(
      <ImageMessage message={mockMessage} className="custom-class" />
    );
    expect(container).toMatchSnapshot();
  });
});