import React, { useState, useEffect } from 'react';
import styles from './NonClickableImages.module.scss';
import { ImageUrl, Message } from '@/types/messages';
import { filterValidImages } from '@/utils/messageUtils';

interface NonClickableImagesProps {
  message?: Message;
}

export const NonClickableImages: React.FC<NonClickableImagesProps> = ({ message }) => {
  const [validImageUrls, setValidImageUrls] = useState<ImageUrl[]>([]);

  useEffect(() => {
    const loadValidImages = async () => {
      if (!message?.additionalData?.imageUrls || message.additionalData?.imageUrls.length === 0) {
        return;
      }

      const imageUrls = message.additionalData.imageUrls.map((item) => item.imageUrl);
      const validUrls = await filterValidImages(imageUrls);

      const validImages = message.additionalData.imageUrls.filter((item) =>
        validUrls.includes(item.imageUrl)
      );

      setValidImageUrls(validImages);
    };

    loadValidImages();
  }, [message?.additionalData?.imageUrls]);

  if (
    !message?.additionalData?.imageUrls ||
    message.additionalData?.imageUrls.length === 0 ||
    validImageUrls.length === 0
  ) {
    return null;
  }

  return (
    <div className={styles.container}>
      <div className={styles.imageGrid}>
        {validImageUrls.map(({ imageUrl, description }, index) => (
          <div key={`${imageUrl}-${index}`} className={styles.imageItemContainer}>
            <div className={styles.imageItem}>
              <img src={imageUrl} alt={`Image ${index + 1}`} className={styles.image} />
            </div>
            {description && <p className={styles.caption}>{description}</p>}
          </div>
        ))}
      </div>
    </div>
  );
};
