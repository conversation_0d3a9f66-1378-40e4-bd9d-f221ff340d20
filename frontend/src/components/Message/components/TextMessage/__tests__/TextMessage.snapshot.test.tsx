import React from 'react';
import { render } from '@testing-library/react';
import { TextMessage } from '../TextMessage';
import { MessageTypeValue } from '@/types/messages';
import { vi } from 'vitest';

const mockMessage = {
  id: 1,
  content: 'Hello world',
  type: MessageTypeValue.Text,
  senderType: 'user' as const,
  timestamp: '2024-01-01T12:00:00.000Z',
};

vi.mock('next/navigation', () => ({
  useParams: vi.fn(() => ({ chatId: 'test-chat-id' })),
  useRouter: vi.fn(() => ({
    push: vi.fn(),
    replace: vi.fn(),
    refresh: vi.fn(),
    back: vi.fn(),
    forward: vi.fn(),
  })),
  useSearchParams: vi.fn(() => ({
    get: vi.fn(() => null),
    has: vi.fn(() => false),
    getAll: vi.fn(() => []),
    keys: vi.fn(() => []),
    values: vi.fn(() => []),
    entries: vi.fn(() => []),
    forEach: vi.fn(),
    append: vi.fn(),
    delete: vi.fn(),
    set: vi.fn(),
    sort: vi.fn(),
    toString: vi.fn(() => ''),
  })),
  usePathname: vi.fn(() => '/'),
  redirect: vi.fn(),
  notFound: vi.fn(),
}));

vi.mock('next/headers.js', () => ({
  headers: () => new Map(),
  cookies: () => new Map(),
}));

// Mock useAuth and useUser from @clerk/nextjs
vi.mock('@/app/store', () => ({
  useAppSelector: vi.fn().mockImplementation((selector) =>
    selector({
      prompts: {
        currentId: null,
        prompts: {},
      },
      user: {
        hasCompletedOnboarding: true,
      },
    })
  ),
  useAppDispatch: vi.fn().mockReturnValue(vi.fn()),
}));

vi.mock('@clerk/nextjs', () => ({
  useAuth: vi.fn().mockReturnValue({
    getToken: vi.fn().mockResolvedValue('mock-token'),
  }),
  useUser: () => ({
    user: {
      firstName: 'Test User',
    },
  }),
}));

vi.mock('../../ClickableImages', () => ({
  ClickableImages: () => null,
}));

describe('TextMessage Snapshots', () => {
  it('matches user message snapshot', () => {
    const { container } = render(<TextMessage message={mockMessage} />);
    expect(container).toMatchSnapshot();
  });

  it('matches system message snapshot', () => {
    const systemMessage = { ...mockMessage, senderType: 'system' as const };
    const { container } = render(<TextMessage message={systemMessage} />);
    expect(container).toMatchSnapshot();
  });

  it('matches empty content snapshot', () => {
    const emptyMessage = { ...mockMessage, content: '' };
    const { container } = render(<TextMessage message={emptyMessage} />);
    expect(container).toMatchSnapshot();
  });

  it('matches message with custom className snapshot', () => {
    const { container } = render(
      <TextMessage message={mockMessage} className="custom-class" />
    );
    expect(container).toMatchSnapshot();
  });
});
