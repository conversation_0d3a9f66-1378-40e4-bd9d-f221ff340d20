.messageWrapper {
  padding: 0 0 16px 0;
  box-sizing: border-box;
  min-height: 32px;
}

.messageContainer {
  display: flex;

  &.userContainer {
    align-items: flex-end;
    flex-direction: column;
  }

  &.systemContainer {
    align-items: flex-start;
  }
}

.messageRow {
  display: flex;
  width: 100%;

  .userContainer & {
    justify-content: flex-end;
  }

  .systemContainer & {
    justify-content: flex-start;
  }
}

.bubble {
  border-radius: 24px;
  position: relative;
  font-family: 'Quasimoda', sans-serif;
  max-width: 80%;

  &.userBubble {
    background-color: #e5e7eb;
    padding: 12px;

    .content {
      color: #000000;
    }

    &.empty {
      display: none;
    }
  }

  &.systemBubble {
    background-color: #ffffff;
    border-bottom-left-radius: 4px;
    padding: 0 0 0 5px;

    .content {
      color: #000000;
    }
  }
}

.content {
  margin: 0;
  font-size: 16px;
  line-height: 150%;
  white-space: pre-wrap;
  word-break: break-word;
}

.logoContainer {
  width: 32px;
  height: 32px;
  margin-right: 12px;
}

.loadingDots {
  pointer-events: none;
  display: inline-block;
  vertical-align: middle;
  aspect-ratio: 1 / 1;
  width: 24px;
  background-color: var(--colors-yellow-500);
  mask-size: 100%;
  mask-repeat: no-repeat;
  mask-position: center;
  mask-image: url("data:image/svg+xml,%3Csvg width='24' height='24' viewBox='0 0 24 24' xmlns='http://www.w3.org/2000/svg'%3E%3Cstyle%3E.spinner_qM83%7Banimation:spinner_8HQG 1.05s infinite;opacity:1%7D.spinner_oXPr%7Banimation-delay:.1s;opacity:0.5%7D.spinner_ZTLf%7Banimation-delay:.2s;opacity:0.25%7D@keyframes spinner_8HQG%7B0%25,57.14%25%7Banimation-timing-function:cubic-bezier(0.33,.66,.66,1);transform:translate(0)%7D28.57%25%7Banimation-timing-function:cubic-bezier(0.33,0,.66,.33);transform:translateY(-6px)%7D100%25%7Btransform:translate(0)%7D%7D%3C/style%3E%3Ccircle class='spinner_qM83' cx='4' cy='12' r='3' fill='currentColor'/%3E%3Ccircle class='spinner_qM83 spinner_oXPr' cx='12' cy='12' r='3' fill='currentColor'/%3E%3Ccircle class='spinner_qM83 spinner_ZTLf' cx='20' cy='12' r='3' fill='currentColor'/%3E%3C/svg%3E");
}

.disabled {
  opacity: 0.5;
  pointer-events: none;

  :global {
    button {
      color: var(--colors-gray-500);

      &:hover {
        color: var(--colors-gray-500);
      }

      &:focus {
        outline: none;
      }
    }
  }
}
