import { Message } from '@/types/messages';
import { ReactNode } from 'react';
export interface TextMessageProps {
  message?: Message;
  className?: string;
  isAIThinking?: boolean;
  messages?: Message[];
  messageIndex?: number;
  scrollToBottom?: () => void;
  children?: ReactNode;
  showNotificationTag?: boolean;
}

export const imageExtensions = ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp', 'svg', 'tiff'];
