import React, { useEffect, useState, useMemo, useCallback } from 'react';
import { useAuth } from '@clerk/nextjs';
import { Spinner } from '@/components/Spinner/Spinner';
import styles from './MessageAttachments.module.scss';
import { HugeiconsIcon } from '@/components/HugeiconsIcon';
import * as StrokeRounded from '@hugeicons-pro/core-stroke-rounded';
import { IconSvgObject } from '@hugeicons/react';
import { MessageDocumentPreview } from './components';
import classNames from 'classnames';
import { Attachment } from '@/types/messages';
import { isImageFile } from '../TextMessage/TextMessage';
import { getApiUrl, API_ENDPOINTS } from '@/constants/api';

interface DocumentWithBlob {
  id: number;
  name: string;
  type: string;
  size: number;
  url: string;
  blob?: Blob;
  objectUrl?: string;
  error?: boolean;
  isImage?: boolean;
}

export interface FileAttachment {
  documentId: number;
  originalFileName?: string;
  fileExtension?: string;
  sizeInKiloBytes?: number;
  createdAt?: string;
  file?: File;
}

export interface MessageAttachmentsProps {
  isContentEmpty: boolean;
  attachments?: FileAttachment[];
  onDocumentsLoaded?: (data: {
    imageUploads: Attachment[];
    nonImageUploads: Attachment[];
    imageDocuments: DocumentWithBlob[];
    fileDocuments: DocumentWithBlob[];
  }) => void;
  renderNonImageUploads?: boolean;
}

const MessageImagePreview: React.FC<{ file: Attachment }> = ({ file }) => {
  const isImage = isImageFile(file);
  const filename = file.originalFileName || file.file?.name;

  return (
    <div className={styles.documentPreview}>
      {file.status !== 'success' && (
        <div className={styles.loaderContainer}>
          {file.status === 'uploading' && <Spinner color="#D28E28" />}
          {file.status === 'error' && (
            <HugeiconsIcon
              icon={StrokeRounded.AlertCircleStrokeRounded as unknown as IconSvgObject}
              size={24}
              color="#d32f2f"
            />
          )}
        </div>
      )}
      {file.status === 'success' && (
        <div className={styles.previewContent}>
          {isImage && file.cdnUrl && (
            <img src={file.cdnUrl} alt={filename} className={styles.previewImage} />
          )}
          {!isImage && <div className={styles.fileNameOnly}>{filename}</div>}
        </div>
      )}
    </div>
  );
};

export const NonImageUploads: React.FC<{
  uploads?: Attachment[];
}> = ({ uploads }) => {
  if (uploads?.length === 0) return null;

  return (
    <div className={styles.previewsContainer}>
      <div className={classNames(styles.itemsContainer, styles.nonImageUploads)}>
        {uploads?.map((fileUpload, index) => (
          <MessageDocumentPreview
            key={`${fileUpload.documentId}-${index}`}
            fileUpload={fileUpload}
            index={index}
          />
        ))}
      </div>
    </div>
  );
};

interface UseMessageAttachmentsResult {
  imageUploads: Attachment[];
  nonImageUploads: Attachment[];
  imageDocuments: DocumentWithBlob[];
  fileDocuments: DocumentWithBlob[];
  loading: boolean;
  error: string | null;
}

// Global cache for document requests to avoid duplicate fetches
const documentCache = new Map<number, Promise<DocumentWithBlob | null>>();
const documentResults = new Map<number, DocumentWithBlob>();

// Clean up cache periodically to prevent memory leaks
const cleanupCache = () => {
  // Keep only the most recent 50 documents in cache
  const MAX_CACHE_SIZE = 50;
  if (documentResults.size > MAX_CACHE_SIZE) {
    const entries = Array.from(documentResults.entries());
    const toDelete = entries.slice(0, entries.length - MAX_CACHE_SIZE);

    toDelete.forEach(([id, doc]) => {
      // Revoke blob URLs to free memory
      if (doc.url?.startsWith('blob:')) {
        URL.revokeObjectURL(doc.url);
      }
      if (doc.objectUrl && doc.objectUrl !== doc.url && doc.objectUrl.startsWith('blob:')) {
        URL.revokeObjectURL(doc.objectUrl);
      }
      documentResults.delete(id);
    });
  }
};

export const useMessageAttachments = (
  attachments?: FileAttachment[]
): UseMessageAttachmentsResult => {
  const { getToken } = useAuth();
  const [documents, setDocuments] = useState<DocumentWithBlob[]>([]);
  const [fileUploads, setFileUploads] = useState<Attachment[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Memoize attachment IDs to prevent unnecessary re-runs
  const attachmentIds = useMemo(() => {
    return (
      attachments
        ?.map((a) => a.documentId)
        .sort()
        .join(',') || ''
    );
  }, [attachments]);

  const { imageUploads, nonImageUploads } = useMemo(() => {
    return fileUploads.reduce(
      (acc, fileUpload) => {
        const isImage = isImageFile(fileUpload);
        if (isImage) {
          acc.imageUploads.push(fileUpload);
        } else {
          acc.nonImageUploads.push(fileUpload);
        }
        return acc;
      },
      {
        imageUploads: [] as Attachment[],
        nonImageUploads: [] as Attachment[],
      }
    );
  }, [fileUploads]);

  const { imageDocuments, fileDocuments } = useMemo(() => {
    return documents.reduce(
      (acc, document) => {
        if (document.isImage) {
          acc.imageDocuments.push(document);
        } else {
          acc.fileDocuments.push(document);
        }
        return acc;
      },
      {
        imageDocuments: [] as DocumentWithBlob[],
        fileDocuments: [] as DocumentWithBlob[],
      }
    );
  }, [documents]);

  const fetchDocument = useCallback(
    async (
      attachment: FileAttachment,
      token: string,
      signal: AbortSignal
    ): Promise<DocumentWithBlob | null> => {
      const documentId = attachment.documentId;

      if (documentResults.has(documentId)) {
        return documentResults.get(documentId)!;
      }

      if (documentCache.has(documentId)) {
        return documentCache.get(documentId)!;
      }

      const requestPromise = (async (): Promise<DocumentWithBlob | null> => {
        try {
          if ('file' in attachment && attachment.file instanceof File) {
            const file = attachment.file;
            const contentType = file.type || '';
            const isImage =
              contentType.startsWith('image/') ||
              /^image\/(jpeg|png|gif|bmp|webp|svg\+xml|tiff)/.test(contentType);
            const filename = attachment.originalFileName || `Document ${documentId}`;

            const document: DocumentWithBlob = {
              id: documentId,
              name: filename,
              type: contentType,
              size: attachment.sizeInKiloBytes ? attachment.sizeInKiloBytes * 1024 : file.size,
              url: URL.createObjectURL(file),
              blob: file,
              isImage,
              objectUrl: isImage ? URL.createObjectURL(file) : undefined,
            };

            documentResults.set(documentId, document);
            cleanupCache(); // Clean up old entries periodically
            return document;
          }

          const response = await fetch(getApiUrl(`${API_ENDPOINTS.DOCUMENTS}/${documentId}/`), {
            headers: {
              Authorization: `Bearer ${token}`,
              Accept: '*/*',
            },
            signal,
          });

          if (!response.ok) {
            throw new Error(`Failed to fetch document: ${response.status}`);
          }

          const contentType = response.headers.get('content-type') || '';
          const isImage =
            contentType.startsWith('image/') ||
            /^image\/(jpeg|png|gif|bmp|webp|svg\+xml|tiff)/.test(contentType);
          const blob = await response.blob();

          let filename = attachment.originalFileName || `Document ${documentId}`;
          if (!filename) {
            const disposition = response.headers.get('content-disposition');
            if (disposition?.includes('filename=')) {
              const filenameMatch = disposition.match(/filename="(.+)"/);
              if (filenameMatch?.[1]) {
                filename = filenameMatch[1];
              }
            } else if (contentType) {
              const ext = contentType.split('/')[1]?.split(';')[0];
              if (ext) filename = `${filename}.${ext}`;
            }
          }

          const document: DocumentWithBlob = {
            id: documentId,
            name: filename,
            type: contentType,
            size: attachment.sizeInKiloBytes ? attachment.sizeInKiloBytes * 1024 : blob.size,
            url: URL.createObjectURL(blob),
            blob,
            isImage,
            objectUrl: isImage ? URL.createObjectURL(blob) : undefined,
          };

          documentResults.set(documentId, document);
          cleanupCache(); // Clean up old entries periodically
          return document;
        } catch (error) {
          if (signal.aborted) {
            return null;
          }
          console.error('Error fetching document:', documentId, error);
          return null;
        } finally {
          documentCache.delete(documentId);
        }
      })();

      documentCache.set(documentId, requestPromise);
      return requestPromise;
    },
    []
  );

  useEffect(() => {
    if (!attachments?.length) {
      setFileUploads([]);
      setDocuments([]);
      setLoading(false);
      return;
    }

    let isMounted = true;
    const abortController = new AbortController();
    const { signal } = abortController;

    const loadDocuments = async () => {
      setLoading(true);
      setError(null);

      try {
        const token = await getToken();
        if (!token || !isMounted) return;

        const imageAttachments: FileAttachment[] = [];
        const nonImageAttachments: FileAttachment[] = [];
        const isImageExtension = /^(jpg|jpeg|png|gif|bmp|webp|svg|tiff)$/i;

        attachments.forEach((attachment) => {
          if (attachment.fileExtension && isImageExtension.test(attachment.fileExtension)) {
            imageAttachments.push(attachment);
          } else {
            nonImageAttachments.push(attachment);
          }
        });

        const initialUploads: Attachment[] = attachments.map((attachment) => {
          const fileType = attachment.fileExtension
            ? `application/${attachment.fileExtension}`
            : 'application/octet-stream';
          const fileName = attachment.originalFileName || `Document ${attachment.documentId}`;
          const isImage =
            attachment.fileExtension && isImageExtension.test(attachment.fileExtension);

          return {
            documentId: attachment.documentId,
            name: fileName,
            type: isImage ? 'image' : fileType,
            size: attachment.sizeInKiloBytes ? attachment.sizeInKiloBytes * 1024 : undefined,
            fileId: attachment.documentId.toString(),
            file: new File([], fileName, { type: isImage ? 'image' : fileType }),
            status: 'uploading' as const,
            progress: 0,
            createdAt: attachment.createdAt,
            fileExtension: attachment.fileExtension,
            originalFileName: attachment.originalFileName,
            sizeInKiloBytes: attachment.sizeInKiloBytes,
          };
        });

        if (isMounted) {
          setFileUploads(initialUploads);
        }

        const documentPromises = attachments.map((attachment) =>
          fetchDocument(attachment, token, signal)
        );

        const results = await Promise.allSettled(documentPromises);
        const validDocuments: DocumentWithBlob[] = [];

        results.forEach((result, index) => {
          const attachment = attachments[index];

          if (result.status === 'fulfilled' && result.value) {
            validDocuments.push(result.value);

            if (isMounted) {
              setFileUploads((prev) =>
                prev.map((upload) => {
                  if (upload.documentId === attachment.documentId) {
                    return {
                      ...upload,
                      status: 'success' as const,
                      progress: 100,
                      cdnUrl: result.value!.url,
                    };
                  }
                  return upload;
                })
              );
            }
          } else {
            if (isMounted) {
              setFileUploads((prev) =>
                prev.map((upload) => {
                  if (upload.documentId === attachment.documentId) {
                    return {
                      ...upload,
                      status: 'error' as const,
                      progress: 0,
                    };
                  }
                  return upload;
                })
              );
            }
          }
        });

        if (isMounted) {
          setDocuments(validDocuments);
          setLoading(false);
        }
      } catch (err) {
        if (isMounted) {
          setError('Failed to load document attachments');
          setLoading(false);
          console.error('Error loading attachments:', err);
        }
      }
    };

    loadDocuments();

    return () => {
      isMounted = false;
      abortController.abort();

      // Note: Don't clean up document cache or blob URLs here as they might be reused
      // The cache will be cleaned up when documents are no longer referenced
    };
    // Using attachmentIds instead of attachments to prevent unnecessary re-runs when array reference changes
    // but content is the same. getToken is stable from Clerk and doesn't need to be included.
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [attachmentIds, fetchDocument]);

  return {
    imageUploads,
    nonImageUploads,
    imageDocuments,
    fileDocuments,
    loading,
    error,
  };
};

export const MessageAttachments: React.FC<MessageAttachmentsProps> = ({
  isContentEmpty,
  attachments,
  onDocumentsLoaded,
  renderNonImageUploads = true,
}) => {
  const { imageUploads, nonImageUploads, imageDocuments, fileDocuments, loading, error } =
    useMessageAttachments(attachments);

  const hasNotifiedRef = React.useRef(false);

  useEffect(() => {
    if (
      onDocumentsLoaded &&
      (imageUploads.length > 0 || nonImageUploads.length > 0) &&
      !hasNotifiedRef.current
    ) {
      hasNotifiedRef.current = true;
      onDocumentsLoaded({
        imageUploads,
        nonImageUploads,
        imageDocuments,
        fileDocuments,
      });
    }
  }, [imageUploads, nonImageUploads, imageDocuments, fileDocuments, onDocumentsLoaded]);

  useEffect(() => {
    hasNotifiedRef.current = false;
  }, [attachments]);

  if (!attachments || attachments.length === 0) {
    return null;
  }

  if (loading && imageUploads.length === 0 && nonImageUploads.length === 0) {
    return <div className={styles.loading}>Loading attachments...</div>;
  }

  if (error && imageUploads.length === 0 && nonImageUploads.length === 0) {
    return <div className={styles.error}>{error}</div>;
  }

  return (
    <div
      className={classNames(styles.attachmentsContainer, {
        [styles.spacing]: isContentEmpty,
      })}
    >
      {imageUploads.length > 0 || nonImageUploads.length > 0 ? (
        <>
          {renderNonImageUploads && nonImageUploads.length > 0 && (
            <NonImageUploads uploads={nonImageUploads} />
          )}

          {imageUploads.length > 0 && (
            <div className={styles.previewsContainer}>
              <div className={styles.itemsContainer}>
                {imageUploads.map((fileUpload: Attachment, index: number) => (
                  <MessageImagePreview
                    key={`${fileUpload.documentId}-${index}`}
                    file={fileUpload}
                  />
                ))}
              </div>
            </div>
          )}
        </>
      ) : imageDocuments.length === 0 && fileDocuments.length === 0 ? (
        <div className={styles.error}>Unable to load attachments</div>
      ) : (
        <div className={styles.fallbackContainer}>
          {imageDocuments.length > 0 && (
            <div className={styles.imagesContainer}>
              <h4 className={styles.sectionTitle}>Images</h4>
              {imageDocuments.map((document: DocumentWithBlob) => (
                <div key={document.id} className={styles.imageContainer}>
                  <img
                    src={document.objectUrl}
                    alt={document.name}
                    className={styles.attachmentImage}
                  />
                  <div className={styles.imageName}>{document.name}</div>
                </div>
              ))}
            </div>
          )}

          {renderNonImageUploads && fileDocuments.length > 0 && (
            <div className={styles.filesContainer}>
              <h4 className={styles.sectionTitle}>Files</h4>
              {fileDocuments.map((document: DocumentWithBlob) => (
                <div key={document.id} className={styles.documentContainer}>
                  <HugeiconsIcon
                    icon={StrokeRounded.AiFileStrokeRounded as unknown as IconSvgObject}
                    size={24}
                    color="#666"
                  />
                  <a
                    href={document.url}
                    target="_blank"
                    rel="noopener noreferrer"
                    download={document.name}
                    className={styles.documentLink}
                  >
                    {document.name}
                  </a>
                </div>
              ))}
            </div>
          )}
        </div>
      )}
    </div>
  );
};
