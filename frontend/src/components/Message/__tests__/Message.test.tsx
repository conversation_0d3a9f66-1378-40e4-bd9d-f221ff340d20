import React from 'react';
import { render, screen } from '@testing-library/react';
import { Message } from '../Message';
import { MessageTypeValue } from '@/types/messages';
import { vi } from 'vitest';

const mockTextMessage = {
  id: 1,
  content: 'Hello world',
  type: MessageTypeValue.Text,
  senderType: 'user' as const,
  timestamp: new Date().toISOString(),
};

const mockImageMessage = {
  id: 2,
  content: 'Image caption',
  type: MessageTypeValue.Image,
  senderType: 'system' as const,
  timestamp: new Date().toISOString(),
  additionalData: {
    imageUrl: 'https://example.com/image.jpg',
    category: 'test',
  },
};

vi.mock('next/navigation', () => ({
  useParams: vi.fn(() => ({ chatId: 'test-chat-id' })),
  useRouter: vi.fn(() => ({
    push: vi.fn(),
    replace: vi.fn(),
    refresh: vi.fn(),
    back: vi.fn(),
    forward: vi.fn(),
  })),
  useSearchParams: vi.fn(() => ({
    get: vi.fn(() => null),
    has: vi.fn(() => false),
    getAll: vi.fn(() => []),
    keys: vi.fn(() => []),
    values: vi.fn(() => []),
    entries: vi.fn(() => []),
    forEach: vi.fn(),
    append: vi.fn(),
    delete: vi.fn(),
    set: vi.fn(),
    sort: vi.fn(),
    toString: vi.fn(() => ''),
  })),
  usePathname: vi.fn(() => '/'),
  redirect: vi.fn(),
  notFound: vi.fn(),
}));

// Mock useAuth and useUser from @clerk/nextjs
vi.mock('@/app/store', () => ({
  useAppSelector: vi.fn().mockImplementation((selector) =>
    selector({
      prompts: {
        currentId: null,
        prompts: {},
      },
      user: {
        hasCompletedOnboarding: true,
      },
    })
  ),
  useAppDispatch: vi.fn().mockReturnValue(vi.fn()),
}));

vi.mock('@clerk/nextjs', () => ({
  useAuth: vi.fn().mockReturnValue({
    getToken: vi.fn().mockResolvedValue('mock-token'),
  }),
  useUser: () => ({
    user: {
      firstName: 'Test User',
    },
  }),
}));

describe('Message Component', () => {
  describe('Rendering', () => {
    it('renders text message correctly', () => {
      render(<Message message={mockTextMessage} />);
      expect(screen.getByText('Hello world')).toBeInTheDocument();
    });

    it('renders user message without logo', () => {
      render(<Message message={mockTextMessage} />);
      const logoContainer = screen.queryByTestId('logo-container');
      expect(logoContainer).not.toBeInTheDocument();
    });
  });

  describe('Message Types', () => {
    it('does not render image message without imageUrl', () => {
      const invalidImageMessage = {
        ...mockImageMessage,
        additionalData: {
          imageUrl: '',
          category: 'test',
        },
      };
      const { container } = render(<Message message={invalidImageMessage} />);
      expect(container.firstChild).toBeNull();
    });

    it('handles missing additionalData for image message', () => {
      const invalidImageMessage = {
        ...mockImageMessage,
        additionalData: undefined,
      };
      const { container } = render(<Message message={invalidImageMessage} />);
      expect(container.firstChild).toBeNull();
    });
  });

  describe('Edge Cases', () => {
    it('handles special characters in content', () => {
      const specialCharsMessage = {
        ...mockTextMessage,
        content: '!@#$%^&*()_+<>?:"{}|',
      };
      render(<Message message={specialCharsMessage} />);
      expect(screen.getByText('!@#$%^&*()_+<>?:"{}|')).toBeInTheDocument();
    });
  });
});
