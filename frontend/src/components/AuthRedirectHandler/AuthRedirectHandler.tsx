import { useEffect } from 'react';
import { useAuth, useUser } from '@clerk/nextjs';
import { useRouter } from 'next/navigation';
import { useRedirectQueryUrl } from '@/hooks/useRedirectQueryUrl';

export const AuthRedirectHandler = () => {
  const { isSignedIn, isLoaded } = useAuth();
  const { user } = useUser();
  const router = useRouter();
  const { redirectUrl, clearRedirectUrl } = useRedirectQueryUrl();

  useEffect(() => {
    if (isLoaded && isSignedIn) {
      const savedUrl = redirectUrl;
      const hasCompletedOnboarding = user?.unsafeMetadata?.visitedOnboarding === true;

      if (savedUrl) {
        try {
          const url = new URL(savedUrl);
          const redirectPath = url.pathname + url.search;

          if (hasCompletedOnboarding) {
            clearRedirectUrl();

            if (redirectPath !== window.location.pathname + window.location.search) {
              router.replace(redirectPath);
            }
          }
        } catch (error) {
          console.error('Error parsing saved redirect URL:', error);
          if (hasCompletedOnboarding) {
            clearRedirectUrl();
          }
        }
      }
    }
  }, [isLoaded, isSignedIn, user, router, redirectUrl, clearRedirectUrl]);

  return null;
};
