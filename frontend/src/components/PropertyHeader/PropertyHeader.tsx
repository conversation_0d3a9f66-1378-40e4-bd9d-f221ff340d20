import React, { useRef, useState, useEffect } from 'react';
import classNames from 'classnames';
import { IconSvgObject } from '@hugeicons/react';
import { House04Icon } from '@hugeicons-pro/core-stroke-standard';
import { Button } from '../Button';
import { ButtonColor, ButtonSize, ButtonState, ButtonType } from '../Button/Button.types';
import { HugeiconsIcon } from '../HugeiconsIcon';
import { AddressRoleForm } from '../AddressRoleForm';
import { AddressType } from '../AddressRoleForm/AddressRoleForm.types';
import { PropertyHeaderProps, PropertyHeaderState } from './PropertyHeader.types';
import styles from './PropertyHeader.module.scss';
import { Modal } from '../Modal';
import { useAuth } from '@clerk/nextjs';
import { useModalForGuest } from '@/hooks/useModalGuest';

export const PropertyHeader: React.FC<PropertyHeaderProps> = ({
  address,
  ownerStatus,
  profileImageUrl,
  state = PropertyHeaderState.EMPTY,
  onAddAddress,
  onEditAddress,
  onProfileImageChange,
  onAddressComplete,
  className,
  children,
}) => {
  const fileInputRef = useRef<HTMLInputElement>(null);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [localAddress, setLocalAddress] = useState(address);
  const [localOwnerStatus, setLocalOwnerStatus] = useState(ownerStatus);
  const [localState, setLocalState] = useState(state);
  const [localProfileImageUrl, setLocalProfileImageUrl] = useState(profileImageUrl);
  const [tempAddress, setTempAddress] = useState<AddressType | null>(null);
  const [tempDisplayAddress, setTempDisplayAddress] = useState<string | null>(null);
  const [tempRole, setTempRole] = useState<string | null>(null);
  const { isSignedIn } = useAuth();
  const { openModal, closeModal } = useModalForGuest();

  useEffect(() => {
    setLocalAddress(address);
    setLocalOwnerStatus(ownerStatus);
    setLocalState(state);
    setLocalProfileImageUrl(profileImageUrl);
  }, [address, ownerStatus, state, profileImageUrl]);

  useEffect(() => {
    return () => {
      if (localProfileImageUrl && localProfileImageUrl.startsWith('blob:')) {
        URL.revokeObjectURL(localProfileImageUrl);
      }
    };
  }, [localProfileImageUrl]);

  const isEmpty = localState === PropertyHeaderState.EMPTY || !localAddress;

  const headerClasses = classNames(
    styles.propertyHeader,
    {
      [styles.empty]: isEmpty,
      [styles.filled]: !isEmpty,
    },
    className
  );

  const handleButtonClick = () => {
    if (!isSignedIn) {
      openModal();
      return;
    }
    if (isEmpty) {
      setIsModalOpen(true);
      setTempAddress(null);
      setTempDisplayAddress(null);
      setTempRole(null);
      onAddAddress?.();
    } else if (!isEmpty && onEditAddress) {
      onEditAddress();
    }
  };

  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      const imageUrl = URL.createObjectURL(file);
      setLocalProfileImageUrl(imageUrl);

      if (onProfileImageChange) {
        onProfileImageChange(file);
      }
    }
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  const handleAddressChange = (address: AddressType, displayAddress?: string) => {
    setTempAddress(address);
    setTempDisplayAddress(displayAddress || null);
  };

  const handleRoleChange = (role: string) => {
    setTempRole(role);
  };

  const handleSave = () => {
    if (tempAddress && tempRole) {
      let addressForDisplay: string;
      let addressForBackend: AddressType;

      if (typeof tempAddress === 'string') {
        // Address from autocomplete - use display text for UI, ID for backend
        addressForDisplay = tempDisplayAddress || tempAddress;
        addressForBackend = tempAddress; // This is the ID
      } else {
        // Manual address entry - format for both display and backend
        addressForDisplay =
          `${tempAddress.line1}, ${tempAddress.city}, ${tempAddress.postcode}`.trim();
        addressForBackend = tempAddress;
      }

      setLocalAddress(addressForDisplay);

      let ownerStatus = '';
      switch (tempRole) {
        case 'owner':
          ownerStatus = 'Owner and occupier';
          break;
        case 'landlord':
          ownerStatus = 'Landlord';
          break;
        case 'tenant':
          ownerStatus = 'Tenant';
          break;
        default:
          ownerStatus = tempRole;
      }

      setLocalOwnerStatus(ownerStatus);
      setLocalState(PropertyHeaderState.FILLED);
      setIsModalOpen(false);

      if (onAddressComplete) {
        if (typeof addressForBackend === 'string') {
          // For autocomplete - pass both ID and display text
          onAddressComplete({
            address: addressForBackend,
            displayAddress: addressForDisplay,
            role: tempRole,
          });
        } else {
          // For manual entry - pass the address object
          onAddressComplete({
            address: addressForBackend,
            role: tempRole,
          });
        }
      }
    }
  };

  const isFormValid = () => {
    if (!tempRole || !tempAddress) return false;

    if (typeof tempAddress === 'string') {
      return tempAddress.trim() !== '';
    } else {
      return !Object.values(tempAddress).every((v) => !v || v.trim() === '');
    }
  };

  const handleModalClose = () => {
    setIsModalOpen(false);
  };

  const renderIcon = () => {
    if (localProfileImageUrl) {
      // eslint-disable-next-line @next/next/no-img-element
      return <img src={localProfileImageUrl} alt="Profile" className={styles.profileImage} />;
    }
    return (
      // eslint-disable-next-line @next/next/no-img-element
      <img src="/profileDefault.png" alt="Default Profile" className={styles.defaultProfileImage} />
    );
  };

  return (
    <>
      <div className={headerClasses}>
        <input
          ref={fileInputRef}
          type="file"
          accept="image/*"
          style={{ display: 'none' }}
          onChange={handleFileChange}
        />

        {isEmpty ? (
          <div className={styles.emptyContent}>
            <div className={styles.iconSection}>
              <div className={styles.iconContainer}>{renderIcon()}</div>
            </div>

            <div className={styles.rightContent}>
              <div className={styles.textSection}>
                <span className={styles.title}>Your address</span>
              </div>

              <div className={styles.buttonSection}>
                <Button
                  type={ButtonType.PRIMARY}
                  color={ButtonColor.GREEN_PRIMARY}
                  size={ButtonSize.BASE}
                  onClick={handleButtonClick}
                  className={styles.actionButton}
                >
                  Add address
                </Button>
              </div>
            </div>
          </div>
        ) : (
          <div className={styles.filledContent}>
            <div className={styles.iconSection}>
              <div className={styles.iconContainer}>{renderIcon()}</div>
            </div>

            <div className={styles.rightContent}>
              <div className={styles.headerLine}>
                <span className={styles.title}>Your address</span>
                {localOwnerStatus && <span className={styles.ownerBadge}>{localOwnerStatus}</span>}
              </div>
              <div className={styles.addressLine}>
                <div className={styles.homeIcon}>
                  <HugeiconsIcon icon={House04Icon as unknown as IconSvgObject} size={24} />
                </div>
                <span className={styles.address}>{localAddress}</span>
              </div>
            </div>
          </div>
        )}

        {children}
      </div>

      {isModalOpen && (
        <Modal
          open={isModalOpen}
          onClose={handleModalClose}
          title="Your address"
          actionButtons={
            <Button
              onClick={handleSave}
              type={ButtonType.PRIMARY}
              color={ButtonColor.GREEN_PRIMARY}
              size={ButtonSize.L}
              state={!isFormValid() ? ButtonState.DISABLED : ButtonState.DEFAULT}
              disabled={!isFormValid()}
            >
              Save
            </Button>
          }
        >
          <AddressRoleForm
            selectedAddress={tempAddress}
            selectedRole={tempRole}
            onAddressChange={handleAddressChange}
            onRoleChange={handleRoleChange}
            forceDropdownPosition="bottom"
          />
        </Modal>
      )}
    </>
  );
};
