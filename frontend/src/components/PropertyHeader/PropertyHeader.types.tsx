import { ReactNode } from 'react';
import { AddressType } from '../AddressRoleForm/AddressRoleForm.types';

export enum PropertyHeaderState {
  EMPTY = 'empty',
  FILLED = 'filled',
}

export interface PropertyHeaderProps {
  address?: string;
  ownerStatus?: string;
  profileImageUrl?: string;
  state?: PropertyHeaderState;
  onAddAddress?: () => void;
  onEditAddress?: () => void;
  onProfileImageChange?: (file: File) => void;
  onAddressComplete?: (data:
    | { address: string; displayAddress: string; role: string }  // Autocomplete
    | { address: AddressType; role: string }                     // Manual entry
  ) => void;
  className?: string;
  children?: ReactNode;
}
