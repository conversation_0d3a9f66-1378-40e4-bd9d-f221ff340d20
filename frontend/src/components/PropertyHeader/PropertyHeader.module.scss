.propertyHeader {
  display: flex;
  flex-direction: column;
  background: transparent;
  padding: 0;

  .editIcon {
    color: #1b6e5a;
  }

  .editIconContainer {
    position: absolute;
    bottom: -2px;
    right: -2px;
    width: 27px;
    height: 27px;
    background: #ffffff;
    border: 1px solid #1b6e5a;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
  }

  .homeIcon {
    display: flex;
    align-items: center;
    margin-right: 8px;
    color: var(--colors-black);
  }

  .profileImage {
    width: 100%;
    height: 100%;
    border-radius: 50%;
    object-fit: cover;
  }

  .defaultProfileImage {
    width: 100%;
    height: 100%;
    object-fit: contain;
    border-radius: 50%;
  }

  .iconContainer {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 72px;
    height: 72px;
    background: #f9fafb;
    border-radius: 50%;
    border: 3px solid var(--colors-yellow-500);
  }

  .title {
    font-size: 16px;
    font-weight: 700;
    color: #000000;
    white-space: nowrap;
  }

  &.filled {
    .ownerBadge {
      font-size: 12px;
      color: var(--colors-green-700);
      background: var(--colors-green-50);
      border: 1px solid var(--colors-green-700);
      padding: 2px 10px;
      border-radius: 9999px;
      margin-left: 8px;
      white-space: nowrap;
    }

    .address {
      font-size: 16px;
      color: #000000;
      gap: 8px;
    }
  }
}

.emptyContent {
  display: flex;
  align-items: center;
  height: 100%;
  gap: 16px;
  padding: 0 16px 30px 0;
}

.filledContent {
  display: flex;
  height: 100%;
  gap: 16px;
  padding-bottom: 30px;
}

.rightContent {
  display: flex;
  flex-direction: column;
  justify-content: center;
  flex: 1;
  gap: 8px;
}

.headerLine {
  display: flex;
  align-items: center;
}

.addressLine {
  display: flex;
  align-items: flex-start;
}

.propertyHeaderModal {
  :global(.Modal_modalInner__x8l3s) {
    display: none;
  }
}
