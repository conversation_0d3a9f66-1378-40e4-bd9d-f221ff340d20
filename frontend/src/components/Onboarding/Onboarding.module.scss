.onboardingWrapper {
  display: flex;
  flex-direction: column;
  background: #fff;
  max-width: 100%;
  width: 589px;
  border: 1px solid #dadada;
  border-radius: 32px;
  min-height: 656px;
  box-shadow:
    0 4px 4px rgba(0, 0, 0, 0.25),
    0 20px 25px -5px rgba(0, 0, 0, 0.1);
  padding-bottom: 24px;
}

.stepperContainer {
  margin-bottom: var(--spacing-6);
  margin-top: 18px;
  width: 100%;
}

.contentWrapper {
  display: flex;
  flex-direction: column;
  flex-grow: 1;
}

.content {
  display: flex;
  flex-direction: column;
  padding: 0 16px 0 16px;
  flex-grow: 1;
}

.buttonContainer {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 24px 102px 0 102px;
  margin-top: auto;

  @media (max-width: 768px) {
    position: sticky;
    bottom: 0;
  }
}

.button {
  width: 100%;
}

@media (max-width: 768px) {
  .onboardingWrapper {
    box-shadow: none;
    border: none;
    width: 100%;
    max-width: 100%;
    padding-bottom: 16px;
    border-radius: 0;
    min-height: 100dvh;
  }

  .stepperContainer {
    margin-bottom: 0;
  }

  .contentWrapper {
    min-height: calc(100dvh - 240px);
    width: 100%;
  }

  .content {
    padding: 24px 16px 14px 16px;
    width: 100%;
  }

  .buttonContainer {
    padding: 0 19px 10px 19px;
  }
}