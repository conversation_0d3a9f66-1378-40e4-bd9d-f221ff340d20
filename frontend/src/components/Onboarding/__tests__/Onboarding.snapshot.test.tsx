import React from 'react';
import { render } from '@testing-library/react';
import { Onboarding } from '../Onboarding';
import { useOnboarding } from '@/hooks/useOnboarding';
import { useBreakpoint } from '@/utils/breakpointUtils';

vi.mock('@/hooks/useOnboarding');
vi.mock('@/utils/breakpointUtils');
vi.mock('axios');

vi.mock('@/hooks/useSendOnboardingData', () => ({
  useSendOnboardingData: () => ({
    sendOnboardingData: vi.fn().mockResolvedValue({
      addressSendRequest: Promise.resolve(),
    }),
    sendDemoRequest: vi.fn().mockResolvedValue({
      demoSendRequest: Promise.resolve(),
    }),
  }),
}));

vi.mock('ezheaders', () => ({
  default: {
    get: vi.fn(),
    set: vi.fn(),
    delete: vi.fn(),
  },
}));

vi.mock('../components/OnboardingStepper/OnboardingStepper', () => ({
  OnboardingStepper: () => <div data-testid="mock-stepper">OnboardingStepper</div>,
}));

vi.mock('../components/OnboardingHeader/OnboardingHeader', () => ({
  OnboardingHeader: ({ title }: { title: string }) => <div data-testid="mock-header">{title}</div>,
}));

vi.mock('../components/OnboardingSteps/OnboardingSteps', () => ({
  OnboardingSteps: ({ step }: { step?: number }) => (
    <div data-testid="mock-steps">Step: {step ?? 0}</div>
  ),
}));

vi.mock('../components/OnboardingDemo/OnboardingDemo', () => ({
  OnboardingDemo: () => <div data-testid="mock-demo">Demo Form</div>,
  useDemoForm: () => ({
    companyName: '',
    numberOfProperties: null,
    email: '',
  }),
}));

describe('Onboarding snapshots', () => {
  const mockSteps = [{ key: 'role' }, { key: 'address' }, { key: 'details' }, { key: 'finish' }];

  beforeEach(() => {
    vi.clearAllMocks();
    (useBreakpoint as jest.Mock).mockReturnValue({ isMobile: false });
  });

  it('snapshot for first step', () => {
    (useOnboarding as jest.Mock).mockReturnValue({
      steps: mockSteps,
      currentStep: 0,
      goToStep: vi.fn(),
      nextStep: vi.fn(),
    });

    const { container } = render(<Onboarding />);
    expect(container).toMatchSnapshot();
  });

  it('snapshot for address step', () => {
    (useOnboarding as jest.Mock).mockReturnValue({
      steps: mockSteps,
      currentStep: 1,
      goToStep: vi.fn(),
      nextStep: vi.fn(),
    });

    const { container } = render(<Onboarding />);
    expect(container).toMatchSnapshot();
  });

  it('snapshot for details step', () => {
    (useOnboarding as jest.Mock).mockReturnValue({
      steps: mockSteps,
      currentStep: 2,
      goToStep: vi.fn(),
      nextStep: vi.fn(),
    });

    const { container } = render(<Onboarding />);
    expect(container).toMatchSnapshot();
  });

  it('snapshot for last step', () => {
    (useOnboarding as jest.Mock).mockReturnValue({
      steps: mockSteps,
      currentStep: 3,
      goToStep: vi.fn(),
      nextStep: vi.fn(),
    });

    const { container } = render(<Onboarding />);
    expect(container).toMatchSnapshot();
  });

  it('snapshot for mobile view', () => {
    (useOnboarding as jest.Mock).mockReturnValue({
      steps: mockSteps,
      currentStep: 0,
      goToStep: vi.fn(),
      nextStep: vi.fn(),
    });

    (useBreakpoint as jest.Mock).mockReturnValue({ isMobile: true });

    const { container } = render(<Onboarding />);
    expect(container).toMatchSnapshot();
  });
});
