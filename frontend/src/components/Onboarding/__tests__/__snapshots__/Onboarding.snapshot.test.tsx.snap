// Vitest Snapshot v1, https://vitest.dev/guide/snapshot.html

exports[`Onboarding snapshots > snapshot for address step 1`] = `
<div>
  <div
    class="_onboardingWrapper_48c0af"
  >
    <div
      data-testid="mock-header"
    >
      Set up your account
    </div>
    <div
      class="_stepperContainer_48c0af"
    >
      <div
        data-testid="mock-stepper"
      >
        OnboardingStepper
      </div>
    </div>
    <div
      class="_contentWrapper_48c0af"
    >
      <div
        class="_content_48c0af"
      >
        <div
          data-testid="mock-steps"
        >
          Step: 
          1
        </div>
      </div>
    </div>
    <div
      class="_buttonContainer_48c0af"
    >
      <button
        class="_button_ee73f7 _primary_ee73f7 _l_ee73f7 _green-primary_ee73f7 _withText_ee73f7 _disabled_ee73f7 _button_48c0af"
        disabled=""
      >
        <span
          class="_text_ee73f7 _text-l_ee73f7"
        >
          Next
        </span>
      </button>
    </div>
  </div>
</div>
`;

exports[`Onboarding snapshots > snapshot for details step 1`] = `
<div>
  <div
    class="_onboardingWrapper_48c0af"
  >
    <div
      data-testid="mock-header"
    >
      Set up your account
    </div>
    <div
      class="_stepperContainer_48c0af"
    >
      <div
        data-testid="mock-stepper"
      >
        OnboardingStepper
      </div>
    </div>
    <div
      class="_contentWrapper_48c0af"
    >
      <div
        class="_content_48c0af"
      >
        <div
          data-testid="mock-steps"
        >
          Step: 
          2
        </div>
      </div>
    </div>
    <div
      class="_buttonContainer_48c0af"
    >
      <button
        class="_button_ee73f7 _primary_ee73f7 _l_ee73f7 _green-primary_ee73f7 _withText_ee73f7 _disabled_ee73f7 _button_48c0af"
        disabled=""
      >
        <span
          class="_text_ee73f7 _text-l_ee73f7"
        >
          Submit
        </span>
      </button>
    </div>
  </div>
</div>
`;

exports[`Onboarding snapshots > snapshot for first step 1`] = `
<div>
  <div
    class="_onboardingWrapper_48c0af"
  >
    <div
      data-testid="mock-header"
    >
      Set up your account
    </div>
    <div
      class="_stepperContainer_48c0af"
    >
      <div
        data-testid="mock-stepper"
      >
        OnboardingStepper
      </div>
    </div>
    <div
      class="_contentWrapper_48c0af"
    >
      <div
        class="_content_48c0af"
      >
        <div
          data-testid="mock-steps"
        >
          Step: 
          0
        </div>
      </div>
    </div>
    <div
      class="_buttonContainer_48c0af"
    >
      <button
        class="_button_ee73f7 _primary_ee73f7 _l_ee73f7 _green-primary_ee73f7 _withText_ee73f7 _button_48c0af"
      >
        <span
          class="_text_ee73f7 _text-l_ee73f7"
        >
          Next
        </span>
      </button>
    </div>
  </div>
</div>
`;

exports[`Onboarding snapshots > snapshot for last step 1`] = `
<div>
  <div
    class="_onboardingWrapper_48c0af"
  >
    <div
      data-testid="mock-header"
    >
      Set up your account
    </div>
    <div
      class="_stepperContainer_48c0af"
    >
      <div
        data-testid="mock-stepper"
      >
        OnboardingStepper
      </div>
    </div>
    <div
      class="_contentWrapper_48c0af"
    >
      <div
        class="_content_48c0af"
      >
        <div
          data-testid="mock-steps"
        >
          Step: 
          3
        </div>
      </div>
    </div>
  </div>
</div>
`;

exports[`Onboarding snapshots > snapshot for mobile view 1`] = `
<div>
  <div
    class="_onboardingWrapper_48c0af"
  >
    <div
      data-testid="mock-header"
    >
      Set up your account
    </div>
    <div
      class="_stepperContainer_48c0af"
    >
      <div
        data-testid="mock-stepper"
      >
        OnboardingStepper
      </div>
    </div>
    <div
      class="_contentWrapper_48c0af"
    >
      <div
        class="_content_48c0af"
      >
        <div
          data-testid="mock-steps"
        >
          Step: 
          0
        </div>
      </div>
    </div>
    <div
      class="_buttonContainer_48c0af"
    >
      <button
        class="_button_ee73f7 _primary_ee73f7 _l_ee73f7 _green-primary_ee73f7 _withText_ee73f7 _button_48c0af"
      >
        <span
          class="_text_ee73f7 _text-l_ee73f7"
        >
          Next
        </span>
      </button>
    </div>
  </div>
</div>
`;
