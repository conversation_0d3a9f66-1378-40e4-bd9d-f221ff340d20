import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import { Onboarding } from '../Onboarding';
import { useOnboarding } from '@/hooks/useOnboarding';
import { useBreakpoint } from '@/utils/breakpointUtils';
import axios from 'axios';

vi.mock('@/hooks/useOnboarding');
vi.mock('@/utils/breakpointUtils');
vi.mock('axios');

// Mock the useSendOnboardingData hook
vi.mock('@/hooks/useSendOnboardingData', () => ({
  useSendOnboardingData: () => ({
    sendOnboardingData: vi.fn().mockResolvedValue({
      addressSendRequest: Promise.resolve(),
    }),
    sendDemoRequest: vi.fn().mockResolvedValue({
      demoSendRequest: Promise.resolve(),
    }),
  }),
}));

vi.mock('ezheaders', () => ({
  default: {
    get: vi.fn(),
    set: vi.fn(),
    delete: vi.fn(),
  },
}));

type StepType = {
  slug: string;
  isActive: boolean;
  isCompleted: boolean;
};

vi.mock('../components/OnboardingStepper/OnboardingStepper', () => ({
  OnboardingStepper: ({ steps, onBack }: { steps: StepType[]; onBack?: () => void }) => (
    <div data-testid="stepper">
      {steps.map((s: StepType, i: number) => (
        <div
          key={i}
          data-testid={`step-${i}`}
          data-active={s.isActive}
          data-completed={s.isCompleted}
        >
          {s.slug}
        </div>
      ))}
      {onBack && (
        <button onClick={onBack} data-testid="stepper-back">
          Back
        </button>
      )}
    </div>
  ),
}));

vi.mock('../components/OnboardingHeader/OnboardingHeader', () => ({
  OnboardingHeader: ({ title, onBack }: { title: string; onBack?: () => void }) => (
    <div data-testid="header">
      <h1>{title}</h1>
      {onBack && (
        <button onClick={onBack} data-testid="header-back">
          Back
        </button>
      )}
    </div>
  ),
}));

type AddressFieldsType = {
  line1: string;
  line2: string;
  city: string;
  postcode: string;
};

interface OnboardingStepsProps {
  onRoleSelected?: (role: string) => void;
  selectedRole?: string | null;
  onAddressSelected?: (address: string) => void;
  selectedAddress?: string | AddressFieldsType;
  step?: number;
  onPropertyTypeSelected?: (type: string) => void;
  selectedPropertyType?: string | null;
  onOwnershipTypeSelected?: (type: string) => void;
  selectedOwnershipType?: string | null;
}

vi.mock('../components/OnboardingSteps/OnboardingSteps', () => ({
  OnboardingSteps: ({
    onRoleSelected,
    onAddressSelected,
    selectedAddress,
    step,
    onPropertyTypeSelected,
    onOwnershipTypeSelected,
  }: OnboardingStepsProps) => (
    <div data-testid={`onboarding-step-${step || 0}`}>
      {step === undefined && (
        <div>
          <button
            data-testid="select-tenant"
            onClick={() => onRoleSelected && onRoleSelected('tenant')}
          >
            Tenant
          </button>
          <button
            data-testid="select-landlord"
            onClick={() => onRoleSelected && onRoleSelected('landlord')}
          >
            Landlord
          </button>
          <button
            data-testid="select-manager"
            onClick={() => onRoleSelected && onRoleSelected('manager')}
          >
            Manager
          </button>
        </div>
      )}
      {step === 1 && (
        <div>
          <input
            data-testid="address-input"
            value={typeof selectedAddress === 'string' ? selectedAddress : ''}
            onChange={(e) => onAddressSelected && onAddressSelected(e.target.value)}
          />
        </div>
      )}
      {step === 2 && (
        <div>
          <button
            data-testid="select-property-type-apartment"
            onClick={() => onPropertyTypeSelected && onPropertyTypeSelected('apartment')}
          >
            Apartment
          </button>
          <button
            data-testid="select-ownership-type-owner"
            onClick={() => onOwnershipTypeSelected && onOwnershipTypeSelected('owner')}
          >
            Owner
          </button>
        </div>
      )}
      {step === 3 && <div data-testid="final-step">Completion</div>}
    </div>
  ),
}));

interface OnboardingDemoProps {
  onSubmit?: () => void;
  loading?: boolean;
  setSelectedRole?: (role: string | null) => void;
}

vi.mock('../components/OnboardingDemo/OnboardingDemo', () => ({
  OnboardingDemo: ({ onSubmit, loading, setSelectedRole }: OnboardingDemoProps) => (
    <div data-testid="demo-form">
      <input data-testid="company-name" />
      <input data-testid="email" />
      <button onClick={onSubmit} disabled={loading} data-testid="submit-demo">
        Submit
      </button>
      <button onClick={() => setSelectedRole && setSelectedRole(null)} data-testid="change-role">
        Change role
      </button>
    </div>
  ),
  useDemoForm: () => ({
    companyName: '',
    numberOfProperties: { label: '10-50' },
    email: '',
  }),
}));

describe('Onboarding', () => {
  const mockNextStep = vi.fn();
  const mockGoToStep = vi.fn();
  const mockSetSelectedRole = vi.fn();
  const mockSetSelectedAddress = vi.fn();
  const mockSetSelectedPropertyType = vi.fn();
  const mockSetSelectedOwnershipType = vi.fn();
  const mockClearData = vi.fn();

  beforeEach(() => {
    vi.clearAllMocks();

    (useOnboarding as jest.Mock).mockReturnValue({
      steps: [{ key: 'role' }, { key: 'address' }, { key: 'details' }, { key: 'finish' }],
      currentStep: 0,
      goToStep: mockGoToStep,
      nextStep: mockNextStep,
      selectedRole: null,
      selectedAddress: null,
      selectedPropertyType: null,
      selectedOwnershipType: null,
      setSelectedRole: mockSetSelectedRole,
      setSelectedAddress: mockSetSelectedAddress,
      setSelectedPropertyType: mockSetSelectedPropertyType,
      setSelectedOwnershipType: mockSetSelectedOwnershipType,
      clearData: mockClearData,
    });

    (useBreakpoint as jest.Mock).mockReturnValue({ isMobile: false });
    (axios.post as jest.Mock).mockResolvedValue({ data: {} });
  });

  it('should render first step on initialization', () => {
    render(<Onboarding />);
    expect(screen.getByTestId('onboarding-step-0')).toBeInTheDocument();
    expect(screen.getByText('Set up your account')).toBeInTheDocument();
  });

  it('should activate Next button after selecting role', () => {
    (useOnboarding as jest.Mock).mockReturnValue({
      steps: [{ key: 'role' }, { key: 'address' }, { key: 'details' }, { key: 'finish' }],
      currentStep: 0,
      goToStep: mockGoToStep,
      nextStep: mockNextStep,
      selectedRole: 'tenant',
      selectedAddress: null,
      selectedPropertyType: null,
      selectedOwnershipType: null,
      setSelectedRole: mockSetSelectedRole,
      setSelectedAddress: mockSetSelectedAddress,
      setSelectedPropertyType: mockSetSelectedPropertyType,
      setSelectedOwnershipType: mockSetSelectedOwnershipType,
      clearData: mockClearData,
    });

    render(<Onboarding />);

    fireEvent.click(screen.getByTestId('select-tenant'));

    const nextButton = screen.getByText('Next');
    expect(nextButton.closest('button')).not.toHaveClass('_disabled_f8f296');
  });

  it('should call nextStep when Next button is clicked', () => {
    (useOnboarding as jest.Mock).mockReturnValue({
      steps: [{ key: 'role' }, { key: 'address' }, { key: 'details' }, { key: 'finish' }],
      currentStep: 0,
      goToStep: mockGoToStep,
      nextStep: mockNextStep,
      selectedRole: 'tenant',
      selectedAddress: null,
      selectedPropertyType: null,
      selectedOwnershipType: null,
      setSelectedRole: mockSetSelectedRole,
      setSelectedAddress: mockSetSelectedAddress,
      setSelectedPropertyType: mockSetSelectedPropertyType,
      setSelectedOwnershipType: mockSetSelectedOwnershipType,
      clearData: mockClearData,
    });

    render(<Onboarding />);

    fireEvent.click(screen.getByTestId('select-tenant'));
    fireEvent.click(screen.getByText('Next'));

    expect(mockNextStep).toHaveBeenCalledTimes(1);
  });

  it('should display Back button on second step', () => {
    (useOnboarding as jest.Mock).mockReturnValue({
      steps: [{ key: 'role' }, { key: 'address' }, { key: 'details' }, { key: 'finish' }],
      currentStep: 1,
      goToStep: mockGoToStep,
      nextStep: mockNextStep,
      selectedRole: 'tenant',
      selectedAddress: null,
      selectedPropertyType: null,
      selectedOwnershipType: null,
      setSelectedRole: mockSetSelectedRole,
      setSelectedAddress: mockSetSelectedAddress,
      setSelectedPropertyType: mockSetSelectedPropertyType,
      setSelectedOwnershipType: mockSetSelectedOwnershipType,
      clearData: mockClearData,
    });

    render(<Onboarding />);

    expect(screen.getByTestId('header-back')).toBeInTheDocument();
  });
});
