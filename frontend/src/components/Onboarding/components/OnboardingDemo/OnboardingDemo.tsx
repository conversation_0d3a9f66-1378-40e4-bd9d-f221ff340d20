import React from 'react';
import styles from './OnboardingDemo.module.scss';
import { useOnboarding } from '@/hooks/useOnboarding';
import { CheckmarkCircle01Icon, ArrowDown01Icon } from '@hugeicons-pro/core-stroke-rounded';
import { HugeiconsIcon } from '@/components/HugeiconsIcon';
import { Button } from '@/components/Button';
import { ButtonType, ButtonColor, ButtonSize, ButtonState } from '@/components/Button/Button.types';
import { IconSvgObject } from '@hugeicons/react';
import { useRouter } from 'next/navigation';

const companyNumberOfPropertiesOptions = [
  { value: 'Fewer than 50', label: 'Fewer than 50' },
  { value: '51 - 200', label: '51 - 200' },
  { value: '201 - 1,000', label: '201 - 1,000' },
  { value: '2,500 - 5,000', label: '2,500 - 5,000' },
  { value: 'More than 5,000', label: 'More than 5,000' },
];

type OptionType = { value: string; label: string };

export const useDemoForm = () => {
  const [companyName, setCompanyName] = React.useState('');
  const [numberOfProperties, setNumberOfProperties] = React.useState<OptionType | null>(null);
  const [email, setEmail] = React.useState('');
  const [numberOfPropertiesDropdownOpen, setNumberOfPropertiesDropdownOpen] = React.useState(false);
  const numberOfPropertiesInputRef = React.useRef<HTMLDivElement>(null);

  const isDemoButtonDisabled =
    !companyName.trim() || !numberOfProperties || !/^[^@\s]+@[^@\s]+\.[^@\s]+$/.test(email);

  React.useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (
        numberOfPropertiesInputRef.current &&
        !numberOfPropertiesInputRef.current.contains(event.target as Node)
      ) {
        setNumberOfPropertiesDropdownOpen(false);
      }
    }
    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  return {
    companyName,
    setCompanyName,
    numberOfProperties,
    setNumberOfProperties,
    email,
    setEmail,
    isDemoButtonDisabled,
    numberOfPropertiesDropdownOpen,
    setNumberOfPropertiesDropdownOpen,
    numberOfPropertiesInputRef,
  };
};

interface OnboardingDemoProps {
  form?: ReturnType<typeof useDemoForm>;
  onSubmit?: () => void;
  loading?: boolean;
  setSelectedRole?: (role: string) => void;
  setManagerSingleProperty?: (value: boolean) => void;
  success?: boolean;
  error?: string | null;
}

export const OnboardingDemo: React.FC<OnboardingDemoProps> = ({
  form,
  onSubmit,
  loading,
  setSelectedRole,
  setManagerSingleProperty,
  success: externalSuccess,
  error,
}) => {
  const router = useRouter();
  const localForm = useDemoForm();
  const {
    companyName,
    setCompanyName,
    numberOfProperties,
    setNumberOfProperties,
    email,
    setEmail,
    isDemoButtonDisabled,
    numberOfPropertiesDropdownOpen,
    setNumberOfPropertiesDropdownOpen,
    numberOfPropertiesInputRef,
  } = form || localForm;

  const { goToStep } = useOnboarding();
  const [internalSuccess, setInternalSuccess] = React.useState(false);
  const success = externalSuccess !== undefined ? externalSuccess : internalSuccess;

  const handleSubmit = async () => {
    try {
      if (onSubmit) {
        await onSubmit();
        setInternalSuccess(true);
      }
    } catch (e) {
      console.error(e);
    }
  };

  if (success) {
    return (
      <div className={`${styles.demoWrapper} ${styles.successWrapper}`}>
        <HugeiconsIcon
          icon={CheckmarkCircle01Icon as unknown as IconSvgObject}
          size={92}
          className={styles.successIcon}
        />
        <h2 className={styles.title} style={{ marginBottom: 16 }}>
          We have received your request
        </h2>
        <div className={styles.description} style={{ marginBottom: 32 }}>
          Thanks for your interest! One of our product specialists will be in touch shortly to walk
          you through a personalised demo. We&apos;re excited to show you what we can do.
        </div>
        <div style={{ display: 'flex', flexDirection: 'column', gap: 16 }}>
          <Button
            type={ButtonType.PRIMARY}
            color={ButtonColor.GREEN_PRIMARY}
            size={ButtonSize.L}
            className={styles.demoButton}
            onClick={() => {
              setInternalSuccess(false);
              setSelectedRole && setSelectedRole('manager');
              setManagerSingleProperty && setManagerSingleProperty(true);
              goToStep && goToStep(1);
            }}
          >
            Continue set up for a single property
          </Button>
          <Button
            type={ButtonType.SECONDARY}
            color={ButtonColor.GREEN_PRIMARY}
            size={ButtonSize.L}
            className={styles.demoButton}
            outline
            onClick={() => goToStep && goToStep(0)}
          >
            Set up Alfie for your own property
          </Button>
          <Button
            type={ButtonType.TERTIARY}
            color={ButtonColor.GREEN_PRIMARY}
            size={ButtonSize.L}
            className={styles.demoButton}
            onClick={() => router.replace('/')}
          >
            Try Alfie without property setup
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className={styles.demoWrapper}>
      <h2 className={styles.title}>Request a demo for your business</h2>
      <p className={styles.description}>
        Our business product for property management professionals includes a tenant experience app
        and enhanced features to manage property portfolios efficiently with AI native operations
        and workflows
      </p>
      <div className={styles.formGroup}>
        <label className={styles.label}>Company name</label>
        <input
          className={styles.input}
          placeholder="Name of company"
          value={companyName}
          onChange={(e) => setCompanyName(e.target.value)}
        />
      </div>
      <div
        className={styles.formGroup}
        ref={numberOfPropertiesInputRef}
        style={{ position: 'relative' }}
      >
        <label className={styles.label}>Properties under management</label>
        <input
          className={styles.input}
          placeholder="Choose a range"
          value={numberOfProperties ? numberOfProperties.label : ''}
          readOnly
          onFocus={() => setNumberOfPropertiesDropdownOpen(true)}
          onClick={() => setNumberOfPropertiesDropdownOpen(true)}
          style={{ paddingRight: 40 }}
        />
        <HugeiconsIcon
          icon={ArrowDown01Icon as unknown as IconSvgObject}
          size={16}
          className={styles.inputArrow}
        />
        {numberOfPropertiesDropdownOpen && (
          <div className={styles.dropdown}>
            {companyNumberOfPropertiesOptions.map((option) => (
              <div
                key={option.value}
                className={styles.option}
                onClick={() => {
                  setNumberOfProperties(option);
                  setNumberOfPropertiesDropdownOpen(false);
                }}
              >
                {option.label}
              </div>
            ))}
          </div>
        )}
      </div>
      <div className={styles.formGroup}>
        <label className={styles.label}>Business email address</label>
        <input
          className={styles.input}
          placeholder="<EMAIL>"
          type="email"
          value={email}
          onChange={(e) => setEmail(e.target.value)}
        />
      </div>
      {error && (
        <div
          className={styles.errorMessage}
          style={{ color: '#E53E3E', marginTop: 16, textAlign: 'center' }}
        >
          {error}
        </div>
      )}
      <div className={styles.buttonContainer}>
        <Button
          onClick={handleSubmit}
          type={ButtonType.PRIMARY}
          color={ButtonColor.GREEN_PRIMARY}
          size={ButtonSize.L}
          state={loading || isDemoButtonDisabled ? ButtonState.DISABLED : ButtonState.DEFAULT}
          className={styles.demoButton}
        >
          Request a demo
        </Button>
      </div>
    </div>
  );
};
