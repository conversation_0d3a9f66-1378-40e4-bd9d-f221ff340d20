import React, { useState } from 'react';
import { MultiFieldInput } from '@/components/MultiFieldInput';
import {
  MultiFieldInputMode,
  MultiFieldInputType,
} from '@/components/MultiFieldInput/MultiFieldInput.types';
import styles from './OnboardingAddress.module.scss';

export interface OnboardingAddressProps {
  onAddressSelected?: (address: string) => void;
}

export const OnboardingAddress: React.FC<OnboardingAddressProps> = ({ onAddressSelected }) => {
  const [mode, setMode] = useState<MultiFieldInputMode>(MultiFieldInputMode.SELECT);
  const [address, setAddress] = useState('');

  const handleAddressChange = (value: string | object) => {
    if (typeof value === 'string') {
      setAddress(value);
      if (onAddressSelected) {
        onAddressSelected(value);
      }
    }
  };

  const handleSwitchToManual = () => {
    setMode(MultiFieldInputMode.MANUAL);
  };

  return (
    <div className={styles.container}>
      <h1 className={styles.title}>Property address</h1>
      <h2 className={styles.subtitle}>Find the property that you manage</h2>

      <MultiFieldInput
        type={MultiFieldInputType.FINDADDRESS}
        title=""
        placeholder="Find address"
        value={address}
        onChange={handleAddressChange}
        initialMode={mode}
        onSwitchToManual={handleSwitchToManual}
        manualEntryText="Enter address manually"
        className={styles.addressInput}
      />
    </div>
  );
};
