import React from 'react';
import classNames from 'classnames';
import styles from './OnboardingStepper.module.scss';
import { useBreakpoint } from '@/utils/breakpointUtils';
import { OnboardingStepperProps } from '../../Onboarding.types';

export const OnboardingStepper: React.FC<OnboardingStepperProps> = ({ steps }) => {
  const { isMobile } = useBreakpoint();

  return (
    <div className={styles.stepper}>
      <div className={`${styles.steps} ${isMobile ? styles.mobile : ''}`}>
        {steps.map((step) => (
          <div
            key={step.slug}
            className={classNames(
              styles.line,
              { [styles.completed]: step.isCompleted },
              { [styles.active]: step.isActive }
            )}
          />
        ))}
      </div>
    </div>
  );
};
