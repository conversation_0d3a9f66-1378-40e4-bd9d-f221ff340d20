import React from 'react';
import styles from './OnboardingHeader.module.scss';
import { IconSvgObject } from '@hugeicons/react';
import { ArrowLeft01Icon } from '@hugeicons-pro/core-stroke-standard';
import { HugeiconsIcon } from '@/components/HugeiconsIcon';
import { OnboardingHeaderProps } from '../../Onboarding.types';

export const OnboardingHeader: React.FC<OnboardingHeaderProps> = ({ title, onBack }) => (
  <header className={styles.header}>
    {onBack && (
      <HugeiconsIcon
        icon={ArrowLeft01Icon as unknown as IconSvgObject}
        size={24}
        className={styles.backButton}
        onClick={onBack}
      />
    )}
    <div className={styles.title}>{title}</div>
  </header>
);
