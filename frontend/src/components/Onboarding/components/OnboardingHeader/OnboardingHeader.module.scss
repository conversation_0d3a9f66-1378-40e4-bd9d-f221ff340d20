.header {
  display: flex;
  align-items: center;
  padding: var(--spacing-2-5);
  position: relative;
  border-bottom: 1px solid #dadada;
  min-height: 64px;

  @media (max-width: 768px) {
    background-color: var(--colors-white);
    position: sticky;
    left: 0;
    top: 0;
    right: 0;
    z-index: 1;
  }
}

.backButton {
  background: none;
  border: none;
  display: flex;
  align-items: center;
  cursor: pointer;
  color: var(--colors-gray-900);
  margin-right: var(--spacing-2);
  position: absolute;
}

.title {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-semibold);
  color: var(--colors-gray-900);
  text-align: center;
  flex: 1;
}

@media (max-width: 600px) {
  .title {
    font-size: 16px;
    line-height: 20px;
  }

  .backButton {
    margin-right: var(--spacing-2);
  }
}