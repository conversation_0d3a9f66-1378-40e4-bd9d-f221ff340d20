'use client';

import React, { useCallback, useEffect, useRef, useState } from 'react';
import { useAuth } from '@clerk/nextjs';
import { useInView } from 'react-intersection-observer';

import { Button } from '@/components/Button';
import { ButtonColor, ButtonState, ButtonType } from '@/components/Button/Button.types';
import { ApplianceModal } from '@/components/ApplianceModal';
import { ApplianceFormData } from '@/components/ApplianceModal/ApplianceModal.types';
import { ApplianceForm } from '@/components/ApplianceModal/ApplianceForm';
import { PersonalizationCard } from '@/components/PersonalizationCard';
import {
  PersonalizationCardField,
  PersonalizationCardFile,
} from '@/components/PersonalizationCard/PersonalizationCard.types';
import { FileUploadGrid } from '@/components/FileUploadManager';
import { Breadcrumbs } from '@/components/Breadcrumbs';
import { Spinner } from '@/components/Spinner';

import { useWidgets } from '@/hooks/useWidgets';
import { useAppliances } from '@/hooks/useAppliances';
import { AppliancesPageProps } from './AppliancesPage.types';
import styles from './AppliancesPage.module.scss';
import { useFiles } from '@/containers/FilesPage/useFiles';
import { NotificationAlert } from '@/components/NotificationAlert/NotificationAlert';

export const AppliancesPage: React.FC<AppliancesPageProps> = () => {
  const { getToken } = useAuth();
  const { properties } = useWidgets();
  const {
    appliances,
    editingAppliances,
    isCreating,
    hasMoreItems,
    isLoadingMore,
    fetchAppliances,
    loadMoreAppliances,
    createAppliance,
    updateAppliance,
    setEditingAppliance,
    cancelEditingAppliance,
    removeFileFromAppliance,
  } = useAppliances();

  const [isModalOpen, setIsModalOpen] = useState(false);
  const [inlineForm, setInlineForm] = useState<ApplianceFormData>({
    applianceType: '',
    brand: '',
    model: '',
    serialNumber: '',
    warranty: '',
  });
  const [inlineFormTouched, setInlineFormTouched] = useState(false);
  const appliancesContainerRef = useRef<HTMLDivElement>(null);

  const { ref: sentinelRef, inView } = useInView({
    rootMargin: '100px',
    threshold: 0.1,
  });

  const currentPropertyId = properties.length > 0 ? properties[0].id : null;

  const applianceItems = [
    'Fridge/Freezer',
    'Washing machine and dryer',
    'Dishwasher',
    'Boiler and heating systems',
    'Air conditioning',
    'Oven',
    'Microwave',
    'Cooking hob and hood',
    'Other',
  ];

  const loadAppliances = useCallback(async () => {
    const token = await getToken();
    if (token) {
      await fetchAppliances(token, true);
    }
  }, [fetchAppliances, getToken]);

  const { uploadFiles, uploadingFiles, deleteNotification, notifications } = useFiles({
    getUploadContext: 'appliancesPage',
    setUploadContext: 'appliancesPage',
    onChanged: loadAppliances,
  });

  const firstErrorNotification = notifications.find((n) => n.severity === 'error');
  const firstInfoNotification = notifications.find(
    (n) =>
      n.severity === 'info' &&
      !(n.type === 'information_from_document_saved' && n.category === 'appliance')
  );
  const firstWarningNotification = notifications.find((n) => n.severity === 'warning');

  useEffect(() => {
    void loadAppliances();
  }, [loadAppliances]);

  useEffect(() => {
    if (inView && hasMoreItems && !isLoadingMore && appliances.length > 0) {
      getToken().then((token) => {
        token && loadMoreAppliances(token);
      });
    }
  }, [inView, hasMoreItems, isLoadingMore, loadMoreAppliances, getToken, appliances.length]);

  const handleUploadDocument = () => {
    fileInputRef.current?.click();
  };

  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const selectedFiles = event.target.files;
    if (!selectedFiles || selectedFiles.length === 0) return;

    const fileArray = Array.from(selectedFiles);
    void uploadFiles(fileArray);

    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  const handleAddManually = () => {
    setIsModalOpen(true);
  };

  const handleCloseModal = () => {
    setIsModalOpen(false);
  };

  const handleSaveAppliance = async (data: ApplianceFormData) => {
    if (!currentPropertyId) {
      console.error('Missing property ID');
      return;
    }

    try {
      const token = await getToken();
      if (!token) {
        console.error('Failed to get authentication token');
        return;
      }

      await createAppliance(data, currentPropertyId, token);
      setIsModalOpen(false);
    } catch (error) {
      console.error('Failed to save appliance:', error);
    }
  };

  const handleSaveInlineAppliance = async () => {
    if (!inlineForm.applianceType.trim()) {
      setInlineFormTouched(true);
      return;
    }

    if (!currentPropertyId) {
      console.error('Missing property ID');
      return;
    }

    try {
      const token = await getToken();
      if (!token) {
        console.error('Failed to get authentication token');
        return;
      }

      await createAppliance(inlineForm, currentPropertyId, token);
      setInlineForm({
        applianceType: '',
        brand: '',
        model: '',
        serialNumber: '',
        warranty: '',
      });
      setInlineFormTouched(false);
    } catch (error) {
      console.error('Failed to save inline appliance:', error);
    }
  };

  const handleEdit = (id: string) => () => {
    setEditingAppliance(id);
  };

  const handleCancel = (id: string) => () => {
    cancelEditingAppliance(id);
  };

  const handleSave =
    (id: string) =>
    async (
      updatedFields: PersonalizationCardField[],
      updatedFiles: PersonalizationCardFile[],
      updatedTitle?: string
    ) => {
      try {
        const token = await getToken();
        if (!token) {
          console.error('Failed to get authentication token');
          return;
        }

        await updateAppliance(id, updatedFields, updatedFiles, updatedTitle, token);
        cancelEditingAppliance(id);
      } catch (error) {
        console.error('Failed to update appliance:', error);
      }
    };

  const handleFileRemove = (applianceId: string, fileId: string | number) => {
    removeFileFromAppliance(applianceId, String(fileId));
  };

  const fileInputRef = useRef<HTMLInputElement>(null);

  return (
    <div className={styles.container}>
      <Breadcrumbs
        path={[{ value: 'Property Profile', url: '/property-profile' }, { value: 'Appliances' }]}
      />

      <h1 className={styles.heading}>Appliances</h1>

      <div className={styles.content}>
        <h2 className={styles.subheading}>Upload documents, receipts, or photos</h2>
        <p className={styles.paragraph}>Examples include invoices, warranties, and manuals for:</p>
        <ul className={styles.list}>
          {applianceItems.map((item, index) => (
            <li key={index} className={styles.listItem}>
              {item}
            </li>
          ))}
        </ul>

        <div className={styles.buttons}>
          <Button
            type={ButtonType.PRIMARY}
            color={ButtonColor.GREEN_PRIMARY}
            onClick={handleUploadDocument}
          >
            Upload documents
          </Button>
          {appliances.length > 0 && (
            <Button
              type={ButtonType.SECONDARY}
              color={ButtonColor.GREEN_PRIMARY}
              onClick={handleAddManually}
            >
              Add manually
            </Button>
          )}
        </div>

        <input
          type="file"
          ref={fileInputRef}
          onChange={handleFileChange}
          accept=".pdf,.png,.jpg,.jpeg,.webp,.gif,.heic,.heif"
          multiple
          style={{ display: 'none' }}
        />

        {uploadingFiles.length > 0 && (
          <>
            <h2 className={styles.subheading}>Your documents</h2>
            <div className={styles.uploadingFiles}>
              <FileUploadGrid files={uploadingFiles} showRemoveButton={false} />
            </div>
          </>
        )}

        <div className={styles.alerts}>
          {firstErrorNotification && (
            <NotificationAlert notification={firstErrorNotification} onClose={deleteNotification} />
          )}
          {firstWarningNotification && (
            <NotificationAlert
              notification={firstWarningNotification}
              onClose={deleteNotification}
            />
          )}
          {firstInfoNotification && (
            <NotificationAlert notification={firstInfoNotification} onClose={deleteNotification} />
          )}
        </div>

        {appliances.length === 0 && (
          <>
            <div className={styles.divider}></div>
            <h3 className={styles.inlineFormTitle}>Add appliance manually</h3>
            <ApplianceForm
              value={inlineForm}
              onChange={setInlineForm}
              disabled={isCreating}
              showValidation={inlineFormTouched}
            />
            <div className={styles.inlineFormActions}>
              <Button
                type={ButtonType.PRIMARY}
                color={ButtonColor.GREEN_PRIMARY}
                state={inlineForm.applianceType.trim() ? ButtonState.DEFAULT : ButtonState.DISABLED}
                onClick={handleSaveInlineAppliance}
                disabled={!inlineForm.applianceType.trim()}
              >
                Save
              </Button>
            </div>
          </>
        )}
      </div>

      <div className={styles.content} ref={appliancesContainerRef}>
        {appliances.map((appliance) => (
          <div key={appliance.id} className={styles.PersonalizationCard}>
            <PersonalizationCard
              title={appliance.title}
              fields={appliance.fields}
              files={appliance.files}
              isEditing={editingAppliances.includes(appliance.id)}
              onEdit={handleEdit(appliance.id)}
              onCancel={handleCancel(appliance.id)}
              onSave={handleSave(appliance.id)}
              onFileRemove={(fileId) => handleFileRemove(appliance.id, fileId)}
              context="appliances"
            />
          </div>
        ))}
        {hasMoreItems && appliances.length > 0 && <div ref={sentinelRef} style={{ height: 50 }} />}
      </div>

      {isLoadingMore && (
        <div className={styles.loadingSpinner}>
          <Spinner />
        </div>
      )}

      <ApplianceModal
        open={isModalOpen}
        onClose={handleCloseModal}
        onSave={handleSaveAppliance}
        loading={isCreating}
      />
    </div>
  );
};
