import type { <PERSON>a, StoryObj } from '@storybook/react';
import { Todo } from './Todo';

const meta = {
  title: 'Components/Todo/Todo',
  component: Todo,
  parameters: {
    layout: 'centered',
  },
  tags: ['autodocs'],
} satisfies Meta<typeof Todo>;

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
  args: {
    todo: {
      id: 1,
      name: 'Complete the project documentation',
      description: 'Ensure all sections are well-documented and examples are provided.',
      dueDate: new Date('2023-12-31').toUTCString(),
      doneDate: null,
      type: 'userCreated',
      deletedDate: null,
    },
  },
};
