.todoContainer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.5rem 1rem 1rem;
  border-bottom: 1px solid var(--colors-gray-200);

  &:hover {
    background-color: var(--colors-gray-50);
  }
}

.textContainer {
  display: flex;
  flex-direction: column;
}

.title {
  font-size: 1rem;
  color: var(--colors-gray-900);
  max-width: 80%;
  margin-top: 0;
}

.description {
  font-size: 0.75rem;
  color: var(--colors-gray-500);
  max-width: 80%;
}

.dueDateContainer {
  display: flex;
  align-items: center;
  gap: 8px;
}

.dueDate {
  font-size: 0.625rem;
  color: var(--colors-gray-500);
  white-space: nowrap;
}

.ellipsis {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.todoContent {
  display: flex;
  gap: 0.5rem;
  align-items: flex-start;
}

.checkboxContainer {
  margin-top: 4px;
}

.completed {
  text-decoration: line-through;
}
