import { Button } from '@/components/ui/button';
import { ITodoActionsProps } from '../types';
import { HugeiconsIcon } from '@/components/HugeiconsIcon';
import * as SolidStandard from '@hugeicons-pro/core-solid-standard';
import { IconSvgObject } from '@hugeicons/react';
import { MouseEvent, FC } from 'react';
import { useAcceptTodo, useRejectTodo } from '@/hooks/useTodoService';
import { API_ENDPOINTS, getApiUrl } from '@/constants/api';
import { useSWRConfig } from 'swr';
import { toast } from '@/components/ui/sonner';

export const ApprovalActions: FC<ITodoActionsProps> = ({ todo }) => {
  const { trigger: acceptTodo } = useAcceptTodo();
  const { trigger: rejectTodo } = useRejectTodo();
  const { mutate } = useSWRConfig();

  const refreshTodos = () => {
    mutate(`${getApiUrl(API_ENDPOINTS.TODOS_ACCEPTED)}/`);
    mutate(`${getApiUrl(API_ENDPOINTS.TODOS_SUGGESTED)}/`);
    mutate(getApiUrl(`${API_ENDPOINTS.TODOS}/${todo.id}`));
  };

  const handleApprove = async (e: MouseEvent<HTMLButtonElement>) => {
    e.stopPropagation();
    await acceptTodo({ todoId: todo.id });

    refreshTodos();
    toast.success({ title: 'Alfie’s suggested to-do accepted' });
  };

  const handleDelete = async (e: MouseEvent<HTMLButtonElement>) => {
    e.stopPropagation();
    await rejectTodo({ todoId: todo.id });

    refreshTodos();
    toast.info({ title: 'Alfie’s suggested to-do rejected' });
  };

  return (
    <div className="flex gap-2 items-center">
      <Button size="xs" variant="ghost" onClick={handleApprove}>
        <HugeiconsIcon size={12} icon={SolidStandard.Tick02Icon as unknown as IconSvgObject} />
        Accept
      </Button>
      <Button size="xs" variant="danger" onClick={handleDelete}>
        <HugeiconsIcon
          size={12}
          icon={SolidStandard.MultiplicationSignIcon as unknown as IconSvgObject}
        />
        Reject
      </Button>
    </div>
  );
};
