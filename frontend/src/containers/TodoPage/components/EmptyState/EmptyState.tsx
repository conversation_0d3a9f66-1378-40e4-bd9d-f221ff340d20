'use client';

import { CheckListIcon } from '@hugeicons-pro/core-stroke-rounded';
import { HugeiconsIcon } from '@/components/HugeiconsIcon';
import { IconSvgObject } from '@hugeicons/react';
import * as SolidStandard from '@hugeicons-pro/core-solid-standard';
import styles from './EmptyState.module.scss';
import { Button } from '@/components/ui/button';
import { useAuth } from '@clerk/nextjs';
import { useModalForGuest } from '@/hooks/useModalGuest';

interface IEmptyStateProps {
  onClick?: () => void;
}

export const EmptyState = ({ onClick }: IEmptyStateProps) => {
  const { isSignedIn } = useAuth();
  const { openModal } = useModalForGuest();

  const handleClick = () => {
    if (!isSignedIn) {
      openModal();
      return;
    }
    onClick?.();
  };

  return (
    <div className={styles.container}>
      <div className={styles.icon}>
        <HugeiconsIcon icon={CheckListIcon as unknown as IconSvgObject} size={73} />
      </div>
      <h2 className="text-base font-bold text-center font-[Quasimoda]">Nothing here yet</h2>
      <p className="text-base text-center font-[Quasimoda]">
        Start by adding your first task to keep everything organised in one place.
      </p>
      <Button onClick={handleClick}>
        <HugeiconsIcon icon={SolidStandard.Add01Icon as unknown as IconSvgObject} />
        Add a new to-do
      </Button>
    </div>
  );
};
