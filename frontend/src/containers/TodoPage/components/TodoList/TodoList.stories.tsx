import type { <PERSON>a, StoryObj } from '@storybook/react';
import { TodoList } from './TodoList';
import { useState } from 'react';
import { ITodoDto } from '@/api/todos/types';

const meta = {
  title: 'Components/Todo/TodoList',
  component: TodoList,
  parameters: {
    layout: 'centered',
  },
  tags: ['autodocs'],
} satisfies Meta<typeof TodoList>;

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
  args: {
    todos: [
      {
        id: 1,
        name: 'Complete the project documentation',
        description: 'Ensure all sections are well-documented and examples are provided.',
        doneDate: null,
        dueDate: new Date('2023-12-31').toISOString(),
        deletedDate: null,
        type: 'userCreated',
      },
      {
        id: 2,
        name: 'Review the codebase for optimizations',
        description: 'Look for areas where performance can be improved.',
        doneDate: new Date('2023-11-30').toISOString(),
        dueDate: new Date('2023-11-30').toISOString(),
        deletedDate: null,
        type: 'userCreated',
      },
      {
        id: 3,
        name: 'Prepare for the upcoming team meeting',
        description: 'Gather all necessary materials and updates.',
        doneDate: null,
        dueDate: new Date('2023-12-15').toISOString(),
        deletedDate: null,
        type: 'userCreated',
      },
      {
        id: 4,
        name: 'Prepare for the upcoming team meeting',
        doneDate: null,
        description: null,
        dueDate: new Date('2023-12-15').toISOString(),
        deletedDate: null,
        type: 'userCreated',
      },
      {
        id: 5,
        name: 'Prepare for the upcoming team meeting',
        description: null,
        doneDate: null,
        deletedDate: null,
        type: 'userCreated',
        dueDate: null,
      },
    ],
    onChecked: (id: ITodoDto['id']) => {
      console.log(`Todo with id ${id} checked/unchecked`);
    },
    onClick: (id: ITodoDto['id']) => {
      console.log(`Todo with id ${id} clicked`);
    },
  },
  render: function TodoContainer(args) {
    const [todos, setTodos] = useState(args.todos);

    const handleChecked = (id: ITodoDto['id']) => {
      setTodos((prevTodos) =>
        prevTodos.map((todo) =>
          todo.id === id ? { ...todo, doneDate: new Date().toISOString() } : todo
        )
      );
    };

    return <TodoList {...args} todos={todos} onChecked={handleChecked} />;
  },
};
