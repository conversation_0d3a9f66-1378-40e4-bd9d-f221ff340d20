'use client';

import * as SolidStandard from '@hugeicons-pro/core-solid-standard';
import { Dialog, DialogTrigger, DialogContent } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { useRef } from 'react';
import { HugeiconsIcon } from '@/components/HugeiconsIcon';
import { IconSvgObject } from '@hugeicons/react';
import { CreateTodoForm } from './CreateTodoForm';
import { useTodoContext } from '../../useTodoContext';
import { parseAsBoolean, useQueryState } from 'nuqs';

interface CreateTodoProps {
  hideTriggerButton?: boolean;
}

export const CreateTodo: React.FC<CreateTodoProps> = ({ hideTriggerButton }) => {
  const [open, setOpen] = useQueryState<boolean>('createTodo', parseAsBoolean.withDefault(false));
  const triggerButtonRef = useRef<HTMLButtonElement>(null);
  const { confirmAction } = useTodoContext();

  const handleOnSubmit = () => {
    setTimeout(() => {
      setOpen(false);
      triggerButtonRef.current?.focus();
    }, 0);
  };

  const handleCloseModal = async () => {
    try {
      await confirmAction('closeWithUnsaved', null);
      setOpen(true);
    } catch (_err) {
      setOpen(false);
    }
  };

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        {!hideTriggerButton && (
          <Button ref={triggerButtonRef} size="xs">
            <HugeiconsIcon icon={SolidStandard.Add01Icon as unknown as IconSvgObject} /> Add a new
            to-do
          </Button>
        )}
      </DialogTrigger>
      <DialogContent className="lg:!max-w-3xl" showCloseButton={false}>
        <div className="absolute top-4 right-4 flex gap-4 items-center">
          <Button size="icon" variant="link" onClick={handleCloseModal}>
            <HugeiconsIcon
              icon={SolidStandard.MultiplicationSignIcon as unknown as IconSvgObject}
              size={24}
            />
          </Button>
        </div>
        <CreateTodoForm onSubmit={handleOnSubmit} />
      </DialogContent>
    </Dialog>
  );
};
