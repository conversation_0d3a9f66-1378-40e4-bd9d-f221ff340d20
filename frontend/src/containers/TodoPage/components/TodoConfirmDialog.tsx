import { ConfirmDialog } from '@/components/ConfirmDialog';
import { useTodoContext } from '../useTodoContext';
import { ButtonVariants } from '@/components/ui/button';

const dialogData: Record<
  string,
  {
    title: string;
    description: string;
    okText: string;
    cancelText: string;
    cancelType?: ButtonVariants['variant'];
  }
> = {
  markAsDone: {
    title: 'Done!',
    description: 'Mark to-do as done and remove from to-do list',
    okText: 'Keep to-do',
    cancelText: 'Mark as done',
    cancelType: 'default',
  },
  removeTodo: {
    title: 'Are you sure you want to delete this to-do?',
    description: 'Deleting will get rid of your task permanently',
    okText: 'Cancel',
    cancelText: 'Delete',
  },
  closeWithUnsaved: {
    title: 'You have an unsaved to-do',
    description: 'Are you sure you want to close this window?',
    okText: 'Keep editing',
    cancelText: 'Discard to-do',
  },
  editTodo: {
    title: 'You have unsaved changes',
    description: 'What would you like to do?',
    okText: 'Keep editing',
    cancelText: 'Discard',
  },
};

export const TodoConfirmDialog = () => {
  const { dialog, cancelDialog, completeDialog } = useTodoContext();

  if (!dialog.type) return null;

  return (
    <ConfirmDialog
      title={dialogData[dialog.type].title}
      description={dialogData[dialog.type].description}
      isOpen={!!dialog.type}
      onCancel={cancelDialog}
      onOk={completeDialog}
      okText={dialogData[dialog.type].okText}
      cancelText={dialogData[dialog.type].cancelText}
      cancelType={dialogData[dialog.type]?.cancelType}
    />
  );
};
