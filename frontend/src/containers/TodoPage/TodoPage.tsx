'use client';

import { TodoList } from './components/TodoList/TodoList';
import { CreateTodo } from './components/CreateTodo';
import { TodoDetails } from './components/TodoDetails';
import { useTodoPage } from './useTodoPage';
import { TodoConfirmDialog } from './components/TodoConfirmDialog';
import { Typography } from '@/components/Typography/Typography';
import { EmptyState } from './components/EmptyState';
import { parseAsBoolean, useQueryState } from 'nuqs';
import { ApprovalActions } from './components/Todo/actions/ApprovalActions';

export const TodoPage = () => {
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const [_, setOpen] = useQueryState<boolean>('createTodo', parseAsBoolean.withDefault(false));
  const {
    todos,
    todosSuggested,
    selectedTodoId,
    handleChecked,
    handleClickTodo,
    handleCloseTodoDetails,
  } = useTodoPage();

  return (
    <div className="py-6 w-full mx-auto px-4 overflow-x-auto max-w-[798px]">
      <div className="flex items-center justify-between mb-4">
        <Typography font="quasimoda" variant="h4">
          To-Do List
        </Typography>
        <CreateTodo hideTriggerButton={todos.length === 0} />
      </div>
      {todos.length ? (
        <div className="mb-10">
          <TodoList todos={todos} onChecked={handleChecked} onClick={handleClickTodo} />
        </div>
      ) : (
        <EmptyState onClick={() => setOpen(true)} />
      )}
      {todosSuggested.length > 0 && (
        <>
          <div className="mb-4">
            <Typography font="quasimoda" variant="h4">
              Suggested by Alfie
            </Typography>
          </div>
          <TodoList
            todos={todosSuggested}
            onClick={handleClickTodo}
            ActionsComponent={ApprovalActions}
          />
        </>
      )}
      <TodoDetails
        isOpen={selectedTodoId !== null}
        onClose={handleCloseTodoDetails}
        todoId={selectedTodoId}
      />
      <TodoConfirmDialog />
    </div>
  );
};
