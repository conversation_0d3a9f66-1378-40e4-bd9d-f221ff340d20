.container {
  width: 100%;
  padding: 0 16px 48px;
  margin: 0 auto;
  box-sizing: border-box;
  height: 100vh;
  max-width: 798px;
  overflow-y: auto;

  @media (max-width: 768px) {
    padding-bottom: 24px;
  }

  &::-webkit-scrollbar {
    display: none;
  }

  -ms-overflow-style: none;
  scrollbar-width: none;
}

.content {
  max-width: 798px;
  width: 100%;
  margin: 0 auto;
  overflow: visible;
}

.heading {
  font-size: 24px;
  font-weight: bold;
  margin-bottom: 14px;
}

.subheading {
  font-size: 16px;
  font-weight: bold;
  margin-bottom: 8px;
}

.list {
  margin: 0 24px;
  padding-bottom: 24px;
  list-style-type: disc;
}

.listItem {
  margin-bottom: 2px;
  font-size: 16px;
  padding-left: 8px;
}

.buttons {
  display: flex;
  gap: 16px;
  margin-top: 24px;
  margin-bottom: 16px;

  @media (max-width: 768px) {
    flex-direction: column;
    gap: 12px;
  }
}

.inlineFormTitle {
  font-family: 'Quasimoda';
  font-style: normal;
  font-weight: 700;
  font-size: 20px;
  line-height: 150%;
  color: #000000;
  margin-bottom: 24px;
  flex: none;
  order: 0;
  align-self: stretch;
  flex-grow: 0;
}

.inlineFormActions {
  display: flex;
  gap: 16px;
  margin-bottom: 32px;
}

.uploadingFiles {
  padding: 8px 16px 0 6px;
  overflow-x: auto;
  width: 100%;

  &::-webkit-scrollbar {
    display: none;
  }

  -ms-overflow-style: none;
  scrollbar-width: none;
}

.PersonalizationCard {
  margin-bottom: 24px;
}

.paragraph {
  font-size: 16px;
  margin-bottom: 16px;
  line-height: 1.5;
  color: #333;
}

.alerts {
  display: flex;
  flex-direction: column;
  gap: 12px;
  margin-top: 16px;
  margin-bottom: 16px;
}