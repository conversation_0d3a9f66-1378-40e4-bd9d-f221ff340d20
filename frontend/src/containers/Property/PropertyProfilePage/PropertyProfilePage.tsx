'use client';

import React, { useCallback, useMemo, useRef } from 'react';

import { IntegratedPropertyHeader } from '@/containers/Property/IntegratedPropertyHeader';
import { Button } from '@/components/Button';
import { ButtonColor, ButtonType } from '@/components/Button/Button.types';
import { FileUploadGrid } from '@/components/FileUploadManager';
import { HugeiconsIcon } from '@/components/HugeiconsIcon';

import { useFiles } from '@/containers/FilesPage/useFiles';
import { useAuth } from '@clerk/nextjs';
import { useModalForGuest } from '@/hooks/useModalGuest';

import { IconSvgObject } from '@hugeicons/react';
import { ArrowRight01Icon } from '@hugeicons-pro/core-stroke-standard';

import styles from './PropertyProfilePage.module.scss';
import { NotificationAlert } from '@/components/NotificationAlert/NotificationAlert';
import {
  DocumentCategoryType,
  InformationFromDocumentSavedNotification,
} from '@/api/notifications';
import Link from 'next/link';

interface CircularCountButtonProps {
  count: number;
}

const CircularCountButton: React.FC<CircularCountButtonProps> = ({ count }) => (
  <div style={{ display: count === 0 ? 'none' : 'flex' }}>
    <Button
      type={ButtonType.PRIMARY}
      color={ButtonColor.GREEN_PRIMARY}
      className={styles.circularCountButton}
    >
      {count}
    </Button>
  </div>
);

export const PropertyProfilePage: React.FC = () => {
  const fileInputRef = useRef<HTMLInputElement>(null);

  const refreshPropertyData = useCallback(async () => {
    // This will be called by IntegratedPropertyHeader when property data changes
  }, []);

  const { uploadFiles, uploadingFiles, deleteNotification, notifications } = useFiles({
    setUploadContext: 'propertyProfile',
    getUploadContext: null,
    onChanged: refreshPropertyData,
  });

  const firstErrorNotification = notifications.find((n) => n.severity === 'error');
  const firstInfoNotification = notifications.find((n) => n.severity === 'info');
  const firstWarningNotification = notifications.find((n) => n.severity === 'warning');

  const getCategoryUniqueDocumentCount = useCallback(
    (category: DocumentCategoryType) => {
      const filteredNotifications = notifications.filter(
        (n) => n.type === 'information_from_document_saved' && n.category === category
      ) as InformationFromDocumentSavedNotification[];
      const uniqueDocumentIds = new Set(filteredNotifications.map((n) => n.documentId));
      return uniqueDocumentIds.size;
    },
    [notifications]
  );

  const documentCategorizedNotifications = useMemo(
    () => notifications.filter((n) => n.type === 'document_categorized'),
    [notifications]
  );

  const { isSignedIn } = useAuth();
  const { openModal, closeModal } = useModalForGuest();

  const handleUploadDocument = () => {
    if (!isSignedIn) {
      openModal();
      return;
    }
    fileInputRef.current?.click();
  };

  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const selectedFiles = event.target.files;
    if (!selectedFiles || selectedFiles.length === 0) return;

    const fileArray = Array.from(selectedFiles);
    void uploadFiles(fileArray);

    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  const handleNavCardClick = (e: React.MouseEvent<HTMLAnchorElement>, href: string) => {
    if (!isSignedIn) {
      e.preventDefault();
      openModal();
      return;
    }
  };

  return (
    <div className={styles.container}>
      <h1 className={styles.heading}>Property Profile</h1>

      <div className={styles.content}>
        <IntegratedPropertyHeader onPropertyDataChange={refreshPropertyData} />
      </div>

      <div className={styles.content}>
        <h2 className={styles.subheading}>Build your Property Profile</h2>

        <div className={styles.uploadSection}>
          <div className={styles.uploadContent}>
            <div className={styles.houseIcon}>
              <img src="/House_8x.png" alt="House illustration" className={styles.houseImage} />
            </div>
            <div className={styles.uploadText}>
              <h3 className={styles.uploadTitle}>
                Upload documents for the quickest way to complete your Property Profile
              </h3>
              <p className={styles.uploadDescription}>
                Alfie can automatically complete your Property Profile based on your floor plan,
                survey report, insurance policy, utility bills, appliance receipts, and other
                documents. Upload files or sync with Google Drive to supercharge Alfie&apos;s
                personalised assistance to you
              </p>
              <Button
                type={ButtonType.PRIMARY}
                color={ButtonColor.GREEN_PRIMARY}
                onClick={handleUploadDocument}
              >
                Upload documents
              </Button>
            </div>
          </div>
        </div>

        <input
          type="file"
          ref={fileInputRef}
          onChange={handleFileChange}
          accept=".pdf,.png,.jpg,.jpeg,.webp,.gif,.heic,.heif"
          multiple
          style={{ display: 'none' }}
        />

        {uploadingFiles.length > 0 && (
          <>
            <h2 className="text-[16px] font-bold mt-[16px] mb-[8px]">Your document</h2>
            <div className={styles.uploadingFiles}>
              <FileUploadGrid files={uploadingFiles} showRemoveButton={false} />
            </div>
          </>
        )}

        {(firstErrorNotification || firstWarningNotification || firstInfoNotification) && (
          <div className={styles.alerts}>
            {firstErrorNotification && (
              <NotificationAlert
                notification={firstErrorNotification}
                onClose={deleteNotification}
              />
            )}
            {firstWarningNotification && (
              <NotificationAlert
                notification={firstWarningNotification}
                onClose={deleteNotification}
              />
            )}
            {firstInfoNotification && (
              <NotificationAlert
                notification={firstInfoNotification}
                onClose={deleteNotification}
              />
            )}
          </div>
        )}

        <div className={styles.navigationCards}>
          <Link
            href="/property-profile/property-details"
            className={styles.navigationCard}
            onClick={(e) => handleNavCardClick(e, '/property-profile/property-details')}
          >
            <div className={styles.cardContent}>
              <h3 className={styles.cardTitle}>Property Details</h3>
              <p className={styles.cardDescription}>
                Basic details including type, size, bedrooms, and bathrooms
              </p>
            </div>
            <div className={styles.cardIcon}>
              <CircularCountButton count={getCategoryUniqueDocumentCount('propertyDetails')} />
              <HugeiconsIcon icon={ArrowRight01Icon as unknown as IconSvgObject} size={20} />
            </div>
          </Link>

          <Link
            href="/property-profile/appliances"
            className={styles.navigationCard}
            onClick={(e) => handleNavCardClick(e, '/property-profile/appliances')}
          >
            <div className={styles.cardContent}>
              <h3 className={styles.cardTitle}>Appliances</h3>
              <p className={styles.cardDescription}>
                Heating, washing machine, kitchen, and other appliances
              </p>
            </div>
            <div className={styles.cardIcon}>
              <CircularCountButton count={getCategoryUniqueDocumentCount('appliance')} />
              <HugeiconsIcon icon={ArrowRight01Icon as unknown as IconSvgObject} size={20} />
            </div>
          </Link>

          <Link
            href="/property-profile/files"
            className={styles.navigationCard}
            onClick={(e) => handleNavCardClick(e, '/property-profile/files')}
          >
            <div className={styles.cardContent}>
              <h3 className={styles.cardTitle}>Files</h3>
              <p className={styles.cardDescription}>Index of all uploaded files</p>
            </div>
            <div className={styles.cardIcon}>
              <CircularCountButton count={documentCategorizedNotifications.length} />
              <HugeiconsIcon icon={ArrowRight01Icon as unknown as IconSvgObject} size={20} />
            </div>
          </Link>
        </div>
      </div>
    </div>
  );
};
