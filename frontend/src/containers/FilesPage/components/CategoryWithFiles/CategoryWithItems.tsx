import { DocumentPreview } from '@/components/DocumentPreview';
import { Typography } from '@/components/Typography/Typography';
import { format } from 'date-fns';
import { CategorizedFile } from '@/types/file';

interface ICategoryWithItemsProps {
  category: string;
  files: CategorizedFile[];
}

export const CategoryWithFiles = ({ category, files }: ICategoryWithItemsProps) => (
  <div key={category} className="mb-6">
    <Typography variant="body-s" font="quasimoda" className="mb-3 font-bold">
      {category}
    </Typography>
    <div className="flex flex-col gap-4">
      {files.map((doc) => (
        <DocumentPreview
          key={doc.id}
          description={doc.label}
          filename={doc.name}
          uploadedOn={doc.createdAt && format(new Date(doc.createdAt), 'dd/MM/yy')}
          thumbnailDocumentId={doc.documentId}
          isImage={doc.type === 'image'}
        />
      ))}
    </div>
  </div>
);
