import type { <PERSON>a, StoryObj } from '@storybook/react';

import { EmptyState } from './EmptyState';
import React from 'react';

const meta: Meta<typeof EmptyState> = {
  title: 'Components/EmptyState',
  component: EmptyState,
  parameters: {
    layout: 'centered',
  },
};

export default meta;
type Story = StoryObj<typeof EmptyState>;

export const Default: Story = {
  render: () => <EmptyState />,
};
