'use client';

import { But<PERSON> } from '@/components/Button';
import { ButtonType } from '@/components/Button/Button.types';
import { Files02Icon } from '@hugeicons-pro/core-stroke-rounded';
import { HugeiconsIcon } from '@/components/HugeiconsIcon';
import { IconSvgObject } from '@hugeicons/react';
import { Typography } from '@/components/Typography/Typography';
import styles from './EmptyState.module.scss';

interface IEmptyStateProps {
  onClick?: () => void;
}

export const EmptyState = ({ onClick }: IEmptyStateProps) => (
  <div className={styles.container}>
    <div className={styles.icon}>
      <HugeiconsIcon icon={Files02Icon as unknown as IconSvgObject} size={73} />
    </div>
    <Typography variant="body-s" font="quasimoda" className={styles.heading}>
      File Library
    </Typography>
    <Typography variant="body-s" font="quasimoda" className={styles.description}>
      All documents uploaded to <PERSON> <PERSON><PERSON> will show in your Files here. These documents contain
      information that helps <PERSON><PERSON> provide personalised property management for you.
    </Typography>
    <Button type={ButtonType.PRIMARY} onClick={onClick}>
      Upload documents
    </Button>
  </div>
);
