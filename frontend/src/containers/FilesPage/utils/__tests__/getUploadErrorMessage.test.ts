import { getUploadErrorMessage } from '../getUploadErrorMessage';

describe('getUploadErrorMessage', () => {
  const fileName = 'example.pdf';

  it('returns message for "size limit exceeded" in detail', () => {
    const error = {
      response: {
        data: {
          detail: 'Size limit exceeded',
        },
      },
    };
    expect(getUploadErrorMessage(error, fileName)).toBe(
      `'${fileName}' is too large. The maximum file size allowed is 20MB.`
    );
  });

  it('returns message for "unsupported" file type in detail', () => {
    const error = {
      response: {
        data: {
          detail: 'Unsupported file format',
        },
      },
    };
    expect(getUploadErrorMessage(error, fileName)).toBe(
      `'${fileName}' has an unsupported file format. Please use PDF, PNG, JPG, JPEG, WEBP, GIF, HEIC, or HEIF files.`
    );
  });

  it('returns generic detail error message if no match found', () => {
    const error = {
      response: {
        data: {
          detail: 'Something went wrong',
        },
      },
    };
    expect(getUploadErrorMessage(error, fileName)).toBe(
      `Failed to upload '${fileName}': Something went wrong`
    );
  });

  it('returns message for Network Error in message', () => {
    const error = { message: 'Network Error' };
    expect(getUploadErrorMessage(error, fileName)).toBe(
      `Network error while uploading '${fileName}'. Please check your connection and try again.`
    );
  });

  it('returns message for message containing "too large"', () => {
    const error = { message: 'File is too large' };
    expect(getUploadErrorMessage(error, fileName)).toBe(
      `'${fileName}' is too large. The maximum file size allowed is 20MB.`
    );
  });

  it('returns message for status 413', () => {
    const error = { status: 413 };
    expect(getUploadErrorMessage(error, fileName)).toBe(
      `'${fileName}' is too large. The maximum file size allowed is 20MB.`
    );
  });

  it('returns message for status 400', () => {
    const error = { status: 400 };
    expect(getUploadErrorMessage(error, fileName)).toBe(
      `'${fileName}' could not be uploaded due to a validation error. Please check the file and try again.`
    );
  });

  it('returns message for status 500', () => {
    const error = { status: 500 };
    expect(getUploadErrorMessage(error, fileName)).toBe(
      `Server error while uploading '${fileName}'. Please try again later.`
    );
  });

  it('returns default error message for unknown error object', () => {
    const error = { foo: 'bar' };
    expect(getUploadErrorMessage(error, fileName)).toBe(
      `Failed to upload '${fileName}'. Please try again later.`
    );
  });

  it('handles non-object error input', () => {
    expect(getUploadErrorMessage(null, fileName)).toBe(
      `Failed to upload '${fileName}'. Please try again later.`
    );
  });
});
