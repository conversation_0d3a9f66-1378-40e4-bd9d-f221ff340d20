import React from 'react';
import { render, screen } from '@testing-library/react';
import { vi } from 'vitest';
import { ChatPage } from '../ChatPage';

vi.mock('next/navigation', () => ({
  useParams: vi.fn(() => ({ chatId: 'test-chat-id' })),
  useRouter: vi.fn(() => ({
    push: vi.fn(),
    replace: vi.fn(),
    refresh: vi.fn(),
    back: vi.fn(),
    forward: vi.fn(),
  })),
  useSearchParams: vi.fn(() => ({
    get: vi.fn(() => null),
    has: vi.fn(() => false),
    getAll: vi.fn(() => []),
    keys: vi.fn(() => []),
    values: vi.fn(() => []),
    entries: vi.fn(() => []),
    forEach: vi.fn(),
    append: vi.fn(),
    delete: vi.fn(),
    set: vi.fn(),
    sort: vi.fn(),
    toString: vi.fn(() => ''),
  })),
  usePathname: vi.fn(() => '/'),
  redirect: vi.fn(),
  notFound: vi.fn(),
}));

vi.mock('@/components/Composer', () => ({
  Composer: ({ placeholder }: { placeholder: string }) => (
    <div data-testid="composer">
      <input placeholder={placeholder} />
    </div>
  ),
}));

vi.mock('@/components/Messages', () => ({
  Messages: () => (
    <div data-testid="messages">
      <div>Messages component</div>
    </div>
  ),
}));

vi.mock('@clerk/nextjs', () => ({
  SignUpButton: ({ children }: { children: React.ReactNode }) => (
    <div>{children}</div>
  ),
  SignInButton: ({ children }: { children: React.ReactNode }) => (
    <div>{children}</div>
  ),
  useAuth: () => ({
    isSignedIn: false,
    userId: null,
    getToken: () => Promise.resolve('mock-token'),
  }),
  useUser: () => ({
    user: null,
    isLoaded: true,
  }),
}));

vi.mock('@/components/Composer', () => ({
  Composer: () => (
    <div data-testid="composer">
      <input placeholder="Message Alfie" />
    </div>
  ),
}));

vi.mock('@/hooks/useMessages', () => ({
  useMessages: () => ({
    messages: [],
    isLoading: false,
    isSubmitting: false,
    hasMore: false,
    fetchMessages: vi.fn(async () => { }),
    loadMoreMessages: vi.fn(),
  }),
}));

vi.mock('@/hooks/useChats', () => ({
  useChats: () => ({
    isSubmitting: false,
    optimisticMessage: null,
    chats: [],
  }),
}));

vi.mock('@/hooks/useChatParams', () => ({
  default: () => ({
    chatId: 123,
    query: null,
  }),
}));

vi.mock('@/hooks/useMessageSender', () => ({
  default: () => ({
    sendMessage: vi.fn(),
  }),
}));

describe('ChatPage', () => {
  it('renders with chatId', () => {
    render(<ChatPage chatId={123} />);
    expect(screen.getByPlaceholderText('Message Alfie')).toBeInTheDocument();
  });
});
