'use client';

import React, { useEffect, useMemo, useRef } from 'react';
import { Composer } from '@/components/Composer';
import { Messages } from '@/components/Messages';
import { useMessages } from '@/hooks/useMessages';
import styles from './ChatPage.module.scss';
import { ChatPageProps } from './ChatPage.types';
import { useChats } from '@/hooks/useChats';
import classNames from 'classnames';
import useChatParams from '@/hooks/useChatParams';
import useMessageSender from '@/hooks/useMessageSender';
import { useUniversalAuth } from '@/hooks/useUniversalAuth';

export const ChatPage: React.FC<ChatPageProps> = ({ chatId }) => {
  const { isLoading, hasMore, fetchMessages, loadMoreMessages } = useMessages();
  const { isSubmitting, optimisticMessage, chats } = useChats();
  const anchorRef = useRef<HTMLDivElement>(null);
  const { getToken } = useUniversalAuth();
  const messages = useMemo(
    () => chats.find((chat) => chat.id === chatId)?.messages || [],
    [chats, chatId]
  );
  const isChatLocked = !!messages[0]?.additionalData?.jobSummary?.jobId;
  const { query } = useChatParams();
  const { sendMessage } = useMessageSender();

  useEffect(() => {
    if (chatId && messages.length === 0) {
      getToken().then((token) => {
        if (token) {
          fetchMessages(chatId, token).then(() => console.log('messages fetched'));
        }
      });
    }
  }, [chatId, fetchMessages, getToken, messages.length]);

  useEffect(() => {
    if (!chatId && query) {
      sendMessage(query).then(() => console.log('message sent'));
    }
  }, [chatId, query, sendMessage]);

  return (
    <div className={styles.chatContainer}>
      <Messages
        optimisticMessage={optimisticMessage}
        anchorRef={anchorRef}
        messages={messages}
        isLoading={isLoading}
        isSubmitting={isSubmitting}
        hasMore={hasMore}
        onLoadMore={async () => {
          const token = await getToken();
          if (token && chatId) {
            loadMoreMessages(chatId, token).then(() => console.log('more messages loaded'));
          }
        }}
      />
      <div
        className={classNames(styles.composerContainer, {
          [styles.locked]: isChatLocked,
        })}
      >
        <Composer anchorRef={anchorRef} isLocked={isChatLocked} />
      </div>
    </div>
  );
};
