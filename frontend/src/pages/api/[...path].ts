import { NextApiRequest, NextApiResponse } from 'next';
import httpProxyMiddleware from 'next-http-proxy-middleware';
import { API_BASE_URL } from '@/constants/api';

export const config = {
  api: {
    bodyParser: false,
  },
};

export default function handler(req: NextApiRequest, res: NextApiResponse) {
  return httpProxyMiddleware(req, res, {
    target: API_BASE_URL,
    changeOrigin: true,
    pathRewrite: {
      '^/api': '',
    },
  });
}
