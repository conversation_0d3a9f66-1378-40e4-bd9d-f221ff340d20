// Functions
@function spacing($key) {
  @return var(--spacing-#{$key});
}

@function color($color-name, $variant: '30') {
  @return var(--color-#{$color-name}-#{$variant});
}

@function theme-color($theme: 'default', $category, $variant) {
  @return var(--#{$theme}-#{$category}-#{$variant});
}

// Primitive Colors
$colors: (
  'yellow': (
    '30': #D28E28,
    '20': #E3A13E,
    '10': #F7BC64
  ),
  'purple': (
    '30': #23063A,
    '20': #63318B,
    '10': #743F9E
  ),
  'green': (
    '30': #0E372C,
    '20': #1B6E5A,
    '10': #3E9681
  ),
  'red': (
    '30': #35000D,
    '20': #991039,
    '10': #C11E4F
  ),
  'blue': (
    '30': #022034,
    '20': #21587E,
    '10': #31709B
  ),
  'dark': (
    '40': #342B25,
    '30': #4A403A,
    '20': #615853,
    '10': #756B66
  ),
  'neutral': (
    '30': #DFD8CB,
    '20': #F0E8DC,
    '10': #F8F3E9
  ),
  'pink': (
    '30': #831843,
    '20': #BE185D,
    '10': #EC4899
  ),
  'orange': (
    '30': #7C2D12,
    '20': #C2410C,
    '10': #FB923C
  ),
  'teal': (
    '30': #134E4A,
    '20': #0F766E,
    '10': #14B8A6
  ),
  'indigo': (
    '30': #312E81,
    '20': #4338CA,
    '10': #6366F1
  ),
  'base': (
    'black': #000000,
    'white': #FFFFFF
  ),
  'gray': (
    '300': #D1D5DB,
    '600': #4B5563
  )
);

// Update CSS variables generation to handle base colors differently
// e.g --color-yellow-30: #D28E28;
:root {
  @each $color-name, $shades in $colors {
    @if $color-name == 'base' {
      @each $shade, $value in $shades {
        --color-#{$shade}: #{$value};
      }
    } @else {
      @each $shade, $value in $shades {
        --color-#{$color-name}-#{$shade}: #{$value};
      }
    }
  }
}

// Semantic Colors
$semantic-colors: (
  'default': (
    'background': (
      'primary': var(--color-neutral-10),
      'secondary': var(--color-neutral-20),
      'tertiary': var(--color-neutral-30)
    ),
    'border': (
      'primary': var(--color-neutral-30),
      'secondary': var(--color-dark-30)
    ),
    'text': (
      'primary': var(--color-dark-40),
      'secondary': var(--color-dark-20),
      'tertiary': var(--color-dark-10),
      'link': var(--color-blue-20)
    ),
    'button': (
      'text': var(--color-neutral-10),
      'background': var(--color-green-20),
      'background-hover': var(--color-dark-40),
      'background-focus': var(--color-green-10),
      'link': var(--color-green-20)
    ),
    'system': (
      'error': var(--color-red-20),
      'success': var(--color-green-20),
      'disabled': var(--color-dark-20)
    )
  ),
  'yellow': (
    'background': (
      'primary': var(--color-yellow-30),
      'secondary': var(--color-yellow-10),
      'tertiary': var(--color-neutral-10)
    ),
    'border': (
      'primary': var(--color-dark-10),
      'secondary': var(--color-neutral-30)
    ),
    'text': (
      'primary': var(--color-dark-40),
      'secondary': var(--color-dark-30),
      'tertiary': var(--color-dark-20),
      'link': var(--color-blue-20)
    ),
    'button': (
      'text': var(--color-dark-40),
      'background': var(--color-neutral-10),
      'background-hover': var(--color-dark-40),
      'background-focus': var(--color-neutral-20),
      'link': var(--color-neutral-10)
    ),
    'system': (
      'error': var(--color-red-20),
      'success': var(--color-green-20),
      'disabled': var(--color-dark-20)
    )
  )
);

// Typography
$font-families: (
  'primary': ('Ryker', sans-serif),
  'heading': ('Quasimoda', sans-serif),
  'mono': ('Menlo', 'Monaco', 'Consolas', monospace),
);

$font-sizes: (
  // Headers - Desktop
  'h1-desktop': 3.35rem,    // 60px
  'h2-desktop': 3rem,       // 48px
  'h3-desktop': 2rem,       // 32px
  'h4-desktop': 1.125rem,   // 18px
  'h5-desktop': 1rem,       // 16px
  'h6-desktop': 0.75rem,    // 14px
  
  // Headers - Mobile
  'h1-mobile': 2.55rem,     // 36px
  'h2-mobile': 2.15rem,     // 30px
  'h3-mobile': 1.3rem,      // 24px
  'h4-mobile': 1rem,        // 16px
  'h5-mobile': 0.875rem,    // 14px
  'h6-mobile': 0.75rem,     // 12px
  
  // Body - Desktop
  'body-l': 1.25rem,        // 20px
  'body': 1.125rem,         // 18px
  'body-s': 1rem,           // 16px
  'body-xs': .875rem,       // 14px
  'body-xss': .75rem,       // 12px
  
  // Body - Mobile
  'body-l-mobile': 1.05rem, // 15px
  'body-mobile': 0.875rem,  // 14px
  'body-s-mobile': 0.75rem, // 12px
  
  // Button
  'button-desktop': 1.125rem,// 18px
  'button-mobile': 0.875rem  // 14px
);

// Line Heights
$line-heights: (
  'h1': 1.1em,    // 1.1em
  'h2': 1.2em,    // 1.2em
  'h3': 1.2em,    // 1.2em
  'h4': 1.4em,    // 1.4em
  'h5': 1.4em,    // 1.4em
  'h6': 1.4em,    // 1.4em
  'body': 1.5em,  // 1.5em
  'button': 1.3em // 1.3em
);

// Spacing
$spacing: (
  '0': 0,          // 0px
  'px': 0.0625rem, // 1px
  '0.5': 0.125rem, // 2px
  '1': 0.25rem,    // 4px
  '1.5': 0.375rem, // 6px
  '2': 0.5rem,     // 8px
  '2.5': 0.625rem, // 10px
  '3': 0.75rem,    // 12px
  '3.5': 0.875rem, // 14px
  '4': 1rem,       // 16px
  '5': 1.25rem,    // 20px
  '6': 1.5rem,     // 24px
  '7': 1.75rem,    // 28px
  '8': 2rem,       // 32px
  '9': 2.25rem,    // 36px
  '10': 2.5rem,    // 40px
  '11': 2.75rem,   // 44px
  '12': 3rem,      // 48px
  '14': 3.5rem,    // 56px
  '16': 4rem,      // 64px
  '20': 5rem,      // 80px
  '24': 6rem,      // 96px
  '28': 7rem,      // 112px
  '32': 8rem,      // 128px
  '36': 9rem,      // 144px
  '40': 10rem,     // 160px
  '44': 11rem,     // 176px
  '48': 12rem,     // 192px
  '52': 13rem,     // 208px
  '56': 14rem,     // 224px
  '60': 15rem,     // 240px
  '64': 16rem,     // 256px
  '72': 18rem,     // 288px
  '80': 20rem,     // 320px
  '96': 24rem,     // 384px
);

// Breakpoints
$breakpoints: (
  'sm': 640px,
  'md': 768px,
  'lg': 1024px,
  'xl': 1280px,
  '2xl': 1536px,
); 