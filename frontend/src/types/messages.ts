import { BaseFile } from './file';

export type ChatStatus = 'active' | 'closed';
export type MessageType = 'text' | 'image' | 'diagnostic_report';
export type SenderType = 'user' | 'system' | 'customer_support';

export interface SendMessageMetadata {
  device: string;
  location?: string;
}

export interface ResponseMessageMetadata {
  timestamp: Date;
  category?: string;
  confidence?: number;
}

export interface Attachment extends Omit<BaseFile, 'id'> {
  documentId: number;
  fileExtension?: string;
  originalFileName?: string;
  sizeInKiloBytes?: number;
  fileId?: string;
  cdnUrl?: string;
  category?: string;
}

export interface SendMessage {
  content: string;
  type: 'text';
  additionalData: SendMessageMetadata;
}

export interface ResponseMessage {
  content: string;
  type: MessageType;
  additionalData?: ResponseMessageMetadata;
}

export interface SendMessageRequest {
  chatId?: number;
  message: SendMessage;
  attachments?: Attachment[];
}

export interface SendMessageResponse {
  chatId: number;
  userMessageId: number;
  systemMessageId: number;
  message: ResponseMessage;
  attachments: Attachment[];
  additionalData?: MessageAdditionalData;
}

export interface ChatInfo {
  id: number;
  title: string;
  status: ChatStatus;
}

export interface ImageUrl {
  imageUrl: string;
  description: string;
  source: string;
}

export interface MessageAdditionalData {
  imageUrl?: string;
  category?: string;
  device?: string;
  location?: string;
  timestamp?: string;
  confidence?: number;
  suggestedActions?: {
    type: string;
    label: string;
    action: string;
  }[];
  imageClickableUrls?: ImageUrl[];
  imageUrls?: ImageUrl[];
  jobSummary?: {
    jobId: string;
    jobCategory: string;
    jobSubCategory: string;
    jobHeadline: string;
    jobDetails: string;
    jobDate: string;
    jobTimeOfDay: string;
    messageContainingJobSummary: string;
    errorDuringParsing: string;
  };
}

export interface Message {
  id: number;
  content: string;
  type: MessageTypeValue;
  senderType: SenderType;
  timestamp: string;
  attachments?: Attachment[];
  additionalData?: MessageAdditionalData;
  isOptimistic?: boolean;
  documents?: Array<{
    id: number;
    fileName: string;
    sizeInKiloBytes: number;
    browserMimeType: string;
    createdAt: string;
    uploadContext: string;
    status: string;
    category?: string;
    label?: string;
  }>;
}

export interface MessageResponse {
  Chatid: number;
  UserMessageId: number;
  SystemMessageId: number;
  message: Message;
  attachments: Attachment[];
}

export interface Chat extends ChatInfo {
  messages: Message[];
  propertyId?: number;
  userId: number;
  created_at: Date;
  updated_at: Date;
}

export interface MessageListResponse {
  items: Message[];
  total: number;
  page: number;
  size: number;
  pages: number;
}

export interface ChatListResponse {
  items: ChatInfo[];
  total: number;
  page: number;
  size: number;
  pages: number;
}

export interface MessageService {
  sendMessage(request: SendMessageRequest): Promise<SendMessageResponse>;
  getMessages(chatId: number, page?: number): Promise<MessageListResponse>;
  getChats(page?: number): Promise<ChatListResponse>;
}

export enum MessageTypeValue {
  Text = 'text',
  Image = 'image',
}
