export interface SendMessageRequest {
  chatId: number;
  message: {
    content: string;
    type: 'text';
    additionalData: {
      device: string;
      location?: string;
    }
  };
  attachments?: Array<{
    documentId: number;
  }>;
}

export interface SendMessageResponse {
  chatId: number;
  userMessageId: number;
  systemMessageId: number;
  message: {
    content: string;
    type: string;
    additionalData: {
      timestamp: string;
      category?: string;
      confidence?: number;
    }
  };
  attachments: Array<{
    documentId: number;
  }>;
} 