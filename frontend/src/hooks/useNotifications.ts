import {
  Context,
  deleteAllNotifications, deleteNotificationById,
  deleteNotificationsByContext,
  DocumentCategoryType,
  fetchAllNotifications,
  fetchNotificationsByContext,
  NotificationInfo,
  NotificationPayload,
} from '@/api/notifications';
import { useAuth } from '@clerk/nextjs';
import { useCallback, useState } from 'react';

interface UseNotificationsOptions {
  context: Context | null;
}

export type NotificationSeverity = 'info' | 'warning' | 'error';

export type AlfieNotification = NotificationPayload & {
  id: string;
  systemId: number | null;
  severity: NotificationSeverity;
  title: string;
  message: string;
};

const documentCategoryTypeToName = (type: DocumentCategoryType) => {
  switch (type) {
    case 'propertyDetails':
      return 'Property details';
    case 'insurance':
      return 'Insurances';
    case 'billsAndSubscriptions':
      return 'Bills and subscriptions';
    case 'legal':
      return 'Legal';
    case 'appliance':
      return 'Appliances';
  }
};

const notificationInfoToNotification = (notification: NotificationInfo): AlfieNotification => {
  const id = `__${notification.id}`;
  switch (notification.payload.type) {
    case 'irrelevant_document':
      return {
        ...notification.payload,
        id,
        systemId: notification.id,
        type: 'irrelevant_document',
        severity: 'warning',
        title: 'No information saved to your Property Profile',
        message: `Alfie could not find any relevant property information from '${notification.payload.fileName}' and has not saved the file on your Property Profile.`,
      };
    case 'document_error':
      return {
        ...notification.payload,
        id,
        systemId: notification.id,
        type: 'document_error',
        severity: 'error',
        title: 'Upload failed',
        message: `Failed to process '${notification.payload.fileName}'. Please try again.`,
      };
    case 'information_from_document_saved':
      const name =
        notification.payload.category && documentCategoryTypeToName(notification.payload.category);
      return {
        ...notification.payload,
        id,
        systemId: notification.id,
        type: 'information_from_document_saved',
        severity: 'info',
        title: name ? `Information saved to ${name}` : `Information extracted from file`,
        message:
          `Alfie processed ${notification.payload.fileName}` +
          (name ? ` and saved relevant information to your ${name}.` : ' and saved it to your files.'),
      };
    case 'document_categorized':
      return {
        ...notification.payload,
        id,
        type: 'document_categorized',
        severity: 'info',
        systemId: notification.id,
        title: `Document processed and saved`,
        message: `Alfie processed ${notification.payload.fileName} and saved it to your files.`,
      };
    default:
      throw new Error(`Invalid notification payload type: ${JSON.stringify(notification.payload)}`);
  }
};

export const useNotifications = (options: UseNotificationsOptions) => {
  const { getToken } = useAuth();
  const { context } = options;

  const [notifications, setNotifications] = useState<AlfieNotification[]>([]);

  const fetchNotifications = useCallback(async () => {
    const token = await getToken();
    if (!token) return;

    try {
      const response = await (context
        ? fetchNotificationsByContext(token, context)
        : fetchAllNotifications(token));
      const notifications = response.items
        .sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime())
        .map(notificationInfoToNotification);
      setNotifications((prev) => [...prev.filter((n) => n.systemId === null), ...notifications]);
    } catch (error) {
      console.error('Failed to fetch notifications:', error);
    }
  }, [getToken, context]);

  const deleteNotifications = useCallback(async () => {
    const token = await getToken();
    if (!token) return;
    try {
      setNotifications([]);
      await (context
        ? deleteNotificationsByContext(token, context)
        : deleteAllNotifications(token));
    } catch (error) {
      console.error(`Failed to dismiss documents notifications for context "${context}"`, error);
    }
  }, [context, getToken]);

  const deleteNotification = useCallback(
    async (id: string) => {
      const token = await getToken();
      if (!token) return;
      try {
        setNotifications((prev) => prev.filter((n) => n.id !== id));
        const systemId = notifications.find((n) => n.id === id)?.systemId;
        if (!systemId) return;
        await deleteNotificationById(token, systemId);
      } catch (error) {
        console.error(
          `Failed to dismiss documents notifications for system id "${screenY}"`,
          error
        );
      }
    },
    [getToken, notifications]
  );

  const pushNotification = useCallback((notification: AlfieNotification) => {
    setNotifications((prev) => [...prev, notification]);
  }, []);

  return {
    notifications,
    deleteNotifications,
    deleteNotification,
    pushNotification,
    fetchNotifications,
  };
};
