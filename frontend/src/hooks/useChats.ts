import { create } from 'zustand';
import { fetchChats, sendMessage } from '@/api/chats';
import { Attachment, Message, MessageTypeValue, SendMessageResponse } from '@/types/messages';
import { Chat } from '@/types/chats';
import { useStreamingState } from './useStreamingState';

interface MessageDocument {
  id: number;
  fileName: string;
  sizeInKiloBytes: number;
  browserMimeType: string;
  createdAt: string;
  uploadContext: string;
  status: string;
  category?: string;
  label?: string;
}

interface OptimisticMessage {
  content: string;
  attachments?: Attachment[];
}

interface ChatState {
  chats: Chat[];
  isLoading: boolean;
  isSubmitting: boolean;
  hasMore: boolean;
  currentPage: number;
  optimisticMessage: OptimisticMessage | null;
  jobSummaryConfirmed: boolean;
  isRetryButtonShown: boolean;
  isMessagesSpacingActive: boolean;
  isStreamingMessage: boolean;
  fetchChats: (token: string) => Promise<void>;
  loadMoreChats: (token: string) => Promise<void>;
  addChat: (newChat: Chat) => void;
  sendMessage: (
    chatId: number | undefined,
    content: string,
    token: string,
    attachments?: Attachment[],
    onChatId?: (chatId: number) => void
  ) => Promise<void>;
  appendMessages: (messages: Message[], chatId: number) => void;
  replaceMessages: (messages: Message[], chatId: number) => void;
  updateMessageDocuments: (
    chatId: number,
    messageId: number,
    updatedDocuments: MessageDocument[]
  ) => void;
  setJobSummaryConfirmed: (confirmed: boolean) => void;
  setIsMessagesSpacingActive: (active: boolean) => void;
  setIsStreamingMessage: (streaming: boolean) => void;
}

function mergeMessages(
  current: Message[],
  systemResponse: SendMessageResponse,
  userMessage: { content: string; attachments: Attachment[] }
): Message[] {
  const documentsFromAttachments = userMessage.attachments.map((attachment) => ({
    id: attachment.documentId,
    fileName: attachment.originalFileName || attachment.name,
    sizeInKiloBytes: attachment.sizeInKiloBytes || 0,
    browserMimeType: attachment.type,
    createdAt: attachment.createdAt || new Date().toISOString(),
    uploadContext: 'chat',
    status: 'saved',
    category: attachment.category || undefined,
    label: undefined,
  }));

  return [
    {
      content: systemResponse.message.content,
      attachments: systemResponse.attachments,
      id: systemResponse.systemMessageId,
      senderType: 'system',
      timestamp: new Date().toISOString(),
      type: MessageTypeValue.Text,
      additionalData: systemResponse.additionalData,
    },
    {
      id: systemResponse.userMessageId,
      content: userMessage.content,
      attachments: userMessage.attachments,
      documents: documentsFromAttachments,
      type: MessageTypeValue.Text,
      timestamp: new Date().toISOString(),
      senderType: 'user',
    },
    ...current.filter(
      (m) => m.id !== systemResponse.systemMessageId && m.id !== systemResponse.userMessageId
    ),
  ];
}

export const useChats = create<ChatState>((set, get) => ({
  chats: [],
  isLoading: false,
  isSubmitting: false,
  hasMore: true,
  currentPage: 1,
  optimisticMessage: null,
  jobSummaryConfirmed: false,
  isRetryButtonShown: false,
  isMessagesSpacingActive: false,
  isStreamingMessage: false,

  setJobSummaryConfirmed: (confirmed: boolean) => {
    set({ jobSummaryConfirmed: confirmed });
  },

  fetchChats: async (token: string) => {
    set({ isLoading: true });
    try {
      const response = await fetchChats({ token });
      set({
        chats: response.items,
        hasMore: response.pages > 1,
        currentPage: 1,
      });
    } catch (error) {
      console.error('Failed to fetch chats:', error);
      set({ chats: [] });
    } finally {
      set({ isLoading: false });
    }
  },

  loadMoreChats: async (token: string) => {
    const { currentPage, hasMore, isLoading } = get();
    if (!hasMore || isLoading) return;

    set({ isLoading: true });
    try {
      const response = await fetchChats({
        token,
        page: currentPage + 1,
      });

      set((state) => ({
        chats: [...state.chats, ...response.items],
        hasMore: response.page < response.pages,
        currentPage: currentPage + 1,
      }));
    } catch (error) {
      console.error('Failed to load more chats:', error);
    } finally {
      set({ isLoading: false });
    }
  },

  addChat: (newChat: Chat) => {
    set((state) => ({
      chats: [...state.chats, newChat],
    }));
  },

  appendMessages: (messages, chatId) => {
    set((state) => ({
      chats: state.chats.map((chat) =>
        chat.id === chatId
          ? {
              ...chat,
              messages: [...(chat.messages || []), ...messages],
            }
          : chat
      ),
    }));
  },

  replaceMessages: (messages, chatId) => {
    set((state) => ({
      chats: state.chats.some((chat) => chat.id === chatId)
        ? state.chats.map((chat) =>
            chat.id === chatId
              ? {
                  ...chat,
                  messages,
                }
              : chat
          )
        : [
            ...state.chats,
            {
              id: chatId,
              messages,
              title: '',
              status: '',
            },
          ],
    }));
  },

  sendMessage: async (
    chatId: number | undefined,
    content: string,
    token: string,
    attachments: Attachment[] = [],
    onChatId?: (chatId: number) => void
  ) => {
    set({
      isSubmitting: true,
      isRetryButtonShown: false,
    });
    try {
      set({
        optimisticMessage: {
          content,
          attachments,
        },
        isMessagesSpacingActive: true,
      });

      let responsesReceived = 0;
      await sendMessage({
        chatId,
        message: {
          content,
          type: 'text',
          additionalData: {
            device: navigator.userAgent,
            location: window.location.href,
          },
        },
        attachments: attachments.map((attachment) => ({
          documentId: attachment.documentId,
        })),
        token,
        onStreamStart: () => {
          useStreamingState.getState().setIsStreamingMessage(true);
        },
        onStreamEnd: () => {
          useStreamingState.getState().setIsStreamingMessage(false);
        },
        onError: () => {
          set(() => ({ isRetryButtonShown: true }));
          useStreamingState.getState().setIsStreamingMessage(false);
        },
        onResponse: async (res) => {
          const isFirstResponse = responsesReceived++ === 0;
          const chatId = res.chatId;
          isFirstResponse && onChatId && onChatId(chatId);
          set((state) => ({
            isSubmitting: false,
            chats: state.chats.some((c) => c.id === chatId)
              ? state.chats.map((c) =>
                  c.id === chatId
                    ? {
                        ...c,
                        messages: mergeMessages(c.messages || [], res, {
                          content,
                          attachments,
                        }),
                      }
                    : c
                )
              : [
                  ...state.chats,
                  {
                    id: chatId,
                    messages: mergeMessages([], res, {
                      content,
                      attachments,
                    }),
                    title: '',
                    status: '',
                  },
                ],
          }));

          if (isFirstResponse && chatId) {
            set((state) => {
              const existingChatIndex = state.chats.findIndex((c) => c.id === chatId);
              if (existingChatIndex >= 0) {
                const updatedChats = [...state.chats];
                const [existingChat] = updatedChats.splice(existingChatIndex, 1);
                existingChat.title = content.slice(0, 50);
                return { chats: [existingChat, ...updatedChats] };
              } else {
                const newChat = {
                  id: chatId,
                  title: content.slice(0, 50),
                  messages: [],
                  status: 'active',
                };
                return { chats: [newChat, ...state.chats] };
              }
            });

            setTimeout(async () => {
              try {
                const chatsListResponse = await fetchChats({
                  token,
                  page: 1,
                  size: 5,
                });
                const updatedChat = chatsListResponse.items.find((c) => c.id === chatId);

                if (updatedChat) {
                  set((state) => {
                    const existingChatIndex = state.chats.findIndex((c) => c.id === chatId);
                    if (existingChatIndex >= 0) {
                      const updatedChats = [...state.chats];
                      updatedChats[existingChatIndex] = {
                        ...updatedChats[existingChatIndex],
                        title: updatedChat.title,
                      };
                      return { chats: updatedChats };
                    }
                    return state;
                  });
                }
              } catch (error) {
                console.error('Failed to update chat title:', error);
              }
            }, 1000);
          }
        },
      });
    } catch (error) {
      console.error('Failed to send message:', error);
      set({ isRetryButtonShown: true });
    }
  },

  setIsMessagesSpacingActive: (isMessagesSpacingActive: boolean) => {
    set({ isMessagesSpacingActive });
  },

  setIsStreamingMessage: (isStreamingMessage: boolean) => {
    set({ isStreamingMessage });
  },

  updateMessageDocuments: (
    chatId: number,
    messageId: number,
    updatedDocuments: MessageDocument[]
  ) => {
    set((state) => ({
      chats: state.chats.map((chat) =>
        chat.id === chatId
          ? {
              ...chat,
              messages: (chat.messages || []).map((message) =>
                message.id === messageId
                  ? {
                      ...message,
                      documents: updatedDocuments,
                    }
                  : message
              ),
            }
          : chat
      ),
    }));
  },
}));
