import { useState, useEffect, useCallback, useMemo } from 'react';
import { isEmpty, validateFieldValue } from '@/utils/formValidationUtils';

export interface EditableField {
  id: string;
  value: string | number;
  type?: string;
  editable?: boolean;
}

export interface UseEditableFieldsOptions<T extends EditableField> {
  initialFields: T[];
  initialTitle?: string;
  isEditingProp?: boolean;
  onSave?: (fields: T[], title?: string) => void;
  onCancel?: () => void;
  onEdit?: () => void;
}

export interface UseEditableFieldsReturn<T extends EditableField> {
  fields: T[];
  title: string;
  isEditing: boolean;
  userHasStartedEditing: boolean;
  isAllFieldsFilled: boolean;
  hasEmptyFields: boolean;
  handleEdit: () => void;
  handleSave: () => void;
  handleCancel: () => void;
  handleFieldChange: (id: string, value: string | number) => void;
  handleTitleChange: (title: string) => void;
  resetEditingState: () => void;
}

export const useEditableFields = <T extends EditableField>({
  initialFields,
  initialTitle = '',
  isEditingProp,
  onSave,
  onCancel,
  onEdit,
}: UseEditableFieldsOptions<T>): UseEditableFieldsReturn<T> => {
  const [fields, setFields] = useState<T[]>(initialFields);
  const [title, setTitle] = useState(initialTitle);
  const [userHasStartedEditing, setUserHasStartedEditing] = useState(false);
  const [isEditingState, setIsEditingState] = useState(false);

  const isEditing = isEditingProp !== undefined ? isEditingProp : isEditingState;

  const isAllFieldsFilled = useMemo(() => {
    if (isEmpty(title)) {
      return false;
    }

    const hasChanges = title !== initialTitle ||
      fields.some((field) => {
        const initialField = initialFields.find(f => f.id === field.id);
        return initialField && String(field.value) !== String(initialField.value);
      });

    const hasExistingData = !isEmpty(initialTitle) || initialFields.some(field => !isEmpty(field.value));

    if (!hasChanges && (userHasStartedEditing || (isEditing && hasExistingData))) {
      return false;
    }

    return true;
  }, [title, initialTitle, fields, initialFields, userHasStartedEditing, isEditing]);

  const hasEmptyFields = useMemo(() => {
    if (!fields.length) return false;
    return fields.filter((field) => field.editable !== false).some((field) => isEmpty(field.value));
  }, [fields]);

  useEffect(() => {
    if (!isEditing) {
      setFields(initialFields);
    }
  }, [initialFields, isEditing]);

  useEffect(() => {
    setTitle(initialTitle);
  }, [initialTitle]);

  const resetEditingState = useCallback(() => {
    setUserHasStartedEditing(false);
    if (isEditingProp === undefined) {
      setIsEditingState(false);
    }
  }, [isEditingProp]);

  const handleEdit = useCallback(() => {
    if (onEdit) {
      onEdit();
    } else {
      setIsEditingState(true);
    }
  }, [onEdit]);

  const handleFieldChange = useCallback(
    (id: string, value: string | number) => {
      if (!userHasStartedEditing) {
        setUserHasStartedEditing(true);
      }

      if (!isEditing && isEditingProp === undefined) {
        setIsEditingState(true);
      }

      setFields((prevFields) =>
        prevFields.map((field) => {
          if (field.id === id) {
            const validatedValue = validateFieldValue(field.type || 'text', value);
            return { ...field, value: validatedValue } as T;
          }
          return field;
        })
      );
    },
    [isEditing, isEditingProp, userHasStartedEditing]
  );

  const handleTitleChange = useCallback(
    (newTitle: string) => {
      setTitle(newTitle);
      if (!userHasStartedEditing) {
        setUserHasStartedEditing(true);
      }
    },
    [userHasStartedEditing]
  );

  const handleSave = useCallback(() => {
    if (isEmpty(title)) {
      return;
    }

    onSave?.(fields, title);
    resetEditingState();
  }, [fields, title, onSave, resetEditingState]);

  const handleCancel = useCallback(() => {
    setFields(initialFields);
    setTitle(initialTitle);
    onCancel?.();
    resetEditingState();
  }, [initialFields, onCancel, initialTitle, resetEditingState]);

  return {
    fields,
    title,
    isEditing,
    userHasStartedEditing,
    isAllFieldsFilled,
    hasEmptyFields,
    handleEdit,
    handleSave,
    handleCancel,
    handleFieldChange,
    handleTitleChange,
    resetEditingState,
  };
};
