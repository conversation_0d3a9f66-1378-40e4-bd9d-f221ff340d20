import { useRouter } from 'next/navigation';
import useChatParams from '@/hooks/useChatParams';
import { useChats } from './useChats';
import { Attachment } from '@/types/messages';
import { useCallback } from 'react';
import { useUniversalAuth } from './useUniversalAuth';

export default function useMessageSender() {
  const { getToken: getAuthToken } = useUniversalAuth();
  const router = useRouter();
  const { chatId } = useChatParams();
  const { sendMessage } = useChats();
  const wrappedSendMessage = useCallback(
    async (value: string, attachments?: Attachment[]): Promise<boolean> => {
      try {
        const token = await getAuthToken();

        if (token) {
          await sendMessage(chatId, value, token, attachments, (newChatId) => {
            router.replace(`/chats/${newChatId}`);
          });
          return true;
        }
      } catch (error) {
        console.error('Failed to send message:', error);
      }
      return false;
    },
    [chatId, getAuthToken, router, sendMessage]
  );

  return {
    sendMessage: wrappedSendMessage,
  };
}
