import { create } from 'zustand';
import { useAuth } from '@clerk/nextjs';
import {
  createProperty,
  mapRoleToUserPropertyRelationType,
  Property, PropertyCreate, PropertyUpdate,
  updateProperty,
} from '@/api/properties';
import { AddressFields } from '@/components/MultiFieldInput/MultiFieldInput.types';

export interface PropertyDetailsData {
  address: string | AddressFields;
  role: string;
  propertyType?: string;
  ownershipType?: string;
}

interface PropertyDetailsState {
  property: Property | null;
  isLoading: boolean;
  isCreating: boolean;
  isSaving: boolean;
  error: string | null;

  createPropertyFromDetails: (data: PropertyDetailsData, token: string) => Promise<Property>;
  updatePropertyFromDetails: (
    propertyId: number,
    data: PropertyDetailsData,
    token: string,
  ) => Promise<Property>;
  reset: () => void;
}

const toCreateRequest = (data: PropertyDetailsData): PropertyCreate => {
  const userPropertyRelationshipType = data.role
    ? mapRoleToUserPropertyRelationType(data.role)
    : undefined;
  if (typeof data.address === 'string') {
    return {
      idealPostcodesAddressId: data.address,
      userPropertyRelationshipType,
    };
  } else {
    return {
      manualAddress: {
        streetLine1: data.address.line1 || '',
        streetLine2: data.address.line2 || null,
        townOrCity: data.address.city || '',
        postcode: data.address.postcode || '',
      },
      userPropertyRelationshipType,
    };
  }
};

const toUpdateRequest = (data: PropertyDetailsData): PropertyUpdate => {
  const userPropertyRelationshipType = data.role
    ? mapRoleToUserPropertyRelationType(data.role)
    : undefined;
  if (typeof data.address === 'string') {
    return {
      idealPostcodesAddressId: data.address,
      userPropertyRelationshipType,
    };
  } else {
    return {
      manualAddress: data.address
        ? {
          streetLine1: data.address.line1 || '',
          streetLine2: data.address.line2 || null,
          townOrCity: data.address.city || '',
          postcode: data.address.postcode || '',
        }
        : undefined,
      userPropertyRelationshipType,
    };
  }
};

export const usePropertyDetails = create<PropertyDetailsState>((set) => ({
  property: null,
  isLoading: false,
  isCreating: false,
  isSaving: false,
  error: null,

  createPropertyFromDetails: async (data: PropertyDetailsData, token: string) => {
    set({ isCreating: true, error: null });

    try {
      const createdProperty = await createProperty({ request: toCreateRequest(data), token });
      set({ property: createdProperty, isCreating: false });
      return createdProperty;
    } catch (error) {
      set({
        error: error instanceof Error ? error.message : 'Failed to create property',
        isCreating: false,
      });
      throw error;
    }
  },

  updatePropertyFromDetails: async (
    propertyId: number,
    data: PropertyDetailsData,
    token: string,
  ) => {
    set({ isCreating: true, error: null });

    try {
      const updatedProperty = await updateProperty({
        propertyId,
        request: toUpdateRequest(data),
        token,
      });

      set({ property: updatedProperty, isCreating: false });
      return updatedProperty;
    } catch (error) {
      set({
        error: error instanceof Error ? error.message : 'Failed to update property',
        isCreating: false,
      });
      throw error;
    }
  },

  reset: () => {
    set({
      property: null,
      isLoading: false,
      isCreating: false,
      isSaving: false,
      error: null,
    });
  },
}));

export const usePropertyDetailsActions = () => {
  const { getToken } = useAuth();
  const { createPropertyFromDetails, updatePropertyFromDetails, reset } = usePropertyDetails();

  const handleCreateProperty = async (data: PropertyDetailsData) => {
    const token = await getToken();
    if (!token) {
      throw new Error('Authentication token not available');
    }

    return await createPropertyFromDetails(data, token);
  };

  const handleUpdateProperty = async (propertyId: number, data: PropertyDetailsData) => {
    const token = await getToken();
    if (!token) {
      throw new Error('Authentication token not available');
    }

    return await updatePropertyFromDetails(propertyId, data, token);
  };

  return {
    createProperty: handleCreateProperty,
    updateProperty: handleUpdateProperty,
    reset,
  };
};
