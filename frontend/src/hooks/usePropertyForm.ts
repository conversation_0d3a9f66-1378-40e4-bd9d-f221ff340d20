import { useState } from 'react';
import { PropertyFormData, YesNoOption } from '@/components/PropertyForm/PropertyForm.types';

export const usePropertyForm = (initialData: Partial<PropertyFormData> = {}) => {
  const [formData, setFormData] = useState<PropertyFormData>({
    userRole: initialData.userRole || '',
    ownershipType: initialData.ownershipType || '',
    propertyType: initialData.propertyType || '',
    propertySubType: initialData.propertySubType || '',
    numberOfBedrooms: initialData.numberOfBedrooms || '',
    numberOfBathrooms: initialData.numberOfBathrooms || '',
    numberOfFloors: initialData.numberOfFloors || '',
    floorPropertyIsOn: initialData.floorPropertyIsOn || '',
    grossInternalArea: initialData.grossInternalArea || '',
    balconyTerrace: initialData.balconyTerrace || '',
    garden: initialData.garden || '',
    swimmingPool: initialData.swimmingPool || '',
    documents: initialData.documents || [],
  });

  const handleInputChange = (field: keyof PropertyFormData, value: string) => {
    setFormData((prev) => ({
      ...prev,
      [field]: value,
    }));
  };

  const handleRadioChange = (field: keyof PropertyFormData, value: YesNoOption) => {
    setFormData((prev) => ({
      ...prev,
      [field]: value,
    }));
  };

  const handleDropdownSelect = (field: keyof PropertyFormData, value: string) => {
    if (field === 'propertyType') {
      const wasHouse = formData.propertyType === 'House';
      const isNowHouse = value === 'House';

      setFormData((prev) => {
        const updated: PropertyFormData = {
          ...prev,
          [field]: value as PropertyFormData[typeof field]
        };

        if (wasHouse && !isNowHouse) {
          updated.propertySubType = '';
          updated.numberOfFloors = '';
        } else if (!wasHouse && isNowHouse) {
          updated.floorPropertyIsOn = '';
        }

        return updated;
      });
    } else {
      setFormData((prev) => ({
        ...prev,
        [field]: value,
      }));
    }
  };

  const isFormComplete = () => {
    const isHouse = formData.propertyType === 'House';
    const baseFieldsComplete =
      formData.ownershipType !== '' &&
      formData.propertyType !== '' &&
      formData.numberOfBedrooms !== '' &&
      formData.numberOfBathrooms !== '' &&
      formData.grossInternalArea !== '' &&
      formData.balconyTerrace !== '' &&
      formData.balconyTerrace !== undefined &&
      formData.garden !== '' &&
      formData.garden !== undefined &&
      formData.swimmingPool !== '' &&
      formData.swimmingPool !== undefined;

    if (isHouse) {
      return baseFieldsComplete &&
        formData.propertySubType !== '' &&
        formData.numberOfFloors !== '';
    } else if (formData.propertyType !== '') {
      return baseFieldsComplete &&
        formData.floorPropertyIsOn !== '';
    }

    return baseFieldsComplete;
  };

  return {
    formData,
    handleInputChange,
    handleRadioChange,
    handleDropdownSelect,
    isFormComplete,
  };
};
