import useSWRMutation from 'swr/mutation';
import { API_ENDPOINTS, getApiUrl } from '@/constants/api';
import { uploadDocument } from '@/api/documents';
import { useCallback } from 'react';
import { Context } from '@/api/notifications';

export interface UploadError {
  message: string;
  status?: number;
  details?: string;
}

// Maximum file size in MB
const MAX_FILE_SIZE = 100;


export function useSWRDocumentUpload(uploadContext: Context) {
  const uploadFetcher = useCallback(async (url: string, { arg }: { arg: { formData: FormData; token: string } }) => {
    const { formData, token } = arg;

    const file = formData.get('file') as File;
    if (!file) {
      throw new Error('No file provided');
    }

    const maxSizeInBytes = MAX_FILE_SIZE * 1024 * 1024;
    if (file.size > maxSizeInBytes) {
      throw new Error(`File too large. Maximum size is ${MAX_FILE_SIZE}MB.`);
    }

    return await uploadDocument(file, token, uploadContext);
  }, [uploadContext]);

  const { trigger, isMutating, error } = useSWRMutation(
    getApiUrl(`${API_ENDPOINTS.DOCUMENTS}/`),
    uploadFetcher,
  );

  return {
    uploadDocument: trigger,
    isUploading: isMutating,
    error: error as UploadError | null,
  };
}
