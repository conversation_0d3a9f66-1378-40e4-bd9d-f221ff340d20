import { create } from 'zustand';
import { fetchMessages } from '@/api/chats';
import { useChats } from './useChats';

export interface MessageState {
  isLoading: boolean;
  hasMore: boolean;
  currentPage: number;
  jobSummaryConfirmed: boolean;
  messageHeight: { optimistic?: number; lastSystem?: number };
  fetchMessages: (chatId: number, token: string, page?: number) => Promise<void>;
  loadMoreMessages: (chatId: number, token: string) => Promise<void>;
  setJobSummaryConfirmed: (confirmed: boolean) => void;
  setMessageHeight: (height: { optimistic?: number; lastSystem?: number }) => void;
}

export const useMessages = create<MessageState>((set, get) => ({
  isLoading: false,
  hasMore: true,
  currentPage: 1,
  jobSummaryConfirmed: false,
  messageHeight: { optimistic: 0, lastSystem: 0 },
  fetchMessages: async (chatId, token, page = 1) => {
    if (!chatId) return;
    set({ isLoading: true });
    try {
      const response = await fetchMessages({
        chatId,
        page,
        token,
      });
      useChats.getState().replaceMessages(response.items, chatId);
      set({
        hasMore: response.pages > 1,
        currentPage: 1,
      });
    } catch (error) {
      console.error('Failed to fetch messages:', error);
    } finally {
      set({ isLoading: false });
    }
  },

  loadMoreMessages: async (chatId, token) => {
    const { currentPage, hasMore, isLoading } = get();
    if (!hasMore || isLoading) return;

    set({ isLoading: true });
    try {
      const response = await fetchMessages({
        chatId,
        page: currentPage + 1,
        token,
      });
      useChats.getState().appendMessages(response.items, chatId);
      set({
        hasMore: response.page < response.pages,
        currentPage: currentPage + 1,
      });
    } catch (error) {
      console.error('Failed to load more messages:', error);
    } finally {
      set({ isLoading: false });
    }
  },

  setJobSummaryConfirmed: (confirmed: boolean) => {
    set({ jobSummaryConfirmed: confirmed });
  },

  setMessageHeight: (height: { optimistic?: number; lastSystem?: number }) => {
    set((state) => {
      if (
        state.messageHeight?.optimistic !== height.optimistic ||
        state.messageHeight?.lastSystem !== height.lastSystem
      ) {
        return { messageHeight: { ...state.messageHeight, ...height } };
      }
      return state;
    });
  },
}));
