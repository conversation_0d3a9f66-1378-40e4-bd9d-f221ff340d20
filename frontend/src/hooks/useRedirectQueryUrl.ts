import { create } from 'zustand';
import { persist } from 'zustand/middleware';

interface RedirectQueryUrlState {
  redirectUrl: string | null;
  setRedirectUrl: (url: string) => void;
  clearRedirectUrl: () => void;
}

export const useRedirectQueryUrl = create<RedirectQueryUrlState>()(
  persist(
    (set, get) => ({
      redirectUrl: null,

      setRedirectUrl: (url: string) => {
        set({ redirectUrl: url });
      },

      clearRedirectUrl: () => {
        set({ redirectUrl: null });
      },
    }),
    {
      name: 'redirect-query-url-storage',
      skipHydration: false,
    }
  )
);
