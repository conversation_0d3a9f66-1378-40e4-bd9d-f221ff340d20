import { useMemo } from 'react';
import { useUser } from '@clerk/nextjs';
import { useWidgets } from '@/hooks/useWidgets';

export default function useJobSubmissionValidation() {
  const { user, isLoaded } = useUser();
  const { addressValue } = useWidgets();

  const areRequiredFieldsFilled = useMemo(() => {
    if (!isLoaded || !user) return false;
    const firstName = user.firstName?.trim();
    const lastName = user.lastName?.trim();
    const email = user.primaryEmailAddress?.emailAddress?.trim();
    const phone = user.primaryPhoneNumber?.phoneNumber?.trim();
    const address = addressValue?.trim();
    const isPhoneVerified = user.primaryPhoneNumber?.verification?.status === 'verified';
    return Boolean(firstName && lastName && email && phone && address && isPhoneVerified);
  }, [isLoaded, user, addressValue]);

  return {
    areRequiredFieldsFilled,
  }
}