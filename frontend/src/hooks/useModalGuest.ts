import { create } from 'zustand';
import { useRedirectQueryUrl } from './useRedirectQueryUrl';

interface ModalGuestState {
  isModalOpen: boolean;
  openModal: (redirectUrl?: string) => void;
  closeModal: () => void;
}

export const useModalForGuest = create<ModalGuestState>((set) => ({
  isModalOpen: false,
  openModal: (redirectUrl?: string) => {
    const currentUrl = redirectUrl || (typeof window !== 'undefined' ? window.location.href : null);
    if (currentUrl) {
      useRedirectQueryUrl.getState().setRedirectUrl(currentUrl);
    }
    set({ isModalOpen: true });
  },
  closeModal: () => set({ isModalOpen: false }),
}));
