import { useCallback } from 'react';
import { useAuth } from '@clerk/nextjs';
import { useGuestAuth } from './useGuestAuth';

export const useUniversalAuth = () => {
  const { getToken: getClerkToken, isSignedIn } = useAuth();
  const { ensureGuestAuth } = useGuestAuth();

  const getToken = useCallback(async (): Promise<string | null> => {
    try {
      if (isSignedIn) {
        const clerkToken = await getClerkToken();
        if (clerkToken) {
          return clerkToken;
        }
      }

      const guestToken = await ensureGuestAuth();
      return guestToken;
    } catch (error) {
      console.error('Failed to get token:', error);
      return null;
    }
  }, [isSignedIn, getClerkToken, ensureGuestAuth]);

  return {
    getToken,
  };
};
