/* eslint-disable @typescript-eslint/no-unused-vars */
import { create } from 'zustand';
import {
  PersonalizationCardField,
  PersonalizationCardFile,
  FieldType,
} from '@/components/PersonalizationCard/PersonalizationCard.types';
import { PropertyFormData } from '@/components/PropertyForm/PropertyForm.types';
import {
  updateProperty,
  convertFrontendToBackend,
  convertBackendToFrontend,
  convertFieldsToBackend,
  Property,
} from '@/api/properties';
import { DocumentDto } from '@/api/documents';
import { CategorizedFile } from '@/types/file';

const DEFAULT_PAGE_SIZE = 10;

export interface PropertyDetailsEntry {
  id: string;
  title: string;
  fields: PersonalizationCardField[];
  files: PersonalizationCardFile[];
}

interface PropertyDetailsEntriesState {
  entries: PropertyDetailsEntry[];
  editingEntries: string[];
  isCreating: boolean;
  hasMoreItems: boolean;
  currentPage: number;
  totalPages: number | null;
  isLoading: boolean;
  isUpdating: boolean;

  // Actions
  createEntry: (formData: PropertyFormData, propertyId: number, token: string) => Promise<void>;
  updateEntry: (
    id: string,
    updatedFields: PersonalizationCardField[],
    updatedFiles: PersonalizationCardFile[],
    updatedTitle: string | undefined,
    token: string
  ) => Promise<void>;
  loadPropertyEntry: (property: Property) => void;
  setEditingEntry: (id: string) => void;
  cancelEditingEntry: (id: string) => void;
  reset: () => void;
}

const convertFormDataToFields = (formData: PropertyFormData): PersonalizationCardField[] => {
  const fields: PersonalizationCardField[] = [];

  fields.push({
    id: 'userRole',
    label: 'Relationship',
    value: formData.userRole || '',
    editable: true,
    type: 'select',
    placeholder: 'Choose an option',
    options: [
      { label: 'Owner and occupier', value: 'Owner and occupier' },
      { label: 'Landlord', value: 'Landlord' },
      { label: 'Tenant', value: 'Tenant' },
    ],
  });

  // Always include all fields, even if empty
  fields.push({
    id: 'ownershipType',
    label: 'Ownership Type',
    value: formData.ownershipType || '',
    editable: true,
    type: 'select',
    placeholder: 'Choose an option',
    options: [
      { label: 'Freehold', value: 'Freehold' },
      { label: 'Leasehold', value: 'Leasehold' },
      { label: 'Share of freehold', value: 'Share of freehold' },
    ],
  });

  fields.push({
    id: 'propertyType',
    label: 'Property Type',
    value: formData.propertyType || '',
    editable: true,
    type: 'select',
    placeholder: 'Choose an option',
    options: [
      { label: 'House', value: 'House' },
      { label: 'Flat', value: 'Flat' },
      { label: 'Office', value: 'Office' },
      { label: 'Retail', value: 'Retail' },
    ],
  });

  fields.push({
    id: 'propertySubType',
    label: 'Property Sub Type',
    value: formData.propertySubType || '',
    editable: true,
    type: 'select',
    placeholder: 'Choose an option',
    options: [
      { label: 'Terraced', value: 'Terraced' },
      { label: 'Semi-Detached', value: 'Semi-Detached' },
      { label: 'Detached', value: 'Detached' },
    ],
  });

  fields.push({
    id: 'numberOfBedrooms',
    label: 'Number of Bedrooms',
    value: formData.numberOfBedrooms || '',
    editable: true,
    type: 'number',
    placeholder: 'e.g. 2',
  });

  fields.push({
    id: 'numberOfBathrooms',
    label: 'Number of Bathrooms',
    value: formData.numberOfBathrooms || '',
    editable: true,
    type: 'number',
    placeholder: 'e.g. 2',
  });

  fields.push({
    id: 'floorPropertyIsOn',
    label: 'Floor property is on',
    value: formData.floorPropertyIsOn || '',
    editable: true,
    type: 'number',
    placeholder: 'e.g. 2',
  });

  fields.push({
    id: 'numberOfFloors',
    label: 'Number of Floors',
    value: formData.numberOfFloors || '',
    editable: true,
    type: 'number',
    placeholder: 'e.g. 2',
  });

  fields.push({
    id: 'grossInternalArea',
    label: 'Gross internal area (ft²)',
    value: formData.grossInternalArea || '',
    editable: true,
    type: 'number',
    placeholder: 'e.g. 1200',
  });

  fields.push({
    id: 'balconyTerrace',
    label: 'Balcony/Terrace',
    value: formData.balconyTerrace || '',
    editable: true,
    type: 'radio',
    radioOptions: [
      { label: 'Yes', value: 'Yes' },
      { label: 'No', value: 'No' },
    ],
  });

  fields.push({
    id: 'garden',
    label: 'Garden',
    value: formData.garden || '',
    editable: true,
    type: 'radio',
    radioOptions: [
      { label: 'Yes', value: 'Yes' },
      { label: 'No', value: 'No' },
    ],
  });

  fields.push({
    id: 'swimmingPool',
    label: 'Swimming Pool',
    value: formData.swimmingPool || '',
    editable: true,
    type: 'radio',
    radioOptions: [
      { label: 'Yes', value: 'Yes' },
      { label: 'No', value: 'No' },
    ],
  });

  return fields;
};

export const usePropertyDetailsEntries = create<PropertyDetailsEntriesState>((set, get) => ({
  entries: [],
  editingEntries: [],
  isCreating: false,
  hasMoreItems: true,
  isLoadingMore: false,
  currentPage: 1,
  totalPages: null,
  isLoading: false,
  isUpdating: false,

  loadPropertyEntry: (property: Property) => {
    const fields = convertBackendToFrontend(property);
    const entry: PropertyDetailsEntry = {
      id: `property-${property.id}`,
      title: 'Property Details',
      fields,
      files: property.sourcedDocuments,
    };
    set({ entries: [entry] });
  },

  createEntry: async (formData: PropertyFormData, propertyId: number, token: string) => {
    set({ isCreating: true });
    try {
      const backendRequest = convertFrontendToBackend(formData);
      const updatedProperty = await updateProperty({
        propertyId,
        request: backendRequest,
        token,
      });

      const fields = convertBackendToFrontend(updatedProperty);
      const entry: PropertyDetailsEntry = {
        id: `property-${propertyId}`,
        title: 'Property Details',
        fields,
        files: [],
      };

      set({ entries: [entry] });
    } catch (error) {
      console.error('Failed to create property details:', error);
      // Create fallback local entry
      const fields = convertFormDataToFields(formData);
      const fallbackEntry: PropertyDetailsEntry = {
        id: `property-${propertyId}`,
        title: 'Property Details',
        fields,
        files: [],
      };
      set({ entries: [fallbackEntry] });
    } finally {
      set({ isCreating: false });
    }
  },

  updateEntry: async (
    id: string,
    updatedFields: PersonalizationCardField[],
    updatedFiles: PersonalizationCardFile[],
    updatedTitle: string | undefined,
    token: string
  ) => {
    set({ isUpdating: true });
    try {
      // Extract property ID from entry ID
      const propertyId = parseInt(id.replace('property-', ''), 10);
      if (isNaN(propertyId)) {
        throw new Error('Invalid property ID');
      }

      const backendRequest = convertFieldsToBackend(updatedFields);
      const updatedProperty = await updateProperty({
        propertyId,
        request: backendRequest,
        token,
      });

      const fields = convertBackendToFrontend(updatedProperty);
      const updatedEntry: PropertyDetailsEntry = {
        id,
        title: updatedTitle || 'Property Details',
        fields,
        files: updatedFiles,
      };

      set((state) => ({
        entries: state.entries.map((entry) => (entry.id === id ? updatedEntry : entry)),
      }));
    } catch (error) {
      console.error('Failed to update property details:', error);
      // Fallback to local update
      set((state) => ({
        entries: state.entries.map((entry) =>
          entry.id === id
            ? {
              ...entry,
              fields: updatedFields,
              files: updatedFiles,
              title: updatedTitle || entry.title,
            }
            : entry
        ),
      }));
    } finally {
      set({ isUpdating: false });
    }
  },

  setEditingEntry: (id: string) => {
    set((state) => ({
      editingEntries: state.editingEntries.includes(id)
        ? state.editingEntries
        : [...state.editingEntries, id],
    }));
  },

  cancelEditingEntry: (id: string) => {
    set((state) => ({
      editingEntries: state.editingEntries.filter((entryId) => entryId !== id),
    }));
  },

  reset: () => {
    set({
      entries: [],
      editingEntries: [],
      currentPage: 1,
      totalPages: null,
      hasMoreItems: true,
      isLoading: false,
      isCreating: false,
      isUpdating: false,
    });
  },
}));
