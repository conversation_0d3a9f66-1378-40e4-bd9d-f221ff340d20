import { create } from 'zustand';
import {
  ApplianceCreate,
  ApplianceInfo,
  AppliancesResponse,
  ApplianceUpdate,
  convertBackendToFrontend,
  convertFrontendToBackend,
  createAppliance,
  fetchAppliances,
  updateAppliance,
} from '@/api/appliances';
import { ApplianceFormData } from '@/components/ApplianceModal/ApplianceModal.types';
import {
  PersonalizationCardField,
  PersonalizationCardFile,
} from '@/components/PersonalizationCard/PersonalizationCard.types';

const DEFAULT_PAGE_SIZE = 10;

const createFallbackAppliance = (data: ApplianceFormData): ApplianceItem => ({
  id: `appliance-${Date.now()}`,
  title: data.applianceType,
  fields: [
    {
      id: '1',
      label: 'Brand',
      value: data.brand,
      editable: true,
      type: 'text' as const,
    },
    {
      id: '2',
      label: 'Model',
      value: data.model,
      editable: true,
      type: 'text' as const,
    },
    {
      id: '3',
      label: 'Serial number',
      value: data.serialNumber,
      editable: true,
      type: 'text' as const,
    },
    {
      id: '4',
      label: 'Warranty',
      value: data.warranty,
      editable: true,
      type: 'text' as const,
    },
  ],
  files: data.files ?? [],
});

const mapFieldsToUpdateRequest = (
  fields: PersonalizationCardField[],
  title?: string
): ApplianceUpdate => ({
  type: title || undefined,
  brand: fields.find((f) => f.label === 'Brand')?.value?.toString() || null,
  model: fields.find((f) => f.label === 'Model')?.value?.toString() || null,
  serialNumber: fields.find((f) => f.label === 'Serial number')?.value?.toString() || null,
  warranty: fields.find((f) => f.label === 'Warranty')?.value?.toString() || null,
});

export interface ApplianceItem {
  id: string;
  title: string;
  fields: PersonalizationCardField[];
  files: PersonalizationCardFile[];
}

interface AppliancesState {
  appliances: ApplianceItem[];
  editingAppliances: string[];

  // Pagination state
  currentPage: number;
  totalPages: number | null;
  hasMoreItems: boolean;
  isLoadingMore: boolean;

  // Loading states
  isLoading: boolean;
  isCreating: boolean;
  isUpdating: boolean;
  isFetching: boolean;

  // Actions
  fetchAppliances: (token: string, reset?: boolean) => Promise<void>;
  loadMoreAppliances: (token: string) => Promise<void>;
  createAppliance: (data: ApplianceFormData, propertyId: number, token: string) => Promise<void>;
  updateAppliance: (
    id: string,
    updatedFields: PersonalizationCardField[],
    updatedFiles: PersonalizationCardFile[],
    updatedTitle: string | undefined,
    token: string
  ) => Promise<void>;

  // UI state management
  setEditingAppliance: (id: string) => void;
  cancelEditingAppliance: (id: string) => void;
  removeFileFromAppliance: (applianceId: string, fileId: string) => void;
  addFileBasedAppliance: (file: PersonalizationCardFile) => void;

  // Reset
  reset: () => void;
}

export const useAppliances = create<AppliancesState>((set, get) => ({
  appliances: [],
  editingAppliances: [],

  currentPage: 1,
  totalPages: null,
  hasMoreItems: true,
  isLoadingMore: false,

  isLoading: false,
  isCreating: false,
  isUpdating: false,
  isFetching: false,

  fetchAppliances: async (token: string, reset?: boolean) => {
    set({ isFetching: true });
    try {
      if (reset) {
        set({ appliances: [], currentPage: 1 });
      }
      const response: AppliancesResponse = await fetchAppliances({
        token,
        page: reset ? 1 : get().currentPage,
        size: DEFAULT_PAGE_SIZE,
      });
      const convertedAppliances = response.items.map(convertBackendToFrontend);
      set((state) => ({
        appliances: reset ? convertedAppliances : [...state.appliances, ...convertedAppliances],
        totalPages: response.pages,
        hasMoreItems:
          response.page !== null && response.pages !== null && response.page < response.pages,
      }));
    } catch (error) {
      console.error('Failed to fetch appliances:', error);
    } finally {
      set({ isFetching: false });
    }
  },

  loadMoreAppliances: async (token: string) => {
    set({ isLoadingMore: true });
    try {
      const currentPage = get().currentPage;
      const response: AppliancesResponse = await fetchAppliances({
        token,
        page: currentPage + 1,
        size: DEFAULT_PAGE_SIZE,
      });
      const convertedAppliances = response.items.map(convertBackendToFrontend);
      set((state) => ({
        appliances: [...state.appliances, ...convertedAppliances],
        totalPages: response.pages,
        hasMoreItems:
          response.page !== null && response.pages !== null && response.page < response.pages,
        currentPage: currentPage + 1,
      }));
    } catch (error) {
      console.error('Failed to load more appliances:', error);
    } finally {
      set({ isLoadingMore: false });
    }
  },

  createAppliance: async (data: ApplianceFormData, propertyId: number, token: string) => {
    set({ isCreating: true });
    try {
      const backendRequest: ApplianceCreate = convertFrontendToBackend(data, propertyId);
      const createdAppliance: ApplianceInfo = await createAppliance({
        request: backendRequest,
        token,
      });

      const convertedAppliance = convertBackendToFrontend(createdAppliance);
      set((state) => ({
        appliances: [...state.appliances, convertedAppliance],
      }));
    } catch (error) {
      console.error('Failed to create appliance:', error);
      // Create fallback local appliance
      const fallbackAppliance = createFallbackAppliance(data);

      set((state) => ({
        appliances: [...state.appliances, fallbackAppliance],
      }));
    } finally {
      set({ isCreating: false });
    }
  },

  updateAppliance: async (
    id: string,
    updatedFields: PersonalizationCardField[],
    updatedFiles: PersonalizationCardFile[],
    updatedTitle: string | undefined,
    token: string
  ) => {
    set({ isUpdating: true });
    try {
      const applianceId = parseInt(id);
      if (isNaN(applianceId)) {
        throw new Error('Invalid appliance ID');
      }

      const updateRequest: ApplianceUpdate = mapFieldsToUpdateRequest(updatedFields, updatedTitle);

      const updatedAppliance: ApplianceInfo = await updateAppliance({
        applianceId,
        request: updateRequest,
        token,
      });

      const convertedAppliance = convertBackendToFrontend(updatedAppliance);

      set((state) => ({
        appliances: state.appliances.map((appliance) =>
          appliance.id === id ? convertedAppliance : appliance
        ),
      }));
    } catch (error) {
      console.error('Failed to update appliance:', error);
      // Fallback to local update
      set((state) => ({
        appliances: state.appliances.map((appliance) =>
          appliance.id === id
            ? {
                ...appliance,
                fields: updatedFields,
                files: updatedFiles,
                title: updatedTitle || appliance.title,
              }
            : appliance
        ),
      }));
    } finally {
      set({ isUpdating: false });
    }
  },

  setEditingAppliance: (id: string) => {
    set((state) => ({
      editingAppliances: state.editingAppliances.includes(id)
        ? state.editingAppliances
        : [...state.editingAppliances, id],
    }));
  },

  cancelEditingAppliance: (id: string) => {
    set((state) => ({
      editingAppliances: state.editingAppliances.filter((appId) => appId !== id),
    }));
  },

  removeFileFromAppliance: (applianceId: string, fileId: string) => {
    set((state) => ({
      appliances: state.appliances.map((appliance) => {
        if (appliance.id === applianceId) {
          return {
            ...appliance,
            files: appliance.files.filter((file) => String(file.id) !== fileId),
          };
        }
        return appliance;
      }),
    }));
  },

  addFileBasedAppliance: (file: PersonalizationCardFile) => {
    const applianceId = `appliance-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
    const newAppliance: ApplianceItem = {
      id: applianceId,
      title: file.fileName.replace(/\.[^/.]+$/, ''),
      fields: [
        {
          id: '1',
          label: 'Brand',
          value: '',
          editable: true,
          placeholder: 'e.g. Samsung, LG, Bosch',
          type: 'text' as const,
        },
        {
          id: '2',
          label: 'Model',
          value: '',
          editable: true,
          placeholder: 'e.g. Series 6 SpaceMax',
          type: 'text' as const,
        },
        {
          id: '3',
          label: 'Serial number',
          value: '',
          editable: true,
          placeholder: 'e.g. AB1234CDEFG',
          type: 'text' as const,
        },
        {
          id: '4',
          label: 'Warranty',
          value: '',
          editable: true,
          placeholder: 'e.g. 5 years',
          type: 'text' as const,
        },
      ],
      files: [file],
    };

    set((state) => ({
      appliances: [...state.appliances, newAppliance],
      editingAppliances: [...state.editingAppliances, applianceId],
    }));
  },

  reset: () => {
    set({
      appliances: [],
      editingAppliances: [],
      currentPage: 1,
      totalPages: null,
      hasMoreItems: true,
      isLoadingMore: false,
      isLoading: false,
      isCreating: false,
      isUpdating: false,
      isFetching: false,
    });
  },
}));
