'use client';

import { NuqsAdapter } from 'nuqs/adapters/next/app';
import { ClerkProvider, useAuth, useUser } from '@clerk/nextjs';
import Script from 'next/script';
import '@/styles/tailwind.css';
import '@/styles/custom.scss';
import '@/styles/main.css';
import { Header } from '@/components/Header/Header';
import { Sidebar } from '@/components/Sidebar/Sidebar';
import React, { useEffect } from 'react';
import { AuthProvider } from '@/providers/AuthProvider';
import { AnalyticsProvider } from '@/providers/AnalyticsProvider';
import { useSidebar } from '@/hooks/useSidebar';
import { useBreakpoint } from '@/utils/breakpointUtils';
import styles from '@/containers/ChatPage/ChatPage.module.scss';
import { initDataLayer, getIsProduction } from '@/utils/gtm';
import { usePathname, useRouter } from 'next/navigation';
import { SWRConfig } from 'swr';
import { swrConfig } from '@/lib/swrConfig';
import { Toaster } from '@/components/ui/sonner';
import { WelcomeAssistantModal } from '@/components/WelcomeAssistantModal';
import { AuthRedirectHandler } from '@/components/AuthRedirectHandler/AuthRedirectHandler';
import { useGuestAuth } from '@/hooks/useGuestAuth';
import { useConversionState } from '@/hooks/useGuestConversion';
import { getConversionInProgress } from '@/hooks/useGuestConversion';

function AppContainer({ children }: { children: React.ReactNode }) {
  const { isLoaded, isSignedIn } = useAuth();
  const { isSidebarOpen, closeSidebar } = useSidebar();
  const { isMobile } = useBreakpoint();
  const pathname = usePathname();
  const { user } = useUser();
  const router = useRouter();
  const { ensureGuestAuth } = useGuestAuth();
  const { isConverting: isGuestConverting } = useConversionState();
  const isOnboardingPage = pathname === '/onboarding';

  useEffect(() => {
    if (!isMobile) {
      closeSidebar();
    }
  }, [isMobile, closeSidebar]);

  // Initialize guest authentication for non-signed-in users
  useEffect(() => {
    if (!isLoaded) return;

    // If user is not signed in, ensure we have guest authentication
    if (!isSignedIn) {
      ensureGuestAuth().catch((error) => {
        console.error('Failed to initialize guest authentication:', error);
      });
    }
  }, [isLoaded, isSignedIn]);

  useEffect(() => {
    if (!isLoaded || !user) return;

    const currentPath = window.location.pathname;
    const hasCompletedOnboarding = user?.unsafeMetadata?.visitedOnboarding === true;
    // Don't redirect during guest conversion process
    if (isGuestConverting) {
      return;
    }

    if (!hasCompletedOnboarding && currentPath !== '/onboarding') {
      router.replace('/onboarding');
    }
  }, [user, isGuestConverting]);

  if (!isLoaded) {
    return null;
  }

  if (isOnboardingPage) {
    return <div className={styles.onboardingContainer}>{children}</div>;
  }

  return (
    <div className={`${styles.container} ${isSidebarOpen ? styles.sidebarOpen : ''}`}>
      <div className={styles.sidebarWrapper}>
        <Sidebar />
      </div>
      <main className={styles.main}>
        <Header />
        {children}
      </main>
      {isSidebarOpen && isMobile && <div className={styles.overlay} onClick={closeSidebar} />}
      <WelcomeAssistantModal onSignUp={() => {}} onLogin={() => {}} />
      <AuthRedirectHandler />
    </div>
  );
}

export default function RootLayout({ children }: { children: React.ReactNode }) {
  // Check if we're in production for GTM
  const isProd = getIsProduction();

  // Initialize dataLayer for GTM
  useEffect(() => {
    initDataLayer();
  }, []);

  return (
    <ClerkProvider afterSignOutUrl="/">
      <AuthProvider>
        <AnalyticsProvider>
          <SWRConfig value={swrConfig}>
            <html lang="en">
              <head>
                <title>Hey Alfie - AI property manager</title>
                <meta
                  name="viewport"
                  content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no"
                />
                {/* Google Tag Manager - only in production */}
                {isProd && (
                  <Script id="google-tag-manager" strategy="afterInteractive">
                    {`(function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start':
                  new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0],
                  j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src=
                  'https://www.googletagmanager.com/gtm.js?id='+i+dl;f.parentNode.insertBefore(j,f);
                  })(window,document,'script','dataLayer','GTM-TWJWB6MT');`}
                  </Script>
                )}
                <Script
                  id="Cookiebot"
                  src="https://consent.cookiebot.com/uc.js"
                  data-cbid="23dbcc9a-f444-43ac-bfcb-e492f957571d"
                  type="text/javascript"
                  defer
                />

                {/* Contentsquare - only in production */}
                {isProd && (
                  <Script id="contentsquare" strategy="afterInteractive">
                    {`
                  (function (c, s, q, u, a, r, e) {
                      c.hj=c.hj||function(){(c.hj.q=c.hj.q||[]).push(arguments)};
                      c._hjSettings = { hjid: a };
                      r = s.getElementsByTagName('head')[0];
                      e = s.createElement('script');
                      e.async = true;
                      e.src = q + c._hjSettings.hjid + u;
                      r.appendChild(e);
                  })(window, document, 'https://static.hj.contentsquare.net/c/csq-', '.js', 6397345);
                  `}
                  </Script>
                )}
              </head>
              <body>
                {/* Google Tag Manager (noscript) - only in production */}
                {isProd && (
                  <noscript>
                    <iframe
                      src="https://www.googletagmanager.com/ns.html?id=GTM-TWJWB6MT"
                      height="0"
                      width="0"
                      style={{ display: 'none', visibility: 'hidden' }}
                    />
                  </noscript>
                )}
                <AppContainer>
                  <NuqsAdapter>
                    {children}
                    <Toaster position="bottom-center" />
                  </NuqsAdapter>
                </AppContainer>
              </body>
            </html>
          </SWRConfig>
        </AnalyticsProvider>
      </AuthProvider>
    </ClerkProvider>
  );
}
