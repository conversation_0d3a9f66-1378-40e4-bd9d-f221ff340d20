'use client';

import { useUser } from '@clerk/nextjs';
import { useRouter } from 'next/navigation';
import { useLayoutEffect, useRef, useState } from 'react';

export default function ProtectedLayout({
  children,
}: {
  children: React.ReactNode
}) {
  const { user, isLoaded } = useUser();
  const router = useRouter();
  const [isChecking, setIsChecking] = useState(true);
  const effectRan = useRef(false);

  useLayoutEffect(() => {
    if (effectRan.current) return;
    if (!isLoaded || !user) return;

    effectRan.current = true;

    const handleFirstLogin = async () => {
      try {
        await user.update({
          unsafeMetadata: {
            ...user.unsafeMetadata,
            visitedOnboarding: true
          }
        });
      } catch (error) {
        console.error('Error setting initial onboarding metadata:', error);
      }
    };

    const handleRedirects = () => {
      const currentPath = window.location.pathname;
      const hasCompletedOnboarding = user.unsafeMetadata?.visitedOnboarding === true;

      if (!hasCompletedOnboarding && currentPath !== '/onboarding') {
        router.replace('/onboarding');
        return true;
      }

      return false;
    };

    handleFirstLogin();

    const redirected = handleRedirects();
    if (!redirected) {
      setIsChecking(false);
    }
  }, [user, isLoaded, router]);

  if (isChecking && isLoaded) {
    return null;
  }

  return <>{children}</>;
} 