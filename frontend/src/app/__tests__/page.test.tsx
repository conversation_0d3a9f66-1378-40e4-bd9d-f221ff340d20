import { render, screen } from '@testing-library/react';
import { useUser } from '@clerk/nextjs';
import Page from '../page';
import { vi } from 'vitest';

vi.mock('@clerk/nextjs', () => ({
  useAuth: vi.fn().mockReturnValue({
    getToken: vi.fn().mockResolvedValue('mock-token'),
  }),
  auth: () => Promise.resolve({ userId: 'test-user-id' }),
  ClerkProvider: ({ children }: { children: React.ReactNode }) => <>{children}</>,
  useUser: vi.fn(),
  SignedIn: ({ children }: { children: React.ReactNode }) => <>{children}</>,
  SignedOut: ({ children }: { children: React.ReactNode }) => <>{children}</>,
  SignInButton: ({ children }: { children: React.ReactNode }) => <div>{children}</div>,
  SignUpButton: ({ children }: { children: React.ReactNode }) => <div>{children}</div>,
}));

vi.mock('next/navigation', () => ({
  useParams: vi.fn(() => ({ chatId: 'test-chat-id' })),
  useRouter: vi.fn(() => ({
    push: vi.fn(),
    replace: vi.fn(),
    refresh: vi.fn(),
    back: vi.fn(),
    forward: vi.fn(),
  })),
  useSearchParams: vi.fn(() => ({
    get: vi.fn(() => null),
    has: vi.fn(() => false),
    getAll: vi.fn(() => []),
    keys: vi.fn(() => []),
    values: vi.fn(() => []),
    entries: vi.fn(() => []),
    forEach: vi.fn(),
    append: vi.fn(),
    delete: vi.fn(),
    set: vi.fn(),
    sort: vi.fn(),
    toString: vi.fn(() => ''),
  })),
  usePathname: vi.fn(() => '/'),
  redirect: vi.fn(),
  notFound: vi.fn(),
}));

describe('Home Page', () => {
  beforeEach(() => {
    vi.mocked(useUser).mockReturnValue({
      isSignedIn: true,
      user: {
        id: 'test-user-id',
        fullName: 'Test User',
      },
    } as ReturnType<typeof useUser>);
  });

  afterEach(() => {
    vi.clearAllMocks();
  });

  it('renders the welcome message', () => {
    render(<Page />);
    expect(screen.getByRole('heading', { name: /What can I help you with?/i })).toBeInTheDocument();
  });

  it('renders quick action buttons when not authenticated', () => {
    vi.mocked(useUser).mockReturnValue({
      isSignedIn: false,
      user: null,
    } as ReturnType<typeof useUser>);

    render(<Page />);
    expect(screen.getByRole('button', { name: /Help with an issue/i })).toBeInTheDocument();
    expect(
      screen.getByRole('button', { name: /DIY instructions and support/i })
    ).toBeInTheDocument();
    expect(screen.getByRole('button', { name: /Find me a tradesperson/i })).toBeInTheDocument();
  });
});
