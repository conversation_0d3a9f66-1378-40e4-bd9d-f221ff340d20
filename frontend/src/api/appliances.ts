'use client';

import axios from 'axios';
import { API_ENDPOINTS, getApiUrl } from '@/constants/api';
import { DocumentDto } from '@/api/documents';

// Backend API interfaces based on OpenAPI spec
export interface ApplianceInfo {
  id: number;
  type: string;
  brand: string | null;
  model: string | null;
  serialNumber: string | null;
  warranty: string | null;
  dateOfPurchase: string | null; // ISO date string
  otherDetails: string | null;
  propertyId: number;
  invoiceReceiptDocumentId: number | null;
  sourcedDocuments: DocumentDto[] | null;
}

export interface ApplianceCreate {
  type: string; // required
  brand?: string | null;
  model?: string | null;
  serialNumber?: string | null;
  warranty?: string | null;
  dateOfPurchase?: string | null; // ISO date string
  otherDetails?: string | null;
  invoiceReceiptDocumentId?: number | null;
  propertyId: number; // required
}

export interface ApplianceUpdate {
  type?: string | null;
  brand?: string | null;
  model?: string | null;
  serialNumber?: string | null;
  warranty?: string | null;
  dateOfPurchase?: string | null; // ISO date string
  otherDetails?: string | null;
  invoiceReceiptDocumentId?: number | null;
}

export interface AppliancesResponse {
  items: ApplianceInfo[];
  total: number | null;
  page: number | null;
  size: number | null;
  pages: number | null;
}

// API functions
export async function fetchAppliances({
  token,
  page = 1,
  size = 50,
}: {
  token?: string | null;
  page?: number;
  size?: number;
}): Promise<AppliancesResponse> {
  const response = await axios.get<AppliancesResponse>(getApiUrl(`${API_ENDPOINTS.APPLIANCES}/`), {
    params: { page, size },
    headers: {
      Authorization: `Bearer ${token}`,
      Accept: 'application/json',
    },
  });

  if (!response.data) {
    throw new Error('Failed to fetch appliances');
  }

  return response.data;
}

export async function createAppliance({
  request,
  token,
}: {
  request: ApplianceCreate;
  token?: string | null;
}): Promise<ApplianceInfo> {
  const response = await axios.post<ApplianceInfo>(
    getApiUrl(`${API_ENDPOINTS.APPLIANCES}/`),
    request,
    {
      headers: {
        Authorization: `Bearer ${token}`,
        Accept: 'application/json',
        'Content-Type': 'application/json',
      },
    }
  );

  if (!response.data) {
    throw new Error('Failed to create appliance');
  }

  return response.data;
}

export async function updateAppliance({
  applianceId,
  request,
  token,
}: {
  applianceId: number;
  request: ApplianceUpdate;
  token?: string | null;
}): Promise<ApplianceInfo> {
  const response = await axios.patch<ApplianceInfo>(
    getApiUrl(`${API_ENDPOINTS.APPLIANCES}/${applianceId}`),
    request,
    {
      headers: {
        Authorization: `Bearer ${token}`,
        Accept: 'application/json',
        'Content-Type': 'application/json',
      },
    }
  );

  if (!response.data) {
    throw new Error('Failed to update appliance');
  }

  return response.data;
}

// Utility functions to convert between frontend and backend formats
export function convertFrontendToBackend(
  frontendData: {
    applianceType: string;
    brand: string;
    model: string;
    serialNumber: string;
    warranty: string;
    files?: Array<DocumentDto>;
  },
  propertyId: number
): ApplianceCreate {
  return {
    type: frontendData.applianceType,
    brand: frontendData.brand || null,
    model: frontendData.model || null,
    serialNumber: frontendData.serialNumber || null,
    warranty: frontendData.warranty || null,
    propertyId,
  };
}

export function convertBackendToFrontend(backendData: ApplianceInfo) {
  return {
    id: backendData.id.toString(),
    title: backendData.type,
    fields: [
      {
        id: '1',
        label: 'Brand',
        value: backendData.brand || '',
        editable: true,
        type: 'text' as const,
      },
      {
        id: '2',
        label: 'Model',
        value: backendData.model || '',
        editable: true,
        type: 'text' as const,
      },
      {
        id: '3',
        label: 'Serial number',
        value: backendData.serialNumber || '',
        editable: true,
        type: 'text' as const,
      },
      {
        id: '4',
        label: 'Warranty',
        value: backendData.warranty || '',
        editable: true,
        type: 'text' as const,
      },
    ],
    files: backendData.sourcedDocuments || [],
  };
}
