'use client';
import { getApiUrl, API_ENDPOINTS } from '@/constants/api';

interface Job {
  id?: number;
  headline?: string;
  subTitle?: string;
  details?: string;
  urgency?: string;
  availability?: string;
  status: string;
  timestamp?: string;
}

export async function acceptJob({
  jobId,
  token,
}: {
  jobId: number;
  token?: string | null;
}): Promise<Response> {
  const response = await fetch(getApiUrl(`${API_ENDPOINTS.JOBS}/${jobId}/user-accept/`), {
    method: 'POST',
    headers: {
      Authorization: `Bearer ${token}`,
      Accept: 'application/json',
    },
  });

  if (!response.ok) {
    throw new Error(`Request failed with status code ${response.status}`);
  }

  return response;
}

export async function fetchJob({
  jobId,
  token,
}: {
  jobId: number;
  token?: string | null;
}): Promise<Job> {
  const response = await fetch(getApiUrl(`${API_ENDPOINTS.JOBS}/${jobId}`), {
    method: 'GET',
    headers: {
      Authorization: `Bearer ${token}`,
      Accept: 'application/json',
    },
  });

  if (!response.ok) {
    throw new Error(`Request failed with status code ${response.status}`);
  }

  return response.json();
}
