export interface ICreateTodoDto {
  name: string;
  description: string | null;
  dueDate: string | null;
  doneDate: string | null;
}

export interface ITodoDto {
  id: number;
  name: string;
  description: string | null;
  dueDate: string | null;
  doneDate: string | null;
  type: 'userCreated' | 'systemCreated' | 'systemAccepted';
  deletedDate: string | null;
}

export interface IUpdateTodoDto {
  name: string;
  description?: string | null;
  dueDate?: string | null;
  doneDate?: string | null;
}
