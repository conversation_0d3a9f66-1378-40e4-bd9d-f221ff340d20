import { api } from '@/lib/api';
import { ICreateTodoDto, ITodoDto, IUpdateTodoDto } from './types';

export const createTodo = async (
  url: string,
  { arg }: { arg: { token: string; createTodoDto: ICreateTodoDto } }
): Promise<void> => {
  await api.post<void>(url, arg.createTodoDto, {
    headers: {
      Authorization: `Bearer ${arg.token}`,
      Accept: 'application/json',
    },
  });
};

export const fetchTodos = async (url: string, token: string): Promise<ITodoDto[]> => {
  const response = await api.get<unknown>(url, {
    headers: {
      Authorization: `Bearer ${token}`,
      Accept: 'application/json',
    },
  });

  const data = response.data as { items: ITodoDto[] };

  return data.items;
};

export const fetchTodoDetails = async (url: string, token: string): Promise<ITodoDto | null> => {
  const response = await api.get<unknown>(url, {
    headers: {
      Authorization: `Bear<PERSON> ${token}`,
      Accept: 'application/json',
    },
  });

  return response.data as ITodoDto | null;
};

export const updateTodo = async (
  url: string,
  { arg }: { arg: { token: string; updateTodo: IUpdateTodoDto } }
): Promise<void> => {
  await api.patch<void>(url, arg.updateTodo, {
    headers: {
      Authorization: `Bearer ${arg.token}`,
      Accept: 'application/json',
    },
  });
};

export const deleteTodo = async (
  url: string,
  { arg }: { arg: { token: string } }
): Promise<void> => {
  await api.delete<void>(url, {
    headers: {
      Authorization: `Bearer ${arg.token}`,
      Accept: 'application/json',
    },
  });
};

export const acceptTodo = async (
  url: string,
  { arg }: { arg: { token: string } }
): Promise<void> => {
  await api.post<void>(url, null, {
    headers: {
      Authorization: `Bearer ${arg.token}`,
      Accept: 'application/json',
    },
  });
};

export const rejectTodo = async (
  url: string,
  { arg }: { arg: { token: string } }
): Promise<void> => {
  await api.post<void>(url, null, {
    headers: {
      Authorization: `Bearer ${arg.token}`,
      Accept: 'application/json',
    },
  });
};
