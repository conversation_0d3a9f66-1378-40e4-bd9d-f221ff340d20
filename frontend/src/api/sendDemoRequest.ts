import axios from 'axios';
import { getApiUrl, API_ENDPOINTS } from '@/constants/api';

export interface DemoRequestData {
  companyName: string;
  companyType: string;
  numberOfProperties: string;
  email: string;
  callTime: string;
}

export async function sendDemoRequest(data: DemoRequestData) {
  const response = await axios.post(getApiUrl(`${API_ENDPOINTS.DEMO_REQUEST}/`), data, {
    headers: {
      Accept: 'application/json',
      'Content-Type': 'application/json',
    },
  });

  if (!response.data) {
    throw new Error('Failed to send demo request');
  }

  return response.data;
}
