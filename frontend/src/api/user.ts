'use client';

import axios from 'axios';
import { getApiUrl, API_ENDPOINTS } from '@/constants/api';

export enum UserPropertyRelationType {
  OwnerAndOccupier = 'ownerAndOccupier',
  Landlord = 'landlord',
  Tenant = 'tenant',
  ManagingProfessional = 'managingProfessional',
}

export interface UserDetails {
  id: number;
  clerkId: string;
  email: string;
  firstName: string;
  lastName: string | null;
  phoneNumber: string | null;
  canUserAcceptJobs: boolean;
}

export interface UserUpdate {
  mainUsage: UserPropertyRelationType;
}

export interface GuestUserResponse {
  token: string;
  userId: number;
  expiresAt: string;
}

export interface GuestConvertRequest {
  email: string;
  firstName: string;
  lastName: string;
}

export interface GuestConvertResponse {
  userId: number;
  clerkId: string;
  signInToken: string;
}

export async function getCurrentUser({ token }: { token?: string | null }): Promise<UserDetails> {
  const response = await axios.get<UserDetails>(getApiUrl(`${API_ENDPOINTS.USER}/`), {
    headers: {
      Authorization: `Bearer ${token}`,
      Accept: 'application/json',
    },
  });

  if (!response.data) {
    throw new Error('Failed to fetch user details');
  }

  return response.data;
}

export async function updateUserDetails({
  request,
  token,
}: {
  request: UserUpdate;
  token?: string | null;
}): Promise<UserDetails> {
  const response = await axios.patch<UserDetails>(getApiUrl(`${API_ENDPOINTS.USER}/`), request, {
    headers: {
      Authorization: `Bearer ${token}`,
      Accept: 'application/json',
      'Content-Type': 'application/json',
    },
  });

  if (!response.data) {
    throw new Error('Failed to update user details');
  }

  return response.data;
}

export async function createGuestUser(): Promise<GuestUserResponse> {
  const response = await axios.post<GuestUserResponse>(
    getApiUrl(`${API_ENDPOINTS.USER_GUEST}/`),
    {},
    {
      headers: {
        Accept: 'application/json',
        'Content-Type': 'application/json',
      },
    }
  );

  if (!response.data) {
    throw new Error('Failed to create guest user');
  }

  if (!response.data.expiresAt) {
    const expiresAt = new Date();
    expiresAt.setDate(expiresAt.getDate() + 14);
    response.data.expiresAt = expiresAt.toISOString();
  }

  return response.data;
}

export async function convertGuestUser({
  request,
  token,
}: {
  request: GuestConvertRequest;
  token?: string | null;
}): Promise<GuestConvertResponse> {
  const url = getApiUrl(`${API_ENDPOINTS.USER_GUEST}/convert/`);

  try {
    const response = await axios.post<GuestConvertResponse>(url, request, {
      headers: {
        Authorization: `Bearer ${token}`,
        Accept: 'application/json',
        'Content-Type': 'application/json',
      },
    });

    if (!response.data) {
      throw new Error('Failed to convert guest user - no response data');
    }

    if (!response.data.signInToken || !response.data.clerkId) {
      throw new Error('Failed to convert guest user - missing required fields in response');
    }

    return response.data;
  } catch (error) {
    if (axios.isAxiosError(error)) {
      if (error.response?.data?.message) {
        throw new Error(`Server error: ${error.response.data.message}`);
      } else if (error.response?.status) {
        throw new Error(`HTTP ${error.response.status}: ${error.response.statusText}`);
      } else if (error.request) {
        throw new Error('Network error: No response from server');
      }
    }

    throw error;
  }
}
