'use client';

import axios from 'axios';
import { getApiUrl, API_ENDPOINTS } from '@/constants/api';
import {
  PropertyFormData,
  OwnershipType,
  PropertyType as FrontendPropertyType,
  PropertySubType,
  YesNoOption,
  UserRole,
} from '@/components/PropertyForm/PropertyForm.types';
import {
  PersonalizationCardField,
  FieldType,
} from '@/components/PersonalizationCard/PersonalizationCard.types';
import { DocumentDto } from '@/api/documents';

export interface Property {
  id: number;
  type: PropertyType | null;
  tenureType?: PropertyTenureType | null;
  subgroupType?: PropertySubgroupType | null;
  numberOfBedrooms?: number | null;
  numberOfBathrooms?: number | null;
  numberOfFloors?: number | null;
  sizeInSqft?: number | null;
  hasBalconyTerrace?: boolean | null;
  hasGarden?: boolean | null;
  hasSwimmingPool?: boolean | null;
  onFloorLevel?: number | null;
  userPropertyRelationshipType?: UserPropertyRelationType | null;
  address: {
    id: number;
    streetLine1: string;
    streetLine2?: string;
    townOrCity: string;
    postcode: string;
    houseAccess: string | null;
    parkingInstructions: string | null;
  } | null;
  sourcedDocuments: Array<DocumentDto>;
}

export interface PropertiesResponse {
  items: Property[];
  total: number;
  page: number;
  pages: number;
}

export async function fetchProperties({
                                        token,
                                        page = 1,
                                      }: {
  token?: string | null;
  page?: number;
}): Promise<PropertiesResponse> {
  const response = await axios.get<PropertiesResponse>(getApiUrl(`${API_ENDPOINTS.PROPERTIES}/`), {
    params: { page },
    headers: {
      Authorization: `Bearer ${token}`,
      Accept: 'application/json',
    },
  });

  if (!response.data) {
    throw new Error('Failed to fetch properties');
  }

  return response.data;
}

export interface FindAddressResponse {
  id: string;
  addressLine1: string;
  addressLine2: string;
  city: string;
  postcode: string;
  country: string;
}

export async function findAddress({
                                    query,
                                    token,
                                  }: {
  query: string;
  token?: string | null;
}): Promise<FindAddressResponse[]> {
  const response = await axios.get<FindAddressResponse[]>(
    getApiUrl(`${API_ENDPOINTS.EXTERNAL_DATASOURCES}/find-address`),
    {
      params: { query },
      headers: {
        Authorization: `Bearer ${token}`,
        Accept: 'application/json',
      },
    },
  );

  if (!response.data) {
    throw new Error('Failed to find address');
  }

  return response.data;
}

interface ManualAddress {
  streetLine1: string;
  streetLine2?: string;
  townOrCity: string;
  postcode: string;
}

interface SaveAddressRequest {
  idealPostcodesAddressId?: string;
  addressLine1?: string;
  addressLine2?: string;
  city?: string;
  postcode?: string;
  country?: string;
  manualAddress?: ManualAddress;
}

export async function saveAddress({
                                    request,
                                    token,
                                  }: {
  request: SaveAddressRequest;
  token?: string | null;
}): Promise<Property> {
  const response = await axios.post<Property>(getApiUrl(`${API_ENDPOINTS.PROPERTIES}/`), request, {
    headers: {
      Authorization: `Bearer ${token}`,
      Accept: 'application/json',
      'Content-Type': 'application/json',
    },
  });

  if (!response.data) {
    throw new Error('Failed to save address');
  }

  return response.data;
}

interface SaveAccessInstructionRequest {
  propertyId: number;
  address: {
    houseAccess?: string;
    parkingInstructions?: string;
  };
}

interface SaveAccessInstructionResponse {
  id: number;
  address: {
    houseAccess?: string;
    parkingInstructions?: string;
  };
}

export async function saveAccessInstruction({
                                              request,
                                              token,
                                            }: {
  request: SaveAccessInstructionRequest;
  token?: string | null;
}): Promise<SaveAccessInstructionResponse> {
  const response = await axios.patch<SaveAccessInstructionResponse>(
    getApiUrl(`${API_ENDPOINTS.PROPERTIES}/${request.propertyId}`),
    {
      address: request.address,
    },
    {
      headers: {
        Authorization: `Bearer ${token}`,
        Accept: 'application/json',
        'Content-Type': 'application/json',
      },
    },
  );

  if (!response.data) {
    throw new Error('Failed to save access instruction');
  }

  return response.data;
}

export enum PropertyType {
  House = 'house',
  Flat = 'flat',
  Office = 'office',
  Retail = 'retail',
}

export enum PropertyTenureType {
  Freehold = 'freehold',
  Leasehold = 'leasehold',
  ShareOfFreehold = 'shareOfFreehold',
  NotApplicable = 'NOT_APPLICABLE',
}

export interface ManualAddressCreate {
  streetLine1: string;
  streetLine2: string | null;
  townOrCity: string;
  postcode: string;
}

export enum UserPropertyRelationType {
  OwnerAndOccupier = 'ownerAndOccupier',
  Landlord = 'landlord',
  Tenant = 'tenant',
  ManagingProfessional = 'managingProfessional',
}

type PropertyMutation = {
  type?: PropertyType | null;
  tenureType?: PropertyTenureType | null;
  userPropertyRelationshipType?: UserPropertyRelationType | null;
};

type AddressMutation = { manualAddress: ManualAddressCreate } | { idealPostcodesAddressId: string; };

export type PropertyCreate = PropertyMutation & AddressMutation;

export enum PropertySubgroupType {
  Detached = 'detached',
  SemiDetached = 'semiDetached',
  Terraced = 'terraced',
  NotApplicable = 'NOT_APPLICABLE',
}

export type PropertyUpdate = Partial<AddressMutation> & {
  tenureType?: PropertyTenureType | null;
  type?: PropertyType | null;
  subgroupType?: PropertySubgroupType | null;
  numberOfBedrooms?: number | null;
  numberOfBathrooms?: number | null;
  numberOfFloors?: number | null;
  sizeInSqft?: number | null;
  hasBalconyTerrace?: boolean | null;
  hasGarden?: boolean | null;
  hasSwimmingPool?: boolean | null;
  onFloorLevel?: number | null;
  userPropertyRelationshipType?: UserPropertyRelationType | null;
};

export async function createProperty({
                                       request,
                                       token,
                                     }: {
  request: PropertyCreate;
  token?: string | null;
}): Promise<Property> {
  const response = await axios.post<Property>(getApiUrl(`${API_ENDPOINTS.PROPERTIES}/`), request, {
    headers: {
      Authorization: `Bearer ${token}`,
      Accept: 'application/json',
      'Content-Type': 'application/json',
    },
  });

  if (!response.data) {
    throw new Error('Failed to create property');
  }

  return response.data;
}

export async function updateProperty({
                                       propertyId,
                                       request,
                                       token,
                                     }: {
  propertyId: number;
  request: PropertyUpdate;
  token?: string | null;
}): Promise<Property> {
  const response = await axios.patch<Property>(
    getApiUrl(`${API_ENDPOINTS.PROPERTIES}/${propertyId}`),
    request,
    {
      headers: {
        Authorization: `Bearer ${token}`,
        Accept: 'application/json',
        'Content-Type': 'application/json',
      },
    },
  );

  if (!response.data) {
    throw new Error('Failed to update property');
  }

  return response.data;
}

export interface PropertyDetailsField {
  id: string;
  label: string;
  value: string;
  type: string;
  options?: Array<{
    label: string;
    value: string;
  }>;
}

export interface PropertyDetailsFile {
  id: number;
  name: string;
  type: string;
  url?: string;
}

export interface PropertyDetailsInfo {
  id: number;
  title: string;
  fields: PropertyDetailsField[];
  files: PropertyDetailsFile[];
}

export interface PropertyDetailsResponse {
  items: PropertyDetailsInfo[];
  total: number;
  page: number;
  pages: number;
}

export interface PropertyDetailsCreate {
  title: string;
  fields: PropertyDetailsField[];
  propertyId: number;
}

export interface PropertyDetailsUpdate {
  title?: string;
  fields: PropertyDetailsField[];
  files: PropertyDetailsFile[];
}

export async function fetchPropertyDetails({
                                             token,
                                             page = 1,
                                             size = 10,
                                           }: {
  token?: string | null;
  page?: number;
  size?: number;
}): Promise<PropertyDetailsResponse> {
  const response = await axios.get<PropertyDetailsResponse>(
    getApiUrl(`${API_ENDPOINTS.PROPERTIES}/details`),
    {
      params: { page, size },
      headers: {
        Authorization: `Bearer ${token}`,
        Accept: 'application/json',
      },
    },
  );

  if (!response.data) {
    throw new Error('Failed to fetch property details');
  }

  return response.data;
}

export async function createPropertyDetails({
                                              request,
                                              token,
                                            }: {
  request: PropertyDetailsCreate;
  token?: string | null;
}): Promise<PropertyDetailsInfo> {
  const response = await axios.post<PropertyDetailsInfo>(
    getApiUrl(`${API_ENDPOINTS.PROPERTIES}/details`),
    request,
    {
      headers: {
        Authorization: `Bearer ${token}`,
        Accept: 'application/json',
        'Content-Type': 'application/json',
      },
    },
  );

  if (!response.data) {
    throw new Error('Failed to create property details');
  }

  return response.data;
}

// export async function updatePropertyDetails({
//   detailsId,
//   request,
//   token,
// }: {
//   detailsId: number;
//   request: PropertyDetailsUpdate;
//   token?: string | null;
// }): Promise<PropertyDetailsInfo> {
//   return {
//     id: 1,
//     title: 'Property Details',
//     fields: [],
//     files: [],
//   };
// }

// Mapping functions
export const convertFrontendToBackend = (formData: PropertyFormData): PropertyUpdate => {
  const update: PropertyUpdate = {};

  if (formData.userRole) {
    switch (formData.userRole) {
      case UserRole.OWNER_AND_OCCUPIER:
        update.userPropertyRelationshipType = UserPropertyRelationType.OwnerAndOccupier;
        break;
      case UserRole.LANDLORD:
        update.userPropertyRelationshipType = UserPropertyRelationType.Landlord;
        break;
      case UserRole.TENANT:
        update.userPropertyRelationshipType = UserPropertyRelationType.Tenant;
        break;
    }
  } else {
    // Explicitly set to null when empty
    update.userPropertyRelationshipType = null;
  }

  // Convert ownership type - handle "Not applicable" as distinct value, null only for empty
  if (formData.ownershipType) {
    if (formData.ownershipType === OwnershipType.NOT_APPLICABLE) {
      update.tenureType = PropertyTenureType.NotApplicable;
    } else {
      switch (formData.ownershipType) {
        case OwnershipType.FREEHOLD:
          update.tenureType = PropertyTenureType.Freehold;
          break;
        case OwnershipType.LEASEHOLD:
          update.tenureType = PropertyTenureType.Leasehold;
          break;
        case OwnershipType.SHARE_OF_FREEHOLD:
          update.tenureType = PropertyTenureType.ShareOfFreehold;
          break;
      }
    }
  } else {
    // Explicitly set to null when empty (never set by user)
    update.tenureType = null;
  }

  // Convert property type
  if (formData.propertyType) {
    switch (formData.propertyType) {
      case FrontendPropertyType.HOUSE:
        update.type = PropertyType.House;
        break;
      case FrontendPropertyType.FLAT:
        update.type = PropertyType.Flat;
        break;
      case FrontendPropertyType.OFFICE:
        update.type = PropertyType.Office;
        break;
      case FrontendPropertyType.RETAIL:
        update.type = PropertyType.Retail;
        break;
    }
  } else {
    // Explicitly set to null when empty
    update.type = null;
  }

  // Convert property sub type - handle "Not applicable" as distinct value, null only for empty
  if (formData.propertySubType) {
    if (formData.propertySubType === PropertySubType.NOT_APPLICABLE) {
      update.subgroupType = PropertySubgroupType.NotApplicable;
    } else {
      switch (formData.propertySubType) {
        case PropertySubType.DETACHED:
          update.subgroupType = PropertySubgroupType.Detached;
          break;
        case PropertySubType.SEMI_DETACHED:
          update.subgroupType = PropertySubgroupType.SemiDetached;
          break;
        case PropertySubType.TERRACED:
          update.subgroupType = PropertySubgroupType.Terraced;
          break;
      }
    }
  } else {
    // Explicitly set to null when empty (never set by user)
    update.subgroupType = null;
  }

  // Convert numeric fields - handle empty strings as null
  if (formData.numberOfBedrooms && formData.numberOfBedrooms.trim()) {
    const bedrooms = parseInt(formData.numberOfBedrooms, 10);
    if (!isNaN(bedrooms)) {
      update.numberOfBedrooms = bedrooms;
    } else {
      update.numberOfBedrooms = null;
    }
  } else {
    update.numberOfBedrooms = null;
  }

  if (formData.numberOfBathrooms && formData.numberOfBathrooms.trim()) {
    const bathrooms = parseInt(formData.numberOfBathrooms, 10);
    if (!isNaN(bathrooms)) {
      update.numberOfBathrooms = bathrooms;
    } else {
      update.numberOfBathrooms = null;
    }
  } else {
    update.numberOfBathrooms = null;
  }

  if (formData.numberOfFloors && formData.numberOfFloors.trim()) {
    const floors = parseInt(formData.numberOfFloors, 10);
    if (!isNaN(floors)) {
      update.numberOfFloors = floors;
    } else {
      update.numberOfFloors = null;
    }
  } else {
    update.numberOfFloors = null;
  }

  if (formData.floorPropertyIsOn && formData.floorPropertyIsOn.trim()) {
    const floorLevel = parseInt(formData.floorPropertyIsOn, 10);
    if (!isNaN(floorLevel)) {
      update.onFloorLevel = floorLevel;
    } else {
      update.onFloorLevel = null;
    }
  } else {
    update.onFloorLevel = null;
  }

  if (formData.grossInternalArea && formData.grossInternalArea.trim()) {
    const area = parseInt(formData.grossInternalArea, 10);
    if (!isNaN(area)) {
      update.sizeInSqft = area;
    } else {
      update.sizeInSqft = null;
    }
  } else {
    update.sizeInSqft = null;
  }

  // Convert boolean fields - handle empty strings as null
  if (formData.balconyTerrace) {
    update.hasBalconyTerrace = formData.balconyTerrace === YesNoOption.YES;
  } else {
    update.hasBalconyTerrace = null;
  }

  if (formData.garden) {
    update.hasGarden = formData.garden === YesNoOption.YES;
  } else {
    update.hasGarden = null;
  }

  if (formData.swimmingPool) {
    update.hasSwimmingPool = formData.swimmingPool === YesNoOption.YES;
  } else {
    update.hasSwimmingPool = null;
  }

  return update;
};

export const convertBackendToFrontend = (property: Property): PersonalizationCardField[] => {
  const fields: PersonalizationCardField[] = [];

  let userRoleValue = '';
  if (property.userPropertyRelationshipType) {
    switch (property.userPropertyRelationshipType) {
      case UserPropertyRelationType.OwnerAndOccupier:
        userRoleValue = UserRole.OWNER_AND_OCCUPIER;
        break;
      case UserPropertyRelationType.Landlord:
        userRoleValue = UserRole.LANDLORD;
        break;
      case UserPropertyRelationType.Tenant:
        userRoleValue = UserRole.TENANT;
        break;
    }
  }

  fields.push({
    id: 'userRole',
    label: 'Relationship',
    value: userRoleValue,
    editable: true,
    type: 'select' as FieldType,
    placeholder: 'Choose an option',
    options: [
      { label: 'Owner and occupier', value: UserRole.OWNER_AND_OCCUPIER },
      { label: 'Landlord', value: UserRole.LANDLORD },
      { label: 'Tenant', value: UserRole.TENANT },
    ],
  });

  // Convert tenure type - handle NotApplicable enum value, empty string for null
  let ownershipTypeValue = '';
  if (property.tenureType === PropertyTenureType.NotApplicable) {
    ownershipTypeValue = OwnershipType.NOT_APPLICABLE;
  } else if (property.tenureType) {
    switch (property.tenureType) {
      case PropertyTenureType.Freehold:
        ownershipTypeValue = OwnershipType.FREEHOLD;
        break;
      case PropertyTenureType.Leasehold:
        ownershipTypeValue = OwnershipType.LEASEHOLD;
        break;
      case PropertyTenureType.ShareOfFreehold:
        ownershipTypeValue = OwnershipType.SHARE_OF_FREEHOLD;
        break;
    }
  }

  fields.push({
    id: 'ownershipType',
    label: 'Ownership Type',
    value: ownershipTypeValue,
    editable: true,
    type: 'select' as FieldType,
    placeholder: 'Choose an option',
    options: [
      { label: 'Freehold', value: OwnershipType.FREEHOLD },
      { label: 'Leasehold', value: OwnershipType.LEASEHOLD },
      { label: 'Share of freehold', value: OwnershipType.SHARE_OF_FREEHOLD },
      { label: 'Not applicable', value: OwnershipType.NOT_APPLICABLE },
    ],
  });

  // Convert property type
  let propertyTypeValue = '';
  if (property.type) {
    switch (property.type) {
      case PropertyType.House:
        propertyTypeValue = FrontendPropertyType.HOUSE;
        break;
      case PropertyType.Flat:
        propertyTypeValue = FrontendPropertyType.FLAT;
        break;
      case PropertyType.Office:
        propertyTypeValue = FrontendPropertyType.OFFICE;
        break;
      case PropertyType.Retail:
        propertyTypeValue = FrontendPropertyType.RETAIL;
        break;
    }
  }

  fields.push({
    id: 'propertyType',
    label: 'Property Type',
    value: propertyTypeValue,
    editable: true,
    type: 'select' as FieldType,
    placeholder: 'Choose an option',
    options: [
      { label: 'House', value: FrontendPropertyType.HOUSE },
      { label: 'Flat', value: FrontendPropertyType.FLAT },
      { label: 'Office', value: FrontendPropertyType.OFFICE },
      { label: 'Retail', value: FrontendPropertyType.RETAIL },
    ],
  });

  // Convert property sub type - handle NotApplicable enum value, empty string for null
  let propertySubTypeValue = '';
  if (property.subgroupType === PropertySubgroupType.NotApplicable) {
    propertySubTypeValue = PropertySubType.NOT_APPLICABLE;
  } else if (property.subgroupType) {
    switch (property.subgroupType) {
      case PropertySubgroupType.Detached:
        propertySubTypeValue = PropertySubType.DETACHED;
        break;
      case PropertySubgroupType.SemiDetached:
        propertySubTypeValue = PropertySubType.SEMI_DETACHED;
        break;
      case PropertySubgroupType.Terraced:
        propertySubTypeValue = PropertySubType.TERRACED;
        break;
    }
  }
  // null values remain as empty string (never set by user)

  fields.push({
    id: 'propertySubType',
    label: 'Property Sub Type',
    value: propertySubTypeValue,
    editable: true,
    type: 'select' as FieldType,
    placeholder: 'Choose an option',
    options: [
      { label: 'Terraced', value: PropertySubType.TERRACED },
      { label: 'Semi-Detached', value: PropertySubType.SEMI_DETACHED },
      { label: 'Detached', value: PropertySubType.DETACHED },
      { label: 'Not applicable', value: PropertySubType.NOT_APPLICABLE },
    ],
  });

  // Convert numeric fields
  fields.push({
    id: 'numberOfBedrooms',
    label: 'Number of Bedrooms',
    value: property.numberOfBedrooms ? String(property.numberOfBedrooms) : '',
    editable: true,
    type: 'number' as FieldType,
    placeholder: 'e.g. 2',
  });

  fields.push({
    id: 'numberOfBathrooms',
    label: 'Number of Bathrooms',
    value: property.numberOfBathrooms ? String(property.numberOfBathrooms) : '',
    editable: true,
    type: 'number' as FieldType,
    placeholder: 'e.g. 2',
  });

  fields.push({
    id: 'floorPropertyIsOn',
    label: 'Floor property is on',
    value: property.onFloorLevel ? String(property.onFloorLevel) : '',
    editable: true,
    type: 'number' as FieldType,
    placeholder: 'e.g. 2',
  });

  fields.push({
    id: 'numberOfFloors',
    label: 'Number of Floors',
    value: property.numberOfFloors ? String(property.numberOfFloors) : '',
    editable: true,
    type: 'number' as FieldType,
    placeholder: 'e.g. 2',
  });

  fields.push({
    id: 'grossInternalArea',
    label: 'Gross internal area (ft²)',
    value: property.sizeInSqft ? String(property.sizeInSqft) : '',
    editable: true,
    type: 'number' as FieldType,
    placeholder: 'e.g. 800',
  });

  // Convert boolean fields
  fields.push({
    id: 'balconyTerrace',
    label: 'Balcony/Terrace',
    value:
      property.hasBalconyTerrace !== null
        ? property.hasBalconyTerrace
          ? YesNoOption.YES
          : YesNoOption.NO
        : '',
    editable: true,
    type: 'radio' as FieldType,
    radioOptions: [
      { label: 'Yes', value: YesNoOption.YES },
      { label: 'No', value: YesNoOption.NO },
    ],
  });

  fields.push({
    id: 'garden',
    label: 'Garden',
    value:
      property.hasGarden !== null ? (property.hasGarden ? YesNoOption.YES : YesNoOption.NO) : '',
    editable: true,
    type: 'radio' as FieldType,
    radioOptions: [
      { label: 'Yes', value: YesNoOption.YES },
      { label: 'No', value: YesNoOption.NO },
    ],
  });

  fields.push({
    id: 'swimmingPool',
    label: 'Swimming Pool',
    value:
      property.hasSwimmingPool !== null
        ? property.hasSwimmingPool
          ? YesNoOption.YES
          : YesNoOption.NO
        : '',
    editable: true,
    type: 'radio' as FieldType,
    radioOptions: [
      { label: 'Yes', value: YesNoOption.YES },
      { label: 'No', value: YesNoOption.NO },
    ],
  });

  return fields;
};

export const convertFieldsToFormData = (fields: PersonalizationCardField[]): PropertyFormData => {
  const formData: PropertyFormData = {
    userRole: '',
    ownershipType: '',
    propertyType: '',
    propertySubType: '',
    numberOfBedrooms: '',
    numberOfBathrooms: '',
    numberOfFloors: '',
    floorPropertyIsOn: '',
    grossInternalArea: '',
    balconyTerrace: '',
    garden: '',
    swimmingPool: '',
  };

  fields.forEach((field) => {
    switch (field.id) {
      case 'userRole':
        formData.userRole = field.value as UserRole | '';
        break;
      case 'ownershipType':
        formData.ownershipType = field.value as OwnershipType | '';
        break;
      case 'propertyType':
        formData.propertyType = field.value as FrontendPropertyType | '';
        break;
      case 'propertySubType':
        formData.propertySubType = field.value as PropertySubType | '';
        break;
      case 'numberOfBedrooms':
        formData.numberOfBedrooms = String(field.value);
        break;
      case 'numberOfBathrooms':
        formData.numberOfBathrooms = String(field.value);
        break;
      case 'numberOfFloors':
        formData.numberOfFloors = String(field.value);
        break;
      case 'floorPropertyIsOn':
        formData.floorPropertyIsOn = String(field.value);
        break;
      case 'grossInternalArea':
        formData.grossInternalArea = String(field.value);
        break;
      case 'balconyTerrace':
        formData.balconyTerrace = field.value as YesNoOption | '';
        break;
      case 'garden':
        formData.garden = field.value as YesNoOption | '';
        break;
      case 'swimmingPool':
        formData.swimmingPool = field.value as YesNoOption | '';
        break;
    }
  });

  return formData;
};

export const convertFieldsToBackend = (fields: PersonalizationCardField[]): PropertyUpdate => {
  const formData = convertFieldsToFormData(fields);
  return convertFrontendToBackend(formData);
};

// Mapping function for user role
export const mapRoleToUserPropertyRelationType = (
  role: string,
): UserPropertyRelationType | null => {
  switch (role) {
    case 'owner':
      return UserPropertyRelationType.OwnerAndOccupier;
    case 'landlord':
      return UserPropertyRelationType.Landlord;
    case 'tenant':
      return UserPropertyRelationType.Tenant;
    default:
      return null;
  }
};

// Mapping function to convert backend enum to display text
export const mapUserPropertyRelationTypeToDisplayText = (
  type: UserPropertyRelationType | null,
): string => {
  if (!type) return '';

  switch (type) {
    case UserPropertyRelationType.OwnerAndOccupier:
      return 'Owner and occupier';
    case UserPropertyRelationType.Landlord:
      return 'Landlord';
    case UserPropertyRelationType.Tenant:
      return 'Tenant';
    case UserPropertyRelationType.ManagingProfessional:
      return 'Managing professional';
    default:
      return '';
  }
};
