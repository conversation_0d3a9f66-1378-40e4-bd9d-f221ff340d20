import { API_ENDPOINTS, getApiUrl } from '@/constants/api';
import axios from 'axios';

export type DocumentCategoryType =
  | 'propertyDetails'
  | 'insurance'
  | 'billsAndSubscriptions'
  | 'legal'
  | 'appliance'
  | 'buildingInformation'
  | 'other';

export interface IrrelevantDocumentNotification {
  type: 'irrelevant_document';
  fileName: string;
}

export interface ErrorDocumentNotification {
  type: 'document_error';
  fileName: string;
}

export interface DocumentCategorizedNotification {
  type: 'document_categorized';
  fileName: string;
  category: DocumentCategoryType | undefined;
}

export interface InformationFromDocumentSavedNotification {
  type: 'information_from_document_saved';
  fileName: string;
  category: DocumentCategoryType;
  documentId: number;
}

export type NotificationPayload =
  | IrrelevantDocumentNotification
  | ErrorDocumentNotification
  | DocumentCategorizedNotification
  | InformationFromDocumentSavedNotification;

export interface NotificationInfo {
  id: number;
  context: string | null;
  payload: NotificationPayload;
  createdAt: string;
  updatedAt: string;
}

export interface NotificationPage {
  items: NotificationInfo[];
  total: number;
  page: number;
  size: number;
  pages: number;
}

export async function fetchAllNotifications(
  token: string,
  page: number = 1,
  size: number = 50
): Promise<NotificationPage> {
  try {
    const response = await axios.get<NotificationPage>(
      getApiUrl(`${API_ENDPOINTS.NOTIFICATIONS}/`),
      {
        params: { page, size },
        headers: {
          Authorization: `Bearer ${token}`,
          Accept: 'application/json',
        },
      }
    );

    if (!response.data) {
      throw new Error('Failed to fetch notifications');
    }

    return response.data;
  } catch (error) {
    console.error('Failed to fetch notifications:', error);
    throw new Error('Network error during fetch');
  }
}

export async function fetchNotificationsByContext(
  token: string,
  context: string,
  page: number = 1,
  size: number = 50
): Promise<NotificationPage> {
  try {
    const response = await axios.get<NotificationPage>(
      getApiUrl(`${API_ENDPOINTS.NOTIFICATIONS}/${context}`),
      {
        params: { page, size },
        headers: {
          Authorization: `Bearer ${token}`,
          Accept: 'application/json',
        },
      }
    );

    if (!response.data) {
      throw new Error('Failed to fetch notifications by context');
    }

    return response.data;
  } catch (error) {
    console.error('Failed to fetch notifications by context:', error);
    throw new Error('Network error during fetch');
  }
}

export async function deleteNotificationsByContext(token: string, context: string): Promise<void> {
  try {
    await axios.delete(getApiUrl(`${API_ENDPOINTS.NOTIFICATIONS}/${context}`), {
      headers: {
        Authorization: `Bearer ${token}`,
        Accept: 'application/json',
      },
    });
  } catch (error) {
    console.error('Failed to delete notifications by context:', error);
    throw new Error('Failed to delete notifications');
  }
}

export async function deleteNotificationById(token: string, notificationId: number): Promise<void> {
  try {
    await axios.delete(getApiUrl(`${API_ENDPOINTS.NOTIFICATIONS}/-/${notificationId}`), {
      headers: {
        Authorization: `Bearer ${token}`,
        Accept: 'application/json',
      },
    });
  } catch (error) {
    console.error('Failed to delete notification by context and id:', error);
    throw new Error('Failed to delete notification');
  }
}

export async function deleteAllNotifications(token: string): Promise<void> {
  try {
    await axios.delete(getApiUrl(`${API_ENDPOINTS.NOTIFICATIONS}`), {
      headers: {
        Authorization: `Bearer ${token}`,
        Accept: 'application/json',
      },
    });
  } catch (error) {
    console.error('Failed to delete notifications:', error);
    throw new Error('Failed to delete notifications');
  }
}

export type Context =
  | 'filesPage'
  | 'appliancesPage'
  | 'chat'
  | 'propertyDetails'
  | 'propertyProfile';
