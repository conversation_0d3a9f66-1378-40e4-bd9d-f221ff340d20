.spacing-container {
  padding: 24px;
  font-family: 'Nunito Sans', 'Helvetica Neue', Helvetica, Arial, sans-serif;
}

.section-title {
  margin-bottom: 32px;
}

.section-title h2 {
  font-size: 32px;
  font-weight: 700;
  margin-bottom: 8px;
}

.section-title p {
  color: #666;
  font-size: 14px;
  line-height: 1.5;
}

.spacing-table {
  width: 100%;
  border-collapse: collapse;
  background: white;
  border-radius: 8px;
  overflow: hidden;
}

.table-header {
  display: grid;
  grid-template-columns: 100px 100px 100px 1fr;
  background-color: #f5f5f5;
  border-bottom: 1px solid #eee;
}

.header-cell {
  padding: 12px 16px;
  font-weight: 600;
  font-size: 14px;
  color: #333;
}

.table-body {
  display: flex;
  flex-direction: column;
}

.table-row {
  display: grid;
  grid-template-columns: 100px 100px 100px 1fr;
  border-bottom: 1px solid #eee;
  align-items: center;
}

.table-cell {
  padding: 12px 16px;
  font-size: 14px;
  color: #666;
}

.spacing-preview {
  height: 16px;
  background-color: #21587E;
  border-radius: 2px;
  min-width: 1px;
}

.table-row:hover {
  background-color: #f9f9f9;
}

.table-cell:first-child {
  color: #333;
  font-weight: 500;
} 