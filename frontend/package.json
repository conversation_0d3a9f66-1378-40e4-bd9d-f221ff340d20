{"name": "frontend", "version": "0.1.0", "private": true, "type": "module", "engines": {"node": "20.12.2"}, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint --fix", "lint:all": "eslint --fix \"src/**/*.{ts,tsx}\"", "format": "prettier --write \"src/**/*.{ts,tsx,js,jsx,json,css,scss,md}\"", "format:check": "prettier --check \"src/**/*.{ts,tsx,js,jsx,json,css,scss,md}\"", "format:staged": "git diff --cached --name-only | grep -E \"\\.(js|jsx|ts|tsx|css|scss|json|md)$\" | xargs prettier --write", "storybook": "storybook dev -p 6006", "build-storybook": "storybook build", "chromatic": "npx chromatic --project-token=chpt_85f553e2ff6521c", "test": "vitest", "test:run": "vitest run", "test:ui": "vitest --ui", "test:coverage": "vitest run --coverage", "test:ci": "vitest run"}, "dependencies": {"@clerk/clerk-react": "^4.6.4", "@clerk/nextjs": "^6.5.1", "@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@hugeicons-pro/core-bulk-rounded": "^1.0.1", "@hugeicons-pro/core-duotone-rounded": "^1.0.1", "@hugeicons-pro/core-solid-rounded": "^1.0.1", "@hugeicons-pro/core-solid-sharp": "^1.0.1", "@hugeicons-pro/core-solid-standard": "^1.0.1", "@hugeicons-pro/core-stroke-rounded": "^1.0.1", "@hugeicons-pro/core-stroke-sharp": "^1.0.1", "@hugeicons-pro/core-stroke-standard": "^1.0.1", "@hugeicons-pro/core-twotone-rounded": "^1.0.1", "@hugeicons/react": "^0.6.3", "@mui/icons-material": "^6.2.0", "@mui/material": "^6.2.0", "@next/bundle-analyzer": "^15.3.5", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-slot": "^1.2.3", "@types/react-dropzone": "^5.1.0", "axios": "^1.7.9", "calendar-link": "^2.11.0", "class-variance-authority": "^0.7.1", "classnames": "^2.5.1", "clsx": "^2.1.1", "daisyui": "^4.12.23", "date-fns": "^4.1.0", "formik": "^2.4.6", "formik-validator-zod": "^2.2.0", "framer-motion": "^12.19.1", "lodash": "^4.17.21", "lucide-react": "^0.518.0", "next": "^14.2.18", "next-http-proxy-middleware": "^1.2.6", "next-themes": "^0.4.6", "nuqs": "^2.4.3", "react": "18.3.1", "react-daisyui": "^5.0.5", "react-day-picker": "^9.7.0", "react-dom": "18.3.1", "react-dropzone": "^14.3.5", "react-intersection-observer": "^9.16.0", "react-markdown": "^8.0.6", "react-virtualized-auto-sizer": "^1.0.25", "react-window": "^1.8.11", "remark-gfm": "^1.0.0", "sonner": "^2.0.5", "swr": "^2.3.3", "tailwind-merge": "^3.3.1", "tailwindcss-animate": "^1.0.7", "tw-animate-css": "^1.3.4", "zod": "^3.25.67", "zustand": "^5.0.3"}, "devDependencies": {"@chromatic-com/storybook": "^3.2.2", "@storybook/addon-essentials": "8.4.6", "@storybook/addon-interactions": "8.4.6", "@storybook/addon-links": "8.4.6", "@storybook/addon-onboarding": "8.4.6", "@storybook/blocks": "8.4.6", "@storybook/nextjs": "8.4.6", "@storybook/react": "8.4.6", "@storybook/test": "8.4.6", "@svgr/core": "^8.1.0", "@svgr/plugin-jsx": "^8.1.0", "@tailwindcss/postcss": "^4.1.11", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^14.0.0", "@types/lodash": "^4.17.14", "@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "@types/react-window": "^1.8.8", "@types/testing-library__jest-dom": "^5.14.9", "@typescript-eslint/eslint-plugin": "^6.21.0", "@typescript-eslint/parser": "^6.21.0", "@vitejs/plugin-react": "^4.2.1", "@vitest/coverage-v8": "^3.2.4", "@vitest/ui": "^3.2.4", "autoprefixer": "^10.4.20", "chromatic": "^11.18.1", "css-loader": "^6.8.1", "eslint": "^8.57.0", "eslint-config-next": "^14.1.3", "eslint-plugin-storybook": "^0.8.0", "jsdom": "^22.1.0", "postcss": "^8.5.6", "postcss-loader": "^7.3.3", "prettier": "^3.2.5", "sass": "^1.69.5", "sass-loader": "^13.3.2", "storybook": "^8.0.0", "style-loader": "^3.3.4", "tailwindcss": "^4.1.11", "ts-node": "^10.9.2", "typescript": "^5", "vitest": "^3.2.4"}}