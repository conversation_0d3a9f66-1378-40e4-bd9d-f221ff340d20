name: Unit Tests

on:
  push:
    branches: [main, develop, staging]
  pull_request:
    branches: [main, develop, staging]

jobs:
  test:
    name: Run Unit Tests
    runs-on: ubuntu-latest

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: "20"
          cache: "npm"

      - name: Create .npmrc
        run: |
          echo "@hugeicons:registry=https://npm.hugeicons.com/" > .npmrc
          echo "//npm.hugeicons.com/:_authToken=${{ secrets.HUGE_NPM_TOKEN }}" >> .npmrc

      - name: Install dependencies
        run: npm ci

      - name: Run tests
        run: npm test
