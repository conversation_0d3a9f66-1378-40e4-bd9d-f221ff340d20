import logging
from typing import Optional

from sqlalchemy import select
from sqlalchemy.ext.asyncio import AsyncSession

from src.db_models.job import Job
from src.db_models.user import User

logger = logging.getLogger("uvicorn")


class JobDoesNotExist(Exception):
    pass


class OperationNotAllowed(Exception):
    pass


class JobService:
    def __init__(self, db: AsyncSession):
        self._db = db

    async def get_job_by_id(self, job_id: int, user: User) -> Optional[Job]:
        return (await self._db.execute(self.get_job_by_id_query(job_id, user))).scalar()

    async def get_job_by_user_id_chat_id(self, user_id: int, chat_id: int):
        logger.info(f"get_job_by_user_id_chat_id(): user_id={user_id} chat_id={chat_id} ...")
        job_ids = (await self._db.execute(self.list_jobs_by_user_id_chat_id(user_id, chat_id))).scalars().all()
        logger.info(
            f"get_job_by_user_id_chat_id(): user_id={user_id} chat_id={chat_id} returned jobIds: {str(job_ids)}"
        )
        return job_ids

    async def get_last_job_for_user_and_chat(self, user_id: int, chat_id: int):
        logger.info(f"get_last_job_for_user_and_chat(): user_id={user_id} chat_id={chat_id} ...")
        job_id = (
            await self._db.execute(self.list_jobs_by_user_id_chat_id(user_id, chat_id).order_by(Job.id.desc()))
        ).scalar_one_or_none()
        logger.info(
            f"get_last_job_for_user_and_chat(): user_id={user_id} chat_id={chat_id} "
            f"returned jobId: {str(job_id.id if job_id is not None else 'None')}"
        )
        return job_id

    @staticmethod
    def get_job_by_id_query(job_id: int, user: User):
        return select(Job).where(Job.id == job_id, Job.userId == user.id)

    @staticmethod
    def list_jobs_by_user_query(user: User):
        return select(Job).where(Job.userId == user.id)

    @staticmethod
    def list_jobs_by_user_id_chat_id(user_id: int, chat_id: int):
        return select(Job).where(Job.userId == user_id, Job.chatId == chat_id)
