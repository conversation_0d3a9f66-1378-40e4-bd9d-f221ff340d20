import asyncio
import time
from pathlib import Path
import anthropic
import base64
import pdf2image
import io
from PIL import Image
import logging
import os
import aiofiles
import google.generativeai as genai


class DocumentSummarizationService:
    def __init__(self):
        if "ANTHROPIC_API_KEY" not in os.environ:
            raise KeyError("ANTHROPIC_API_KEY environment variable is required")
        self.anthropic_api_key = os.environ["ANTHROPIC_API_KEY"]
        self.anthropic_client = anthropic.Anthropic(api_key=self.anthropic_api_key)
        if "GEMINI_API_KEY" not in os.environ:
            raise KeyError("GEMINI_API_KEY environment variable is required")
        self.gemini_api_key = os.environ["GEMINI_API_KEY"]
        # Configure Gemini
        genai.configure(api_key=self.gemini_api_key)
        self.gemini_model = genai.GenerativeModel("gemini-2.5-flash-preview-05-20")
        # Set up logging
        self.logger = logging.getLogger("uvicorn")
        self.pdf_prompt = (
            "Describe the content of this document in detail, focusing on key information "
            "that would be useful for retrieval. Include specific details about any visible "
            "text, objects, or important information. This is a multi-page document, "
            "so please provide a comprehensive summary of all pages."
        )
        self.image_prompt = (
            "Describe the content of this image in detail, focusing on key information that "
            "would be useful for retrieval. Include specific details about any visible text, "
            "objects, or important information."
        )

    @staticmethod
    def encode_image(image: Image.Image) -> str:
        """Encode PIL Image to base64."""
        buffered = io.BytesIO()
        image.save(buffered, format="PNG")
        return base64.b64encode(buffered.getvalue()).decode("utf-8")

    @staticmethod
    def get_media_type(file_path):
        extension = file_path.lower().split(".")[-1]
        media_types = {
            "jpg": "image/jpeg",
            "jpeg": "image/jpeg",
            "png": "image/png",
            "gif": "image/gif",
            "webp": "image/webp",
            "heic": "image/heic",
            "heif": "image/heif",
        }
        return media_types.get(extension)

    def process_pdf(self, pdf_path: Path, describing_llm_prompt: str = None) -> str:
        """Convert PDF to images and get description."""
        self.logger.info(f"Converting PDF to images: {pdf_path}")

        try:
            # Convert PDF to images
            images = pdf2image.convert_from_path(pdf_path)
            self.logger.info(f"Converted PDF to {len(images)} pages")

            # Prepare messages for LLM
            messages = [
                {
                    "role": "user",
                    "content": [
                        {
                            "type": "text",
                            "text": describing_llm_prompt if describing_llm_prompt is not None else self.pdf_prompt,
                        }
                    ],
                }
            ]

            # Add each page as an image
            for i, image in enumerate(images[:3]):
                encoded_image = self.encode_image(image)
                messages[0]["content"].append(
                    {"type": "image", "source": {"type": "base64", "media_type": "image/png", "data": encoded_image}}
                )

            self.logger.info("Sending PDF pages to Haiku for analysis")

            # Get description from Haiku
            response = self.anthropic_client.messages.create(
                model="claude-3-5-sonnet-latest", messages=messages, max_tokens=1000
            )

            return response.content[0].text

        except Exception as e:
            self.logger.error(f"Error processing PDF {pdf_path}: {str(e)}")
            raise

    def process_image(self, image_path: Path, describing_llm_prompt: str = None) -> str:
        """Process a single image file by converting to PNG before sending to Anthropic."""
        self.logger.info(f"Processing image: {image_path}")

        try:
            # Open image and convert to PNG format
            with Image.open(image_path) as img:
                # Convert to RGB if image is in RGBA mode to ensure compatibility
                if img.mode == "RGBA":
                    img = img.convert("RGB")

                # No need to save to disk - we'll encode directly to base64
                encoded_image = self.encode_image(img)

            messages = [
                {
                    "role": "user",
                    "content": [
                        {"type": "text", "text": describing_llm_prompt or self.image_prompt},
                        {
                            "type": "image",
                            "source": {
                                "type": "base64",
                                "media_type": "image/png",  # Always use PNG
                                "data": encoded_image,
                            },
                        },
                    ],
                }
            ]

            response = self.anthropic_client.messages.create(
                model="claude-3-5-sonnet-latest", messages=messages, max_tokens=1000
            )

            return response.content[0].text

        except Exception as e:
            self.logger.error(f"Error processing image {image_path}: {str(e)}")
            raise

    async def get_ai_short_description(self, document_description: str = None) -> str:
        self.logger.info(f"get_ai_short_description(): {document_description[:20]}")

        try:
            short_desc_prompt = (
                "Please generate one to eight words short description " "of the following content from a file:"
            )

            # Generate content asynchronously
            response = await self.gemini_model.generate_content_async(
                contents=[f"{short_desc_prompt}\n {document_description}"],
                generation_config={"temperature": 0.4, "top_p": 0.8, "max_output_tokens": 1000},
            )

            # Extract and return the description
            description = response.text

            self.logger.info(f"get_ai_short_description(): Successfully created short summary: {description}")
            return description

        except Exception as e:
            self.logger.error(
                f"Error generating short summary with get_ai_short_description(): {document_description[:20]}: {str(e)}"
            )
            raise

    async def process_image_gemini(self, image_path: Path, describing_llm_prompt: str = None) -> str:
        """Process an image file using Google's Gemini Flash 2.0 asynchronously.

        Args:
            image_path (Path): Path to the image file
            describing_llm_prompt (str, optional): Custom prompt for image analysis

        Returns:
            str: Description of the image content

        Raises:
            Exception: If there's an error processing the image
        """
        self.logger.info(f"Processing image with Gemini Flash: {image_path}")

        try:

            # Read the image file asynchronously
            async with aiofiles.open(image_path, "rb") as image_file:
                image_data = await image_file.read()

            # Get the correct MIME type for the image
            mime_type = self.get_media_type(str(image_path))
            if not mime_type:
                raise ValueError(f"Unsupported image format: {image_path}")

            # Create the prompt
            prompt = describing_llm_prompt or self.image_prompt

            # Generate content asynchronously
            response = await self.gemini_model.generate_content_async(
                contents=[prompt, {"mime_type": mime_type, "data": image_data}],
                generation_config={"temperature": 0.4, "top_p": 0.8, "max_output_tokens": 10000},
            )

            # Extract and return the description
            description = response.text

            self.logger.info(f"Successfully processed image with Gemini Flash: {image_path}")
            return description

        except Exception as e:
            self.logger.error(f"Error processing image with Gemini Flash {image_path}: {str(e)}")
            raise

    async def process_image_of_pdf_page(self, image, index):
        img_byte_arr = io.BytesIO()
        # Run potentially blocking image operations in a thread
        await asyncio.to_thread(image.save, img_byte_arr, format="PNG")
        img_byte_arr.seek(0)
        return {"mime_type": "image/png", "data": img_byte_arr.getvalue()}

    async def process_pdf_gemini(
        self, pdf_path: Path, describing_llm_prompt: str = None, number_of_pages_to_include: int = 4
    ) -> str:
        """Convert PDF to images and get description using Gemini Flash 2.0 asynchronously.

        Args:
            pdf_path (Path): Path to the PDF file
            describing_llm_prompt (str, optional): Custom prompt for PDF analysis
            number_of_pages_to_include

        Returns:
            str: Description of the PDF content

        Raises:
            Exception: If there's an error processing the PDF
        """
        self.logger.info(f"Converting PDF to images using Gemini Flash: {pdf_path}")

        try:
            # Convert PDF to images and keep them in memory
            # Use in-memory conversion with pdf2image without saving to disk
            images = await asyncio.to_thread(
                pdf2image.convert_from_path,
                pdf_path,
                output_folder=None,
                fmt="png",
                transparent=False,
                use_pdftocairo=True,
            )
            self.logger.info(f"Converted PDF to {len(images)} pages")

            # Prepare prompt
            prompt = describing_llm_prompt if describing_llm_prompt is not None else self.pdf_prompt

            # Prepare message content
            contents = [prompt]

            # Add pages as images (up to first 4 pages)
            # Process images concurrently
            image_contents = await asyncio.gather(
                *[
                    self.process_image_of_pdf_page(image, i)
                    for i, image in enumerate(images[:number_of_pages_to_include])
                ]
            )

            # Add to contents
            contents = [prompt] + image_contents

            # Generate content asynchronously
            response = await self.gemini_model.generate_content_async(
                contents=contents, generation_config={"temperature": 0.4, "top_p": 0.8, "max_output_tokens": 300000}
            )

            # Extract and return the description
            description = response.text

            self.logger.info(f"Successfully processed PDF with Gemini Flash: {pdf_path}")
            return description

        except Exception as e:
            self.logger.error(f"Error processing PDF with Gemini Flash {pdf_path}: {str(e)}")
            raise

    def process_file(self, file_path: Path, describing_llm_prompt: str = None) -> str:
        if not file_path.exists():
            raise FileNotFoundError(f"File not found: {file_path}")
        # Process based on file type
        start_time = time.time()
        if file_path.suffix.lower() == ".pdf":
            description = self.process_pdf(file_path, describing_llm_prompt)
        else:
            description = self.process_image(file_path, describing_llm_prompt)

        processing_time = time.time() - start_time
        self.logger.info(f"Completed processing {file_path} in {processing_time:.2f} seconds")

        return description

    async def process_file_gemini(
        self, file_path: Path, describing_llm_prompt: str = None, number_of_pages_to_include: int = 4
    ) -> str:
        """Process a file using Gemini asynchronously based on file type.

        Args:
            file_path (Path): Path to the file
            describing_llm_prompt (str, optional): Custom prompt for file analysis
            number_of_pages_to_include
        Returns:
            str: Description of the file content

        Raises:
            FileNotFoundError: If the file doesn't exist
            Exception: If there's an error processing the file
        """
        if not file_path.exists():
            raise FileNotFoundError(f"File not found: {file_path}")

        # Process based on file type
        start_time = time.time()
        if file_path.suffix.lower() == ".pdf":
            description = await self.process_pdf_gemini(file_path, describing_llm_prompt, number_of_pages_to_include)
        else:
            description = await self.process_image_gemini(file_path, describing_llm_prompt)

        processing_time = time.time() - start_time
        self.logger.info(f"Completed processing {file_path} with Gemini in {processing_time:.2f} seconds")

        return description
