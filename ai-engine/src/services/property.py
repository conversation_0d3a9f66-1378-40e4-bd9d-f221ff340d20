from typing import Optional
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select
from sqlalchemy.orm import selectinload

from src.db_models.property import Property
from src.db_models.relationships import UsersProperties


class PropertyService:
    def __init__(self, db: AsyncSession):
        self.db = db

    async def get_user_primary_property(self, user_id: int) -> Optional[Property]:
        """
        Get the user's primary property (assumes user has one main property).
        In a real implementation, you might want to handle multiple properties.
        """
        # Query for user's properties through the UsersProperties relationship
        stmt = (
            select(Property)
            .join(UsersProperties, Property.id == UsersProperties.propertyId)
            .where(UsersProperties.userId == user_id)
            .options(selectinload(Property.address), selectinload(Property.building))
            .limit(1)  # Get the first property for now
        )

        result = await self.db.execute(stmt)
        return result.scalar_one_or_none()

    async def get_user_properties(self, user_id: int) -> list[Property]:
        """Get all properties for a user."""
        stmt = (
            select(Property)
            .join(UsersProperties, Property.id == UsersProperties.propertyId)
            .where(UsersProperties.userId == user_id)
            .options(selectinload(Property.address), selectinload(Property.building))
        )

        result = await self.db.execute(stmt)
        return list(result.scalars().all())

    async def get_property_by_id(self, property_id: int) -> Optional[Property]:
        """Get a property by its ID."""
        stmt = (
            select(Property)
            .where(Property.id == property_id)
            .options(selectinload(Property.address), selectinload(Property.building))
        )

        result = await self.db.execute(stmt)
        return result.scalar_one_or_none()
