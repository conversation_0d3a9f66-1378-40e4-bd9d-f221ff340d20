import base64
import binascii
import os
from pathlib import Path
import tempfile
import boto3
import logging

from src.agents.dataextractors.DataExtractorService import DataExtractorService
from src.dao.QdrantDAO import QdrantDAO
from src.schemas import DocumentType
from src.services.DocumentSummarizationService import DocumentSummarizationService

import magic


class FileParser:
    def __init__(self, summarization_service=None, qdrant_dao=None):
        self.summarization_service = summarization_service or DocumentSummarizationService()
        self.qdrant_dao = qdrant_dao or QdrantDAO()
        IS_AWS = os.environ.get("IS_AWS", "false")
        if IS_AWS == "false":
            self.s3_client = boto3.client(
                "s3",
                aws_access_key_id=os.environ.get("AWS_ACCESS_KEY_ID", "test_key_id"),
                aws_secret_access_key=os.environ.get("AWS_SECRET_ACCESS_KEY", "test_key"),
            )
        else:
            self.s3_client = boto3.client("s3", region_name=os.environ.get("AWS_REGION", "eu-west-2"))

        # Map mime types to extensions
        self.MIME_TO_EXT = {
            "application/pdf": ".pdf",
            "image/png": ".png",
            "image/jpeg": ".jpg",
            "image/gif": ".gif",
            "image/heic": ".heic",
            "image/heif": ".heif",
        }

        self.logger = logging.getLogger("uvicorn")

    def _download_from_s3(self, file_key: str, bucket_name: str, local_path: Path) -> None:
        """Download file from S3 to local path."""
        try:
            self.s3_client.download_file(bucket_name, file_key, str(local_path))
        except Exception as e:
            self.logger.error(f"Error downloading file from S3: {str(e)}")
            raise ValueError(f"Failed to download file from S3: {str(e)}")

    async def process_file(
        self,
        user_id: int,
        document_id: int,
        file_key: str,
        bucket_name: str,
        doc_type: DocumentType,
        data_extractor_service: DataExtractorService,
    ) -> None:
        """
        Process file from S3 URL and store results in Qdrant.

        Args:
            user_id: User identifier
            document_id: Document identifier
            file_key: S3 file key
            bucket_name: S3 bucket name
            doc_type: Type of the document (pdf, jpg, etc.)
            data_extractor_service: DataExtractorService

        Raises:
            ValueError: If file download or processing fails
            Exception: For other unexpected errors
        """
        with tempfile.NamedTemporaryFile(suffix=f".{doc_type}", delete=True) as temp_file:
            temp_path = Path(temp_file.name)
            try:
                # Download file from S3
                self._download_from_s3(file_key, bucket_name, temp_path)

                is_file_relevant, content = await data_extractor_service.process_file(temp_path, document_id, user_id)

                if content:
                    # Store results in Qdrant
                    await self.qdrant_dao.upsert(
                        user_id=str(user_id),
                        document_id=str(document_id),
                        bucket_name=bucket_name,
                        content=content,
                        file_key=file_key,
                    )

            except Exception as e:
                self.logger.error(f"Error processing file: {str(e)}")
                raise

    async def process_file_for_appliance_analysis_base64(self, base64_data: str, describing_llm_prompt: str) -> str:
        try:
            # Decode base64
            file_content = base64.b64decode(base64_data)

            # Detect mime type
            mime = magic.Magic(mime=True)
            file_type = mime.from_buffer(file_content)

            extension = self.MIME_TO_EXT.get(file_type)
            if not extension:
                raise ValueError(f"Unsupported file type: {file_type}")

            with tempfile.NamedTemporaryFile(suffix=extension, delete=True) as temp_file:
                temp_path = Path(temp_file.name)
                temp_path.write_bytes(file_content)

                content = await self.summarization_service.process_file_gemini(temp_path, describing_llm_prompt)
                return content

        except binascii.Error as e:
            raise ValueError(f"Invalid base64 data: {str(e)}")
        except Exception as e:
            raise Exception(f"Failed to process file: {str(e)}")

    async def process_file_for_appliance_analysis(
        self,
        user_id: int,
        document_id: int,
        file_key: str,
        bucket_name: str,
        doc_type: DocumentType,
        describing_llm_prompt: str,
    ) -> str:
        """
        Process file from S3 URL and store results in Qdrant.

        Args:
            user_id: User identifier
            document_id: Document identifier
            file_key: S3 file key
            bucket_name: S3 bucket name
            doc_type: Type of the document (pdf, jpg, etc.)

        Raises:
            ValueError: If file download or processing fails
            Exception: For other unexpected errors
        """
        with tempfile.NamedTemporaryFile(suffix=f".{doc_type}", delete=True) as temp_file:
            temp_path = Path(temp_file.name)
            try:
                # Download file from S3
                self._download_from_s3(file_key, bucket_name, temp_path)

                # Process file using DocumentSummarizationService
                content = await self.summarization_service.process_file_gemini(temp_path, describing_llm_prompt)
                return content

            except Exception as e:
                self.logger.error(f"Error processing file: {str(e)}")
                raise
