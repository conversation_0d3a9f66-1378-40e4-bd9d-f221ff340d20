from enum import Enum
from typing import Optional

from sqlalchemy import Column, ForeignKey, Table
from sqlalchemy.orm import Mapped, mapped_column, relationship

from .base import BaseModel

# Many-to-many relationship tables
users_types = Table(
    "users_types",
    BaseModel.metadata,
    Column("user_id", Foreign<PERSON>ey("users.id"), primary_key=True),
    Column("type_id", ForeignKey("user_types.id"), primary_key=True),
)

users_addresses = Table(
    "users_addresses",
    BaseModel.metadata,
    Column("user_id", ForeignKey("users.id"), primary_key=True),
    Column("address_id", ForeignKey("addresses.id"), primary_key=True),
)


class UserPropertyRelationType(Enum):
    ownerAndOccupier = "ownerAndOccupier"
    landlord = "landlord"
    tenant = "tenant"
    managingProfessional = "managingProfessional"


class UsersProperties(BaseModel):
    __tablename__ = "users_properties"

    userId: Mapped[int] = mapped_column(ForeignKey("users.id"), primary_key=True)
    propertyId: Mapped[int] = mapped_column(ForeignKey("properties.id"), primary_key=True)
    relationType: Mapped[Optional[UserPropertyRelationType]]
    user: Mapped["User"] = relationship(back_populates="usersProperties")
    property: Mapped["Property"] = relationship()


jobs_professionals = Table(
    "jobs_professionals",
    BaseModel.metadata,
    Column("job_id", ForeignKey("jobs.id"), primary_key=True),
    Column("professional_id", ForeignKey("professionals.id"), primary_key=True),
)
