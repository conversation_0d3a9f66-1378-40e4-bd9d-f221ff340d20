from typing import List, Optional
from datetime import datetime

from sqlalchemy import Integer, String, Boolean, false, DateTime
from sqlalchemy.ext.associationproxy import AssociationProxy
from sqlalchemy.ext.associationproxy import association_proxy
from sqlalchemy.orm import relationship, mapped_column, Mapped

from .base import BaseModel
from .relationships import users_types, users_addresses, UserPropertyRelationType


class User(BaseModel):
    __tablename__ = "users"

    id: Mapped[int] = mapped_column(Integer, primary_key=True, index=True)
    clerkId: Mapped[str] = mapped_column(String, unique=True, index=True, nullable=False)
    hubspotId: Mapped[Optional[str]] = mapped_column(String, unique=True, index=True, nullable=True)
    hubspotSyncAt: Mapped[Optional[datetime]] = mapped_column(DateTime, nullable=True)
    # contact details:
    email: Mapped[str] = mapped_column(String, unique=True, index=True, nullable=False)
    isEmailVerified: Mapped[bool] = mapped_column(<PERSON>olean, server_default=false())
    isWelcomeEmailSent: Mapped[bool] = mapped_column(<PERSON>olean, server_default=false())
    firstName: Mapped[str]
    lastName: Mapped[Optional[str]]
    phoneNumber: Mapped[Optional[str]]
    isPhoneNumberVerified: Mapped[Optional[bool]]
    # personalization:
    mainUsage: Mapped[Optional[UserPropertyRelationType]]
    diyProficiency: Mapped[Optional[str]]
    projectManagementAbilityAndWillingness: Mapped[Optional[str]]
    propertyImprovementInterest: Mapped[Optional[str]]
    preferenceForDIYOrProfessionals: Mapped[Optional[str]]
    aestheticPreferences: Mapped[Optional[str]]
    householdPeople: Mapped[Optional[str]]
    householdChildren: Mapped[Optional[str]]
    householdPets: Mapped[Optional[str]]
    vehicle: Mapped[Optional[str]]
    ecoConsciousness: Mapped[Optional[str]]
    budgetConsciousness: Mapped[Optional[str]]
    brandAffinity: Mapped[Optional[str]]
    occupation: Mapped[Optional[str]]
    dailyWorkRoutineGeneralAvailability: Mapped[Optional[str]]
    # relationships:
    types: Mapped[List["UserType"]] = relationship(
        "UserType", secondary=users_types, back_populates="users", lazy="selectin"
    )
    addresses: Mapped[List["Address"]] = relationship("Address", secondary=users_addresses, back_populates="users")
    usersProperties: Mapped[List["UsersProperties"]] = relationship(
        "UsersProperties", back_populates="user", cascade="all,delete"
    )
    properties: AssociationProxy[List["Property"]] = association_proxy("usersProperties", "property")
    documents: Mapped[List["Document"]] = relationship(back_populates="user")
    messages: Mapped[List["Message"]] = relationship(back_populates="user")
    chats: Mapped[List["Chat"]] = relationship(back_populates="user")
    projects: Mapped[List["Project"]] = relationship(back_populates="user")
    jobs: Mapped[List["Job"]] = relationship(back_populates="user")
    bills: Mapped[List["Bill"]] = relationship(back_populates="user")
    insurances: Mapped[List["Insurance"]] = relationship(back_populates="user")
    legals: Mapped[List["Legal"]] = relationship(back_populates="user")
    buildingsManaged: Mapped[List["Building"]] = relationship(back_populates="managingAgent")
