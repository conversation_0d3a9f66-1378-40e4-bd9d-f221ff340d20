from enum import Enum
from typing import Optional, List

from sqlalchemy import Integer, Foreign<PERSON>ey, String, Boolean
from sqlalchemy.orm import relationship, mapped_column, Mapped

from .base import BaseModel


class DocumentStatusType(Enum):
    saved = "saved"
    processing = "processing"
    processingCompleted = "processingCompleted"
    irrelevant = "irrelevant"
    error = "error"


class DocumentCategoryType(Enum):
    propertyDetails = "propertyDetails"
    insurance = "insurance"
    billsAndSubscriptions = "billsAndSubscriptions"
    legal = "legal"
    appliance = "appliance"
    buildingInformation = "buildingInformation"
    other = "other"


class Document(BaseModel):
    __tablename__ = "documents"

    id: Mapped[int] = mapped_column(Integer, primary_key=True, index=True)
    type: Mapped[Optional[str]]  # user photo, warranty etc.
    source: Mapped[str]  # user, system, customer_support
    s3Key: Mapped[str] = mapped_column(String, unique=True, nullable=False)
    s3Bucket: Mapped[str]
    fileExtension: Mapped[str]
    sizeInKiloBytes: Mapped[int]
    originalFileName: Mapped[str]
    aiGeneratedFileName: Mapped[Optional[str]]
    browserMimeType: Mapped[Optional[str]]
    uploadContext: Mapped[Optional[str]]
    status: Mapped[DocumentStatusType]
    hasStatusBeenDisplayed: Mapped[bool] = mapped_column(Boolean, default=False, server_default="false")
    category: Mapped[Optional[DocumentCategoryType]]
    label: Mapped[Optional[str]]
    errorMessage: Mapped[Optional[str]]
    # relationships:
    userId: Mapped[Optional[int]] = mapped_column(ForeignKey("users.id"))
    user: Mapped["User"] = relationship(back_populates="documents")
    propertyId: Mapped[Optional[int]] = mapped_column(ForeignKey("properties.id"))
    property: Mapped["Property"] = relationship(back_populates="documents")
    messageId: Mapped[Optional[int]] = mapped_column(ForeignKey("messages.id"))
    message: Mapped["Message"] = relationship(back_populates="documents")
    appliances: Mapped[List["Appliance"]] = relationship(back_populates="invoiceReceiptDocument")
    bills: Mapped[List["Bill"]] = relationship(back_populates="document")
    insurances: Mapped[List["Insurance"]] = relationship(back_populates="document")
    legals: Mapped[List["Legal"]] = relationship(back_populates="document")
