from datetime import datetime
from typing import Optional, Any

from sqlalchemy import DateT<PERSON>, Enum, Foreign<PERSON><PERSON>, Integer
from sqlalchemy.orm import relationship, mapped_column, Mapped
from sqlalchemy.sql import func

from .base import BaseModel


class Message(BaseModel):
    __tablename__ = "messages"

    id: Mapped[int] = mapped_column(Integer, primary_key=True, index=True)
    content: Mapped[Optional[str]]
    type: Mapped[str] = mapped_column(Enum("text", "image", "diagnostic_report", name="message_type"))
    senderType: Mapped[str] = mapped_column(Enum("user", "system", "customer_support", name="sender_type"))
    timestamp: Mapped[datetime] = mapped_column(DateTime, server_default=func.now(), nullable=False)
    additionalData: Mapped[Optional[dict[str, Any]]]
    chatId: Mapped[int] = mapped_column(ForeignKey("chats.id"))
    chat: Mapped["Chat"] = relationship(back_populates="messages")
    userId: Mapped[Optional[int]] = mapped_column(ForeignKey("users.id"))
    user: Mapped[Optional["User"]] = relationship(back_populates="messages")
    documents: Mapped[list["Document"]] = relationship(back_populates="message", lazy="selectin")
