from decimal import Decimal
from enum import Enum
from typing import Optional

from sqlalchemy import Integer, ForeignKey
from sqlalchemy.orm import relationship, mapped_column, Mapped

from .base import BaseModel


class BillType(Enum):
    energyGas = "energyGas"
    energyElectricity = "energyElectricity"
    energyGasAndElectricity = "energyGasAndElectricity"
    water = "water"
    securitySystem = "securitySystem"
    councilTax = "councilTax"
    mortgage = "mortgage"
    rent = "rent"
    serviceCharge = "serviceCharge"
    groundRent = "groundRent"
    tv = "tv"
    internet = "internet"
    mobile = "mobile"
    other = "other"


class PaymentMethodType(Enum):
    directDebit = "directDebit"
    debitCreditCard = "debitCreditCard"
    standingOrder = "standingOrder"
    prepayment = "prepayment"
    other = "other"


class Bill(BaseModel):
    __tablename__ = "bills"

    id: Mapped[int] = mapped_column(Integer, primary_key=True, index=True)
    type: Mapped[BillType]
    provider: Mapped[Optional[str]]
    accountNumber: Mapped[Optional[str]]
    contractDates: Mapped[Optional[str]]
    tariff: Mapped[Optional[str]]
    estimatedMonthlyUsage: Mapped[Optional[str]]
    estimatedMonthlyCost: Mapped[Optional[Decimal]]
    paymentMethod: Mapped[Optional[PaymentMethodType]]
    otherDetails: Mapped[Optional[str]]  # subscription description, broadband speed, limits, tv channels etc.
    isRecentBill: Mapped[bool]
    # relationships:
    userId: Mapped[int] = mapped_column(ForeignKey("users.id"))
    user: Mapped["User"] = relationship(back_populates="bills")
    propertyId: Mapped[Optional[int]] = mapped_column(ForeignKey("properties.id"))
    property: Mapped[Optional["Property"]] = relationship(back_populates="bills")
    documentId: Mapped[Optional[int]] = mapped_column(ForeignKey("documents.id"))
    document: Mapped["Document"] = relationship(back_populates="bills")
