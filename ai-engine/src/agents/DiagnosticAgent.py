import logging
import uuid

from fastapi import Depends
from langchain_anthropic import Chat<PERSON>nthropic

# from langchain_together import Together
from langsmith import Client
import langsmith as ls
import os

from langsmith.run_helpers import get_run_tree_context

from src.agents.ImageAgentGemini import ImageAgentGemini
from src.agents.JobSummaryExtractor import JobSummaryExtractor
from src.dao.QdrantDAO import QdrantDAO
from src.dependencies import get_job_service
from src.exceptions import LLMOverloadedException
from src.schemas import ParseFileRequest, ImageUrl
from src.services.FileParser import FileParser
from typing import List, Tuple, Optional

from src.services.jobs import JobService

session_id = "agents-playground-session" + str(uuid.uuid4())
thread_id = uuid.uuid4()
conversation_id = uuid.uuid4()


class DiagnosticAgent:
    def __init__(self, job_service: JobService, agent_prompt):
        # Initialize LangSmith
        self._together_ai_api_key = os.environ["TOGETHER_AI_API_KEY"]
        self.anthropic_api_key = os.environ["ANTHROPIC_API_KEY"]
        self._langsmith_key = os.environ["LANGSMITH_API_KEY"]
        self._PROMPT_VERSION = "DiagnosticAgent_v5.txt"
        os.environ["LANGCHAIN_TRACING_V2"] = "true"
        os.environ["LANGSMITH_ENDPOINT"] = "https://eu.api.smith.langchain.com"

        # Check if the variable is not present in the environment
        if "LANGCHAIN_PROJECT" not in os.environ:
            # Set the environment variable
            os.environ["LANGCHAIN_PROJECT"] = "property_manager"

        self.logger = logging.getLogger("uvicorn")
        self.client = Client()
        # Option 1: Using session_id

        self.job_summary_extractor = JobSummaryExtractor()

        self.file_parser_instance = FileParser()
        self.qdrant_dao = QdrantDAO()
        self.image_agent = ImageAgentGemini()

        self._job_service: JobService = job_service

        # Initialize LLMs
        self.diagnostic_llm = ChatAnthropic(
            model="claude-3-7-sonnet-20250219",
            # temperature=0.3,
            max_tokens=2000,
        )

        # Initialize LLMs
        # self.diagnostic_llm = Together(
        #     model="deepseek-ai/DeepSeek-V3",
        #     temperature=0.3,
        #     max_tokens=2000,
        #     together_api_key=self._together_ai_api_key,
        # )

        # Initialize LLMs
        # self.diagnostic_llm = ChatOpenAI(
        #     model="o1-mini",
        #     # temperature=0.3,
        #     max_tokens=2000,
        #     openai_api_key="********************************************************************************************************************************************************************"
        # )

        # Load system prompt from file
        self.system_prompt = agent_prompt

    @staticmethod
    def get_content(obj):
        # Check if the object has the 'filed_content' attribute
        if hasattr(obj, "content"):
            return obj.content
        else:
            return obj

    @staticmethod
    def format_conversation_for_context(conversation_history):
        """Format conversation history for the LLM in a way that encourages active responses"""
        if not conversation_history:
            return ""

        formatted_history = "Previous conversation:\n"
        for msg in conversation_history[-10:]:  # Only include last 3 messages for context
            formatted_history += f"Human: {msg[0]}\nAssistant's response: {msg[1]}\n\n"
        return formatted_history

    @staticmethod
    def map_to_image_urls(query, image_data: List[str]) -> List[ImageUrl]:

        # Create an ImageUrl object for each URL in the list
        return [ImageUrl(imageUrl=url, description=query) for url in image_data]

    async def process_response(self, response):
        """Process the LLM response and handle any embedded commands"""
        # Extract job summary if present
        processed_response, job_summary = self.job_summary_extractor.extract_job_summary(response)

        # Store job summary if found
        if job_summary:
            # You might want to store this in your database or return it separately
            self.logger.info(f"Job summary extracted: {str(job_summary)}")
            # For now, we'll just use the processed_response without the job summary JSON
            response = processed_response

        # Process any image search commands - get trimmed response and image URLs
        trimmed_response, image_urls, all_image_clickable_urls = await self.image_agent.process_response(response)

        return trimmed_response, job_summary, image_urls, all_image_clickable_urls

    @staticmethod
    def format_image_descriptions(image_data: list[str]) -> str:
        """
        Converts a list of image descriptions into a formatted string.

        Args:
            image_data: List of image description strings

        Returns:
            str: Formatted string with numbered image descriptions

        Example:
            >>> descriptions = ["A cat sleeping", "A dog running"]
            >>> format_image_descriptions(descriptions)
            'IMAGE 1 DESCRIPTION:\nA cat sleeping\n\nIMAGE 2 DESCRIPTION:\nA dog running'
        """
        if not image_data:
            return ""

        formatted_descriptions = []
        for index, description in enumerate(image_data, 1):
            formatted_descriptions.append(f"IMAGE {index} DESCRIPTION:\n{description}")

        return "\n\n".join(formatted_descriptions)

    @ls.traceable(
        run_type="llm",
        name="Process Query Call Decorator",
        tags=["process_user_msg"],
        metadata={"session_id": session_id, "thread_id": thread_id, "conversation_id": conversation_id},
    )
    async def process_query(self, user_input, user_id, chat_id, image_data: list[str]):
        """Process user query with optional image data"""

        self.logger.info(f"process_query(): user_id={user_id}, " f"chat_id={chat_id} user_input={user_input}")

        job_id = None
        job = await self._job_service.get_last_job_for_user_and_chat(user_id, chat_id)
        if job:
            job_id = job.id

        run_context = get_run_tree_context()
        if run_context:
            # Update metadata
            run_context.metadata["session_id"] = f"chat_id: {chat_id}"
            run_context.metadata["user_id"] = f"user_id: {user_id}"

        # If we have image data, process it first
        image_context = ""
        if image_data:
            image_context = f"\nAttached images analysis: {self.format_image_descriptions(image_data)}\n"

        conversation_history = await self.qdrant_dao.update_chat_search_on_history_change(user_id, chat_id)
        formatted_conversation_history = self.format_conversation_for_context(conversation_history)

        # self.logger.info(f"=====> Conv History: {formatted_conversation_history}")

        # Format the prompt to encourage active responses
        full_prompt = (
            f"{self.system_prompt}\n\n"
            f"{formatted_conversation_history}"
            f"{image_context}"
            f"Current user message: {user_input}\n\n"
            "Respond directly to the user's current message, considering any relevant context from the conversation history. "
            "Do not simulate or script a conversation - provide a single, direct response as Alfie:"
        )

        try:
            # Get response from DeepSeek
            llm_output = await self.diagnostic_llm.ainvoke(full_prompt)
            response = self.get_content(llm_output)

            # Update conversation history with original response
            conversation_history.append((user_input, response))
            await self.qdrant_dao.upsert_chat_history(user_id, chat_id, conversation_history)

            # Process any embedded commands
            processed_response, job_summary, image_urls, all_image_clickable_urls = await self.process_response(
                response
            )

            # Update job_summary if job_is was stored by ai engine
            if job_id and job_summary:
                job_summary = job_summary.model_copy(update={"jobId": job_id})

            return processed_response, job_summary, image_urls, all_image_clickable_urls

        except Exception as e:
            self.logger.error(f"Error processing query: {str(e)}")

            # Check for string patterns if it's another type of exception
            error_str = str(e).lower()
            # Sometimes Anthropic returns 500 below while having infra problems:
            # ERROR: Error processing query: Error code: 500 - {'type': 'error', 'error': {'type': 'api_error', 'message': 'Internal server error'}}
            if (("529" in error_str and ("overload" in error_str or "too many requests" in error_str)) or
                    ("500" in error_str and "api_error" in error_str)):
                raise LLMOverloadedException(detail=f"LLM service overloaded.")
            raise e

    async def process_next_message(self, user_input, user_id, chat_id, attachments: list[ParseFileRequest] = None):
        image_data: list[str] = []
        appliance_prompt = (
            "Analyze this image and describe any relevant details about the appliance or issue shown. "
            "Try to identify appliance type and make and model if possible"
        )
        if attachments:
            for file in attachments:
                if file.base64:
                    file_analysis = await self.file_parser_instance.process_file_for_appliance_analysis_base64(
                        file.base64, appliance_prompt
                    )
                    image_data.append(file_analysis)
                else:
                    file_analysis = await self.file_parser_instance.process_file_for_appliance_analysis(
                        file.userId,
                        file.documentId,
                        file.documentS3Key,
                        file.documentS3Bucket,
                        file.type,
                        appliance_prompt,
                    )
                    image_data.append(file_analysis)

        response, job_summary, image_urls, all_image_clickable_urls = await self.process_query(
            user_input, user_id, chat_id, image_data
        )

        self.logger.info(
            f"=====> process_next_message(user_id={user_id}, chat_id={chat_id}) "
            f"response: {response} job_summary: {job_summary}"
        )

        # response_final = await response
        return response, job_summary, image_urls, all_image_clickable_urls
