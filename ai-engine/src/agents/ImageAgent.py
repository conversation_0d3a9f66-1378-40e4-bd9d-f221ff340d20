import logging
import re
import os
import aiohttp
from typing import List, Tuple
import traceback

from src.schemas import ImageUrl


class ImageAgent:
    """
    Agent for handling image search requests and processing LLM responses that contain
    image search commands.
    """

    def __init__(self):
        """Initialize the ImageAgent with necessary configurations."""
        self.logger = logging.getLogger("uvicorn")
        self.serpr_api_key = os.environ.get("SERPR_API_KEY")
        if not self.serpr_api_key:
            self.logger.warning("SERPR_API_KEY not found in environment variables")

        # Default endpoint for Serpr API image search
        self.serpr_endpoint = "https://google.serper.dev/images"

    async def search_images(self, query: str, max_results: int = 3) -> List[dict]:
        """
        Search for images using Serpr API.

        Args:
            query: The search query
            max_results: Maximum number of results to return (default: 3)

        Returns:
            List of image results with URLs and metadata
        """
        try:
            # Append "home uk" to queries to narrow results to UK home-related images
            if "not sure" not in query.lower():
                query_final = f"{query} home uk vertical image"
            else:
                query_final = query

            headers = {"X-API-KEY": self.serpr_api_key, "Content-Type": "application/json"}

            payload = {"q": query_final, "gl": "uk", "engine": "google_images", "num": max_results}  # Set locale to UK

            async with aiohttp.ClientSession() as session:
                async with session.post(self.serpr_endpoint, headers=headers, json=payload) as response:
                    if response.status != 200:
                        error_text = await response.text()
                        self.logger.error(f"Serpr API error: {response.status} - {error_text}")
                        return []

                    result = await response.json()

            # Extract image information from results
            images = result.get("images", [])
            return images[:max_results]

        except Exception as e:
            self.logger.error(f"Error searching for images: {str(e)}")
            self.logger.error(f"Stack trace: {traceback.format_exc()}")
            return []

    async def handle_search_request(self, query: str, max_images=3) -> Tuple[str, List[ImageUrl]]:
        """
        Process a single image search query and return image URLs.

        Args:
            query: The search query
            max_images: max number of images

        Returns:
            Tuple containing:
                - Formatted string with image search results
                - List of ImageUrl objects
        """
        try:
            images = await self.search_images(query, max_images)
            image_urls = []

            if not images:
                return f"No image results found for '{query}'", []

            # Extract image URLs and create ImageUrl objects
            for img in images:
                if "imageUrl" in img and img["imageUrl"]:
                    image_obj = ImageUrl(
                        imageUrl=img["imageUrl"],
                        description=query,  # Use the search query as description
                        source=img.get("source"),  # Get source if available
                    )
                    image_urls.append(image_obj)

            # Format results into a readable string
            results = [f"- {img.get('title', 'Untitled')}: {img.get('imageUrl', 'No URL')}" for img in images]

            result_text = f"Image search results for '{query}':\n" + "\n".join(results)
            return result_text, image_urls

        except Exception as e:
            self.logger.error(f"Error in handle_search_request: {str(e)}")
            self.logger.error(f"Stack trace: {traceback.format_exc()}")
            return f"Error performing image search: {str(e)}", []

    async def process_response(self, response: str) -> Tuple[str, List[ImageUrl], List[ImageUrl]]:
        """
        Process an LLM response, handling any embedded image search commands.

        Args:
            response: The LLM response text

        Returns:
            Tuple containing:
                - Processed response with image search commands removed
                - List of ImageUrl objects found during searches
                - List of all clickable image URLs
        """

        self.logger.info(f"Message with search request: {response}")
        processed_response = response
        all_image_urls: List[ImageUrl] = []
        all_image_clickable_urls: List[ImageUrl] = []

        # Check for web search command
        if "[web search:" in response:
            # Find all search queries
            search_queries = re.findall(r"\[web search: (.*?)\]", response)

            # Process each query
            for query in search_queries:
                search_results_text, image_url_objects = await self.handle_search_request(query)
                all_image_urls.extend(image_url_objects)

                # Remove the search command from the response
                processed_response = processed_response.replace(f"[web search: {query}]", "")

        # Check for OptionsImage phrase
        options_image_pattern = r"\[OptionsImage: (.*?)\]"
        options_matches = re.findall(options_image_pattern, processed_response)

        for options_match in options_matches:
            # Split the options by semicolon and strip whitespace
            options = [opt.strip() for opt in options_match.split(";")]

            # Process each option as a search query
            for option in options:
                search_results_text, image_url_objects = await self.handle_search_request(option, 1)
                # Add to the clickable URLs list
                all_image_clickable_urls.extend(image_url_objects)

            # Remove the entire [OptionsImage: ...] phrase from the response
            processed_response = processed_response.replace(f"[OptionsImage: {options_match}]", "")

        return processed_response, all_image_urls, all_image_clickable_urls
