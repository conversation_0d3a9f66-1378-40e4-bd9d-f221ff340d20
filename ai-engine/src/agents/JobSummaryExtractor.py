import re
import json
import logging
from typing import Optional
from pydantic import BaseModel, ValidationError

from src.schemas import JobSummary


class JobSummaryExtractor:
    def __init__(self):
        self.logger = logging.getLogger("uvicorn")

    def extract_job_summary(self, response: str) -> tuple[str, Optional[JobSummary]]:
        """
        Extract job summary from response if present and remove the JSON block.
        Supports two formats:
        1. [[jobSummary = {...}]]
        2. ```json\njobSummary = {...}\n```

        Args:
            response: The response text from the LLM

        Returns:
            tuple: (processed_response, job_summary)
                - processed_response: Response with job summary JSON block removed
                - job_summary: JobSummary object if found, None otherwise
        """
        # Store the original message
        original_message = response


        # Try the original format first
        job_summary_pattern = r"\[\s*\[\s*([\s\S]*?)jobSummary\s*=\s*\{([\s\S]*?)\}\s*([\s\S]*?)\]\s*\]"
        match = re.search(job_summary_pattern, response)

        # If not found, try the new format
        if not match:
            job_summary_pattern = r"```(?:json)?\s*jobSummary\s*=\s*\{([\s\S]*?)\}\s*```"
            match = re.search(job_summary_pattern, response)

            if match:
                # Extract the JSON string and the full block
                job_summary_block = match.group(0)
                job_summary_text = match.group(1)
            else:
                # No job summary found in either format
                return response, None
        else:
            # Extract from the original format
            job_summary_block = match.group(0)
            job_summary_text = match.group(2)

        # Remove the job summary block from the response
        processed_response = response.replace(job_summary_block, "")

        # Create job summary object
        job_summary = JobSummary(messageContainingJobSummary=original_message)

        try:
            # Clean up the JSON text
            cleaned_json = "{"

            # Process each line of the JSON data
            for line in job_summary_text.split("\n"):
                # Skip empty lines
                if not line.strip():
                    continue

                # Clean up the line and format it as valid JSON
                line = line.strip()

                # Check if line contains a key-value pair
                if ":" in line:
                    key, value = line.split(":", 1)
                    key = key.strip()
                    value = value.strip()

                    # Remove trailing comma if present
                    if value.endswith(","):
                        value = value[:-1]

                    # Ensure keys are properly quoted
                    if not key.startswith('"'):
                        key = f'"{key}"'

                    # Ensure values are properly quoted if they're strings
                    if (
                        not (value.startswith('"') or value.startswith("'"))
                        and not value.startswith("{")
                        and not value.lower() in ["true", "false", "null"]
                    ):
                        value = f'"{value}"'

                    cleaned_json += f"{key}: {value},"

            # Remove trailing comma and close the JSON object
            if cleaned_json.endswith(","):
                cleaned_json = cleaned_json[:-1]
            cleaned_json += "}"

            # Parse the JSON data
            job_data = json.loads(cleaned_json)

            # Update job summary with extracted data
            job_summary.jobHeadline = job_data.get("jobHeadline", "")
            job_summary.jobSubTitle = job_data.get("jobSubTitle", "")
            job_summary.jobDetails = job_data.get("jobDetails", "")
            job_summary.jobDate = job_data.get("jobDate", "")
            job_summary.jobTimeOfDay = job_data.get("jobTimeOfDay", "")

        except (json.JSONDecodeError, ValidationError, Exception) as e:
            self.logger.error(f"Error parsing job summary: {str(e)}")
            job_summary.errorDuringParsing = str(e)

        return processed_response, job_summary
