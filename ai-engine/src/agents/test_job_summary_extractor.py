import unittest
from unittest.mock import Mock, patch
import logging

from src.agents.JobSummaryExtractor import JobSummaryExtractor
from src.schemas import JobSummary

import logging

logging.basicConfig(level=logging.INFO)

class TestJobSummaryExtractor(unittest.TestCase):
    def setUp(self):
        """Set up test fixtures before each test method."""
        self.extractor = JobSummaryExtractor()
        # Mock the logger to avoid logging setup issues in tests
        self.extractor.logger = Mock(spec=logging.Logger)

    def test_extract_job_summary_original_format(self):
        """Test extraction with the original [[jobSummary = {...}]] format."""
        response = """[[
jobSummary = {
  jobHeadline: "Leaking Kitchen Tap",
  jobSubTitle: "Kitchen Mixer Tap Leaking from Base",
  jobDetails: "Kitchen mixer tap (2-5 years old) is constantly leaking from the base even when turned off. This suggests worn internal components such as washers or O-rings that need replacement. The leak is persistent and requires professional attention.",
  jobDate: "Urgent (within 24-48 hours)",
  jobTimeOfDay: "Weekdays after 6 PM"
}
]]"""

        processed_response, job_summary = self.extractor.extract_job_summary(response)

        import sys
        print(f"processed_response={processed_response}", file=sys.stderr)
        # Verify the job summary block was removed
        self.assertNotIn("jobSummary", processed_response)
        #self.assertIn("Here's some response text before", processed_response.strip())
        #
        # # Verify job summary was extracted correctly
        self.assertIsNotNone(job_summary)
        self.assertIsInstance(job_summary, JobSummary)
        self.assertEqual(job_summary.jobHeadline, "Leaking Kitchen Tap")
        self.assertEqual(job_summary.jobSubTitle, "Kitchen Mixer Tap Leaking from Base")
        self.assertEqual(job_summary.jobDate, "Urgent (within 24-48 hours)")
        self.assertEqual(job_summary.jobTimeOfDay, "Weekdays after 6 PM")
        self.assertIn("Kitchen mixer tap", job_summary.jobDetails)


