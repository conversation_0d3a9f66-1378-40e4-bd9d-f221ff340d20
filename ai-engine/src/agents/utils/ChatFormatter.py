import logging



class ChatFormatter:

    def __init__(self, default_prompt_version=None):
        self.logger = logging.getLogger("uvicorn")

    @staticmethod
    def format_conversation_for_context(conversation_history, number_of_recent_msg_to_include: int = 10):
        """Format conversation history for the LLM in a way that encourages active responses"""
        if not conversation_history:
            return ""

        formatted_history = "Previous conversation:\n"
        for msg in conversation_history[-number_of_recent_msg_to_include:]:  # Only include last 10 messages for context
            formatted_history += f"Human: {msg[0]}\nAssistant's response: {msg[1]}\n\n"
        return formatted_history
