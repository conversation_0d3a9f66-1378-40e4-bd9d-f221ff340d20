import asyncio
import time
from pathlib import Path
import base64
from typing import Union

import pdf2image
import io
from PIL import Image
import logging
import os
import aiofiles
import json
import google.generativeai as genai

from src.schemas import RelevantDocumentClassification, NotRelevantDocumentClassification


class DocumentLabellingAgent:
    categories_and_labels = {
        "Property": [
            "Property Deeds",
            "Floor Plans",
            "Home Survey",
            "Property Boundaries",
            "Planning Permissions",
            "Property photo",
            "Property video",
            "Renovation Plans",
            "Contractor Agreements",
            "Building Control Certificates",
            "Receipts for Major Works",
            "Before/After Photos",
            "Service History Records",
            "Annual Maintenance Tasks",
            "Contractor Contact Information"
        ],
        "Insurance": [
            "Home Insurance Policy",
            "Contents Insurance",
            "Warranty Certificates",
            "Claims History"
        ],
        "Bills": [
            "Water Bills",
            "Electricity Bill (Usage)",
            "Gas Bill (Usage)",
            "Council Tax / Property Tax",
            "Rent / Mortgage",
            "Internet / Broadband Bill",
            "Telephone Bill (Landline / Mobile)",
            "TV Bill (Cable/Satellite Subscription / TV License)",
            "Loan Repayments / Credit Cards",
            "Subscription Services (e.g., Streaming, Software)",
            "Utility Company Contracts"
        ],
        "Appliances": [
            "User Manuals",
            "Warranty Cards",
            "Guarantee",
            "Purchase Receipts",
            "Service Records",
            "Gas Certificates",
            "Electrical Safety Certificates",
            "Energy Performance Certificate (EPC)"
        ],
        "Legal": [
            "Mortgage Agreement",
            "Property Title",
            "Leasehold Documents (if applicable)",
            "Party Wall Agreements",
            "Rights of Way"
        ],
        "Inventory Records": [
            "Home Contents Inventory",
            "Valuable Items Documentation",
            "Photos of Belongings"
        ],
        "Other": []
    }

    _labelling_prompt = None

    def __init__(self):
        if "GEMINI_API_KEY" not in os.environ:
            raise KeyError("GEMINI_API_KEY environment variable is required")

        self._gemini_api_key = os.environ["GEMINI_API_KEY"]

        genai.configure(api_key=self._gemini_api_key)
        self.gemini_model = genai.GenerativeModel("gemini-2.5-flash-preview-04-17")
        self.logger = logging.getLogger("uvicorn")

        if DocumentLabellingAgent._labelling_prompt is None:
            DocumentLabellingAgent._labelling_prompt = self._construct_labelling_prompt()
            self.logger.info(f"prompt==============\n\n {DocumentLabellingAgent._labelling_prompt} \n\n")

        self.labelling_prompt = DocumentLabellingAgent._labelling_prompt

    @classmethod
    def _construct_labelling_prompt(cls) -> str:
        categories_str = ""
        for category, labels in cls.categories_and_labels.items():
            labels_str = "\n".join([f"* {label}" for label in labels])
            categories_str += f"{category}\n{labels_str}\n\n"

        prompt = f"""
            You are a document classification agent specialized in property management documents.
            Analyze the content of this document and categorize it according to the following categories and labels:

            {categories_str}

            If the document is not related to property management or services, classify it as "NOT_RELEVANT".

            If the document belongs to a valid category but doesn't match any existing label, you can assign a new label.

            Return ONLY a JSON response with the following structure:
            For relevant documents:
            {{
                "category": "[Category Name]",
                "label": "[Label Name]",
                "isNewLabel": true|false
            }}

            For non-relevant documents:
            {{
                "category": "NOT_RELEVANT"
            }}

            Focus on the document's content, purpose, and usage in property management context to make your decision.
            """
        return prompt

    @staticmethod
    def encode_image(image: Image.Image) -> str:
        """Encode PIL Image to base64."""
        buffered = io.BytesIO()
        image.save(buffered, format="PNG")
        return base64.b64encode(buffered.getvalue()).decode("utf-8")

    @staticmethod
    def get_media_type(file_path):
        extension = file_path.lower().split(".")[-1]
        media_types = {
            "jpg": "image/jpeg",
            "jpeg": "image/jpeg",
            "png": "image/png",
            "gif": "image/gif",
            "webp": "image/webp",
            "heic": "image/heic",
            "heif": "image/heif",
        }
        return media_types.get(extension)

    async def process_image_of_pdf_page(self, image, index):
        img_byte_arr = io.BytesIO()
        # Run blocking image operations in a thread
        await asyncio.to_thread(image.save, img_byte_arr, format="PNG")
        img_byte_arr.seek(0)
        return {"mime_type": "image/png", "data": img_byte_arr.getvalue()}

    async def process_pdf(self, pdf_path: Path) -> Union[
        RelevantDocumentClassification, NotRelevantDocumentClassification]:
        self.logger.info(f"Converting PDF to images using Gemini: {pdf_path}")

        try:
            # Convert PDF to images and keep them in memory
            images = await asyncio.to_thread(
                pdf2image.convert_from_path,
                pdf_path,
                output_folder=None,
                fmt="png",
                transparent=False,
                use_pdftocairo=True,
            )
            self.logger.info(f"Converted PDF to {len(images)} pages")

            image_contents = await asyncio.gather(
                *[self.process_image_of_pdf_page(image, i) for i, image in enumerate(images[:4])]
            )

            # Add to contents
            contents = [self.labelling_prompt] + image_contents

            response = await self.gemini_model.generate_content_async(
                contents=contents,
                generation_config={
                    "temperature": 0.1,
                    "top_p": 0.8,
                    "max_output_tokens": 200000,
                },
            )

            classification_result = self._extract_classification_from_response(response.text)

            self.logger.info(f"Successfully processed PDF with Gemini: {pdf_path}")
            self.logger.info(f"Classification result: {classification_result}")

            return classification_result

        except Exception as e:
            self.logger.error(f"Error processing PDF with Gemini {pdf_path}: {str(e)}")
            raise

    async def process_image(self, image_path: Path) -> Union[
        RelevantDocumentClassification, NotRelevantDocumentClassification]:
        self.logger.info(f"Processing image with Gemini: {image_path}")

        try:
            async with aiofiles.open(image_path, "rb") as image_file:
                image_data = await image_file.read()

            mime_type = self.get_media_type(str(image_path))
            if not mime_type:
                raise ValueError(f"Unsupported image format: {image_path}")

            response = await self.gemini_model.generate_content_async(
                contents=[self.labelling_prompt, {"mime_type": mime_type, "data": image_data}],
                generation_config={
                    "temperature": 0.1,  # Low temperature for more deterministic responses
                    "top_p": 0.8,
                    "max_output_tokens": 200000,
                },
            )

            self.logger.info(f"LLM RESPONSE: {response.text}")
            classification_result = self._extract_classification_from_response(response.text)

            self.logger.info(f"Successfully processed image with Gemini: {image_path}")

            return classification_result

        except Exception as e:
            self.logger.error(f"Error processing image with Gemini {image_path}: {str(e)}")
            raise

    def _extract_classification_from_response(self, response_text: str) -> Union[
        RelevantDocumentClassification, NotRelevantDocumentClassification]:
        """Extract and validate classification from LLM response, returning a Pydantic model."""
        try:
            # Try to find JSON object in the response
            start_idx = response_text.find('{')
            end_idx = response_text.rfind('}') + 1

            if start_idx >= 0 and end_idx > start_idx:
                json_str = response_text[start_idx:end_idx]
                parsed_json = json.loads(json_str)

                # Validate and create appropriate Pydantic model
                if "category" in parsed_json:
                    if parsed_json["category"] == "NOT_RELEVANT":
                        return NotRelevantDocumentClassification(category="NOT_RELEVANT")

                    if "label" in parsed_json and "isNewLabel" in parsed_json:
                        # Verify the category is valid
                        if parsed_json["category"] in self.categories_and_labels or parsed_json["category"] == "Other":
                            return RelevantDocumentClassification(**parsed_json)

            # If we couldn't find valid JSON, return a default "Other" classification
            return RelevantDocumentClassification(
                category="Other",
                label="Unclassified Document",
                isNewLabel=True
            )

        except json.JSONDecodeError as e:
            self.logger.error(f"Failed to parse JSON from response: {response_text}. Error: {e}")
            return RelevantDocumentClassification(
                category="Other",
                label="Unclassified Document",
                isNewLabel=True
            )
        except Exception as e:
            self.logger.error(f"Unexpected error parsing response: {response_text}. Error: {e}")
            return RelevantDocumentClassification(
                category="Other",
                label="Unclassified Document",
                isNewLabel=True
            )

    async def process_file(self, file_path: Path) -> Union[
        RelevantDocumentClassification, NotRelevantDocumentClassification]:
        if not file_path.exists():
            raise FileNotFoundError(f"File not found: {file_path}")

        start_time = time.time()

        if file_path.suffix.lower() == ".pdf":
            classification_result = await self.process_pdf(file_path)
        else:
            classification_result = await self.process_image(file_path)

        processing_time = time.time() - start_time
        self.logger.info(f"Completed classification of {file_path} with Gemini in {processing_time:.2f} seconds")

        return classification_result

