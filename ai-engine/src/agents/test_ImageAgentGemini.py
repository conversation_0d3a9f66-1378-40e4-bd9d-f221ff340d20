import pytest
import aiohttp
import asyncio
from unittest.mock import AsyncMock, patch
import io
from PIL import Image

from src.agents.ImageAgentGemini import ImageAgentGemini


class TestDownloadAndProcessImageWithSession:

    @pytest.fixture
    def image_agent(self):
        """Create an ImageAgentGemini instance for testing."""
        with patch.dict('os.environ', {
            'PSE_API_KEY': 'test_key',
            'PSE_CX': 'test_cx',
            'GEMINI_API_KEY': 'test_gemini_key'
        }):
            return ImageAgentGemini()

    @pytest.fixture
    def mock_session(self):
        """Create a mock aiohttp ClientSession."""
        return AsyncMock(spec=aiohttp.ClientSession)

    @pytest.fixture
    def sample_jpeg_bytes(self):
        """Create sample JPEG image bytes for testing."""
        # Create a small test image
        img = Image.new('RGB', (100, 100), color='red')
        img_io = io.BytesIO()
        img.save(img_io, format='JPEG', quality=85)
        return img_io.getvalue()

    @pytest.mark.asyncio
    async def test_valid_url_returns_image_bytes(self, image_agent, mock_session, sample_jpeg_bytes):
        """Test that a valid URL returns image bytes."""
        # Arrange
        test_url = "https://example.com/test-image.jpg"

        # Mock the HTTP response
        mock_response = AsyncMock()
        mock_response.status = 200
        mock_response.headers = {'Content-Type': 'image/jpeg'}
        mock_response.read = AsyncMock(return_value=sample_jpeg_bytes)

        # Configure the session mock
        mock_session.get.return_value.__aenter__.return_value = mock_response

        # Act
        result = await image_agent.download_and_process_image_with_session(
            session=mock_session,
            url=test_url
        )

        # Assert
        assert result is not None
        assert isinstance(result, bytes)
        assert len(result) > 0

        # Verify the session was called with correct parameters
        mock_session.get.assert_called_once()
        call_args = mock_session.get.call_args
        assert call_args[0][0] == test_url  # First positional argument is the URL

        # Verify headers were set
        headers = call_args[1]['headers']
        assert 'User-Agent' in headers
        assert 'Accept' in headers
        assert 'Referer' in headers

    @pytest.mark.asyncio
    async def test_http_404_returns_none(self, image_agent, mock_session):
        """Test that HTTP 404 response returns None."""
        # Arrange
        test_url = "https://example.com/nonexistent-image.jpg"

        mock_response = AsyncMock()
        mock_response.status = 404

        mock_session.get.return_value.__aenter__.return_value = mock_response

        # Act
        result = await image_agent.download_and_process_image_with_session(
            session=mock_session,
            url=test_url
        )

        # Assert
        assert result is None

    @pytest.mark.asyncio
    async def test_non_image_content_type_returns_none(self, image_agent, mock_session):
        """Test that non-image content type returns None."""
        # Arrange
        test_url = "https://example.com/test.html"

        mock_response = AsyncMock()
        mock_response.status = 200
        mock_response.headers = {'Content-Type': 'text/html'}

        mock_session.get.return_value.__aenter__.return_value = mock_response

        # Act
        result = await image_agent.download_and_process_image_with_session(
            session=mock_session,
            url=test_url
        )

        # Assert
        assert result is None

    @pytest.mark.asyncio
    async def test_timeout_returns_none(self, image_agent, mock_session):
        """Test that timeout exception returns None."""
        # Arrange
        test_url = "https://example.com/slow-image.jpg"

        mock_session.get.side_effect = asyncio.TimeoutError()

        # Act
        result = await image_agent.download_and_process_image_with_session(
            session=mock_session,
            url=test_url
        )

        # Assert
        assert result is None

    @pytest.mark.asyncio
    async def test_client_error_returns_none(self, image_agent, mock_session):
        """Test that aiohttp ClientError returns None."""
        # Arrange
        test_url = "https://example.com/error-image.jpg"

        mock_session.get.side_effect = aiohttp.ClientError("Connection failed")

        # Act
        result = await image_agent.download_and_process_image_with_session(
            session=mock_session,
            url=test_url
        )

        # Assert
        assert result is None

    @pytest.mark.asyncio
    async def test_small_image_returns_none(self, image_agent, mock_session):
        """Test that images smaller than 100 bytes return None."""
        # Arrange
        test_url = "https://example.com/tiny-image.jpg"
        tiny_image_data = b"tiny"  # Less than 100 bytes

        mock_response = AsyncMock()
        mock_response.status = 200
        mock_response.headers = {'Content-Type': 'image/jpeg'}
        mock_response.read = AsyncMock(return_value=tiny_image_data)

        mock_session.get.return_value.__aenter__.return_value = mock_response

        # Act
        result = await image_agent.download_and_process_image_with_session(
            session=mock_session,
            url=test_url
        )

        # Assert
        assert result is None

    @pytest.fixture
    def sample_png_with_transparency_bytes(self):
        """Create sample PNG image with transparency for testing."""
        # Create a test image with transparency
        img = Image.new('RGBA', (200, 200), color=(255, 0, 0, 128))  # Semi-transparent red
        img_io = io.BytesIO()
        img.save(img_io, format='PNG')
        return img_io.getvalue()

    @pytest.fixture
    def large_image_bytes(self):
        """Create a large image for testing resizing."""
        # Create a large test image (2000x2000 pixels) that will definitely exceed 500KB
        img = Image.new('RGB', (2000, 2000), color='blue')
        # Add some noise to make it less compressible
        import random
        pixels = img.load()
        for i in range(0, 2000, 10):
            for j in range(0, 2000, 10):
                pixels[i, j] = (random.randint(0, 255), random.randint(0, 255), random.randint(0, 255))

        img_io = io.BytesIO()
        img.save(img_io, format='JPEG', quality=95)
        return img_io.getvalue()

    @pytest.mark.asyncio
    async def test_transparency_removal_converts_to_rgb(self, image_agent, mock_session,
                                                        sample_png_with_transparency_bytes):
        """Test that PNG with transparency is converted to RGB JPEG with white background."""
        # Arrange
        test_url = "https://example.com/transparent-image.png"

        mock_response = AsyncMock()
        mock_response.status = 200
        mock_response.headers = {'Content-Type': 'image/png'}
        mock_response.read = AsyncMock(return_value=sample_png_with_transparency_bytes)

        mock_session.get.return_value.__aenter__.return_value = mock_response

        # Act
        result = await image_agent.download_and_process_image_with_session(
            session=mock_session,
            url=test_url
        )

        # Assert
        assert result is not None
        assert isinstance(result, bytes)

        # Verify the result is a JPEG without transparency
        processed_img = Image.open(io.BytesIO(result))
        assert processed_img.format == 'JPEG'
        assert processed_img.mode == 'RGB'  # Should be RGB, not RGBA

        # Verify the image has reasonable dimensions
        width, height = processed_img.size
        assert width > 0 and height > 0

    @pytest.mark.asyncio
    async def test_large_image_resizing_and_compression(self, image_agent, mock_session, large_image_bytes):
        """Test that large images are resized and compressed to meet size limits."""
        # Arrange
        test_url = "https://example.com/large-image.jpg"
        max_size = 500 * 1024  # 500KB limit

        original_size = len(large_image_bytes)
        original_img = Image.open(io.BytesIO(large_image_bytes))
        print(f"Original image size: {original_size} bytes ({original_size / 1024:.1f}KB)")
        print(f"Original dimensions: {original_img.size}")
        print(f"Max allowed size: {max_size} bytes ({max_size / 1024:.1f}KB)")

        mock_response = AsyncMock()
        mock_response.status = 200
        mock_response.headers = {'Content-Type': 'image/jpeg'}
        mock_response.read = AsyncMock(return_value=large_image_bytes)

        mock_session.get.return_value.__aenter__.return_value = mock_response

        # Act
        result = await image_agent.download_and_process_image_with_session(
            session=mock_session,
            url=test_url,
            max_size=max_size
        )

        # Assert
        assert result is not None
        assert isinstance(result, bytes)

        # Debug: Check the processed result
        processed_size = len(result)
        processed_img = Image.open(io.BytesIO(result))
        print(f"Processed image size: {processed_size} bytes ({processed_size / 1024:.1f}KB)")
        print(f"Processed dimensions: {processed_img.size}")

        # Verify the result is within size limit
        assert len(result) <= max_size, f"Processed image {processed_size} bytes exceeds limit {max_size} bytes"

        # Verify the result is still a valid JPEG
        assert processed_img.format == 'JPEG'
        assert processed_img.mode == 'RGB'

        # Verify the image was resized (should be smaller than original 2000x2000)
        width, height = processed_img.size

        # Only assert resizing if original was actually too large
        if original_size > max_size:
            assert max(width, height) <= 1000, f"Image not resized: {width}x{height}, expected max dimension ≤ 1000"

        assert width > 0 and height > 0