from fastapi import <PERSON><PERSON><PERSON>, Depends, HTTPException, Security
from fastapi.security import HTTPAuthorizationCredentials, HTT<PERSON><PERSON>earer
from pydantic import BaseModel
from typing import Optional
import uvicorn
import os, configparser

# Import your QueryProcessor
# from QueryProcessor import QueryProcessor

# Initialize FastAPI app
app = FastAPI(title="AI Base RAG", description="API for querying Nigels house data", version="1.0.0")

# Configure security
security = HTTPBearer()
API_TOKEN = "heyalfie2024London"


# Singleton pattern for QueryProcessor
class ProcessorSingleton:
    _instance = None

    @classmethod
    def get_instance(cls):
        # if cls._instance is None:
        #     # Read the config file
        #     config = configparser.ConfigParser()
        #     config.read("./config/ai-base-rag.conf")
        #     api_key = config['OpenAI'].get('API_KEY', None)
        #     os.environ["OPENAI_API_KEY"] = api_key
        #
        #     cls._instance = QueryProcessor(
        #         qdrant_path="qdrant_storage",
        #         openai_api_key=api_key
        #     )
        # return cls._instance
        return "a"


class Query(BaseModel):
    question: str


class Response(BaseModel):
    answer: str


def verify_token(credentials: HTTPAuthorizationCredentials = Security(security)):
    if credentials.credentials != API_TOKEN:
        raise HTTPException(
            status_code=401, detail="Invalid authentication token", headers={"WWW-Authenticate": "Bearer"}
        )
    return credentials.credentials


@app.post("/query", response_model=Response)
async def process_query(query: Query, token: str = Depends(verify_token)):
    """
    Process a natural language query and return the answer.
    """
    try:
        if os.path.exists("./qdrant_storage/.lock"):
            os.remove("./qdrant_storage/.lock")
        # processor = ProcessorSingleton.get_instance()
        # answer = processor.process_query(query.question)
        answer = "Exemplar response"
        return Response(answer=answer)
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@app.post("/message", response_model=Response)
async def message():
    """
    Process a natural language query and return the answer.
    """
    try:
        answer = "Exemplar message response"
        return Response(answer=answer)
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@app.get("/health")
async def health_check():
    """
    Simple health check endpoint
    """
    return {"status": "healthy"}


if __name__ == "__main__":
    # To activate this project's virtualenv, run pipenv shell.
    # Alternatively, run a command inside the virtualenv with pipenv run.
    #
    # Initialize the processor once before starting the server
    ProcessorSingleton.get_instance()

    uvicorn.run("main:app", host="0.0.0.0", port=8000, reload=False)  # Keep reload enabled for development
