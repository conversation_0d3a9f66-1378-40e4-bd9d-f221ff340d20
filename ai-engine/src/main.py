import logging
from contextlib import asynccontextmanager

import uvicorn
from fastapi import <PERSON><PERSON><PERSON>, Request
from fastapi.exceptions import RequestValidationError
from fastapi_pagination import add_pagination
from fastapi.responses import JSONResponse
import sentry_sdk

from src.database import sessionmanager, run_migrations
from src.dependencies import backend_api
from src.logging_conf import setup_logging
from src.routers import healthcheck, message, parsefile
from os import environ

from fastapi.middleware.cors import CORSMiddleware

IS_AWS = environ.get("IS_AWS", "false")
SENTRY_ENVIRONMENT = environ.get("SENTRY_ENVIRONMENT")
SENTRY_DSN = environ.get("SENTRY_DSN")

# Set up logging configuration
setup_logging()
logger = logging.getLogger("uvicorn")


if SENTRY_ENVIRONMENT and SENTRY_DSN:
    sentry_sdk.init(
        dsn=SENTRY_DSN,
        # Set traces_sample_rate to 1.0 to capture 100%
        # of transactions for tracing.
        traces_sample_rate=1.0,
        # Set profiles_sample_rate to 1.0 to profile 100%
        # of sampled transactions.
        # We recommend adjusting this value in production.
        profiles_sample_rate=1.0,
        environment=SENTRY_ENVIRONMENT,
    )
else:
    logger.warning("Sentry environment variables not set")


@asynccontextmanager
async def lifespan(fastapi_app: FastAPI):
    logger.info("Starting application")
    logger.info("Running alembic upgrade head")
    if IS_AWS == "false":
        await run_migrations()
    logger.info("Alembic upgrade head done")
    yield
    if sessionmanager.is_engine_initialized:
        # Close the DB connection
        await sessionmanager.close()
        await backend_api.close()


# pipenv --python python3.12 install
# /usr/src/.venv/bin/uvicorn src.main:app --host 0.0.0.0 --port 8003 --reload
# HOST=0.0.0.0 npm start

app = FastAPI(lifespan=lifespan)


@app.exception_handler(RequestValidationError)
async def validation_exception_handler(request: Request, exc: RequestValidationError):
    """
    Handle Pydantic validation errors and log them
    """
    error_detail = exc.errors()
    logger.error(f"Validation error: {error_detail}")
    return JSONResponse(status_code=422, content={"detail": error_detail})


app.include_router(healthcheck.router)
app.include_router(message.router)
app.include_router(parsefile.router)


app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:3000", "http://*************:3000"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)


add_pagination(app)

if __name__ == "__main__":
    uvicorn.run(app, host="0.0.0.0", port=8001)
