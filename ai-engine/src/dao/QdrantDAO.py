import logging
import os
from dataclasses import dataclass
import uuid

from qdrant_client import Async<PERSON>drant<PERSON>lient, models, QdrantClient
from typing import List

from src.aiclients.OpenAIClient import OpenAIClient


@dataclass
class SearchResult:
    score: float
    userId: str
    documentId: str
    bucketName: str
    fileKey: str
    content: str


@dataclass
class ChatSearchResult:
    score: float
    userId: str
    chatId: str
    chatSummary: str
    conversationHistory: list


class QdrantDAO:
    def __init__(self, qdrant_client_asynch=None, qdrant_client_synch=None, openai_client=None):
        self.logger = logging.getLogger("uvicorn")
        self._qdrant_url = os.environ["QDRANT_DB_URL"]
        self._qdrant_api_key = os.environ["QDRANT_API_KEY"]
        self._collection_name = "users_files"
        self._collection_name_chats = "users_chats"
        self._collection_name_chats_jobs = "users_chats_jobs"
        self._collection_name_chats_search = "users_chats_search"  # New collection for searchable chats
        self._vector_size = 1536
        self._distance = models.Distance.COSINE
        self._qdrant_client_asynch = qdrant_client_asynch or AsyncQdrantClient(
            url=self._qdrant_url, api_key=self._qdrant_api_key
        )
        self._qdrant_client_synch = qdrant_client_synch or QdrantClient(
            url=self._qdrant_url, api_key=self._qdrant_api_key
        )
        self._init_collection()
        self._openai_client = openai_client or OpenAIClient()

    def _init_collection(self):
        if not self._qdrant_client_synch.collection_exists(self._collection_name):
            self._qdrant_client_synch.create_collection(
                collection_name=self._collection_name,
                vectors_config=models.VectorParams(size=self._vector_size, distance=models.Distance.COSINE),
            )
        if not self._qdrant_client_synch.collection_exists(self._collection_name_chats):
            self._qdrant_client_synch.create_collection(
                collection_name=self._collection_name_chats,
                vectors_config=models.VectorParams(size=1, distance=models.Distance.COSINE),
            )
        if not self._qdrant_client_synch.collection_exists(self._collection_name_chats_jobs):
            self._qdrant_client_synch.create_collection(
                collection_name=self._collection_name_chats_jobs,
                vectors_config=models.VectorParams(size=1, distance=models.Distance.COSINE),
            )
        # Initialize the new searchable chats collection
        if not self._qdrant_client_synch.collection_exists(self._collection_name_chats_search):
            self._qdrant_client_synch.create_collection(
                collection_name=self._collection_name_chats_search,
                vectors_config=models.VectorParams(size=self._vector_size, distance=models.Distance.COSINE),
            )

    async def upsert(
        self,
        user_id: str,
        document_id: str,
        bucket_name: str,
        content: str,
        file_key: str,
        embedding: list[float] = None,
    ):
        if embedding is None:
            embedding = self._openai_client.get_embedding(content)

        point = models.PointStruct(
            id=str(uuid.uuid5(uuid.NAMESPACE_DNS, f"{document_id}_{file_key}_{content}")),
            payload={
                "userId": user_id,
                "documentId": document_id,
                "bucketName": bucket_name,
                "fileKey": file_key,
                "content": content,
            },
            vector=embedding,
        )

        await self._qdrant_client_asynch.upsert(collection_name=self._collection_name, points=[point])

    async def query(
        self, user_id: str, query: str, limit: int = 5, query_embedding: list[float] = None
    ) -> List[SearchResult]:
        if query_embedding is None:
            query_embedding = self._openai_client.get_embedding(query)

        results = await self._qdrant_client_asynch.search(
            collection_name=self._collection_name,
            query_vector=query_embedding,
            query_filter=models.Filter(
                must=[models.FieldCondition(key="userId", match=models.MatchValue(value=user_id))]
            ),
            limit=limit,
        )

        return [
            SearchResult(
                score=result.score,
                userId=result.payload["userId"],
                documentId=result.payload["documentId"],
                bucketName=result.payload["bucketName"],
                fileKey=result.payload["fileKey"],
                content=result.payload["content"],
            )
            for result in results
        ]

    async def upsert_chat_history(self, user_id: str, chat_id: str, conversation_history):
        self.logger.info(f"upsert_chat_history(): user_id: {user_id}, chat_id: {chat_id} conversation_history: \n\n {conversation_history} \n\n")
        point = models.PointStruct(
            id=str(uuid.uuid5(uuid.NAMESPACE_DNS, f"{user_id}_{chat_id}")),
            payload={"userId": user_id, "chatId": chat_id, "conversation_history": conversation_history},
            vector=[0],
        )

        await self._qdrant_client_asynch.upsert(collection_name=self._collection_name_chats, points=[point])

    async def get_chat_history(self, user_id: str, chat_id: str):
        my_filter = models.Filter(
            must=[
                models.FieldCondition(key="userId", match=models.MatchValue(value=user_id)),
                models.FieldCondition(key="chatId", match=models.MatchValue(value=chat_id)),
            ]
        )

        results = await self._qdrant_client_asynch.search(
            collection_name=self._collection_name_chats,
            query_vector=[0],  # Dummy vector since we're only using filters
            query_filter=my_filter,
            limit=1,
        )

        if results:
            self.logger.info(f"get_chat_history(): user_id: {user_id}, chat_id: {chat_id} \n\n {results[0].payload["conversation_history"]} \n\n")
            return results[0].payload["conversation_history"]

        self.logger.info(
            f"get_chat_history(): user_id: {user_id}, chat_id: {chat_id} \n\n [] \n\n")
        return []

    def _create_chat_summary(self, conversation_history: list) -> str:
        if not conversation_history:
            return ""

        # Extract user messages and assistant responses
        messages = []

        if not conversation_history:
            return ""

        for msg in conversation_history:
            messages.append(f"Human: {msg[0]}\nAssistant's response: {msg[1]}\n\n")

        # Join messages and truncate if too long (to avoid token limits)
        summary = " | ".join(messages)
        if len(summary) > 8000:  # Rough token limit consideration
            summary = summary[:8000] + "..."

        return summary

    async def upsert_searchable_chat(
            self,
            user_id: str,
            chat_id: str,
            conversation_history: list,
            chat_summary: str = None,
            embedding: list[float] = None
    ):
        if chat_summary is None:
            chat_summary = self._create_chat_summary(conversation_history)

        if not chat_summary:
            self.logger.warning(f"Empty chat summary for user_id: {user_id}, chat_id: {chat_id}")
            return

        if embedding is None:
            embedding = self._openai_client.get_embedding(chat_summary)

        point = models.PointStruct(
            id=str(uuid.uuid5(uuid.NAMESPACE_DNS, f"{user_id}_{chat_id}_search")),
            payload={
                "userId": user_id,
                "chatId": chat_id,
                "chatSummary": chat_summary,
                "conversationHistory": conversation_history,
            },
            vector=embedding,
        )

        await self._qdrant_client_asynch.upsert(
            collection_name=self._collection_name_chats_search,
            points=[point]
        )

        self.logger.info(f"upsert_searchable_chat(): user_id: {user_id}, chat_id: {chat_id}, summary length: {len(chat_summary)}")

    async def search_chat_history(
        self,
        query: str,
        user_id: str,
        limit: int = 5,
        query_embedding: list[float] = None
    ) -> List[ChatSearchResult]:
        if query_embedding is None:
            query_embedding = self._openai_client.get_embedding(query)

        results = await self._qdrant_client_asynch.search(
            collection_name=self._collection_name_chats_search,
            query_vector=query_embedding,
            query_filter=models.Filter(
                must=[models.FieldCondition(key="userId", match=models.MatchValue(value=user_id))]
            ),
            limit=limit,
        )

        search_results = [
            ChatSearchResult(
                score=result.score,
                userId=result.payload["userId"],
                chatId=result.payload["chatId"],
                chatSummary=result.payload["chatSummary"],
                conversationHistory=result.payload["conversationHistory"],
            )
            for result in results
        ]

        self.logger.info(
            f"search_chat_history(): query: '{query}', user_id: {user_id}, found {len(search_results)} results")

        return search_results

    async def update_chat_search_on_history_change(self, user_id: str, chat_id: str, conversation_history: list):
        # Update regular chat history
        await self.upsert_chat_history(user_id, chat_id, conversation_history)

        # Update searchable chat
        await self.upsert_searchable_chat(user_id, chat_id, conversation_history)
