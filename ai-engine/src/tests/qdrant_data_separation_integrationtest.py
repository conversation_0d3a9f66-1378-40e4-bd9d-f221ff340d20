import asyncio
from random import random

import pytest

from src.dao.QdrantDAO import QdrantDAO


@pytest.mark.asyncio
async def main():
    dao = QdrantDAO()
    embedding = [random() for _ in range(1536)]

    # Insert vectors for user_1 and user_2 with the same embedding
    await dao.upsert("user_1", "doc1", "url1", "content1", "file1", embedding)
    await dao.upsert("user_1", "doc2", "url2", "content2", "file2", embedding)
    await dao.upsert("user_2", "doc3", "url3", "content3", "file3", embedding)
    await dao.upsert("user_2", "doc4", "url4", "content4", "file4", embedding)

    # Query for user_1
    user1_results = await dao.query("user_1", "test query", 5, embedding)
    assert len(user1_results) == 2
    assert all(result["userId"] == "user_1" for result in user1_results)
    print(str(user1_results))

    # Query for user_2
    user2_results = await dao.query("user_2", "test query", 5, embedding)
    assert len(user2_results) == 2
    assert all(result["userId"] == "user_2" for result in user2_results)


asyncio.run(main())
