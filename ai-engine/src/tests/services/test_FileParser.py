import pytest
from unittest.mock import Mock, patch, AsyncMock

from src.services.FileParser import FileParser


@pytest.fixture
def file_parser():
    with patch("boto3.client") as mock_s3:
        with patch("src.services.DocumentSummarizationService.DocumentSummarizationService") as mock_summ:
            with patch("src.dao.QdrantDAO") as mock_qdrant:
                parser = FileParser(summarization_service=mock_summ, qdrant_dao=mock_qdrant)
                parser.s3_client = mock_s3
                yield parser


@pytest.fixture
def mock_data_extractor():
    with patch("src.agents.dataextractors.DataExtractorService.DataExtractorService") as mock_extractor:
        # Use AsyncMock instead of Mock for async methods
        mock_extractor.process_file = AsyncMock(return_value=(True, "Test content"))
        yield mock_extractor


@pytest.mark.asyncio
async def test_process_file_success(file_parser, mock_data_extractor):
    # Mock S3 download
    file_parser.s3_client.download_file = Mock()

    # Mock Qdrant upsert
    file_parser.qdrant_dao.upsert = AsyncMock()

    # Test data
    test_data = {
        "user_id": 123,
        "document_id": 456,
        "file_key": "test.pdf",
        "bucket_name": "bucket1",
        "doc_type": "pdf",
        "data_extractor_service": mock_data_extractor,
    }

    # Execute
    await file_parser.process_file(**test_data)

    # Verify S3 download was called
    file_parser.s3_client.download_file.assert_called_once()

    # Verify data extractor was called
    mock_data_extractor.process_file.assert_called_once()

    # Verify Qdrant upsert was called with correct parameters
    file_parser.qdrant_dao.upsert.assert_called_once()
    call_args = file_parser.qdrant_dao.upsert.call_args[1]
    assert call_args["user_id"] == "123"
    assert call_args["document_id"] == "456"
    assert call_args["bucket_name"] == "bucket1"
    assert call_args["content"] == "Test content"
    assert call_args["file_key"] == "test.pdf"


@pytest.mark.asyncio
async def test_process_file_processing_error(file_parser, mock_data_extractor):
    # Mock S3 download
    file_parser.s3_client.download_file = Mock()

    # Mock data extractor to fail - use AsyncMock for async methods
    mock_data_extractor.process_file = AsyncMock(side_effect=Exception("Processing error"))

    with pytest.raises(Exception, match="Processing error"):
        await file_parser.process_file(
            user_id=123,
            document_id=456,
            file_key="hello.pdf",
            bucket_name="bucket1",
            doc_type="pdf",
            data_extractor_service=mock_data_extractor,
        )


@pytest.mark.asyncio
async def test_process_file_no_content_returned(file_parser, mock_data_extractor):
    # Mock S3 download
    file_parser.s3_client.download_file = Mock()

    # Mock data extractor to return no content - use AsyncMock for async methods
    mock_data_extractor.process_file = AsyncMock(return_value=(True, None))

    # Mock Qdrant upsert
    file_parser.qdrant_dao.upsert = AsyncMock()

    # Execute
    await file_parser.process_file(
        user_id=123,
        document_id=456,
        file_key="test.pdf",
        bucket_name="bucket1",
        doc_type="pdf",
        data_extractor_service=mock_data_extractor,
    )

    # Verify S3 download was called
    file_parser.s3_client.download_file.assert_called_once()

    # Verify data extractor was called
    mock_data_extractor.process_file.assert_called_once()

    # Verify Qdrant upsert was NOT called since no content was returned
    file_parser.qdrant_dao.upsert.assert_not_called()


@pytest.mark.asyncio
async def test_process_file_irrelevant_document(file_parser, mock_data_extractor):
    # Mock S3 download
    file_parser.s3_client.download_file = Mock()

    # Mock data extractor to return irrelevant document (is_file_relevant=False)
    mock_data_extractor.process_file = AsyncMock(return_value=(False, "Some content"))

    # Mock Qdrant upsert
    file_parser.qdrant_dao.upsert = AsyncMock()

    # Execute
    await file_parser.process_file(
        user_id=123,
        document_id=456,
        file_key="test.pdf",
        bucket_name="bucket1",
        doc_type="pdf",
        data_extractor_service=mock_data_extractor,
    )

    # Verify S3 download was called
    file_parser.s3_client.download_file.assert_called_once()

    # Verify data extractor was called
    mock_data_extractor.process_file.assert_called_once()

    file_parser.qdrant_dao.upsert.assert_called_once()
    call_args = file_parser.qdrant_dao.upsert.call_args[1]
    assert call_args["user_id"] == "123"
    assert call_args["document_id"] == "456"
    assert call_args["content"] == "Some content"


@pytest.mark.asyncio
async def test_process_file_irrelevant_document_no_content(file_parser, mock_data_extractor):
    # Mock S3 download
    file_parser.s3_client.download_file = Mock()

    # Mock data extractor to return irrelevant document with no content
    mock_data_extractor.process_file = AsyncMock(return_value=(False, None))

    # Mock Qdrant upsert
    file_parser.qdrant_dao.upsert = AsyncMock()

    # Execute
    await file_parser.process_file(
        user_id=123,
        document_id=456,
        file_key="test.pdf",
        bucket_name="bucket1",
        doc_type="pdf",
        data_extractor_service=mock_data_extractor,
    )

    # Verify S3 download was called
    file_parser.s3_client.download_file.assert_called_once()

    # Verify data extractor was called
    mock_data_extractor.process_file.assert_called_once()

    # Verify Qdrant upsert was NOT called since no content was returned
    file_parser.qdrant_dao.upsert.assert_not_called()


