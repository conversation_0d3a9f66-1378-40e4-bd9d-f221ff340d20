from datetime import datetime

import pytest
from fastapi import FastAPI
import os

# from src.routers.message import router

app = FastAPI()
# app.include_router(router)


# @pytest.fixture(autouse=True)
# def mock_env():
#     """Automatically set up test environment variables"""
#     os.environ["AI_ENGINE_API_KEY"] = "test_api_key"
#     yield
#     # Clean up
#     if "AI_ENGINE_API_KEY" in os.environ:
#         del os.environ["AI_ENGINE_API_KEY"]
#
#
# def test_process_message_success(client):
#     request_data = {
#         "message": {"content": "Hello", "type": "text"},
#         "messageId": "123",
#         "chatId": "123",
#         "userId": "123",
#     }
#
#     response = client.post("/message/", json=request_data, headers={"Authorization": "Bearer test_api_key"})
#
#     assert response.status_code == 200
#     data = response.json()
#     assert data["status"] == "success"
#     assert data["data"]["chatId"] == 123
#     assert data["data"]["response"]["type"] == "text"
#
#
# def test_process_message_with_diagnostic_type(client):
#     request_data = {
#         "message": {"content": "Hello", "type": "diagnostic_report"},
#         "messageId": "123",
#         "chatId": "123",
#         "userId": "123",
#     }
#
#     response = client.post("/message/", json=request_data, headers={"Authorization": "Bearer test_api_key"})
#
#     assert response.status_code == 200
#
#
# def test_process_message_invalid_token(client):
#     request_data = {
#         "message": {"content": "Hello", "type": "text"},
#         "messageId": "123",
#         "chatId": "123",
#         "userId": "123",
#     }
#
#     response = client.post("/message/", json=request_data, headers={"Authorization": "Bearer invalid_token"})
#
#     assert response.status_code == 401
#     assert response.json() == {"detail": "Invalid API key"}
#
#
# def test_process_message_no_token(client):
#     request_data = {
#         "message": {"content": "Hello", "type": "text"},
#         "messageId": "123",
#         "chatId": "123",
#         "userId": "123",
#     }
#
#     response = client.post("/message/", json=request_data)
#
#     assert response.status_code == 403
#     assert response.json() == {"detail": "Not authenticated"}
#
#
# def test_process_message_missing_api_key_config(client):
#     # Remove environment variable to simulate missing configuration
#     if "AI_ENGINE_API_KEY" in os.environ:
#         del os.environ["AI_ENGINE_API_KEY"]
#
#     request_data = {
#         "message": {"content": "Hello", "type": "text"},
#         "messageId": "123",
#         "chatId": "123",
#         "userId": "123",
#     }
#
#     response = client.post("/message/", json=request_data, headers={"Authorization": "Bearer any_token"})
#
#     assert response.status_code == 500
#     assert response.json() == {"detail": "API key configuration is missing"}
#
#
# def test_process_message_missing_chat_id(client):
#     request_data = {
#         "message": {"content": "Hello", "type": "text"},
#         "messageId": "123",
#         "userId": "123",
#     }
#
#     response = client.post("/message/", json=request_data, headers={"Authorization": "Bearer test_api_key"})
#
#     assert response.status_code == 422
#     errors = response.json()["detail"]
#     assert any("chatId" in error["loc"] for error in errors)
#
#
# def test_process_message_invalid_message_type(client):
#     request_data = {
#         "message": {"content": "Hello", "type": "invalid_type"},  # Invalid message type
#         "messageId": "123",
#         "chatId": "123",
#         "userId": "123",
#     }
#
#     response = client.post("/message/", json=request_data, headers={"Authorization": "Bearer test_api_key"})
#
#     assert response.status_code == 422
#     errors = response.json()["detail"]
#     assert any("type" in error["loc"] for error in errors)
