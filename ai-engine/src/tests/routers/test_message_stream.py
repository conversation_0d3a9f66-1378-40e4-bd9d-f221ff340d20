import pytest
import json
from unittest.mock import MagicMock, patch

from fastapi.testclient import TestClient

from src.schemas import MessageRe<PERSON>, Message, JobSummary, ImageUrl, ErrorDetail


TEST_API_KEY = "test-api-key-123"
DUMMY_API_KEY = "dummy-key-for-testing"

MOCK_USER_ID = 1
MOCK_CHAT_ID = 101
MOCK_MESSAGE_ID = 202
MOCK_REQUEST = MessageRequest(
    message=Message(content="Test message", type="text"),
    messageId=MOCK_MESSAGE_ID,
    chatId=MOCK_CHAT_ID,
    userId=MOCK_USER_ID,
    attachments=None
)

MOCK_JOB_SUMMARY = JobSummary(
    jobId=5,
    jobHeadline="Fix Leaky Faucet",
    jobDetails="Water dripping from kitchen sink faucet.",
    jobDate="Tomorrow",
    jobTimeOfDay="Morning"
)

MOCK_IMAGE_URLS = [ImageUrl(imageUrl="http://example.com/img1.jpg", description="Leaky faucet")]
MOCK_CLICKABLE_URLS = [ImageUrl(imageUrl="http://example.com/img2.jpg", description="Search results")]

async def mock_agent_stream_success():
    yield f"data: {json.dumps({'type': 'content', 'data': 'Okay, '})}\n\n"
    yield f"data: {json.dumps({'type': 'content', 'data': 'I can help '})}\n\n"
    yield f"data: {json.dumps({'type': 'content', 'data': 'with that.'})}\n\n"
    final_data = {
        "jobSummary": MOCK_JOB_SUMMARY.model_dump(exclude_none=True),
        "imageUrls": [img.model_dump(exclude_none=True) for img in MOCK_IMAGE_URLS],
        "imageClickableUrls": [img.model_dump(exclude_none=True) for img in MOCK_CLICKABLE_URLS],
    }
    yield f"data: {json.dumps({'type': 'final_data', 'data': final_data})}\n\n"
    yield f"event: end\ndata: Stream finished\n\n"

async def mock_agent_stream_error():
    yield f"data: {json.dumps({'type': 'content', 'data': 'Thinking...'})}\n\n"
    error_payload = ErrorDetail(
        code="LLM_OVERLOADED",
        message="The LLM service is currently overloaded",
        details={"original_error": "Simulated overload"}
    ).model_dump()
    yield f"event: error\ndata: {json.dumps(error_payload)}\n\n"
    yield f"event: end\ndata: Stream finished after error\n\n"

async def mock_agent_stream_no_summary():
    yield f"data: {json.dumps({'type': 'content', 'data': 'Got it.'})}\n\n"
    final_data = {
        "jobSummary": None,
        "imageUrls": None,
        "imageClickableUrls": None,
    }
    yield f"data: {json.dumps({'type': 'final_data', 'data': final_data})}\n\n"
    yield f"event: end\ndata: Stream finished\n\n"


@pytest.fixture(scope="module")
def client():
    # Apply mock before importing
    with patch('src.agents.utils.PromptLoader.PromptLoader.load_system_prompt',
               return_value="Mock system prompt for testing"):
        # Only import app after patching
        from src.main import app
        with TestClient(app) as c:
            yield c

@pytest.fixture(autouse=True)
def override_dependencies_and_env(monkeypatch):
    # other variables are set in conftest.py
    monkeypatch.setenv("AI_ENGINE_API_KEY", TEST_API_KEY)


@pytest.mark.asyncio
@patch("src.agents.DiagnosticAgentStreaming.DiagnosticAgentStreaming.process_next_message_stream", new_callable=MagicMock)
async def test_process_message_stream_success(mock_process_stream, client: TestClient):
    mock_process_stream.return_value = mock_agent_stream_success()

    headers = {"Authorization": f"Bearer {TEST_API_KEY}"}
    response = client.post("/message/stream", headers=headers, json=MOCK_REQUEST.model_dump())

    assert response.status_code == 200
    assert response.headers["content-type"] == "text/event-stream; charset=utf-8"

    lines = response.content.decode().strip().split('\n\n')

    assert lines[0] == f"data: {json.dumps({'type': 'content', 'data': 'Okay, '})}"
    assert lines[1] == f"data: {json.dumps({'type': 'content', 'data': 'I can help '})}"
    assert lines[2] == f"data: {json.dumps({'type': 'content', 'data': 'with that.'})}"

    assert lines[3].startswith("data:")
    final_data_json = json.loads(lines[3].split("data: ")[1])
    assert final_data_json["type"] == "final_data"
    assert final_data_json["data"]["jobSummary"] == MOCK_JOB_SUMMARY.model_dump(exclude_none=True)
    assert final_data_json["data"]["imageUrls"][0] == MOCK_IMAGE_URLS[0].model_dump(exclude_none=True)
    assert final_data_json["data"]["imageClickableUrls"][0] == MOCK_CLICKABLE_URLS[0].model_dump(exclude_none=True)

    assert lines[4] == f"event: end\ndata: Stream finished"


@pytest.mark.asyncio
@patch("src.agents.DiagnosticAgentStreaming.DiagnosticAgentStreaming.process_next_message_stream", new_callable=MagicMock)
async def test_process_message_stream_agent_error(mock_process_stream, client: TestClient):
    mock_process_stream.return_value = mock_agent_stream_error() # Return generator yielding error

    headers = {"Authorization": f"Bearer {TEST_API_KEY}"}
    response = client.post("/message/stream", headers=headers, json=MOCK_REQUEST.model_dump())

    assert response.status_code == 200
    assert response.headers["content-type"] == "text/event-stream; charset=utf-8"

    lines = response.content.decode().strip().split('\n\n')

    assert lines[0] == f"data: {json.dumps({'type': 'content', 'data': 'Thinking...'})}"

    # Check error event
    assert lines[1].startswith("event: error")
    error_data_json = json.loads(lines[1].split("data: ")[1])
    assert error_data_json["code"] == "LLM_OVERLOADED"
    assert "Simulated overload" in error_data_json["details"]["original_error"]

    assert lines[2] == f"event: end\ndata: Stream finished after error"


@pytest.mark.asyncio
@patch("src.agents.DiagnosticAgentStreaming.DiagnosticAgentStreaming.process_next_message_stream", new_callable=MagicMock)
async def test_process_message_stream_no_summary_data(mock_process_stream, client: TestClient):
    mock_process_stream.return_value = mock_agent_stream_no_summary()

    headers = {"Authorization": f"Bearer {TEST_API_KEY}"}
    response = client.post("/message/stream", headers=headers, json=MOCK_REQUEST.model_dump())

    assert response.status_code == 200
    assert response.headers["content-type"] == "text/event-stream; charset=utf-8"

    lines = response.content.decode().strip().split('\n\n')

    assert lines[0] == f"data: {json.dumps({'type': 'content', 'data': 'Got it.'})}"

    assert lines[1].startswith("data:")
    final_data_json = json.loads(lines[1].split("data: ")[1])
    assert final_data_json["type"] == "final_data"
    assert final_data_json["data"]["jobSummary"] is None
    assert final_data_json["data"]["imageUrls"] is None
    assert final_data_json["data"]["imageClickableUrls"] is None

    assert lines[2] == f"event: end\ndata: Stream finished"


@pytest.mark.asyncio
async def test_process_message_stream_unauthorized(client: TestClient):
    headers = {"Authorization": "Bearer invalid-key"}
    response = client.post("/message/stream", headers=headers, json=MOCK_REQUEST.model_dump())
    assert response.status_code == 401 # Unauthorized

