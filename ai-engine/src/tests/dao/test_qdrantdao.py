import os

import pytest
from unittest.mock import AsyncMock, MagicMock

from src.dao.QdrantDAO import QdrantDAO


@pytest.fixture(autouse=True)
def mock_env():
    """Automatically set up test environment variables"""
    os.environ["QDRANT_DB_URL"] = "test_api_key"
    os.environ["QDRANT_API_KEY"] = "test_api_key"


@pytest.fixture
def dao():
    dao = QdrantDAO(AsyncMock(), MagicMock(), MagicMock())
    return dao


@pytest.mark.asyncio
async def test_upsert(dao):
    await dao.upsert("user1", "doc1", "url1", "content1", "file1")

    dao._openai_client.get_embedding.assert_called_once_with("content1")
    dao._qdrant_client_asynch.upsert.assert_awaited_once()


@pytest.mark.asyncio
async def test_query(dao):
    dao._openai_client.get_embedding.return_value = [0.1, 0.2, 0.3]
    dao._qdrant_client_asynch.search.return_value = [
        MagicMock(
            score=0.9,
            payload={
                "userId": "user1",
                "documentId": "doc1",
                "bucketName": "url1",
                "fileKey": "file1",
                "content": "content1",
            },
        ),
        MagicMock(
            score=0.8,
            payload={
                "userId": "user1",
                "documentId": "doc2",
                "bucketName": "url2",
                "fileKey": "file2",
                "content": "content2",
            },
        ),
    ]

    results = await dao.query("user1", "query", 2)

    dao._openai_client.get_embedding.assert_called_once_with("query")
    dao._qdrant_client_asynch.search.assert_awaited_once()

    assert len(results) == 2
    assert results[0].userId == "user1"
    assert results[0].score == 0.9
    assert results[1].userId == "user1"
    assert results[1].score == 0.8
