from os import environ

from fastapi import Depends
from sqlalchemy.ext.asyncio import AsyncSession

from src.agents.dataextractors.DataExtractorService import DataExtractorService
from src.database import sessionmanager
from src.integrations.backend import BackendAPI
from src.services.appliances import ApplianceService
from src.services.jobs import JobService
from src.services.property import PropertyService


async def get_db_session():
    async with sessionmanager.session() as session:
        yield session


def get_job_service(db: AsyncSession = Depends(get_db_session)) -> JobService:
    return JobService(db=db)


def get_appliance_service(db: AsyncSession = Depends(get_db_session)) -> ApplianceService:
    return ApplianceService(db=db)


def get_property_service(db: AsyncSession = Depends(get_db_session)) -> PropertyService:
    return PropertyService(db=db)


# the same symmetric api key for ai engine and backend for authentication
backend_api = BackendAPI(environ["AI_ENGINE_API_KEY"], environ["BACKEND_API_BASE_URL"])


def get_backend_api():
    return backend_api


def get_data_extractor_service(
    backend_api: BackendAPI = Depends(get_backend_api),
    appliance_service: ApplianceService = Depends(get_appliance_service),
    property_service: PropertyService = Depends(get_property_service),
) -> DataExtractorService:
    return DataExtractorService(
        backend_api=backend_api,
        appliance_service=appliance_service,
        property_service=property_service
    )
