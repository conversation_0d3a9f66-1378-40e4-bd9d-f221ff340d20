import asyncio

from fastapi import <PERSON><PERSON><PERSON><PERSON>, Depends, HTTPEx<PERSON>
from fastapi.security import <PERSON><PERSON><PERSON><PERSON>ear<PERSON>

from src.agents.dataextractors.DataExtractorService import DataExtractorService
from src.dependencies import get_data_extractor_service
from src.routers.message import verify_api_key
from src.schemas import ParseFileRequest, ParseFileResponse
from src.services.FileParser import FileParser

router = APIRouter(prefix="/parse_file", tags=["parse_file"])

security = HTTPBearer()


async def get_file_parser():
    return FileParser()


@router.post("/", response_model=ParseFileResponse)
async def parse_file(
    request: ParseFileRequest,
    file_parser: FileParser = Depends(get_file_parser),
    api_key: str = Depends(verify_api_key),
    data_extractor_service: DataExtractorService = Depends(get_data_extractor_service)
):
    try:
        # Create a background task for file processing
        asyncio.create_task(
            file_parser.process_file(
                user_id=request.userId,
                document_id=request.documentId,
                file_key=request.documentS3Key,
                bucket_name=request.documentS3Bucket,
                doc_type=request.type,
                data_extractor_service=data_extractor_service
            )
        )

        # Return success immediately without waiting for processing to complete
        return ParseFileResponse(status="success", errorMessage=None)

    except ValueError as ve:
        return ParseFileResponse(status="error", errorMessage=f"Invalid input or file processing error: {str(ve)}")
    except HTTPException as he:
        return ParseFileResponse(status="error", errorMessage=he.detail)
    except Exception as e:
        return ParseFileResponse(status="error", errorMessage=f"Unexpected error: {str(e)}")
