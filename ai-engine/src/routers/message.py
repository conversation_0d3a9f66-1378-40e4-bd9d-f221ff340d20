import json
import logging
import traceback
from http import HTT<PERSON>tatus

from fastapi import <PERSON>Router, Depends, HTTPException
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
import os

from starlette.responses import StreamingResponse

from src.agents.DiagnosticAgent import DiagnosticAgent
from src.agents.DiagnosticAgentStreaming import DiagnosticAgentStreaming
from src.dependencies_agents import get_diagnostic_agent, get_diagnostic_agent_streaming
from src.exceptions import LLMOverloadedException
from src.schemas import MessageResponse, MessageRequest, ResponseData, Message, ResponseAdditionalData, ErrorDetail

router = APIRouter(prefix="/message", tags=["message"])

security = HTTPBearer()
logger = logging.getLogger("uvicorn")

async def verify_api_key(credentials: HTTPAuthorizationCredentials = Depends(security)):
    if not credentials:
        raise HTTPException(status_code=401, detail="Not authenticated")

    try:
        # Get API key from AWS environment variable
        valid_api_key = os.environ.get("AI_ENGINE_API_KEY")

        if not valid_api_key:
            raise HTTPException(status_code=500, detail="API key configuration is missing")

        if credentials.credentials != valid_api_key:
            raise HTTPException(status_code=401, detail="Invalid API key")
        return credentials.credentials
    except HTTPException as he:
        raise he
    except Exception:
        raise HTTPException(status_code=401, detail="Could not validate credentials")


@router.post("/", response_model=MessageResponse)
async def process_message(
    request: MessageRequest,
    diagnostic_agent: DiagnosticAgent = Depends(get_diagnostic_agent),
    api_key: str = Depends(verify_api_key),
):
    try:

        diagnostic_agent_response, job_summary, image_urls, all_image_clickable_urls = await diagnostic_agent.process_next_message(
            request.message.content, request.userId, request.chatId, request.attachments
        )

        response = MessageResponse(
            status="success",
            data=ResponseData(
                chatId=request.chatId,
                response=Message(content=diagnostic_agent_response, type="text"),
                additionalData=ResponseAdditionalData(
                    category="general",
                    confidence=0.95,
                    jobSummary=job_summary,
                    imageUrls=image_urls,
                    imageClickableUrls=all_image_clickable_urls
                    # suggestedActions=[SuggestedAction(type="button", label="Click me", action="click")],
                ),
            ),
        )

        return response
    except LLMOverloadedException as e:
        stacktrace = traceback.format_exc()
        logger.error(f"LLM overload error: {str(e)}\nStacktrace: {stacktrace}")

        error_response = MessageResponse(
            status="error",
            error=ErrorDetail(
                code="LLM_OVERLOADED",
                message="The LLM service is currently overloaded",
                details={"original_error": str(e.detail)}
            ),
        )
        raise HTTPException(status_code=529, detail=error_response.model_dump())
    except Exception as e:
        stacktrace = traceback.format_exc()
        logger.error(f"Unexpected error: {str(e)}\nStacktrace: {stacktrace}")

        error_response = MessageResponse(
            status="error",
            error=ErrorDetail(
                code="INTERNAL_ERROR",
                message="An unexpected error occurred",
                details={"original_error": str(e)}
            ),
        )
        raise HTTPException(status_code=HTTPStatus.INTERNAL_SERVER_ERROR, detail=error_response.model_dump())

@router.post("/stream")
async def process_message_stream(
    request_data: MessageRequest, # Renamed to avoid conflict with FastAPI Request
    diagnostic_agent_streaming: DiagnosticAgentStreaming = Depends(get_diagnostic_agent_streaming),
    api_key: str = Depends(verify_api_key)
):
    logger.info(f"Received streaming request for chat: {request_data.chatId}, user: {request_data.userId}")

    async def stream_generator():
        try:
            stream = diagnostic_agent_streaming.process_next_message_stream(
                request_data.message.content,
                request_data.userId,
                request_data.chatId,
                request_data.attachments
            )
            async for chunk in stream:
                yield chunk

        except Exception as e:
            # Exception during the stream generation setup
            stacktrace = traceback.format_exc()
            logger.error(f"Error setting up or during stream generation: {str(e)}\nStacktrace: {stacktrace}")
            raise HTTPException(status_code=500, detail="Streaming error")

    return StreamingResponse(stream_generator(), media_type="text/event-stream")

