import logging
import sys
from logging.handlers import RotatingFileHandler


def setup_logging():
    # Create a formatter
    formatter = logging.Formatter("{asctime} {levelname} {name}:{lineno} {message}", style="{")

    # Create and configure the root logger
    root_logger = logging.getLogger()
    root_logger.setLevel(logging.INFO)

    # Console handler
    console_handler = logging.StreamHandler(sys.stdout)
    console_handler.setFormatter(formatter)
    root_logger.addHandler(console_handler)

    # Optional: File handler for rotating log files
    file_handler = RotatingFileHandler("ai-engine.log", maxBytes=1048576000, backupCount=5)  # 10MB
    file_handler.setFormatter(formatter)
    root_logger.addHandler(file_handler)

    # Prevent uvicorn default handler from duplicating logs
    for name in logging.root.manager.loggerDict:
        if name.startswith("uvicorn."):
            logging.getLogger(name).handlers = []
