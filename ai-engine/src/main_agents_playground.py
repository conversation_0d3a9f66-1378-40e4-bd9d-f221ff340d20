import asyncio
import os
import httpx
import json  # Import json for potential error parsing


FASTAPI_BASE_URL = os.environ.get("FASTAPI_URL", "http://127.0.0.1:8000")
STREAM_ENDPOINT = f"{FASTAPI_BASE_URL}/message/stream"


USER_ID = "53450154610784"
CHAT_ID = "34534519374629"


async def main():
    print("--- Console <PERSON>lient for Message Stream ---")
    print(f"Connecting to: {STREAM_ENDPOINT}")
    print("Type 'quit' to exit.")

    api_key = os.environ.get("AI_ENGINE_API_KEY")
    if not api_key:
        print("\nError: AI_ENGINE_API_KEY environment variable not set.")
        return

    headers = {
        "Authorization": f"Bearer {api_key}",
        "Content-Type": "application/json",
        "Accept": "text/event-stream",  # Important for SSE
    }

    async with httpx.AsyncClient(timeout=None) as client:  # Use timeout=None for potentially long streams
        while True:
            try:
                user_input = input("\nYou: ").strip()
            except EOFError:  # Handle Ctrl+D
                user_input = "quit"

            if user_input.lower() == "quit":
                print("Goodbye!")
                break

            if not user_input:
                print("Please type something!")
                continue

            payload = {
                "message": {"content": user_input, "type": "text"},
                "userId": USER_ID,
                "chatId": CHAT_ID,
                "messageId": 1,
                "attachments": [],
            }

            print("Alfie:", end="", flush=True)

            try:
                async with client.stream("POST", STREAM_ENDPOINT, json=payload, headers=headers) as response:
                    if response.status_code != 200:
                        error_body = await response.aread()
                        error_detail = ""
                        try:
                            error_data = json.loads(error_body.decode())
                            error_detail = f" - Detail: {error_data.get('detail', 'No detail provided')}"
                        except json.JSONDecodeError:
                            error_detail = f" - Body: {error_body.decode(errors='ignore')}"
                        except Exception:
                            error_detail = " - Could not parse error body"

                        print(f"\nError: Received status code {response.status_code}{error_detail}")
                        if response.status_code == 401:
                            print("Authentication failed. Check your AI_ENGINE_API_KEY.")
                            break
                        continue

                    # If status is OK (200), stream the response
                    async for chunk in response.aiter_text():
                        print(chunk, end="", flush=True)

                print()

            except httpx.ConnectError as e:
                print(f"\nConnection Error: Could not connect to the server at {FASTAPI_BASE_URL}.")
                print("Please ensure the FastAPI server is running and accessible.")
                print(f"Details: {e}")
                break
            except httpx.ReadTimeout:
                print("\nRequest timed out.")
                continue
            except Exception as e:
                print(f"\nAn unexpected error occurred during the request: {e}")
                import traceback

                traceback.print_exc()
                continue


if __name__ == "__main__":
    # server: uvicorn src.main:app --reload --port 8000
    # client: python main_agents_playground.py
    asyncio.run(main())
