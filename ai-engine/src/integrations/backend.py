import logging
from typing import Literal, Optional

from pydantic import BaseModel

from src.integrations.base import BaseAPI

logger = logging.getLogger("uvicorn")


class ApplianceData(BaseModel):
    type: Optional[str] = None
    brand: Optional[str] = None
    model: Optional[str] = None
    serialNumber: Optional[str] = None
    warranty: Optional[str] = None
    otherDetails: Optional[str] = None
    userId: int
    propertyId: Optional[int] = None


class UpdateDocumentStatusRequest(BaseModel):
    status: str
    aiGeneratedFileName: Optional[str] = None
    category: Optional[str] = None
    label: Optional[str] = None
    errorMessage: Optional[str] = None


class CreateApplianceRequest(BaseModel):
    appliance: ApplianceData
    srcType: Literal["chat", "document", "userInput"] = "document"
    srcId: int
    userId: int


class UpdateApplianceRequest(BaseModel):
    appliance: ApplianceData
    srcType: Literal["chat", "document", "userInput"] = "document"
    srcId: int


class Address(BaseModel):
    streetLine1: str
    streetLine2: Optional[str]
    townOrCity: str
    postcode: str
    houseAccess: Optional[str] = None
    parkingInstructions: Optional[str] = None


class PropertyDetailsData(BaseModel):
    address: Optional[Address] = None
    type: Optional[str] = None
    tenureType: Optional[str] = None
    sizeInSqft: Optional[int] = None
    onFloorLevel: Optional[int] = None
    hasBalconyTerrace: Optional[bool] = None
    balconyTerraceDetails: Optional[str] = None
    hasGarden: Optional[bool] = None
    gardenDetails: Optional[str] = None
    hasSwimmingPool: Optional[bool] = None
    swimmingPoolDetails: Optional[str] = None
    numberOfBedrooms: Optional[int] = None
    numberOfBathrooms: Optional[int] = None
    numberOfFloors: Optional[int] = None
    lastSoldPriceInGbp: Optional[int] = None
    condition: Optional[str] = None
    yearsOfOwnership: Optional[int] = None
    architecturalType: Optional[str] = None
    valuationInGbp: Optional[int] = None
    conservationStatus: Optional[str] = None
    typeOfLock: Optional[str] = None
    typeOfConstruction: Optional[str] = None
    proportionOfFlatRoof: Optional[int] = None
    epcRating: Optional[str] = None
    userId: int
    propertyId: int


class UpdatePropertyDetailsRequest(BaseModel):
    propertyDetails: PropertyDetailsData
    srcType: Literal["chat", "document", "userInput"] = "document"
    srcId: int


class BackendAPI(BaseAPI):
    async def update_document_status(self, document_id: int, update_data: UpdateDocumentStatusRequest) -> None:
        """Update document status in the backend."""
        payload = update_data.model_dump(exclude_none=True)

        await self._make_request(
            method="PATCH",
            endpoint=f"ai-engine/documents/{document_id}",
            payload=payload,
            operation_description=f"update document status for document {document_id}",
        )

    async def create_appliance(self, create_data: CreateApplianceRequest) -> dict:
        """Create a new appliance in the backend."""
        payload = create_data.model_dump(exclude_none=True)

        result = await self._make_request(
            method="POST",
            endpoint="ai-engine/appliances",
            payload=payload,
            operation_description=f"create appliance for property {create_data.appliance.propertyId}",
        )

        logger.info(f"Successfully created appliance with ID {result.get('id', 'unknown')}")
        return result

    async def update_appliance(self, appliance_id: int, update_data: UpdateApplianceRequest) -> dict:
        """Update an existing appliance in the backend."""
        payload = update_data.model_dump(exclude_none=True)

        result = await self._make_request(
            method="PATCH",
            endpoint=f"ai-engine/appliances/{appliance_id}",
            payload=payload,
            operation_description=f"update appliance {appliance_id}",
        )

        logger.info(f"Successfully updated appliance {appliance_id}")
        return result

    async def update_property_details(self, property_id: int, update_data: UpdatePropertyDetailsRequest) -> dict:
        """Update property details in the backend."""
        payload = update_data.model_dump(exclude_none=True)

        result = await self._make_request(
            method="PATCH",
            endpoint=f"ai-engine/property_details/{property_id}",
            payload=payload,
            operation_description=f"update property_details {property_id}",
        )

        logger.info(f"Successfully updated property_details {property_id}")
        return result
