import asyncio
import logging
from abc import ABC
from typing import Any, Dict, Optional
from urllib.parse import urljoin

import aiohttp
from fastapi import HTTPException
from sentry_sdk import capture_exception as sentry_capture_exception


class BaseAPI(ABC):
    _session = None
    _session_lock = asyncio.Lock()

    def __init__(self, api_key: str, base_url: str):
        self._api_key = api_key
        self._base_url = base_url

    async def _ensure_session(self):
        if self._session is None:
            async with self._session_lock:
                if self._session is None:
                    self.__class__._session = aiohttp.ClientSession()

    async def close(self):
        logging.info(f"Closing {self.__class__.__name__} ClientSession")
        if self._session:
            await self._session.close()
            self._session = None

    def _get_headers(self) -> Dict[str, str]:
        """Get the default headers for API requests."""
        return {"Authorization": f"Bearer {self._api_key}"}

    async def _make_request(
        self,
        method: str,
        endpoint: str,
        payload: Optional[Dict[str, Any]] = None,
        max_retries: int = 3,
        operation_description: str = "API request",
    ) -> Optional[Dict[str, Any]]:
        await self._ensure_session()
        url = urljoin(self._base_url, endpoint)
        headers = self._get_headers()

        logger = logging.getLogger("uvicorn")
        logger.info(f"Making {method} request to {endpoint} for {operation_description}")

        last_exception = None

        for attempt in range(max_retries):
            try:
                request_kwargs = {"headers": headers, "json": payload if payload else None}

                async with self._session.request(method, url, **request_kwargs) as response:
                    # Handle specific status codes
                    if response.status == 404:
                        error_detail = "Resource not found"
                        try:
                            error_body = await response.text()
                            if error_body:
                                error_detail += f": {error_body}"
                        except Exception:
                            pass
                        logger.error(f"{operation_description} failed: {error_detail}")
                        raise HTTPException(status_code=404, detail=error_detail)

                    elif response.status == 400:
                        error_detail = "Bad request"
                        try:
                            error_body = await response.text()
                            if error_body:
                                error_detail += f": {error_body}"
                        except Exception:
                            pass
                        logger.error(f"{operation_description} failed: {error_detail}")
                        raise HTTPException(status_code=400, detail=error_detail)

                    elif response.status >= 500:
                        error_detail = f"Server error ({response.status})"
                        try:
                            error_body = await response.text()
                            if error_body:
                                error_detail += f": {error_body}"
                        except Exception:
                            pass
                        logger.error(f"{operation_description} failed: {error_detail}")

                        # Retry on server errors
                        if attempt < max_retries - 1:
                            wait_time = 2**attempt  # Exponential backoff
                            logger.info(
                                f"Retrying {operation_description} in {wait_time} seconds "
                                f"(attempt {attempt + 1}/{max_retries})"
                            )
                            await asyncio.sleep(wait_time)
                            continue
                        else:
                            raise HTTPException(status_code=500, detail="Server error occurred")

                    elif response.status >= 400:
                        error_detail = f"Client error ({response.status})"
                        try:
                            error_body = await response.text()
                            if error_body:
                                error_detail += f": {error_body}"
                        except Exception:
                            pass
                        logger.error(f"{operation_description} failed: {error_detail}")
                        raise HTTPException(status_code=response.status, detail=error_detail)

                    # Success case
                    response.raise_for_status()

                    # Try to parse JSON response, return None if not JSON
                    try:
                        result = await response.json()
                        logger.info(f"Successfully completed {operation_description}")
                        return result
                    except aiohttp.ContentTypeError:
                        logger.info(f"Successfully completed {operation_description} (no JSON response)")
                        return None

            except HTTPException:
                # Don't retry HTTPExceptions, they're final
                raise
            except Exception as e:
                last_exception = e
                logger.warning(f"{operation_description} attempt {attempt + 1} failed: {str(e)}")

                if attempt < max_retries - 1:
                    wait_time = 2**attempt  # Exponential backoff
                    logger.info(f"Retrying {operation_description} in {wait_time} seconds")
                    await asyncio.sleep(wait_time)
                else:
                    logger.error(
                        f"{operation_description} failed after {max_retries} attempts: {str(e)}", exc_info=True
                    )
                    sentry_capture_exception(e)
                    raise HTTPException(status_code=500, detail=f"Failed to complete {operation_description}")

        # This should never be reached, but just in case
        if last_exception:
            sentry_capture_exception(last_exception)
            raise HTTPException(status_code=500, detail=f"Failed to complete {operation_description}")
