from fastapi import Depends

from src.agents.DiagnosticAgent import DiagnosticAgent
from src.agents.DiagnosticAgentStreaming import DiagnosticAgentStreaming
from src.agents.utils.PromptLoader import PromptLoader
from src.dependencies import get_job_service
from src.services.jobs import JobService


prompt_loader = PromptLoader(default_prompt_version="DiagnosticAgent_v5.txt")

# Load the system prompt using the PromptLoader
_SYSTEM_PROMPT = prompt_loader.load_system_prompt()


def get_diagnostic_agent(job_service: JobService = Depends(get_job_service)):
    # Create a new instance with the injected job_service
    return DiagnosticAgent(job_service=job_service, agent_prompt=_SYSTEM_PROMPT)


def get_diagnostic_agent_streaming(job_service: JobService = Depends(get_job_service)):
    # Create a new instance with the injected job_service
    return DiagnosticAgentStreaming(job_service=job_service, agent_prompt=_SYSTEM_PROMPT)
