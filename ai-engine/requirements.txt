langchain-qdrant
langchain-community~=0.3.17
langchain-openai~=0.3.4
tiktoken~=0.7.0
langchain~=0.3.16
langchain-core~=0.3.34
qdrant-client~=1.12.1
pandas~=2.2.3
pdf2image~=1.17.0
python-dotenv~=1.0.1
duckduckgo-search
fastapi~=0.115.7
pydantic~=2.10.6
uvicorn~=0.34.0
typing~=*******
fastapi_pagination
pytest_asyncio
faker~=35.0.0
asyncpg~=0.30.0
anthropic~=0.45.2
boto3~=1.36.7
openai~=1.61.1
together
requests~=2.32.3
langchain_together
langsmith~=0.3.5
beautifulsoup4~=4.13.3
python-magic~=0.4.27
aiohttp~=3.11.8
langchain-anthropic~=0.3.7

pytest~=8.3.4
SQLAlchemy~=2.0.37
pillow~=11.1.0
alembic~=1.14.1
numpy~=2.2.2
aiofiles~=24.1.0
google-generativeai

pytest-asyncio~=0.25.2
protobuf~=5.29.0
sentry-sdk~=2.24.1
fastapi-pagination~=0.12.34
hpack~=4.0.0
hyperframe~=6.0.1
h2~=4.1.0
ipython~=8.30.0
olefile~=0.47
html5lib~=1.1
lxml~=5.3.0
soupsieve~=2.6
trio~=0.27.0
idna~=3.10
httpcore~=1.0.7
httpx~=0.28.0
cryptography~=43.0.3
anyio~=4.6.2.post1
h11~=0.14.0
pip~=24.2
emoji~=2.14.0
attrs~=24.2.0
distro~=1.9.0
PySocks~=1.7.1
filelock~=3.16.1
cffi~=1.17.1
setuptools~=75.6.0
grpcio~=1.68.0
grpcio-tools~=1.68.0
Mako~=1.3.6
Pygments~=2.18.0
MarkupSafe~=3.0.2
nltk~=3.9.1
regex~=2024.11.6
click~=8.1.7
tqdm~=4.67.1
pyparsing~=3.2.1
pytz~=2024.2
rich~=13.9.4
markdown-it-py~=3.0.0
PyYAML~=6.0.2
multidict~=6.1.0
propcache~=0.2.0
typing_extensions~=4.12.2
sniffio~=1.3.1
outcome~=1.3.0.post0
python-dateutil~=2.9.0.post0
zstandard~=0.23.0
jiter~=0.6.1
mdurl~=0.1.2
overrides~=7.7.0
charset-normalizer~=3.4.0
primp~=0.10.0
pypdf~=5.1.0
typer~=0.14.0
shellingham~=1.5.4
joblib~=1.4.2
psutil~=6.1.1
websockets~=14.1
pydantic_core~=2.27.2
orjson~=3.10.12
openpyxl~=3.1.5
fsspec~=2024.10.0
pyarrow~=18.1.0
ptyprocess~=0.7.0
Jinja2~=3.1.4
certifi~=2024.8.30
uvloop~=0.21.0
wcwidth~=0.2.13
pluggy~=1.5.0
Deprecated~=1.2.15
iniconfig~=2.0.0
pexpect~=4.9.0
yarl~=1.18.0
aiosignal~=1.3.1
frozenlist~=1.5.0
aiohappyeyeballs~=2.4.4
importlib_resources~=6.4.5
importlib_metadata~=8.5.0
backoff~=2.2.1
chardet~=5.2.0
starlette~=0.41.3
urllib3~=2.3.0
httptools~=0.6.4
wsproto~=1.2.0
watchfiles~=1.0.0
six~=1.16.0
filetype~=1.2.0
greenlet~=3.1.1
webencodings~=0.5.1
annotated-types~=0.7.0
tenacity~=9.0.0
deprecation~=2.1.0
langchain-text-splitters~=0.3.6
requests-toolbelt~=1.0.0
packaging~=24.2
pycparser~=2.22
RapidFuzz~=3.11.0
langdetect~=1.0.9
tomli_w~=1.1.0
platformdirs~=4.3.6
zipp~=3.21.0
build~=1.2.2.post1
schema~=0.7.7
marshmallow~=3.23.1
bcrypt~=4.2.1
jsonpointer~=3.0.0
unstructured~=0.14.8
ndjson~=0.3.1
unstructured-client~=0.29.0
python-iso639~=2024.10.22
wrapt~=1.17.0
networkx~=3.4.2
portalocker~=2.10.1
jsonpatch~=1.33
typing-inspect~=0.9.0
dataclasses-json~=0.6.7
duckduckgo_search~=7.2.1
tomli~=2.2.1
pydantic-settings~=2.6.1
mypy-extensions~=1.0.0
eval_type_backport~=0.2.2
google-cloud-aiplatform~=1.73.0
google-api-python-client~=2.161.0
chromadb~=0.5.20
pdfplumber~=0.11.4
pypdfium2~=4.30.0
botocore~=1.36.16
selenium~=4.27.1
nest-asyncio~=1.6.0
pytest-mock