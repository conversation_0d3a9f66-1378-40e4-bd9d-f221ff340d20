[run]
branch = True
omit =
    src/alembic/*
    src/database.py
    src/tests/*
    test_*.py

[report]
; Regexes for lines to exclude from consideration
exclude_also =
    ; Don't complain about missing debug-only code:
    def __repr__
    if self\.debug

    ; Don't complain if tests don't hit defensive assertion code:
    raise AssertionError
    raise NotImplementedError

    ; Don't complain if non-runnable code isn't run:
    if 0:
    if __name__ == .__main__.:

    ; Don't complain about abstract methods, they aren't run:
    @(abc\.)?abstractmethod

ignore_errors = True
show_missing = True