[tox]
envlist = interation-tests, black, flake8
skipsdist = true

[pytest]
;asyncio_default_fixture_loop_scope=session
asyncio_mode=auto
addopts = --ignore=src/agents/dataextractors/test_document_labelling.py,src/agents/dataextractors/test_data_extractor.py

[testenv]
allowlist_externals =
    black
    flake8
    pytest

[testenv:interation-tests]
commands =
    pytest \
    --cov=src \
    --cov-config=.coveragerc \
    src
passenv = PYTHONPATH,DATABASE_URL, QDRANT_DB_URL, QDRANT_API_KEY, OPEN_AI_API_KEY, ANTHROPIC_API_KEY
sitepackages = true

[testenv:black]
commands = black -l 120 --check src --extend-exclude="src/(db_models|alembic|ai_base_rag|routers|dao|dependencies.py|agents|test)"

[testenv:flake8]
commands = flake8 --exclude src/alembic,src/test,src/agents,src/tests,src/db_models,src/ai_base_rag,main_old.py,message.py,src/dao/QdrantDAO.py --max-line-length=120 src
