
FROM python:3.12.6-slim AS builder

RUN apt-get update && apt-get -y upgrade
RUN pip install pipenv
RUN apt-get install libmagic1 -y
RUN apt-get install poppler-utils  -y
ENV PIPENV_VENV_IN_PROJECT=1
ADD Pipfile.lock Pipfile /usr/src/

WORKDIR /usr/src
RUN pipenv sync

FROM python:3.12.6-slim AS runtime

RUN apt-get update && apt-get -y upgrade
RUN apt-get install -y curl # for healthcheck
RUN mkdir -v /usr/src/.venv
RUN apt-get install libmagic1 -y
RUN apt-get install poppler-utils  -y
COPY --from=builder /usr/src/.venv/ /usr/src/.venv/

WORKDIR /app
COPY src /app/src
COPY prompts /app/prompts
COPY alembic.ini /app

CMD ["/usr/src/.venv/bin/uvicorn", "src.main:app", "--host", "0.0.0.0", "--port", "8000", "--reload"]

FROM builder AS test

WORKDIR /usr/src
RUN pipenv install --dev --system
RUN apt-get install libmagic1 -y

WORKDIR /app
COPY src /app/src
COPY prompts /app/prompts
COPY alembic.ini /app

COPY .coveragerc /app
COPY ./tox.ini /app
# qdrant_data_separation_integrationtest RUN
#CMD ["python", "src/tests/integration/qdrant_data_separation_integrationtest.py"]
CMD ["tox"]