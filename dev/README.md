# dev

<PERSON><PERSON><PERSON><PERSON>'s developer environment. It contains HeyA<PERSON>ie CLI that allows you to run the whole system locally.

**Note: This project is dedicated to devs with Mac OS! If you use a different OS, please reach out
to [<PERSON>otr <PERSON>liwa](<EMAIL>)**.

## Prerequisites

To use this, make sure you have the following tools installed:

- [Docker](https://docs.docker.com/desktop/setup/install/mac-install/)
- [Python](https://www.python.org/downloads/macos/)
- Your personal `.env` file for local development (contact [Piotr Śliwa](<EMAIL>) if you don't have it)

also ensure you have the following repositories cloned:
- [Backend](https://github.com/Hey-Alfie/backend)
- [Frontend](https://github.com/Hey-Alfie/frontend)
- [Ai-engine](https://github.com/Hey-Alfie/ai-engine)

## Run HeyAlfie locally

### 1. Copy your personal `.env` file to this directory

Copy the `.env` file to the directory here (contact [<PERSON><PERSON><PERSON>wa](<EMAIL>) if you don't have it).

### 2. Start the whole HeyAlfie system

The HeyAlfie CLI wraps the Docker Compose executable and passes all arguments to it so you can use

```bash
./heyalfie up --build
```

to build and run the whole system locally (you can alternatively add `-d` flag to detach the process,
and other parameters as per [Docker Compose CLI reference](https://docs.docker.com/reference/cli/docker/compose/)).

Docker Compose will have to access `frontend`, `backend`, and `ai-engine` repositories
which you should have cloned. It will build images based on the current version (you can checkout
any version and modify it however you need). The runner (see below) will ask you to provide paths to
the cloned repositories when you run it for the first time.

#### Services

Services started with the command above will be accessible via `localhost`:

- HeyAlfie Frontend: [`http://localhost:3000`](http://localhost:3000) (**Use this to access the HeyAlfie web app**)
- HeyAlfie Backend: `http://localhost:8000`
- HeyAlfie AI Engine: `http://localhost:8001`
- Postgres: `dev-user:password@localhost:5432/dev_db`
- Qdrant: `http://localhost:6333` (API key: `qdrant-key`)

### 3. Stop HeyAlfie

Use Command+C or

```bash
./heyalfie down
```

to stop.

### Debug selected components

This local environment allows you to debug selected components, with all other components running in Docker.
For example, you can run either Frontend, Backend, or AI Engine in debug mode in your IDE, and everything else
running in Docker.

First, replace `.env` files in the configured local `frontend`, `backend`, and `ai-engine` repositories. To do this
automatically, run

```bash
./heyalfie env
```

**Note: Make sure you run `./heyalfie up --build` before (the services need to be built to extract the env variables
with the command above).**

Then, you can run the system in Docker Compose, excluding selected services with `--scale SERVICE_NAME=0` parameter.

For example, to run everything but `backend`, run

```bash
./heyalfie up --build --scale backend=0
```

Then, you can run/debug the components in your IDE, e.g.

```bash
python src/main.py
```