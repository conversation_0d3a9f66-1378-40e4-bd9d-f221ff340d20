import os
import sys
import subprocess


def prompt(prompt_message, default=None):
    user_input = input(f"{prompt_message}{f' (default: {default})' if default else ''}: ").strip()
    return user_input if user_input else default


def get_repo_path(prompt_message, default_path):
    path = prompt(prompt_message, default_path)

    if not os.path.isdir(os.path.join(path, ".git")):
        print(f"Error: '{path}' is not a valid Git repository.")
        sys.exit(1)

    return path


def read_env():
    env_dict = {}
    env_file = ".env"
    if os.path.exists(env_file):
        with open(env_file, "r") as f:
            for line in f:
                line = line.strip()
                if line and not line.startswith("#") and "=" in line:
                    key, value = line.split("=", 1)
                    env_dict[key.strip()] = value.strip()
    else:
        print(f'Copy your personal .env file to this directory. Contact <EMAIL> for the file.')
        sys.exit(1)
    return env_dict


def store_env(env_dict):
    env_file = ".env"
    with open(env_file, "w") as f:
        for key, value in env_dict.items():
            line = f"{key}={value}\n"
            f.write(line)


def get_service_env(service):
    result = subprocess.run(
        ["docker", "compose", "run", service, "/bin/bash", "-c", "env | awk -F= '{print $1 \"=\" $2}'"],
        stdout=subprocess.PIPE,
        stderr=subprocess.PIPE,
        text=True,
        check=True
    )
    return result.stdout


def replace_hostnames(env: str):
    result = env.replace('qdrant:6333', 'localhost:6333')
    result = result.replace('postgres:5432', 'localhost:5432')
    result = result.replace('ai-engine:8000', 'localhost:8001')
    return result


def store_context_env(context, env):
    env = replace_hostnames(env)
    with open(f"{context}/.env", "w") as f:
        f.write(env)


def store_service_env(service, context):
    env = get_service_env(service)
    store_context_env(context, env)


def main():
    env = read_env()

    if 'BACKEND_CONTEXT' not in env:
        env['BACKEND_CONTEXT'] = get_repo_path("Path to the cloned backend application", '../backend')
    if 'FRONTEND_CONTEXT' not in env:
        env['FRONTEND_CONTEXT'] = get_repo_path("Path to the cloned frontend application", '../frontend')
    if 'AI_ENGINE_CONTEXT' not in env:
        env['AI_ENGINE_CONTEXT'] = get_repo_path("Path to the cloned ai-engine application", '../ai-engine')

    if sys.argv[1] == 'env':
        confirm = input(
            f"This will override .env files in ({env['BACKEND_CONTEXT']}, {env['FRONTEND_CONTEXT']}, and {env['AI_ENGINE_CONTEXT']}). Do you want to continue? (Y/n): ").strip().lower()
        if confirm not in ('y', 'yes', ''):
            print("Aborted.")
            return

        store_service_env('backend', env['BACKEND_CONTEXT'])
        store_service_env('ai-engine', env['AI_ENGINE_CONTEXT'])
        store_context_env(
            env['FRONTEND_CONTEXT'],
            '\n'.join([f"NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY={env['NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY']}",
                       f"NEXT_PUBLIC_CUSTOM_API_URL=http://localhost:8000",
                       f"NEXT_PUBLIC_VERCEL_ENV=staging",
                       f"CLERK_SECRET_KEY={env['CLERK_SECRET_KEY']}",
                       f"NEXT_PUBLIC_TEMP_TOKEN={env['NEXT_PUBLIC_TEMP_TOKEN']}"])
        )
        print('.env files updated.')
        return

    store_env(env)
    os.execvp("docker", ["docker", "compose", *sys.argv[1:]])


if __name__ == "__main__":
    main()
